package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: LazyEncodedSequence */
public class t1 extends r {

    /* renamed from: b  reason: collision with root package name */
    public byte[] f232b;

    public t1(byte[] bArr) throws IOException {
        this.f232b = bArr;
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        byte[] bArr = this.f232b;
        if (bArr != null) {
            pVar.e(48, bArr);
        } else {
            super.m().h(pVar);
        }
    }

    @Override // aa.q
    public int i() throws IOException {
        byte[] bArr = this.f232b;
        if (bArr != null) {
            return v1.a(bArr.length) + 1 + this.f232b.length;
        }
        return super.m().i();
    }

    @Override // aa.r, aa.q
    public q l() {
        if (this.f232b != null) {
            s();
        }
        return super.l();
    }

    @Override // aa.r, aa.q
    public q m() {
        if (this.f232b != null) {
            s();
        }
        return super.m();
    }

    @Override // aa.r
    public synchronized e p(int i10) {
        if (this.f232b != null) {
            s();
        }
        return (e) this.f220a.elementAt(i10);
    }

    @Override // aa.r
    public synchronized Enumeration q() {
        byte[] bArr = this.f232b;
        if (bArr == null) {
            return super.q();
        }
        return new s1(bArr);
    }

    public final void s() {
        s1 s1Var = new s1(this.f232b);
        while (s1Var.hasMoreElements()) {
            this.f220a.addElement(s1Var.nextElement());
        }
        this.f232b = null;
    }

    @Override // aa.r
    public synchronized int size() {
        if (this.f232b != null) {
            s();
        }
        return super.size();
    }
}
