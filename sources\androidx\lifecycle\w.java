package androidx.lifecycle;

import java.io.Closeable;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/* compiled from: ViewModelStore */
public class w {

    /* renamed from: a  reason: collision with root package name */
    public final HashMap<String, q> f2095a = new HashMap<>();

    public final void a() {
        for (q qVar : this.f2095a.values()) {
            qVar.f2091b = true;
            Map<String, Object> map = qVar.f2090a;
            if (map != null) {
                synchronized (map) {
                    for (Object obj : qVar.f2090a.values()) {
                        if (obj instanceof Closeable) {
                            try {
                                ((Closeable) obj).close();
                            } catch (IOException e10) {
                                throw new RuntimeException(e10);
                            }
                        }
                    }
                }
            }
            qVar.a();
        }
        this.f2095a.clear();
    }
}
