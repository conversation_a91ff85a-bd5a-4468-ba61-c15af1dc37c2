package aa;

import java.io.IOException;

/* compiled from: BERSequenceParser */
public class f0 implements s {

    /* renamed from: a  reason: collision with root package name */
    public v f178a;

    public f0(v vVar) {
        this.f178a = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            throw new IllegalStateException(e10.getMessage());
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new e0(this.f178a.c());
    }
}
