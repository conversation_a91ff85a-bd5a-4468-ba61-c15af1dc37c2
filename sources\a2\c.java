package a2;

import android.text.TextUtils;
import com.metv.airkan_sdk.exception.CbcKeyLengthException;

/* compiled from: CBCKey */
public class c {

    /* renamed from: a  reason: collision with root package name */
    public String f13a;

    /* renamed from: b  reason: collision with root package name */
    public String f14b;

    public c() {
    }

    public void a(String str) throws CbcKeyLengthException {
        if (TextUtils.isEmpty(str) || str.length() != 16) {
            throw new CbcKeyLengthException();
        }
        this.f14b = str;
    }

    public void b(String str) throws CbcKeyLengthException {
        if (TextUtils.isEmpty(str) || str.length() != 16) {
            throw new CbcKeyLengthException();
        }
        this.f13a = str;
    }

    public c(String str, String str2) throws Cbc<PERSON>eyLengthException {
        b(str);
        a(str2);
    }
}
