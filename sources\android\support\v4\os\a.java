package android.support.v4.os;

import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import android.support.v4.os.ResultReceiver;
import java.util.Objects;

/* compiled from: IResultReceiver */
public interface a extends IInterface {

    /* renamed from: android.support.v4.os.a$a  reason: collision with other inner class name */
    /* compiled from: IResultReceiver */
    public static abstract class AbstractBinderC0005a extends Binder implements a {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int f266a = 0;

        /* renamed from: android.support.v4.os.a$a$a  reason: collision with other inner class name */
        /* compiled from: IResultReceiver */
        public static class C0006a implements a {

            /* renamed from: a  reason: collision with root package name */
            public IBinder f267a;

            public C0006a(IBinder iBinder) {
                this.f267a = iBinder;
            }

            public IBinder asBinder() {
                return this.f267a;
            }
        }

        public AbstractBinderC0005a() {
            attachInterface(this, "android.support.v4.os.IResultReceiver");
        }

        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i10, Parcel parcel, Parcel parcel2, int i11) throws RemoteException {
            if (i10 == 1) {
                parcel.enforceInterface("android.support.v4.os.IResultReceiver");
                parcel.readInt();
                if (parcel.readInt() != 0) {
                    Bundle bundle = (Bundle) Bundle.CREATOR.createFromParcel(parcel);
                }
                ResultReceiver.b bVar = (ResultReceiver.b) this;
                Objects.requireNonNull(ResultReceiver.this);
                Objects.requireNonNull(ResultReceiver.this);
                return true;
            } else if (i10 != 1598968902) {
                return super.onTransact(i10, parcel, parcel2, i11);
            } else {
                parcel2.writeString("android.support.v4.os.IResultReceiver");
                return true;
            }
        }
    }
}
