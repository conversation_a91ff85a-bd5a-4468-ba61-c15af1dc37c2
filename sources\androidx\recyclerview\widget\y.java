package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.a;

/* compiled from: RecyclerView */
public class y implements a.AbstractC0015a {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ RecyclerView f2480a;

    public y(RecyclerView recyclerView) {
        this.f2480a = recyclerView;
    }

    public void a(a.b bVar) {
        int i10 = bVar.f2337a;
        if (i10 == 1) {
            RecyclerView recyclerView = this.f2480a;
            recyclerView.f2155l.f0(recyclerView, bVar.f2338b, bVar.f2340d);
        } else if (i10 == 2) {
            RecyclerView recyclerView2 = this.f2480a;
            recyclerView2.f2155l.i0(recyclerView2, bVar.f2338b, bVar.f2340d);
        } else if (i10 == 4) {
            RecyclerView recyclerView3 = this.f2480a;
            recyclerView3.f2155l.j0(recyclerView3, bVar.f2338b, bVar.f2340d, bVar.f2339c);
        } else if (i10 == 8) {
            RecyclerView recyclerView4 = this.f2480a;
            recyclerView4.f2155l.h0(recyclerView4, bVar.f2338b, bVar.f2340d, 1);
        }
    }

    public RecyclerView.w b(int i10) {
        RecyclerView recyclerView = this.f2480a;
        int h = recyclerView.f2145e.h();
        int i11 = 0;
        RecyclerView.w wVar = null;
        while (true) {
            if (i11 >= h) {
                break;
            }
            RecyclerView.w K = RecyclerView.K(recyclerView.f2145e.g(i11));
            if (K != null && !K.l() && K.f2269c == i10) {
                if (!recyclerView.f2145e.k(K.f2267a)) {
                    wVar = K;
                    break;
                }
                wVar = K;
            }
            i11++;
        }
        if (wVar != null && !this.f2480a.f2145e.k(wVar.f2267a)) {
            return wVar;
        }
        return null;
    }

    public void c(int i10, int i11, Object obj) {
        int i12;
        int i13;
        RecyclerView recyclerView = this.f2480a;
        int h = recyclerView.f2145e.h();
        int i14 = i11 + i10;
        for (int i15 = 0; i15 < h; i15++) {
            View g10 = recyclerView.f2145e.g(i15);
            RecyclerView.w K = RecyclerView.K(g10);
            if (K != null && !K.t() && (i13 = K.f2269c) >= i10 && i13 < i14) {
                K.b(2);
                K.a(obj);
                ((RecyclerView.LayoutParams) g10.getLayoutParams()).f2190c = true;
            }
        }
        RecyclerView.p pVar = recyclerView.f2140b;
        int size = pVar.f2226c.size();
        while (true) {
            size--;
            if (size >= 0) {
                RecyclerView.w wVar = pVar.f2226c.get(size);
                if (wVar != null && (i12 = wVar.f2269c) >= i10 && i12 < i14) {
                    wVar.b(2);
                    pVar.f(size);
                }
            } else {
                this.f2480a.V0 = true;
                return;
            }
        }
    }

    public void d(int i10, int i11) {
        RecyclerView recyclerView = this.f2480a;
        int h = recyclerView.f2145e.h();
        for (int i12 = 0; i12 < h; i12++) {
            RecyclerView.w K = RecyclerView.K(recyclerView.f2145e.g(i12));
            if (K != null && !K.t() && K.f2269c >= i10) {
                K.p(i11, false);
                recyclerView.R0.f2251f = true;
            }
        }
        RecyclerView.p pVar = recyclerView.f2140b;
        int size = pVar.f2226c.size();
        for (int i13 = 0; i13 < size; i13++) {
            RecyclerView.w wVar = pVar.f2226c.get(i13);
            if (wVar != null && wVar.f2269c >= i10) {
                wVar.p(i11, true);
            }
        }
        recyclerView.requestLayout();
        this.f2480a.U0 = true;
    }

    public void e(int i10, int i11) {
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        int i17;
        int i18;
        RecyclerView recyclerView = this.f2480a;
        int h = recyclerView.f2145e.h();
        int i19 = -1;
        if (i10 < i11) {
            i14 = i10;
            i13 = i11;
            i12 = -1;
        } else {
            i13 = i10;
            i14 = i11;
            i12 = 1;
        }
        for (int i20 = 0; i20 < h; i20++) {
            RecyclerView.w K = RecyclerView.K(recyclerView.f2145e.g(i20));
            if (K != null && (i18 = K.f2269c) >= i14 && i18 <= i13) {
                if (i18 == i10) {
                    K.p(i11 - i10, false);
                } else {
                    K.p(i12, false);
                }
                recyclerView.R0.f2251f = true;
            }
        }
        RecyclerView.p pVar = recyclerView.f2140b;
        if (i10 < i11) {
            i16 = i10;
            i15 = i11;
        } else {
            i15 = i10;
            i16 = i11;
            i19 = 1;
        }
        int size = pVar.f2226c.size();
        for (int i21 = 0; i21 < size; i21++) {
            RecyclerView.w wVar = pVar.f2226c.get(i21);
            if (wVar != null && (i17 = wVar.f2269c) >= i16 && i17 <= i15) {
                if (i17 == i10) {
                    wVar.p(i11 - i10, false);
                } else {
                    wVar.p(i19, false);
                }
            }
        }
        recyclerView.requestLayout();
        this.f2480a.U0 = true;
    }
}
