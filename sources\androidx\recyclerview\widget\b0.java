package androidx.recyclerview.widget;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.l;
import com.duokan.airkan.common.Constant;

/* compiled from: SimpleItemAnimator */
public abstract class b0 extends RecyclerView.ItemAnimator {

    /* renamed from: g  reason: collision with root package name */
    public boolean f2341g = true;

    @Override // androidx.recyclerview.widget.RecyclerView.ItemAnimator
    public boolean a(@NonNull RecyclerView.w wVar, @NonNull RecyclerView.w wVar2, @NonNull RecyclerView.ItemAnimator.c cVar, @NonNull RecyclerView.ItemAnimator.c cVar2) {
        int i10;
        int i11;
        int i12 = cVar.f2186a;
        int i13 = cVar.f2187b;
        if (wVar2.t()) {
            int i14 = cVar.f2186a;
            i10 = cVar.f2187b;
            i11 = i14;
        } else {
            i11 = cVar2.f2186a;
            i10 = cVar2.f2187b;
        }
        l lVar = (l) this;
        if (wVar == wVar2) {
            return lVar.i(wVar, i12, i13, i11, i10);
        }
        float translationX = wVar.f2267a.getTranslationX();
        float translationY = wVar.f2267a.getTranslationY();
        float alpha = wVar.f2267a.getAlpha();
        lVar.n(wVar);
        wVar.f2267a.setTranslationX(translationX);
        wVar.f2267a.setTranslationY(translationY);
        wVar.f2267a.setAlpha(alpha);
        lVar.n(wVar2);
        wVar2.f2267a.setTranslationX((float) (-((int) (((float) (i11 - i12)) - translationX))));
        wVar2.f2267a.setTranslationY((float) (-((int) (((float) (i10 - i13)) - translationY))));
        wVar2.f2267a.setAlpha(Constant.VOLUME_FLOAT_MIN);
        lVar.f2390k.add(new l.a(wVar, wVar2, i12, i13, i11, i10));
        return true;
    }

    public abstract boolean i(RecyclerView.w wVar, int i10, int i11, int i12, int i13);
}
