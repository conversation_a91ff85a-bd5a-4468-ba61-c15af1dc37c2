syntax = "proto3";

package com.xiaomi.idm.service.iot.proto;

option java_package = "com.xiaomi.idm.service.iot.proto";
option java_outer_classname = "IotLocalControlServiceProto";

  message GetDevices {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
  bool isLocal = 4;
}
  message GetHomes {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
}
  message GetScenes {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
}
  message GetDeviceInformations {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
  string deviceId = 4;
}
  message GetHomeFastCommands {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
}
  message GetDeviceProperties {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
  string propertyId = 4;
}
  message SetDeviceProperties {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
  string propertyBody = 4;
  bool isSort = 5;
}
  message ExeScenes {
  int32 aid = 1;
  string serviceToken = 2;
  string appId = 3;
  string sceneId = 4;
}
  message SetToken {
  int32 aid = 1;
  string appId = 2;
  string serviceToken = 3;
}
  message Stop {
  int32 aid = 1;
  string appId = 2;
  string serviceToken = 3;
}
  message GetSceneAlias {
  int32 aid = 1;
  string appId = 2;
  string serviceToken = 3;
  string sceneName = 4;
}
  message InvokeAction {
  int32 aid = 1;
  string appId = 2;
  string serviceToken = 3;
  string actionBody = 4;
}
  message IotResponse {
  int32 code = 1;
  string message = 2;
  string response = 3;
}