package aa;

import com.duokan.airkan.server.f;
import java.io.IOException;

/* compiled from: DERBitString */
public class n0 extends b {
    public n0(byte[] bArr) {
        super(bArr, 0);
    }

    public static n0 p(Object obj) {
        if (obj == null || (obj instanceof n0)) {
            return (n0) obj;
        }
        if (obj instanceof k1) {
            k1 k1Var = (k1) obj;
            return new n0(k1Var.f161a, k1Var.f162b);
        }
        StringBuilder a10 = f.a("illegal object in getInstance: ");
        a10.append(obj.getClass().getName());
        throw new IllegalArgumentException(a10.toString());
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        byte[] n10 = b.n(this.f161a, this.f162b);
        int length = n10.length + 1;
        byte[] bArr = new byte[length];
        bArr[0] = (byte) this.f162b;
        System.arraycopy(n10, 0, bArr, 1, length - 1);
        pVar.e(3, bArr);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f161a.length + 1) + 1 + this.f161a.length + 1;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public n0(byte[] bArr, int i10) {
        super(bArr, i10);
    }

    public n0(e eVar) throws IOException {
        super(eVar.c().f("DER"), 0);
    }
}
