package androidx.recyclerview.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.p;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.n;
import com.xiaomi.mitv.pie.EventResultPersister;
import java.util.Objects;
import k0.b;

public class GridLayoutManager extends LinearLayoutManager {
    public boolean E = false;
    public int F = -1;
    public int[] G;
    public View[] H;
    public final SparseIntArray I = new SparseIntArray();
    public final SparseIntArray J = new SparseIntArray();
    public b K = new a();
    public final Rect L = new Rect();

    public static final class a extends b {
    }

    public static abstract class b {

        /* renamed from: a  reason: collision with root package name */
        public final SparseIntArray f2099a = new SparseIntArray();

        /* renamed from: b  reason: collision with root package name */
        public final SparseIntArray f2100b = new SparseIntArray();

        public int a(int i10, int i11) {
            int i12 = 0;
            int i13 = 0;
            for (int i14 = 0; i14 < i10; i14++) {
                i12++;
                if (i12 == i11) {
                    i13++;
                    i12 = 0;
                } else if (i12 > i11) {
                    i13++;
                    i12 = 1;
                }
            }
            return i12 + 1 > i11 ? i13 + 1 : i13;
        }
    }

    public GridLayoutManager(Context context, AttributeSet attributeSet, int i10, int i11) {
        super(context, attributeSet, i10, i11);
        B1(RecyclerView.j.R(context, attributeSet, i10, i11).f2215b);
    }

    public final void A1(View view, int i10, int i11, boolean z10) {
        boolean z11;
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
        if (z10) {
            z11 = H0(view, i10, i11, layoutParams);
        } else {
            z11 = F0(view, i10, i11, layoutParams);
        }
        if (z11) {
            view.measure(i10, i11);
        }
    }

    public void B1(int i10) {
        if (i10 != this.F) {
            this.E = true;
            if (i10 >= 1) {
                this.F = i10;
                this.K.f2099a.clear();
                w0();
                return;
            }
            throw new IllegalArgumentException(p.a("Span count should be at least 1. Provided ", i10));
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void C0(Rect rect, int i10, int i11) {
        int i12;
        int i13;
        if (this.G == null) {
            super.C0(rect, i10, i11);
        }
        int O = O() + N();
        int M = M() + P();
        if (this.f2101p == 1) {
            i13 = RecyclerView.j.h(i11, rect.height() + M, K());
            int[] iArr = this.G;
            i12 = RecyclerView.j.h(i10, iArr[iArr.length - 1] + O, L());
        } else {
            i12 = RecyclerView.j.h(i10, rect.width() + O, L());
            int[] iArr2 = this.G;
            i13 = RecyclerView.j.h(i11, iArr2[iArr2.length - 1] + M, K());
        }
        this.f2199b.setMeasuredDimension(i12, i13);
    }

    public final void C1() {
        int i10;
        int i11;
        if (this.f2101p == 1) {
            i11 = this.f2210n - O();
            i10 = N();
        } else {
            i11 = this.f2211o - M();
            i10 = P();
        }
        t1(i11 - i10);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public boolean K0() {
        return this.f2111z == null && !this.E;
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager
    public void M0(RecyclerView.t tVar, LinearLayoutManager.c cVar, RecyclerView.j.c cVar2) {
        int i10 = this.F;
        for (int i11 = 0; i11 < this.F && cVar.b(tVar) && i10 > 0; i11++) {
            ((n.b) cVar2).a(cVar.f2127d, Math.max(0, cVar.f2130g));
            Objects.requireNonNull(this.K);
            i10--;
            cVar.f2127d += cVar.f2128e;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int S(RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2101p == 0) {
            return this.F;
        }
        if (tVar.b() < 1) {
            return 0;
        }
        return w1(pVar, tVar, tVar.b() - 1) + 1;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:52:0x00cc, code lost:
        if (r13 == (r2 > r15)) goto L_0x00ce;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:68:0x00fe, code lost:
        if (r13 == (r2 > r9)) goto L_0x0100;
     */
    /* JADX WARNING: Removed duplicated region for block: B:73:0x010a  */
    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.view.View a0(android.view.View r23, int r24, androidx.recyclerview.widget.RecyclerView.p r25, androidx.recyclerview.widget.RecyclerView.t r26) {
        /*
        // Method dump skipped, instructions count: 332
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.GridLayoutManager.a0(android.view.View, int, androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t):android.view.View");
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager
    public View b1(RecyclerView.p pVar, RecyclerView.t tVar, int i10, int i11, int i12) {
        R0();
        int k10 = this.f2103r.k();
        int g10 = this.f2103r.g();
        int i13 = i11 > i10 ? 1 : -1;
        View view = null;
        View view2 = null;
        while (i10 != i11) {
            View w10 = w(i10);
            int Q = Q(w10);
            if (Q >= 0 && Q < i12 && x1(pVar, tVar, Q) == 0) {
                if (((RecyclerView.LayoutParams) w10.getLayoutParams()).c()) {
                    if (view2 == null) {
                        view2 = w10;
                    }
                } else if (this.f2103r.e(w10) < g10 && this.f2103r.b(w10) >= k10) {
                    return w10;
                } else {
                    if (view == null) {
                        view = w10;
                    }
                }
            }
            i10 += i13;
        }
        return view != null ? view : view2;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void e0(RecyclerView.p pVar, RecyclerView.t tVar, View view, k0.b bVar) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (!(layoutParams instanceof LayoutParams)) {
            d0(view, bVar);
            return;
        }
        LayoutParams layoutParams2 = (LayoutParams) layoutParams;
        int w12 = w1(pVar, tVar, layoutParams2.a());
        if (this.f2101p == 0) {
            bVar.n(b.c.a(layoutParams2.f2097e, layoutParams2.f2098f, w12, 1, false, false));
        } else {
            bVar.n(b.c.a(w12, 1, layoutParams2.f2097e, layoutParams2.f2098f, false, false));
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void f0(RecyclerView recyclerView, int i10, int i11) {
        this.K.f2099a.clear();
        this.K.f2100b.clear();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean g(RecyclerView.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void g0(RecyclerView recyclerView) {
        this.K.f2099a.clear();
        this.K.f2100b.clear();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void h0(RecyclerView recyclerView, int i10, int i11, int i12) {
        this.K.f2099a.clear();
        this.K.f2100b.clear();
    }

    /* JADX DEBUG: Multi-variable search result rejected for r17v0, resolved type: androidx.recyclerview.widget.GridLayoutManager */
    /* JADX DEBUG: Multi-variable search result rejected for r13v9, resolved type: android.graphics.Rect */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r11v27, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r11v35 */
    /* JADX WARN: Type inference failed for: r11v36 */
    /* JADX WARN: Type inference failed for: r11v37 */
    /* JADX WARN: Type inference failed for: r11v44 */
    /* JADX WARNING: Unknown variable types count: 1 */
    @Override // androidx.recyclerview.widget.LinearLayoutManager
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void h1(androidx.recyclerview.widget.RecyclerView.p r18, androidx.recyclerview.widget.RecyclerView.t r19, androidx.recyclerview.widget.LinearLayoutManager.c r20, androidx.recyclerview.widget.LinearLayoutManager.b r21) {
        /*
        // Method dump skipped, instructions count: 604
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.GridLayoutManager.h1(androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t, androidx.recyclerview.widget.LinearLayoutManager$c, androidx.recyclerview.widget.LinearLayoutManager$b):void");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void i0(RecyclerView recyclerView, int i10, int i11) {
        this.K.f2099a.clear();
        this.K.f2100b.clear();
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager
    public void i1(RecyclerView.p pVar, RecyclerView.t tVar, LinearLayoutManager.a aVar, int i10) {
        C1();
        if (tVar.b() > 0 && !tVar.f2252g) {
            boolean z10 = i10 == 1;
            int x12 = x1(pVar, tVar, aVar.f2116b);
            if (z10) {
                while (x12 > 0) {
                    int i11 = aVar.f2116b;
                    if (i11 <= 0) {
                        break;
                    }
                    int i12 = i11 - 1;
                    aVar.f2116b = i12;
                    x12 = x1(pVar, tVar, i12);
                }
            } else {
                int b10 = tVar.b() - 1;
                int i13 = aVar.f2116b;
                while (i13 < b10) {
                    int i14 = i13 + 1;
                    int x13 = x1(pVar, tVar, i14);
                    if (x13 <= x12) {
                        break;
                    }
                    i13 = i14;
                    x12 = x13;
                }
                aVar.f2116b = i13;
            }
        }
        u1();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void j0(RecyclerView recyclerView, int i10, int i11, Object obj) {
        this.K.f2099a.clear();
        this.K.f2100b.clear();
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public void k0(RecyclerView.p pVar, RecyclerView.t tVar) {
        if (tVar.f2252g) {
            int x8 = x();
            for (int i10 = 0; i10 < x8; i10++) {
                LayoutParams layoutParams = (LayoutParams) w(i10).getLayoutParams();
                int a10 = layoutParams.a();
                this.I.put(a10, layoutParams.f2098f);
                this.J.put(a10, layoutParams.f2097e);
            }
        }
        super.k0(pVar, tVar);
        this.I.clear();
        this.J.clear();
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int l(RecyclerView.t tVar) {
        return O0(tVar);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public void l0(RecyclerView.t tVar) {
        this.f2111z = null;
        this.f2109x = -1;
        this.f2110y = EventResultPersister.GENERATE_NEW_ID;
        this.A.d();
        this.E = false;
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int m(RecyclerView.t tVar) {
        return P0(tVar);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int o(RecyclerView.t tVar) {
        return O0(tVar);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int p(RecyclerView.t tVar) {
        return P0(tVar);
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager
    public void p1(boolean z10) {
        if (!z10) {
            d(null);
            if (this.f2107v) {
                this.f2107v = false;
                w0();
                return;
            }
            return;
        }
        throw new UnsupportedOperationException("GridLayoutManager does not support stack from end. Consider using reverse layout");
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams t() {
        if (this.f2101p == 0) {
            return new LayoutParams(-2, -1);
        }
        return new LayoutParams(-1, -2);
    }

    public final void t1(int i10) {
        int i11;
        int[] iArr = this.G;
        int i12 = this.F;
        if (!(iArr != null && iArr.length == i12 + 1 && iArr[iArr.length - 1] == i10)) {
            iArr = new int[(i12 + 1)];
        }
        int i13 = 0;
        iArr[0] = 0;
        int i14 = i10 / i12;
        int i15 = i10 % i12;
        int i16 = 0;
        for (int i17 = 1; i17 <= i12; i17++) {
            i13 += i15;
            if (i13 <= 0 || i12 - i13 >= i15) {
                i11 = i14;
            } else {
                i11 = i14 + 1;
                i13 -= i12;
            }
            i16 += i11;
            iArr[i17] = i16;
        }
        this.G = iArr;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams u(Context context, AttributeSet attributeSet) {
        return new LayoutParams(context, attributeSet);
    }

    public final void u1() {
        View[] viewArr = this.H;
        if (viewArr == null || viewArr.length != this.F) {
            this.H = new View[this.F];
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams v(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return new LayoutParams((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return new LayoutParams(layoutParams);
    }

    public int v1(int i10, int i11) {
        if (this.f2101p != 1 || !g1()) {
            int[] iArr = this.G;
            return iArr[i11 + i10] - iArr[i10];
        }
        int[] iArr2 = this.G;
        int i12 = this.F;
        return iArr2[i12 - i10] - iArr2[(i12 - i10) - i11];
    }

    public final int w1(RecyclerView.p pVar, RecyclerView.t tVar, int i10) {
        if (!tVar.f2252g) {
            return this.K.a(i10, this.F);
        }
        int c10 = pVar.c(i10);
        if (c10 != -1) {
            return this.K.a(c10, this.F);
        }
        Log.w("GridLayoutManager", "Cannot find span size for pre layout position. " + i10);
        return 0;
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int x0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        C1();
        u1();
        if (this.f2101p == 1) {
            return 0;
        }
        return n1(i10, pVar, tVar);
    }

    public final int x1(RecyclerView.p pVar, RecyclerView.t tVar, int i10) {
        if (!tVar.f2252g) {
            b bVar = this.K;
            int i11 = this.F;
            Objects.requireNonNull(bVar);
            return i10 % i11;
        }
        int i12 = this.J.get(i10, -1);
        if (i12 != -1) {
            return i12;
        }
        int c10 = pVar.c(i10);
        if (c10 == -1) {
            Log.w("GridLayoutManager", "Cannot find span size for pre layout position. It is not cached, not in the adapter. Pos:" + i10);
            return 0;
        }
        b bVar2 = this.K;
        int i13 = this.F;
        Objects.requireNonNull(bVar2);
        return c10 % i13;
    }

    public final int y1(RecyclerView.p pVar, RecyclerView.t tVar, int i10) {
        if (!tVar.f2252g) {
            Objects.requireNonNull(this.K);
            return 1;
        }
        int i11 = this.I.get(i10, -1);
        if (i11 != -1) {
            return i11;
        }
        if (pVar.c(i10) == -1) {
            Log.w("GridLayoutManager", "Cannot find span size for pre layout position. It is not cached, not in the adapter. Pos:" + i10);
            return 1;
        }
        Objects.requireNonNull(this.K);
        return 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int z(RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2101p == 1) {
            return this.F;
        }
        if (tVar.b() < 1) {
            return 0;
        }
        return w1(pVar, tVar, tVar.b() - 1) + 1;
    }

    @Override // androidx.recyclerview.widget.LinearLayoutManager, androidx.recyclerview.widget.RecyclerView.j
    public int z0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        C1();
        u1();
        if (this.f2101p == 0) {
            return 0;
        }
        return n1(i10, pVar, tVar);
    }

    public final void z1(View view, int i10, boolean z10) {
        int i11;
        int i12;
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        Rect rect = layoutParams.f2189b;
        int i13 = rect.top + rect.bottom + ((ViewGroup.MarginLayoutParams) layoutParams).topMargin + ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
        int i14 = rect.left + rect.right + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
        int v12 = v1(layoutParams.f2097e, layoutParams.f2098f);
        if (this.f2101p == 1) {
            i11 = RecyclerView.j.y(v12, i10, i14, ((ViewGroup.MarginLayoutParams) layoutParams).width, false);
            i12 = RecyclerView.j.y(this.f2103r.l(), this.f2209m, i13, ((ViewGroup.MarginLayoutParams) layoutParams).height, true);
        } else {
            int y10 = RecyclerView.j.y(v12, i10, i13, ((ViewGroup.MarginLayoutParams) layoutParams).height, false);
            int y11 = RecyclerView.j.y(this.f2103r.l(), this.f2208l, i14, ((ViewGroup.MarginLayoutParams) layoutParams).width, true);
            i12 = y10;
            i11 = y11;
        }
        A1(view, i11, i12, z10);
    }

    public static class LayoutParams extends RecyclerView.LayoutParams {

        /* renamed from: e  reason: collision with root package name */
        public int f2097e = -1;

        /* renamed from: f  reason: collision with root package name */
        public int f2098f = 0;

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    public GridLayoutManager(Context context, int i10, int i11, boolean z10) {
        super(i11, z10);
        B1(i10);
    }
}
