package aa;

import androidx.appcompat.widget.p;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/* compiled from: DERExternal */
public class o0 extends q {

    /* renamed from: a  reason: collision with root package name */
    public m f207a;

    /* renamed from: b  reason: collision with root package name */
    public j f208b;

    /* renamed from: c  reason: collision with root package name */
    public q f209c;

    /* renamed from: d  reason: collision with root package name */
    public int f210d;

    /* renamed from: e  reason: collision with root package name */
    public q f211e;

    public o0(f fVar) {
        int i10 = 0;
        q n10 = n(fVar, 0);
        if (n10 instanceof m) {
            this.f207a = (m) n10;
            n10 = n(fVar, 1);
            i10 = 1;
        }
        if (n10 instanceof j) {
            this.f208b = (j) n10;
            i10++;
            n10 = n(fVar, i10);
        }
        if (!(n10 instanceof x)) {
            this.f209c = n10;
            i10++;
            n10 = n(fVar, i10);
        }
        if (fVar.b() != i10 + 1) {
            throw new IllegalArgumentException("input vector too large");
        } else if (n10 instanceof x) {
            x xVar = (x) n10;
            int i11 = xVar.f242a;
            if (i11 < 0 || i11 > 2) {
                throw new IllegalArgumentException(p.a("invalid encoding value: ", i11));
            }
            this.f210d = i11;
            this.f211e = xVar.n();
        } else {
            throw new IllegalArgumentException("No tagged object found in vector. Structure doesn't seem to be of type External");
        }
    }

    @Override // aa.q
    public boolean g(q qVar) {
        q qVar2;
        j jVar;
        m mVar;
        if (!(qVar instanceof o0)) {
            return false;
        }
        if (this == qVar) {
            return true;
        }
        o0 o0Var = (o0) qVar;
        m mVar2 = this.f207a;
        if (mVar2 != null && ((mVar = o0Var.f207a) == null || !mVar.equals(mVar2))) {
            return false;
        }
        j jVar2 = this.f208b;
        if (jVar2 != null && ((jVar = o0Var.f208b) == null || !jVar.equals(jVar2))) {
            return false;
        }
        q qVar3 = this.f209c;
        if (qVar3 == null || ((qVar2 = o0Var.f209c) != null && qVar2.equals(qVar3))) {
            return this.f211e.equals(o0Var.f211e);
        }
        return false;
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        m mVar = this.f207a;
        if (mVar != null) {
            byteArrayOutputStream.write(mVar.f("DER"));
        }
        j jVar = this.f208b;
        if (jVar != null) {
            byteArrayOutputStream.write(jVar.f("DER"));
        }
        q qVar = this.f209c;
        if (qVar != null) {
            byteArrayOutputStream.write(qVar.f("DER"));
        }
        byteArrayOutputStream.write(new f1(true, this.f210d, this.f211e).f("DER"));
        pVar.d(32, 8, byteArrayOutputStream.toByteArray());
    }

    @Override // aa.l
    public int hashCode() {
        m mVar = this.f207a;
        int hashCode = mVar != null ? mVar.hashCode() : 0;
        j jVar = this.f208b;
        if (jVar != null) {
            hashCode ^= jVar.hashCode();
        }
        q qVar = this.f209c;
        if (qVar != null) {
            hashCode ^= qVar.hashCode();
        }
        return hashCode ^ this.f211e.hashCode();
    }

    @Override // aa.q
    public int i() throws IOException {
        return e().length;
    }

    @Override // aa.q
    public boolean k() {
        return true;
    }

    public final q n(f fVar, int i10) {
        if (fVar.b() > i10) {
            return ((e) fVar.f177a.elementAt(i10)).c();
        }
        throw new IllegalArgumentException("too few objects in input vector");
    }
}
