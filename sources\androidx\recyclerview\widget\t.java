package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;

/* compiled from: OrientationHelper */
public final class t extends v {
    public t(RecyclerView.j jVar) {
        super(jVar, null);
    }

    @Override // androidx.recyclerview.widget.v
    public int b(View view) {
        return this.f2473a.F(view) + ((ViewGroup.MarginLayoutParams) ((RecyclerView.LayoutParams) view.getLayoutParams())).rightMargin;
    }

    @Override // androidx.recyclerview.widget.v
    public int c(View view) {
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
        return this.f2473a.E(view) + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
    }

    @Override // androidx.recyclerview.widget.v
    public int d(View view) {
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
        return this.f2473a.D(view) + ((ViewGroup.MarginLayoutParams) layoutParams).topMargin + ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
    }

    @Override // androidx.recyclerview.widget.v
    public int e(View view) {
        return this.f2473a.C(view) - ((ViewGroup.MarginLayoutParams) ((RecyclerView.LayoutParams) view.getLayoutParams())).leftMargin;
    }

    @Override // androidx.recyclerview.widget.v
    public int f() {
        return this.f2473a.f2210n;
    }

    @Override // androidx.recyclerview.widget.v
    public int g() {
        RecyclerView.j jVar = this.f2473a;
        return jVar.f2210n - jVar.O();
    }

    @Override // androidx.recyclerview.widget.v
    public int h() {
        return this.f2473a.O();
    }

    @Override // androidx.recyclerview.widget.v
    public int i() {
        return this.f2473a.f2208l;
    }

    @Override // androidx.recyclerview.widget.v
    public int j() {
        return this.f2473a.f2209m;
    }

    @Override // androidx.recyclerview.widget.v
    public int k() {
        return this.f2473a.N();
    }

    @Override // androidx.recyclerview.widget.v
    public int l() {
        RecyclerView.j jVar = this.f2473a;
        return (jVar.f2210n - jVar.N()) - this.f2473a.O();
    }

    @Override // androidx.recyclerview.widget.v
    public int n(View view) {
        this.f2473a.T(view, true, this.f2475c);
        return this.f2475c.right;
    }

    @Override // androidx.recyclerview.widget.v
    public int o(View view) {
        this.f2473a.T(view, true, this.f2475c);
        return this.f2475c.left;
    }

    @Override // androidx.recyclerview.widget.v
    public void p(int i10) {
        this.f2473a.X(i10);
    }
}
