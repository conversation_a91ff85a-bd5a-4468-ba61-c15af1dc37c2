syntax = "proto3";

package com.xiaomi.idm.uwb.proto;

import "UwbData.proto";

option java_package = "com.xiaomi.idm.uwb.proto";
option java_outer_classname = "MiCloseRange";

message UwbCommand {
  string commandName = 1;
  bytes commandBytes = 2;
}

// Header
message UwbResult {
  int32 dataType = 1;
  bytes data = 2;
}

// 0
message OnScanning {
  repeated MeasurementData measurementData = 1;
}

// 1
message PayLoad {
  string uwbAddress = 1;
  int32 flag = 2;
  bytes data = 3;
}

// 2
message UwbCommandResult {
  string name = 1;
  int32 status = 2;
  string uwbAddress = 3;
  bytes data = 4;
}

// 3
message CallbackMessage {
  string name = 1;
  bytes data = 2;
}
