package androidx.fragment.app;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.lifecycle.q;
import androidx.lifecycle.s;
import androidx.lifecycle.w;
import java.util.HashMap;
import java.util.Iterator;

/* compiled from: FragmentManagerViewModel */
public final class z extends q {

    /* renamed from: i  reason: collision with root package name */
    public static final s f2023i = new a();

    /* renamed from: c  reason: collision with root package name */
    public final HashMap<String, Fragment> f2024c = new HashMap<>();

    /* renamed from: d  reason: collision with root package name */
    public final HashMap<String, z> f2025d = new HashMap<>();

    /* renamed from: e  reason: collision with root package name */
    public final HashMap<String, w> f2026e = new HashMap<>();

    /* renamed from: f  reason: collision with root package name */
    public final boolean f2027f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f2028g = false;
    public boolean h = false;

    /* compiled from: FragmentManagerViewModel */
    public class a implements s {
        @Override // androidx.lifecycle.s
        @NonNull
        public <T extends q> T a(@NonNull Class<T> cls) {
            return new z(true);
        }
    }

    public z(boolean z10) {
        this.f2027f = z10;
    }

    @Override // androidx.lifecycle.q
    public void a() {
        if (FragmentManager.O(3)) {
            Log.d("FragmentManager", "onCleared called for " + this);
        }
        this.f2028g = true;
    }

    public void c(@NonNull Fragment fragment) {
        if (!this.h) {
            if ((this.f2024c.remove(fragment.f1724e) != null) && FragmentManager.O(2)) {
                Log.v("FragmentManager", "Updating retained Fragments: Removed " + fragment);
            }
        } else if (FragmentManager.O(2)) {
            Log.v("FragmentManager", "Ignoring removeRetainedFragment as the state is already saved");
        }
    }

    public boolean d(@NonNull Fragment fragment) {
        if (this.f2024c.containsKey(fragment.f1724e) && this.f2027f) {
            return this.f2028g;
        }
        return true;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || z.class != obj.getClass()) {
            return false;
        }
        z zVar = (z) obj;
        return this.f2024c.equals(zVar.f2024c) && this.f2025d.equals(zVar.f2025d) && this.f2026e.equals(zVar.f2026e);
    }

    public int hashCode() {
        int hashCode = this.f2025d.hashCode();
        return this.f2026e.hashCode() + ((hashCode + (this.f2024c.hashCode() * 31)) * 31);
    }

    @NonNull
    public String toString() {
        StringBuilder sb = new StringBuilder("FragmentManagerViewModel{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append("} Fragments (");
        Iterator<Fragment> it = this.f2024c.values().iterator();
        while (it.hasNext()) {
            sb.append(it.next());
            if (it.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(") Child Non Config (");
        Iterator<String> it2 = this.f2025d.keySet().iterator();
        while (it2.hasNext()) {
            sb.append(it2.next());
            if (it2.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(") ViewModelStores (");
        Iterator<String> it3 = this.f2026e.keySet().iterator();
        while (it3.hasNext()) {
            sb.append(it3.next());
            if (it3.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append(')');
        return sb.toString();
    }
}
