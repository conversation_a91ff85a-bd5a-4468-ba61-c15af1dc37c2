package androidx.fragment.app;

import android.animation.Animator;
import android.content.Context;
import android.graphics.Rect;
import android.transition.Transition;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.r;
import androidx.fragment.app.t0;
import com.duokan.airkan.server.f;
import j0.k;
import j0.m;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;
import u.f;

/* compiled from: DefaultSpecialEffectsController */
public class b extends t0 {

    /* compiled from: DefaultSpecialEffectsController */
    public class a implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ List f1840a;

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ t0.b f1841b;

        public a(List list, t0.b bVar) {
            this.f1840a = list;
            this.f1841b = bVar;
        }

        public void run() {
            if (this.f1840a.contains(this.f1841b)) {
                this.f1840a.remove(this.f1841b);
                b bVar = b.this;
                t0.b bVar2 = this.f1841b;
                Objects.requireNonNull(bVar);
                x0.a(bVar2.f2006a, bVar2.f2008c.f1747u0);
            }
        }
    }

    /* renamed from: androidx.fragment.app.b$b  reason: collision with other inner class name */
    /* compiled from: DefaultSpecialEffectsController */
    public static class C0013b extends c {

        /* renamed from: c  reason: collision with root package name */
        public boolean f1843c = false;
        @Nullable

        /* renamed from: d  reason: collision with root package name */
        public r.a f1844d;

        public C0013b(@NonNull t0.b bVar, @NonNull f0.a aVar) {
            super(bVar, aVar);
        }

        @Nullable
        public r.a c(@NonNull Context context) {
            if (this.f1843c) {
                return this.f1844d;
            }
            t0.b bVar = this.f1845a;
            r.a a10 = r.a(context, bVar.f2008c, bVar.f2006a == 2);
            this.f1844d = a10;
            this.f1843c = true;
            return a10;
        }
    }

    /* compiled from: DefaultSpecialEffectsController */
    public static class c {
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public final t0.b f1845a;
        @NonNull

        /* renamed from: b  reason: collision with root package name */
        public final f0.a f1846b;

        public c(@NonNull t0.b bVar, @NonNull f0.a aVar) {
            this.f1845a = bVar;
            this.f1846b = aVar;
        }

        public void a() {
            t0.b bVar = this.f1845a;
            if (bVar.f2010e.remove(this.f1846b) && bVar.f2010e.isEmpty()) {
                bVar.b();
            }
        }

        public boolean b() {
            int c10 = x0.c(this.f1845a.f2008c.f1747u0);
            int i10 = this.f1845a.f2006a;
            return c10 == i10 || !(c10 == 2 || i10 == 2);
        }
    }

    /* compiled from: DefaultSpecialEffectsController */
    public static class d extends c {
        @Nullable

        /* renamed from: c  reason: collision with root package name */
        public final Object f1847c;

        /* renamed from: d  reason: collision with root package name */
        public final boolean f1848d;
        @Nullable

        /* renamed from: e  reason: collision with root package name */
        public final Object f1849e;

        public d(@NonNull t0.b bVar, @NonNull f0.a aVar, boolean z10, boolean z11) {
            super(bVar, aVar);
            Object obj;
            Object obj2;
            if (bVar.f2006a == 2) {
                if (z10) {
                    obj2 = bVar.f2008c.n();
                } else {
                    bVar.f2008c.g();
                    obj2 = null;
                }
                this.f1847c = obj2;
                if (z10) {
                    Fragment.b bVar2 = bVar.f2008c.f1751x0;
                } else {
                    Fragment.b bVar3 = bVar.f2008c.f1751x0;
                }
                this.f1848d = true;
            } else {
                if (z10) {
                    obj = bVar.f2008c.p();
                } else {
                    bVar.f2008c.i();
                    obj = null;
                }
                this.f1847c = obj;
                this.f1848d = true;
            }
            if (!z11) {
                this.f1849e = null;
            } else if (z10) {
                this.f1849e = bVar.f2008c.r();
            } else {
                bVar.f2008c.q();
                this.f1849e = null;
            }
        }

        @Nullable
        public final m0 c(Object obj) {
            if (obj == null) {
                return null;
            }
            m0 m0Var = k0.f1936b;
            if (obj instanceof Transition) {
                return m0Var;
            }
            m0 m0Var2 = k0.f1937c;
            if (m0Var2 != null && m0Var2.e(obj)) {
                return m0Var2;
            }
            throw new IllegalArgumentException("Transition " + obj + " for fragment " + this.f1845a.f2008c + " is not a valid framework Transition or AndroidX Transition");
        }
    }

    public b(@NonNull ViewGroup viewGroup) {
        super(viewGroup);
    }

    @Override // androidx.fragment.app.t0
    public void b(@NonNull List<t0.b> list, boolean z10) {
        ArrayList arrayList;
        ArrayList arrayList2;
        HashMap hashMap;
        boolean z11;
        ArrayList arrayList3;
        Iterator it;
        t0.b bVar;
        ArrayList<View> arrayList4;
        View view;
        View view2;
        t0.b bVar2;
        d dVar;
        Object obj;
        u.a aVar;
        ArrayList arrayList5;
        ArrayList arrayList6;
        t0.b bVar3;
        t0.b bVar4;
        t0.b bVar5;
        Rect rect;
        HashMap hashMap2;
        ArrayList<View> arrayList7;
        t0.b bVar6;
        m0 m0Var;
        View view3;
        View view4;
        ArrayList<String> arrayList8;
        ArrayList<String> arrayList9;
        ArrayList<String> arrayList10;
        ArrayList<String> arrayList11;
        View view5;
        boolean z12 = z10;
        t0.b bVar7 = null;
        t0.b bVar8 = null;
        for (t0.b bVar9 : list) {
            int c10 = x0.c(bVar9.f2008c.f1747u0);
            int a10 = w0.a(bVar9.f2006a);
            if (a10 != 0) {
                if (a10 != 1) {
                    if (!(a10 == 2 || a10 == 3)) {
                    }
                } else if (c10 != 2) {
                    bVar8 = bVar9;
                }
            }
            if (c10 == 2 && bVar7 == null) {
                bVar7 = bVar9;
            }
        }
        ArrayList arrayList12 = new ArrayList();
        ArrayList arrayList13 = new ArrayList();
        ArrayList arrayList14 = new ArrayList(list);
        Iterator<t0.b> it2 = list.iterator();
        while (it2.hasNext()) {
            t0.b next = it2.next();
            f0.a aVar2 = new f0.a();
            next.d();
            next.f2010e.add(aVar2);
            arrayList12.add(new C0013b(next, aVar2));
            f0.a aVar3 = new f0.a();
            next.d();
            next.f2010e.add(aVar3);
            arrayList13.add(new d(next, aVar3, z12, !z12 ? next == bVar8 : next == bVar7));
            next.f2009d.add(new a(arrayList14, next));
        }
        HashMap hashMap3 = new HashMap();
        Iterator it3 = arrayList13.iterator();
        m0 m0Var2 = null;
        while (it3.hasNext()) {
            d dVar2 = (d) it3.next();
            if (!dVar2.b()) {
                m0 c11 = dVar2.c(dVar2.f1847c);
                m0 c12 = dVar2.c(dVar2.f1849e);
                if (c11 == null || c12 == null || c11 == c12) {
                    if (c11 == null) {
                        c11 = c12;
                    }
                    if (m0Var2 == null) {
                        m0Var2 = c11;
                    } else if (!(c11 == null || m0Var2 == c11)) {
                        StringBuilder a11 = f.a("Mixing framework transitions and AndroidX transitions is not allowed. Fragment ");
                        a11.append(dVar2.f1845a.f2008c);
                        a11.append(" returned Transition ");
                        a11.append(dVar2.f1847c);
                        a11.append(" which uses a different Transition  type than other Fragments.");
                        throw new IllegalArgumentException(a11.toString());
                    }
                } else {
                    StringBuilder a12 = f.a("Mixing framework transitions and AndroidX transitions is not allowed. Fragment ");
                    a12.append(dVar2.f1845a.f2008c);
                    a12.append(" returned Transition ");
                    a12.append(dVar2.f1847c);
                    a12.append(" which uses a different Transition  type than its shared element transition ");
                    a12.append(dVar2.f1849e);
                    throw new IllegalArgumentException(a12.toString());
                }
            }
        }
        if (m0Var2 == null) {
            Iterator it4 = arrayList13.iterator();
            while (it4.hasNext()) {
                d dVar3 = (d) it4.next();
                hashMap3.put(dVar3.f1845a, Boolean.FALSE);
                dVar3.a();
            }
            z11 = false;
            arrayList = arrayList12;
            arrayList2 = arrayList14;
            hashMap = hashMap3;
        } else {
            View view6 = new View(this.f2001a.getContext());
            Rect rect2 = new Rect();
            ArrayList<View> arrayList15 = new ArrayList<>();
            ArrayList<View> arrayList16 = new ArrayList<>();
            u.a aVar4 = new u.a();
            Iterator it5 = arrayList13.iterator();
            Object obj2 = null;
            View view7 = null;
            t0.b bVar10 = bVar7;
            boolean z13 = false;
            Rect rect3 = rect2;
            t0.b bVar11 = bVar8;
            while (it5.hasNext()) {
                Object obj3 = ((d) it5.next()).f1849e;
                if (!(obj3 != null) || bVar10 == null || bVar11 == null) {
                    aVar = aVar4;
                    arrayList7 = arrayList15;
                    arrayList6 = arrayList12;
                    arrayList5 = arrayList13;
                    view3 = view6;
                    m0Var = m0Var2;
                    bVar5 = bVar7;
                    bVar4 = bVar8;
                    hashMap2 = hashMap3;
                    rect = rect3;
                    bVar3 = bVar11;
                } else {
                    Object y10 = m0Var2.y(m0Var2.g(obj3));
                    Fragment.b bVar12 = bVar11.f2008c.f1751x0;
                    if (bVar12 == null || (arrayList8 = bVar12.f1762e) == null) {
                        arrayList8 = new ArrayList<>();
                    }
                    arrayList6 = arrayList12;
                    Fragment.b bVar13 = bVar10.f2008c.f1751x0;
                    if (bVar13 == null || (arrayList9 = bVar13.f1762e) == null) {
                        arrayList9 = new ArrayList<>();
                    }
                    arrayList5 = arrayList13;
                    Fragment.b bVar14 = bVar10.f2008c.f1751x0;
                    if (bVar14 == null || (arrayList10 = bVar14.f1763f) == null) {
                        arrayList10 = new ArrayList<>();
                    }
                    int i10 = 0;
                    while (i10 < arrayList10.size()) {
                        int indexOf = arrayList8.indexOf(arrayList10.get(i10));
                        if (indexOf != -1) {
                            arrayList8.set(indexOf, arrayList9.get(i10));
                        }
                        i10++;
                        arrayList10 = arrayList10;
                    }
                    Fragment.b bVar15 = bVar11.f2008c.f1751x0;
                    if (bVar15 == null || (arrayList11 = bVar15.f1763f) == null) {
                        arrayList11 = new ArrayList<>();
                    }
                    if (!z12) {
                        bVar10.f2008c.j();
                        bVar11.f2008c.h();
                    } else {
                        bVar10.f2008c.h();
                        bVar11.f2008c.j();
                    }
                    int i11 = 0;
                    for (int size = arrayList8.size(); i11 < size; size = size) {
                        aVar4.put(arrayList8.get(i11), arrayList11.get(i11));
                        i11++;
                    }
                    u.a<String, View> aVar5 = new u.a<>();
                    j(aVar5, bVar10.f2008c.f1747u0);
                    u.f.k(aVar5, arrayList8);
                    u.f.k(aVar4, aVar5.keySet());
                    u.a<String, View> aVar6 = new u.a<>();
                    j(aVar6, bVar11.f2008c.f1747u0);
                    u.f.k(aVar6, arrayList11);
                    u.f.k(aVar6, aVar4.values());
                    k0.m(aVar4, aVar6);
                    k(aVar5, aVar4.keySet());
                    k(aVar6, aVar4.values());
                    if (aVar4.isEmpty()) {
                        arrayList15.clear();
                        arrayList16.clear();
                        obj2 = null;
                        aVar = aVar4;
                        arrayList7 = arrayList15;
                        bVar3 = bVar11;
                        bVar5 = bVar7;
                        bVar4 = bVar8;
                        m0Var = m0Var2;
                        rect = rect3;
                        view3 = view6;
                        hashMap2 = hashMap3;
                    } else {
                        k0.c(bVar11.f2008c, bVar10.f2008c, z12, aVar5, true);
                        aVar = aVar4;
                        arrayList7 = arrayList15;
                        rect = rect3;
                        k.a(this.f2001a, new g(this, bVar8, bVar7, z10, aVar6));
                        Iterator it6 = ((f.e) aVar5.values()).iterator();
                        while (true) {
                            f.a aVar7 = (f.a) it6;
                            if (!aVar7.hasNext()) {
                                break;
                            }
                            i(arrayList7, (View) aVar7.next());
                        }
                        if (!arrayList8.isEmpty()) {
                            View view8 = aVar5.get(arrayList8.get(0));
                            m0Var2.t(y10, view8);
                            view4 = view8;
                        } else {
                            view4 = view7;
                        }
                        Iterator it7 = ((f.e) aVar6.values()).iterator();
                        while (true) {
                            f.a aVar8 = (f.a) it7;
                            if (!aVar8.hasNext()) {
                                break;
                            }
                            i(arrayList16, (View) aVar8.next());
                        }
                        arrayList16 = arrayList16;
                        if (!arrayList11.isEmpty() && (view5 = aVar6.get(arrayList11.get(0))) != null) {
                            k.a(this.f2001a, new h(this, m0Var2, view5, rect));
                            z13 = true;
                        }
                        m0Var2.w(y10, view6, arrayList7);
                        view3 = view6;
                        m0Var = m0Var2;
                        m0Var2.r(y10, null, null, null, null, y10, arrayList16);
                        Boolean bool = Boolean.TRUE;
                        hashMap2 = hashMap3;
                        bVar5 = bVar7;
                        hashMap2.put(bVar5, bool);
                        bVar4 = bVar8;
                        hashMap2.put(bVar4, bool);
                        bVar6 = bVar5;
                        obj2 = y10;
                        bVar3 = bVar4;
                        view7 = view4;
                        view6 = view3;
                        m0Var2 = m0Var;
                        bVar10 = bVar6;
                        arrayList15 = arrayList7;
                        hashMap3 = hashMap2;
                        rect3 = rect;
                        bVar7 = bVar5;
                        bVar8 = bVar4;
                        bVar11 = bVar3;
                        arrayList14 = arrayList14;
                        arrayList12 = arrayList6;
                        arrayList13 = arrayList5;
                        aVar4 = aVar;
                        z12 = z10;
                    }
                }
                bVar6 = bVar10;
                view4 = view7;
                view7 = view4;
                view6 = view3;
                m0Var2 = m0Var;
                bVar10 = bVar6;
                arrayList15 = arrayList7;
                hashMap3 = hashMap2;
                rect3 = rect;
                bVar7 = bVar5;
                bVar8 = bVar4;
                bVar11 = bVar3;
                arrayList14 = arrayList14;
                arrayList12 = arrayList6;
                arrayList13 = arrayList5;
                aVar4 = aVar;
                z12 = z10;
            }
            ArrayList<View> arrayList17 = arrayList15;
            arrayList = arrayList12;
            arrayList2 = arrayList14;
            hashMap = hashMap3;
            View view9 = view6;
            t0.b bVar16 = bVar8;
            ArrayList arrayList18 = new ArrayList();
            Iterator it8 = arrayList13.iterator();
            Object obj4 = null;
            Object obj5 = null;
            while (it8.hasNext()) {
                d dVar4 = (d) it8.next();
                if (dVar4.b()) {
                    it = it8;
                    hashMap.put(dVar4.f1845a, Boolean.FALSE);
                    dVar4.a();
                    view = view9;
                    arrayList4 = arrayList17;
                    bVar = bVar16;
                    obj5 = obj5;
                    view2 = view7;
                } else {
                    it = it8;
                    Object g10 = m0Var2.g(dVar4.f1847c);
                    t0.b bVar17 = dVar4.f1845a;
                    boolean z14 = obj2 != null && (bVar17 == bVar10 || bVar17 == bVar11);
                    if (g10 == null) {
                        if (!z14) {
                            hashMap.put(bVar17, Boolean.FALSE);
                            dVar4.a();
                        }
                        view = view9;
                        arrayList4 = arrayList17;
                        bVar = bVar16;
                        obj5 = obj5;
                        view2 = view7;
                    } else {
                        bVar = bVar16;
                        ArrayList<View> arrayList19 = new ArrayList<>();
                        i(arrayList19, bVar17.f2008c.f1747u0);
                        if (z14) {
                            if (bVar17 == bVar10) {
                                arrayList19.removeAll(arrayList17);
                            } else {
                                arrayList19.removeAll(arrayList16);
                            }
                        }
                        if (arrayList19.isEmpty()) {
                            m0Var2.a(g10, view9);
                            view = view9;
                            arrayList4 = arrayList17;
                            bVar2 = bVar17;
                            obj = obj5;
                            dVar = dVar4;
                        } else {
                            m0Var2.b(g10, arrayList19);
                            dVar = dVar4;
                            view = view9;
                            arrayList4 = arrayList17;
                            bVar2 = bVar17;
                            obj = obj5;
                            m0Var2.r(g10, g10, arrayList19, null, null, null, null);
                            if (bVar2.f2006a == 3) {
                                m0Var2.q(g10, bVar2.f2008c.f1747u0, arrayList19);
                                k.a(this.f2001a, new i(this, arrayList19));
                            }
                        }
                        if (bVar2.f2006a == 2) {
                            arrayList18.addAll(arrayList19);
                            if (z13) {
                                m0Var2.s(g10, rect3);
                            }
                            view2 = view7;
                        } else {
                            view2 = view7;
                            m0Var2.t(g10, view2);
                        }
                        hashMap.put(bVar2, Boolean.TRUE);
                        if (dVar.f1848d) {
                            obj5 = m0Var2.m(obj, g10, null);
                        } else {
                            obj4 = m0Var2.m(obj4, g10, null);
                            obj5 = obj;
                        }
                    }
                    bVar11 = bVar;
                }
                it8 = it;
                view7 = view2;
                view9 = view;
                arrayList17 = arrayList4;
                bVar16 = bVar;
            }
            t0.b bVar18 = bVar16;
            Object l10 = m0Var2.l(obj5, obj4, obj2);
            Iterator it9 = arrayList13.iterator();
            while (it9.hasNext()) {
                d dVar5 = (d) it9.next();
                if (!dVar5.b()) {
                    Object obj6 = dVar5.f1847c;
                    t0.b bVar19 = dVar5.f1845a;
                    boolean z15 = obj2 != null && (bVar19 == bVar10 || bVar19 == bVar18);
                    if (obj6 != null || z15) {
                        m0Var2.u(bVar19.f2008c, l10, dVar5.f1846b, new j(this, dVar5));
                    }
                    bVar18 = bVar18;
                }
            }
            k0.o(arrayList18, 4);
            ArrayList<String> n10 = m0Var2.n(arrayList16);
            m0Var2.c(this.f2001a, l10);
            m0Var2.v(this.f2001a, arrayList17, arrayList16, n10, aVar4);
            k0.o(arrayList18, 0);
            m0Var2.x(obj2, arrayList17, arrayList16);
            z11 = false;
        }
        boolean containsValue = hashMap.containsValue(Boolean.TRUE);
        ViewGroup viewGroup = this.f2001a;
        Context context = viewGroup.getContext();
        ArrayList arrayList20 = new ArrayList();
        Iterator it10 = arrayList.iterator();
        boolean z16 = z11;
        while (it10.hasNext()) {
            C0013b bVar20 = (C0013b) it10.next();
            if (bVar20.b()) {
                bVar20.a();
            } else {
                r.a c13 = bVar20.c(context);
                if (c13 == null) {
                    bVar20.a();
                } else {
                    Animator animator = c13.f1990b;
                    if (animator == null) {
                        arrayList20.add(bVar20);
                    } else {
                        t0.b bVar21 = bVar20.f1845a;
                        Fragment fragment = bVar21.f2008c;
                        if (Boolean.TRUE.equals(hashMap.get(bVar21))) {
                            if (FragmentManager.O(2)) {
                                Log.v("FragmentManager", "Ignoring Animator set on " + fragment + " as this Fragment was involved in a Transition.");
                            }
                            bVar20.a();
                        } else {
                            if (bVar21.f2006a == 3) {
                                z11 = true;
                            }
                            if (z11) {
                                arrayList3 = arrayList2;
                                arrayList3.remove(bVar21);
                            } else {
                                arrayList3 = arrayList2;
                            }
                            View view10 = fragment.f1747u0;
                            viewGroup.startViewTransition(view10);
                            animator.addListener(new c(this, viewGroup, view10, z11, bVar21, bVar20));
                            animator.setTarget(view10);
                            animator.start();
                            bVar20.f1846b.b(new d(this, animator));
                            z16 = true;
                            z11 = false;
                            arrayList2 = arrayList3;
                            hashMap = hashMap;
                        }
                    }
                }
            }
        }
        Iterator it11 = arrayList20.iterator();
        while (it11.hasNext()) {
            C0013b bVar22 = (C0013b) it11.next();
            t0.b bVar23 = bVar22.f1845a;
            Fragment fragment2 = bVar23.f2008c;
            if (containsValue) {
                if (FragmentManager.O(2)) {
                    Log.v("FragmentManager", "Ignoring Animation set on " + fragment2 + " as Animations cannot run alongside Transitions.");
                }
                bVar22.a();
            } else if (z16) {
                if (FragmentManager.O(2)) {
                    Log.v("FragmentManager", "Ignoring Animation set on " + fragment2 + " as Animations cannot run alongside Animators.");
                }
                bVar22.a();
            } else {
                View view11 = fragment2.f1747u0;
                r.a c14 = bVar22.c(context);
                Objects.requireNonNull(c14);
                Animation animation = c14.f1989a;
                Objects.requireNonNull(animation);
                if (bVar23.f2006a != 1) {
                    view11.startAnimation(animation);
                    bVar22.a();
                } else {
                    viewGroup.startViewTransition(view11);
                    r.b bVar24 = new r.b(animation, viewGroup, view11);
                    bVar24.setAnimationListener(new e(this, viewGroup, view11, bVar22));
                    view11.startAnimation(bVar24);
                }
                bVar22.f1846b.b(new f(this, view11, viewGroup, bVar22));
            }
        }
        Iterator it12 = arrayList2.iterator();
        while (it12.hasNext()) {
            t0.b bVar25 = (t0.b) it12.next();
            x0.a(bVar25.f2006a, bVar25.f2008c.f1747u0);
        }
        arrayList2.clear();
    }

    public void i(ArrayList<View> arrayList, View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            if (viewGroup.isTransitionGroup()) {
                arrayList.add(viewGroup);
                return;
            }
            int childCount = viewGroup.getChildCount();
            for (int i10 = 0; i10 < childCount; i10++) {
                View childAt = viewGroup.getChildAt(i10);
                if (childAt.getVisibility() == 0) {
                    i(arrayList, childAt);
                }
            }
            return;
        }
        arrayList.add(view);
    }

    public void j(Map<String, View> map, @NonNull View view) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        String transitionName = view.getTransitionName();
        if (transitionName != null) {
            map.put(transitionName, view);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i10 = 0; i10 < childCount; i10++) {
                View childAt = viewGroup.getChildAt(i10);
                if (childAt.getVisibility() == 0) {
                    j(map, childAt);
                }
            }
        }
    }

    public void k(@NonNull u.a<String, View> aVar, @NonNull Collection<String> collection) {
        Iterator it = ((f.b) aVar.entrySet()).iterator();
        while (true) {
            f.d dVar = (f.d) it;
            if (dVar.hasNext()) {
                dVar.next();
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                if (!collection.contains(((View) dVar.getValue()).getTransitionName())) {
                    dVar.remove();
                }
            } else {
                return;
            }
        }
    }
}
