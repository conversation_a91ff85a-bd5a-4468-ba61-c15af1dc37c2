package androidx.fragment.app;

import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.b;
import f0.a;

/* compiled from: DefaultSpecialEffectsController */
public class f implements a.AbstractC0075a {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ View f1894a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ ViewGroup f1895b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ b.C0013b f1896c;

    public f(b bVar, View view, ViewGroup viewGroup, b.C0013b bVar2) {
        this.f1894a = view;
        this.f1895b = viewGroup;
        this.f1896c = bVar2;
    }

    @Override // f0.a.AbstractC0075a
    public void a() {
        this.f1894a.clearAnimation();
        this.f1895b.endViewTransition(this.f1894a);
        this.f1896c.a();
    }
}
