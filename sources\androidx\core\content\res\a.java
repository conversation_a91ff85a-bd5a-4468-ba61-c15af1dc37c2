package androidx.core.content.res;

import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;

/* compiled from: ResourcesCompat */
public final class a {

    /* renamed from: androidx.core.content.res.a$a  reason: collision with other inner class name */
    /* compiled from: ResourcesCompat */
    public static abstract class AbstractC0009a {

        /* renamed from: androidx.core.content.res.a$a$a  reason: collision with other inner class name */
        /* compiled from: ResourcesCompat */
        public class RunnableC0010a implements Runnable {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ Typeface f1579a;

            public RunnableC0010a(Typeface typeface) {
                this.f1579a = typeface;
            }

            public void run() {
                AbstractC0009a.this.d(this.f1579a);
            }
        }

        /* renamed from: androidx.core.content.res.a$a$b */
        /* compiled from: ResourcesCompat */
        public class b implements Runnable {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ int f1581a;

            public b(int i10) {
                this.f1581a = i10;
            }

            public void run() {
                AbstractC0009a.this.c(this.f1581a);
            }
        }

        @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
        public final void a(int i10, @Nullable Handler handler) {
            if (handler == null) {
                handler = new Handler(Looper.getMainLooper());
            }
            handler.post(new b(i10));
        }

        @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
        public final void b(Typeface typeface, @Nullable Handler handler) {
            if (handler == null) {
                handler = new Handler(Looper.getMainLooper());
            }
            handler.post(new RunnableC0010a(typeface));
        }

        public abstract void c(int i10);

        public abstract void d(@NonNull Typeface typeface);
    }

    /* JADX WARNING: Removed duplicated region for block: B:30:0x00b4  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x00b9 A[ADDED_TO_REGION] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static android.graphics.Typeface a(@androidx.annotation.NonNull android.content.Context r16, int r17, android.util.TypedValue r18, int r19, @androidx.annotation.Nullable androidx.core.content.res.a.AbstractC0009a r20, @androidx.annotation.Nullable android.os.Handler r21, boolean r22) {
        /*
        // Method dump skipped, instructions count: 260
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.content.res.a.a(android.content.Context, int, android.util.TypedValue, int, androidx.core.content.res.a$a, android.os.Handler, boolean):android.graphics.Typeface");
    }
}
