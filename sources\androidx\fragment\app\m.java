package androidx.fragment.app;

import com.duokan.airkan.parse.TypeConvertor;
import com.xiaomi.mitv.udt.util.JSONBuilder;

/* compiled from: R8$$SyntheticClass */
public final /* synthetic */ class m {
    public static String a(String str, int i10, String str2, String str3) {
        return new JSONBuilder().put(str, i10).put(str2, str3).toString();
    }

    public static String b(String str, Fragment fragment, String str2) {
        return str + fragment + str2;
    }

    public static short c(int i10, int i11, byte[] bArr, byte[] bArr2, int i12, int i13, byte[] bArr3) {
        System.arraycopy(bArr, i10 + i11, bArr2, i12, i13);
        return TypeConvertor.byteArrayToShort(bArr3);
    }
}
