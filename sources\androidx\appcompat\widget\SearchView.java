package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.app.SearchableInfo;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.database.Cursor;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ImageSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$dimen;
import androidx.appcompat.R$id;
import androidx.appcompat.R$layout;
import androidx.appcompat.R$string;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.WeakHashMap;

public class SearchView extends LinearLayoutCompat implements p.b {
    public static final n Z0 = (Build.VERSION.SDK_INT < 29 ? new n() : null);
    public l A0;
    public k B0;
    public View.OnFocusChangeListener C0;
    public m D0;
    public View.OnClickListener E0;
    public boolean F0;
    public boolean G0;
    public l0.a H0;
    public boolean I0;
    public CharSequence J0;
    public boolean K0;
    public boolean L0;
    public int M0;
    public boolean N0;
    public CharSequence O0;
    public CharSequence P0;
    public boolean Q0;
    public int R0;
    public SearchableInfo S0;
    public Bundle T0;
    public final Runnable U0;
    public Runnable V0;
    public final WeakHashMap<String, Drawable.ConstantState> W0;
    public View.OnKeyListener X0;
    public TextWatcher Y0;

    /* renamed from: m0  reason: collision with root package name */
    public final ImageView f912m0;

    /* renamed from: n0  reason: collision with root package name */
    public final View f913n0;

    /* renamed from: o0  reason: collision with root package name */
    public o f914o0;

    /* renamed from: p  reason: collision with root package name */
    public final SearchAutoComplete f915p;

    /* renamed from: p0  reason: collision with root package name */
    public Rect f916p0;

    /* renamed from: q  reason: collision with root package name */
    public final View f917q;

    /* renamed from: q0  reason: collision with root package name */
    public Rect f918q0;

    /* renamed from: r  reason: collision with root package name */
    public final View f919r;

    /* renamed from: r0  reason: collision with root package name */
    public int[] f920r0;

    /* renamed from: s  reason: collision with root package name */
    public final View f921s;

    /* renamed from: s0  reason: collision with root package name */
    public int[] f922s0;

    /* renamed from: t  reason: collision with root package name */
    public final ImageView f923t;

    /* renamed from: t0  reason: collision with root package name */
    public final ImageView f924t0;

    /* renamed from: u0  reason: collision with root package name */
    public final Drawable f925u0;

    /* renamed from: v0  reason: collision with root package name */
    public final int f926v0;

    /* renamed from: w0  reason: collision with root package name */
    public final int f927w0;

    /* renamed from: x  reason: collision with root package name */
    public final ImageView f928x;

    /* renamed from: x0  reason: collision with root package name */
    public final Intent f929x0;

    /* renamed from: y  reason: collision with root package name */
    public final ImageView f930y;

    /* renamed from: y0  reason: collision with root package name */
    public final Intent f931y0;

    /* renamed from: z0  reason: collision with root package name */
    public final CharSequence f932z0;

    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: c  reason: collision with root package name */
        public boolean f933c;

        public class a implements Parcelable.ClassLoaderCreator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }

        public String toString() {
            StringBuilder a10 = com.duokan.airkan.server.f.a("SearchView.SavedState{");
            a10.append(Integer.toHexString(System.identityHashCode(this)));
            a10.append(" isIconified=");
            a10.append(this.f933c);
            a10.append("}");
            return a10.toString();
        }

        @Override // androidx.customview.view.AbsSavedState
        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeParcelable(this.f1662a, i10);
            parcel.writeValue(Boolean.valueOf(this.f933c));
        }

        public SavedState(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f933c = ((Boolean) parcel.readValue(null)).booleanValue();
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public static class SearchAutoComplete extends AppCompatAutoCompleteTextView {

        /* renamed from: d  reason: collision with root package name */
        public int f934d;

        /* renamed from: e  reason: collision with root package name */
        public SearchView f935e;

        /* renamed from: f  reason: collision with root package name */
        public boolean f936f;

        /* renamed from: g  reason: collision with root package name */
        public final Runnable f937g;

        public class a implements Runnable {
            public a() {
            }

            public void run() {
                SearchAutoComplete searchAutoComplete = SearchAutoComplete.this;
                if (searchAutoComplete.f936f) {
                    ((InputMethodManager) searchAutoComplete.getContext().getSystemService("input_method")).showSoftInput(searchAutoComplete, 0);
                    searchAutoComplete.f936f = false;
                }
            }
        }

        public SearchAutoComplete(Context context, AttributeSet attributeSet) {
            this(context, attributeSet, R$attr.autoCompleteTextViewStyle);
        }

        private int getSearchViewTextMinWidthDp() {
            Configuration configuration = getResources().getConfiguration();
            int i10 = configuration.screenWidthDp;
            int i11 = configuration.screenHeightDp;
            if (i10 >= 960 && i11 >= 720 && configuration.orientation == 2) {
                return 256;
            }
            if (i10 < 600) {
                return (i10 < 640 || i11 < 480) ? 160 : 192;
            }
            return 192;
        }

        public void a() {
            if (Build.VERSION.SDK_INT >= 29) {
                setInputMethodMode(1);
                if (enoughToFilter()) {
                    showDropDown();
                    return;
                }
                return;
            }
            n nVar = SearchView.Z0;
            Objects.requireNonNull(nVar);
            n.a();
            Method method = nVar.f951c;
            if (method != null) {
                try {
                    method.invoke(this, Boolean.TRUE);
                } catch (Exception unused) {
                }
            }
        }

        public boolean enoughToFilter() {
            return this.f934d <= 0 || super.enoughToFilter();
        }

        @Override // androidx.appcompat.widget.AppCompatAutoCompleteTextView
        public InputConnection onCreateInputConnection(EditorInfo editorInfo) {
            InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
            if (this.f936f) {
                removeCallbacks(this.f937g);
                post(this.f937g);
            }
            return onCreateInputConnection;
        }

        public void onFinishInflate() {
            super.onFinishInflate();
            setMinWidth((int) TypedValue.applyDimension(1, (float) getSearchViewTextMinWidthDp(), getResources().getDisplayMetrics()));
        }

        public void onFocusChanged(boolean z10, int i10, Rect rect) {
            super.onFocusChanged(z10, i10, rect);
            SearchView searchView = this.f935e;
            searchView.A(searchView.G0);
            searchView.post(searchView.U0);
            if (searchView.f915p.hasFocus()) {
                searchView.n();
            }
        }

        public boolean onKeyPreIme(int i10, KeyEvent keyEvent) {
            if (i10 == 4) {
                if (keyEvent.getAction() == 0 && keyEvent.getRepeatCount() == 0) {
                    KeyEvent.DispatcherState keyDispatcherState = getKeyDispatcherState();
                    if (keyDispatcherState != null) {
                        keyDispatcherState.startTracking(keyEvent, this);
                    }
                    return true;
                } else if (keyEvent.getAction() == 1) {
                    KeyEvent.DispatcherState keyDispatcherState2 = getKeyDispatcherState();
                    if (keyDispatcherState2 != null) {
                        keyDispatcherState2.handleUpEvent(keyEvent);
                    }
                    if (keyEvent.isTracking() && !keyEvent.isCanceled()) {
                        this.f935e.clearFocus();
                        setImeVisibility(false);
                        return true;
                    }
                }
            }
            return super.onKeyPreIme(i10, keyEvent);
        }

        public void onWindowFocusChanged(boolean z10) {
            super.onWindowFocusChanged(z10);
            if (z10 && this.f935e.hasFocus() && getVisibility() == 0) {
                boolean z11 = true;
                this.f936f = true;
                Context context = getContext();
                n nVar = SearchView.Z0;
                if (context.getResources().getConfiguration().orientation != 2) {
                    z11 = false;
                }
                if (z11) {
                    a();
                }
            }
        }

        public void performCompletion() {
        }

        public void replaceText(CharSequence charSequence) {
        }

        public void setImeVisibility(boolean z10) {
            InputMethodManager inputMethodManager = (InputMethodManager) getContext().getSystemService("input_method");
            if (!z10) {
                this.f936f = false;
                removeCallbacks(this.f937g);
                inputMethodManager.hideSoftInputFromWindow(getWindowToken(), 0);
            } else if (inputMethodManager.isActive(this)) {
                this.f936f = false;
                removeCallbacks(this.f937g);
                inputMethodManager.showSoftInput(this, 0);
            } else {
                this.f936f = true;
            }
        }

        public void setSearchView(SearchView searchView) {
            this.f935e = searchView;
        }

        public void setThreshold(int i10) {
            super.setThreshold(i10);
            this.f934d = i10;
        }

        public SearchAutoComplete(Context context, AttributeSet attributeSet, int i10) {
            super(context, attributeSet, i10);
            this.f937g = new a();
            this.f934d = getThreshold();
        }
    }

    public class a implements TextWatcher {
        public a() {
        }

        public void afterTextChanged(Editable editable) {
        }

        public void beforeTextChanged(CharSequence charSequence, int i10, int i11, int i12) {
        }

        public void onTextChanged(CharSequence charSequence, int i10, int i11, int i12) {
            SearchView searchView = SearchView.this;
            Editable text = searchView.f915p.getText();
            searchView.P0 = text;
            boolean z10 = !TextUtils.isEmpty(text);
            searchView.z(z10);
            searchView.B(!z10);
            searchView.v();
            searchView.y();
            if (searchView.A0 != null && !TextUtils.equals(charSequence, searchView.O0)) {
                searchView.A0.a(charSequence.toString());
            }
            searchView.O0 = charSequence.toString();
        }
    }

    public class b implements Runnable {
        public b() {
        }

        public void run() {
            SearchView.this.w();
        }
    }

    public class c implements Runnable {
        public c() {
        }

        public void run() {
            l0.a aVar = SearchView.this.H0;
            if (aVar instanceof g0) {
                aVar.b(null);
            }
        }
    }

    public class d implements View.OnFocusChangeListener {
        public d() {
        }

        public void onFocusChange(View view, boolean z10) {
            SearchView searchView = SearchView.this;
            View.OnFocusChangeListener onFocusChangeListener = searchView.C0;
            if (onFocusChangeListener != null) {
                onFocusChangeListener.onFocusChange(searchView, z10);
            }
        }
    }

    public class e implements View.OnLayoutChangeListener {
        public e() {
        }

        public void onLayoutChange(View view, int i10, int i11, int i12, int i13, int i14, int i15, int i16, int i17) {
            int i18;
            int i19;
            SearchView searchView = SearchView.this;
            if (searchView.f913n0.getWidth() > 1) {
                Resources resources = searchView.getContext().getResources();
                int paddingLeft = searchView.f919r.getPaddingLeft();
                Rect rect = new Rect();
                boolean b10 = q0.b(searchView);
                if (searchView.F0) {
                    i18 = resources.getDimensionPixelSize(R$dimen.abc_dropdownitem_text_padding_left) + resources.getDimensionPixelSize(R$dimen.abc_dropdownitem_icon_width);
                } else {
                    i18 = 0;
                }
                searchView.f915p.getDropDownBackground().getPadding(rect);
                if (b10) {
                    i19 = -rect.left;
                } else {
                    i19 = paddingLeft - (rect.left + i18);
                }
                searchView.f915p.setDropDownHorizontalOffset(i19);
                searchView.f915p.setDropDownWidth((((searchView.f913n0.getWidth() + rect.left) + rect.right) + i18) - paddingLeft);
            }
        }
    }

    public class f implements View.OnClickListener {
        public f() {
        }

        public void onClick(View view) {
            String str;
            SearchView searchView = SearchView.this;
            if (view == searchView.f923t) {
                searchView.t();
            } else if (view == searchView.f930y) {
                searchView.p();
            } else if (view == searchView.f928x) {
                searchView.u();
            } else if (view == searchView.f912m0) {
                SearchableInfo searchableInfo = searchView.S0;
                if (searchableInfo != null) {
                    try {
                        if (searchableInfo.getVoiceSearchLaunchWebSearch()) {
                            Intent intent = new Intent(searchView.f929x0);
                            ComponentName searchActivity = searchableInfo.getSearchActivity();
                            if (searchActivity == null) {
                                str = null;
                            } else {
                                str = searchActivity.flattenToShortString();
                            }
                            intent.putExtra("calling_package", str);
                            searchView.getContext().startActivity(intent);
                        } else if (searchableInfo.getVoiceSearchLaunchRecognizer()) {
                            searchView.getContext().startActivity(searchView.m(searchView.f931y0, searchableInfo));
                        }
                    } catch (ActivityNotFoundException unused) {
                        Log.w("SearchView", "Could not find voice search activity");
                    }
                }
            } else if (view == searchView.f915p) {
                searchView.n();
            }
        }
    }

    public class g implements View.OnKeyListener {
        public g() {
        }

        public boolean onKey(View view, int i10, KeyEvent keyEvent) {
            int i11;
            SearchView searchView = SearchView.this;
            if (searchView.S0 == null) {
                return false;
            }
            if (!searchView.f915p.isPopupShowing() || SearchView.this.f915p.getListSelection() == -1) {
                if ((TextUtils.getTrimmedLength(SearchView.this.f915p.getText()) == 0) || !keyEvent.hasNoModifiers() || keyEvent.getAction() != 1 || i10 != 66) {
                    return false;
                }
                view.cancelLongPress();
                SearchView searchView2 = SearchView.this;
                searchView2.o(0, null, searchView2.f915p.getText().toString());
                return true;
            }
            SearchView searchView3 = SearchView.this;
            if (searchView3.S0 == null || searchView3.H0 == null || keyEvent.getAction() != 0 || !keyEvent.hasNoModifiers()) {
                return false;
            }
            if (i10 == 66 || i10 == 84 || i10 == 61) {
                return searchView3.q(searchView3.f915p.getListSelection());
            }
            if (i10 == 21 || i10 == 22) {
                if (i10 == 21) {
                    i11 = 0;
                } else {
                    i11 = searchView3.f915p.length();
                }
                searchView3.f915p.setSelection(i11);
                searchView3.f915p.setListSelection(0);
                searchView3.f915p.clearListSelection();
                searchView3.f915p.a();
                return true;
            } else if (i10 != 19) {
                return false;
            } else {
                searchView3.f915p.getListSelection();
                return false;
            }
        }
    }

    public class h implements TextView.OnEditorActionListener {
        public h() {
        }

        public boolean onEditorAction(TextView textView, int i10, KeyEvent keyEvent) {
            SearchView.this.u();
            return true;
        }
    }

    public class i implements AdapterView.OnItemClickListener {
        public i() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
            SearchView.this.q(i10);
        }
    }

    public class j implements AdapterView.OnItemSelectedListener {
        public j() {
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public void onItemSelected(AdapterView<?> adapterView, View view, int i10, long j10) {
            SearchView.this.r(i10);
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public void onNothingSelected(AdapterView<?> adapterView) {
        }
    }

    public interface k {
        boolean a();
    }

    public interface l {
        boolean a(String str);

        boolean b(String str);
    }

    public interface m {
        boolean a(int i10);

        boolean b(int i10);
    }

    public static class n {

        /* renamed from: a  reason: collision with root package name */
        public Method f949a = null;

        /* renamed from: b  reason: collision with root package name */
        public Method f950b = null;

        /* renamed from: c  reason: collision with root package name */
        public Method f951c = null;

        @SuppressLint({"DiscouragedPrivateApi", "SoonBlockedPrivateApi"})
        public n() {
            a();
            try {
                Method declaredMethod = AutoCompleteTextView.class.getDeclaredMethod("doBeforeTextChanged", new Class[0]);
                this.f949a = declaredMethod;
                declaredMethod.setAccessible(true);
            } catch (NoSuchMethodException unused) {
            }
            try {
                Method declaredMethod2 = AutoCompleteTextView.class.getDeclaredMethod("doAfterTextChanged", new Class[0]);
                this.f950b = declaredMethod2;
                declaredMethod2.setAccessible(true);
            } catch (NoSuchMethodException unused2) {
            }
            try {
                Method method = AutoCompleteTextView.class.getMethod("ensureImeVisible", Boolean.TYPE);
                this.f951c = method;
                method.setAccessible(true);
            } catch (NoSuchMethodException unused3) {
            }
        }

        public static void a() {
            if (Build.VERSION.SDK_INT >= 29) {
                throw new UnsupportedClassVersionError("This function can only be used for API Level < 29.");
            }
        }
    }

    public static class o extends TouchDelegate {

        /* renamed from: a  reason: collision with root package name */
        public final View f952a;

        /* renamed from: b  reason: collision with root package name */
        public final Rect f953b = new Rect();

        /* renamed from: c  reason: collision with root package name */
        public final Rect f954c = new Rect();

        /* renamed from: d  reason: collision with root package name */
        public final Rect f955d = new Rect();

        /* renamed from: e  reason: collision with root package name */
        public final int f956e;

        /* renamed from: f  reason: collision with root package name */
        public boolean f957f;

        public o(Rect rect, Rect rect2, View view) {
            super(rect, view);
            this.f956e = ViewConfiguration.get(view.getContext()).getScaledTouchSlop();
            a(rect, rect2);
            this.f952a = view;
        }

        public void a(Rect rect, Rect rect2) {
            this.f953b.set(rect);
            this.f955d.set(rect);
            Rect rect3 = this.f955d;
            int i10 = this.f956e;
            rect3.inset(-i10, -i10);
            this.f954c.set(rect2);
        }

        /* JADX WARNING: Removed duplicated region for block: B:18:0x0043  */
        /* JADX WARNING: Removed duplicated region for block: B:25:? A[RETURN, SYNTHETIC] */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public boolean onTouchEvent(android.view.MotionEvent r9) {
            /*
            // Method dump skipped, instructions count: 117
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.o.onTouchEvent(android.view.MotionEvent):boolean");
        }
    }

    public SearchView(@NonNull Context context) {
        this(context, null);
    }

    private int getPreferredHeight() {
        return getContext().getResources().getDimensionPixelSize(R$dimen.abc_search_view_preferred_height);
    }

    private int getPreferredWidth() {
        return getContext().getResources().getDimensionPixelSize(R$dimen.abc_search_view_preferred_width);
    }

    private void setQuery(CharSequence charSequence) {
        this.f915p.setText(charSequence);
        this.f915p.setSelection(TextUtils.isEmpty(charSequence) ? 0 : charSequence.length());
    }

    public final void A(boolean z10) {
        this.G0 = z10;
        int i10 = 0;
        int i11 = z10 ? 0 : 8;
        boolean z11 = !TextUtils.isEmpty(this.f915p.getText());
        this.f923t.setVisibility(i11);
        z(z11);
        this.f917q.setVisibility(z10 ? 8 : 0);
        if (this.f924t0.getDrawable() == null || this.F0) {
            i10 = 8;
        }
        this.f924t0.setVisibility(i10);
        v();
        B(!z11);
        y();
    }

    public final void B(boolean z10) {
        int i10 = 8;
        if (this.N0 && !this.G0 && z10) {
            this.f928x.setVisibility(8);
            i10 = 0;
        }
        this.f912m0.setVisibility(i10);
    }

    @Override // p.b
    public void c() {
        if (!this.Q0) {
            this.Q0 = true;
            int imeOptions = this.f915p.getImeOptions();
            this.R0 = imeOptions;
            this.f915p.setImeOptions(imeOptions | 33554432);
            this.f915p.setText("");
            setIconified(false);
        }
    }

    public void clearFocus() {
        this.L0 = true;
        super.clearFocus();
        this.f915p.clearFocus();
        this.f915p.setImeVisibility(false);
        this.L0 = false;
    }

    @Override // p.b
    public void e() {
        this.f915p.setText("");
        SearchAutoComplete searchAutoComplete = this.f915p;
        searchAutoComplete.setSelection(searchAutoComplete.length());
        this.P0 = "";
        clearFocus();
        A(true);
        this.f915p.setImeOptions(this.R0);
        this.Q0 = false;
    }

    public int getImeOptions() {
        return this.f915p.getImeOptions();
    }

    public int getInputType() {
        return this.f915p.getInputType();
    }

    public int getMaxWidth() {
        return this.M0;
    }

    public CharSequence getQuery() {
        return this.f915p.getText();
    }

    @Nullable
    public CharSequence getQueryHint() {
        CharSequence charSequence = this.J0;
        if (charSequence != null) {
            return charSequence;
        }
        SearchableInfo searchableInfo = this.S0;
        if (searchableInfo == null || searchableInfo.getHintId() == 0) {
            return this.f932z0;
        }
        return getContext().getText(this.S0.getHintId());
    }

    public int getSuggestionCommitIconResId() {
        return this.f927w0;
    }

    public int getSuggestionRowLayout() {
        return this.f926v0;
    }

    public l0.a getSuggestionsAdapter() {
        return this.H0;
    }

    public final Intent l(String str, Uri uri, String str2, String str3, int i10, String str4) {
        Intent intent = new Intent(str);
        intent.addFlags(268435456);
        if (uri != null) {
            intent.setData(uri);
        }
        intent.putExtra("user_query", this.P0);
        if (str3 != null) {
            intent.putExtra("query", str3);
        }
        if (str2 != null) {
            intent.putExtra("intent_extra_data_key", str2);
        }
        Bundle bundle = this.T0;
        if (bundle != null) {
            intent.putExtra("app_data", bundle);
        }
        if (i10 != 0) {
            intent.putExtra("action_key", i10);
            intent.putExtra("action_msg", str4);
        }
        intent.setComponent(this.S0.getSearchActivity());
        return intent;
    }

    public final Intent m(Intent intent, SearchableInfo searchableInfo) {
        ComponentName searchActivity = searchableInfo.getSearchActivity();
        Intent intent2 = new Intent("android.intent.action.SEARCH");
        intent2.setComponent(searchActivity);
        PendingIntent activity = PendingIntent.getActivity(getContext(), 0, intent2, 1073741824);
        Bundle bundle = new Bundle();
        Bundle bundle2 = this.T0;
        if (bundle2 != null) {
            bundle.putParcelable("app_data", bundle2);
        }
        Intent intent3 = new Intent(intent);
        int i10 = 1;
        Resources resources = getResources();
        String string = searchableInfo.getVoiceLanguageModeId() != 0 ? resources.getString(searchableInfo.getVoiceLanguageModeId()) : "free_form";
        String str = null;
        String string2 = searchableInfo.getVoicePromptTextId() != 0 ? resources.getString(searchableInfo.getVoicePromptTextId()) : null;
        String string3 = searchableInfo.getVoiceLanguageId() != 0 ? resources.getString(searchableInfo.getVoiceLanguageId()) : null;
        if (searchableInfo.getVoiceMaxResults() != 0) {
            i10 = searchableInfo.getVoiceMaxResults();
        }
        intent3.putExtra("android.speech.extra.LANGUAGE_MODEL", string);
        intent3.putExtra("android.speech.extra.PROMPT", string2);
        intent3.putExtra("android.speech.extra.LANGUAGE", string3);
        intent3.putExtra("android.speech.extra.MAX_RESULTS", i10);
        if (searchActivity != null) {
            str = searchActivity.flattenToShortString();
        }
        intent3.putExtra("calling_package", str);
        intent3.putExtra("android.speech.extra.RESULTS_PENDINGINTENT", activity);
        intent3.putExtra("android.speech.extra.RESULTS_PENDINGINTENT_BUNDLE", bundle);
        return intent3;
    }

    public void n() {
        if (Build.VERSION.SDK_INT >= 29) {
            this.f915p.refreshAutoCompleteResults();
            return;
        }
        n nVar = Z0;
        SearchAutoComplete searchAutoComplete = this.f915p;
        Objects.requireNonNull(nVar);
        n.a();
        Method method = nVar.f949a;
        if (method != null) {
            try {
                method.invoke(searchAutoComplete, new Object[0]);
            } catch (Exception unused) {
            }
        }
        n nVar2 = Z0;
        SearchAutoComplete searchAutoComplete2 = this.f915p;
        Objects.requireNonNull(nVar2);
        n.a();
        Method method2 = nVar2.f950b;
        if (method2 != null) {
            try {
                method2.invoke(searchAutoComplete2, new Object[0]);
            } catch (Exception unused2) {
            }
        }
    }

    public void o(int i10, String str, String str2) {
        getContext().startActivity(l("android.intent.action.SEARCH", null, null, str2, i10, null));
    }

    public void onDetachedFromWindow() {
        removeCallbacks(this.U0);
        post(this.V0);
        super.onDetachedFromWindow();
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        super.onLayout(z10, i10, i11, i12, i13);
        if (z10) {
            SearchAutoComplete searchAutoComplete = this.f915p;
            Rect rect = this.f916p0;
            searchAutoComplete.getLocationInWindow(this.f920r0);
            getLocationInWindow(this.f922s0);
            int[] iArr = this.f920r0;
            int i14 = iArr[1];
            int[] iArr2 = this.f922s0;
            int i15 = i14 - iArr2[1];
            int i16 = iArr[0] - iArr2[0];
            rect.set(i16, i15, searchAutoComplete.getWidth() + i16, searchAutoComplete.getHeight() + i15);
            Rect rect2 = this.f918q0;
            Rect rect3 = this.f916p0;
            rect2.set(rect3.left, 0, rect3.right, i13 - i11);
            o oVar = this.f914o0;
            if (oVar == null) {
                o oVar2 = new o(this.f918q0, this.f916p0, this.f915p);
                this.f914o0 = oVar2;
                setTouchDelegate(oVar2);
                return;
            }
            oVar.a(this.f918q0, this.f916p0);
        }
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public void onMeasure(int i10, int i11) {
        int i12;
        if (this.G0) {
            super.onMeasure(i10, i11);
            return;
        }
        int mode = View.MeasureSpec.getMode(i10);
        int size = View.MeasureSpec.getSize(i10);
        if (mode == Integer.MIN_VALUE) {
            int i13 = this.M0;
            size = i13 > 0 ? Math.min(i13, size) : Math.min(getPreferredWidth(), size);
        } else if (mode == 0) {
            size = this.M0;
            if (size <= 0) {
                size = getPreferredWidth();
            }
        } else if (mode == 1073741824 && (i12 = this.M0) > 0) {
            size = Math.min(i12, size);
        }
        int mode2 = View.MeasureSpec.getMode(i11);
        int size2 = View.MeasureSpec.getSize(i11);
        if (mode2 == Integer.MIN_VALUE) {
            size2 = Math.min(getPreferredHeight(), size2);
        } else if (mode2 == 0) {
            size2 = getPreferredHeight();
        }
        super.onMeasure(View.MeasureSpec.makeMeasureSpec(size, 1073741824), View.MeasureSpec.makeMeasureSpec(size2, 1073741824));
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.f1662a);
        A(savedState.f933c);
        requestLayout();
    }

    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        savedState.f933c = this.G0;
        return savedState;
    }

    public void onWindowFocusChanged(boolean z10) {
        super.onWindowFocusChanged(z10);
        post(this.U0);
    }

    public void p() {
        if (!TextUtils.isEmpty(this.f915p.getText())) {
            this.f915p.setText("");
            this.f915p.requestFocus();
            this.f915p.setImeVisibility(true);
        } else if (this.F0) {
            k kVar = this.B0;
            if (kVar == null || !kVar.a()) {
                clearFocus();
                A(true);
            }
        }
    }

    public boolean q(int i10) {
        int i11;
        Uri uri;
        String h6;
        m mVar = this.D0;
        if (mVar != null && mVar.b(i10)) {
            return false;
        }
        Cursor cursor = this.H0.f7418c;
        if (cursor != null && cursor.moveToPosition(i10)) {
            Intent intent = null;
            try {
                int i12 = g0.f1100o0;
                String h10 = g0.h(cursor, cursor.getColumnIndex("suggest_intent_action"));
                if (h10 == null) {
                    h10 = this.S0.getSuggestIntentAction();
                }
                if (h10 == null) {
                    h10 = "android.intent.action.SEARCH";
                }
                String h11 = g0.h(cursor, cursor.getColumnIndex("suggest_intent_data"));
                if (h11 == null) {
                    h11 = this.S0.getSuggestIntentData();
                }
                if (!(h11 == null || (h6 = g0.h(cursor, cursor.getColumnIndex("suggest_intent_data_id"))) == null)) {
                    h11 = h11 + "/" + Uri.encode(h6);
                }
                if (h11 == null) {
                    uri = null;
                } else {
                    uri = Uri.parse(h11);
                }
                intent = l(h10, uri, g0.h(cursor, cursor.getColumnIndex("suggest_intent_extra_data")), g0.h(cursor, cursor.getColumnIndex("suggest_intent_query")), 0, null);
            } catch (RuntimeException e10) {
                try {
                    i11 = cursor.getPosition();
                } catch (RuntimeException unused) {
                    i11 = -1;
                }
                Log.w("SearchView", "Search suggestions cursor at row " + i11 + " returned exception.", e10);
            }
            if (intent != null) {
                try {
                    getContext().startActivity(intent);
                } catch (RuntimeException e11) {
                    Log.e("SearchView", "Failed launch activity: " + intent, e11);
                }
            }
        }
        this.f915p.setImeVisibility(false);
        this.f915p.dismissDropDown();
        return true;
    }

    public boolean r(int i10) {
        m mVar = this.D0;
        if (mVar != null && mVar.a(i10)) {
            return false;
        }
        Editable text = this.f915p.getText();
        Cursor cursor = this.H0.f7418c;
        if (cursor == null) {
            return true;
        }
        if (cursor.moveToPosition(i10)) {
            CharSequence c10 = this.H0.c(cursor);
            if (c10 != null) {
                setQuery(c10);
                return true;
            }
            setQuery(text);
            return true;
        }
        setQuery(text);
        return true;
    }

    public boolean requestFocus(int i10, Rect rect) {
        if (this.L0 || !isFocusable()) {
            return false;
        }
        if (this.G0) {
            return super.requestFocus(i10, rect);
        }
        boolean requestFocus = this.f915p.requestFocus(i10, rect);
        if (requestFocus) {
            A(false);
        }
        return requestFocus;
    }

    public void s(CharSequence charSequence) {
        setQuery(charSequence);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setAppSearchData(Bundle bundle) {
        this.T0 = bundle;
    }

    public void setIconified(boolean z10) {
        if (z10) {
            p();
        } else {
            t();
        }
    }

    public void setIconifiedByDefault(boolean z10) {
        if (this.F0 != z10) {
            this.F0 = z10;
            A(z10);
            x();
        }
    }

    public void setImeOptions(int i10) {
        this.f915p.setImeOptions(i10);
    }

    public void setInputType(int i10) {
        this.f915p.setInputType(i10);
    }

    public void setMaxWidth(int i10) {
        this.M0 = i10;
        requestLayout();
    }

    public void setOnCloseListener(k kVar) {
        this.B0 = kVar;
    }

    public void setOnQueryTextFocusChangeListener(View.OnFocusChangeListener onFocusChangeListener) {
        this.C0 = onFocusChangeListener;
    }

    public void setOnQueryTextListener(l lVar) {
        this.A0 = lVar;
    }

    public void setOnSearchClickListener(View.OnClickListener onClickListener) {
        this.E0 = onClickListener;
    }

    public void setOnSuggestionListener(m mVar) {
        this.D0 = mVar;
    }

    public void setQueryHint(@Nullable CharSequence charSequence) {
        this.J0 = charSequence;
        x();
    }

    public void setQueryRefinementEnabled(boolean z10) {
        this.K0 = z10;
        l0.a aVar = this.H0;
        if (aVar instanceof g0) {
            ((g0) aVar).f1108q = z10 ? 2 : 1;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:30:0x009c, code lost:
        if (getContext().getPackageManager().resolveActivity(r2, com.xiaomi.mitv.socialtv.common.net.media.VideoConstants.SEARCH_MASK_EDUCATION) != null) goto L_0x00a0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void setSearchableInfo(android.app.SearchableInfo r7) {
        /*
        // Method dump skipped, instructions count: 177
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.setSearchableInfo(android.app.SearchableInfo):void");
    }

    public void setSubmitButtonEnabled(boolean z10) {
        this.I0 = z10;
        A(this.G0);
    }

    public void setSuggestionsAdapter(l0.a aVar) {
        this.H0 = aVar;
        this.f915p.setAdapter(aVar);
    }

    public void t() {
        A(false);
        this.f915p.requestFocus();
        this.f915p.setImeVisibility(true);
        View.OnClickListener onClickListener = this.E0;
        if (onClickListener != null) {
            onClickListener.onClick(this);
        }
    }

    public void u() {
        Editable text = this.f915p.getText();
        if (text != null && TextUtils.getTrimmedLength(text) > 0) {
            l lVar = this.A0;
            if (lVar == null || !lVar.b(text.toString())) {
                if (this.S0 != null) {
                    o(0, null, text.toString());
                }
                this.f915p.setImeVisibility(false);
                this.f915p.dismissDropDown();
            }
        }
    }

    public final void v() {
        boolean z10 = true;
        boolean z11 = !TextUtils.isEmpty(this.f915p.getText());
        int i10 = 0;
        if (!z11 && (!this.F0 || this.Q0)) {
            z10 = false;
        }
        ImageView imageView = this.f930y;
        if (!z10) {
            i10 = 8;
        }
        imageView.setVisibility(i10);
        Drawable drawable = this.f930y.getDrawable();
        if (drawable != null) {
            drawable.setState(z11 ? ViewGroup.ENABLED_STATE_SET : ViewGroup.EMPTY_STATE_SET);
        }
    }

    public void w() {
        int[] iArr = this.f915p.hasFocus() ? ViewGroup.FOCUSED_STATE_SET : ViewGroup.EMPTY_STATE_SET;
        Drawable background = this.f919r.getBackground();
        if (background != null) {
            background.setState(iArr);
        }
        Drawable background2 = this.f921s.getBackground();
        if (background2 != null) {
            background2.setState(iArr);
        }
        invalidate();
    }

    public final void x() {
        SpannableStringBuilder queryHint = getQueryHint();
        SearchAutoComplete searchAutoComplete = this.f915p;
        if (queryHint == null) {
            queryHint = "";
        }
        if (this.F0 && this.f925u0 != null) {
            int textSize = (int) (((double) searchAutoComplete.getTextSize()) * 1.25d);
            this.f925u0.setBounds(0, 0, textSize, textSize);
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("   ");
            spannableStringBuilder.setSpan(new ImageSpan(this.f925u0), 1, 2, 33);
            spannableStringBuilder.append(queryHint);
            queryHint = spannableStringBuilder;
        }
        searchAutoComplete.setHint(queryHint);
    }

    public final void y() {
        int i10 = 0;
        if (!((this.I0 || this.N0) && !this.G0) || !(this.f928x.getVisibility() == 0 || this.f912m0.getVisibility() == 0)) {
            i10 = 8;
        }
        this.f921s.setVisibility(i10);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:14:0x001e, code lost:
        if (r2.N0 == false) goto L_0x0023;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void z(boolean r3) {
        /*
            r2 = this;
            boolean r0 = r2.I0
            r1 = 0
            if (r0 == 0) goto L_0x0021
            if (r0 != 0) goto L_0x000b
            boolean r0 = r2.N0
            if (r0 == 0) goto L_0x0011
        L_0x000b:
            boolean r0 = r2.G0
            if (r0 != 0) goto L_0x0011
            r0 = 1
            goto L_0x0012
        L_0x0011:
            r0 = r1
        L_0x0012:
            if (r0 == 0) goto L_0x0021
            boolean r0 = r2.hasFocus()
            if (r0 == 0) goto L_0x0021
            if (r3 != 0) goto L_0x0023
            boolean r3 = r2.N0
            if (r3 != 0) goto L_0x0021
            goto L_0x0023
        L_0x0021:
            r1 = 8
        L_0x0023:
            android.widget.ImageView r3 = r2.f928x
            r3.setVisibility(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SearchView.z(boolean):void");
    }

    public SearchView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.searchViewStyle);
    }

    public SearchView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f916p0 = new Rect();
        this.f918q0 = new Rect();
        this.f920r0 = new int[2];
        this.f922s0 = new int[2];
        this.U0 = new b();
        this.V0 = new c();
        this.W0 = new WeakHashMap<>();
        f fVar = new f();
        this.X0 = new g();
        h hVar = new h();
        i iVar = new i();
        j jVar = new j();
        this.Y0 = new a();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.SearchView, i10, 0);
        m0 m0Var = new m0(context, obtainStyledAttributes);
        LayoutInflater.from(context).inflate(m0Var.m(R$styleable.SearchView_layout, R$layout.abc_search_view), (ViewGroup) this, true);
        SearchAutoComplete searchAutoComplete = (SearchAutoComplete) findViewById(R$id.search_src_text);
        this.f915p = searchAutoComplete;
        searchAutoComplete.setSearchView(this);
        this.f917q = findViewById(R$id.search_edit_frame);
        View findViewById = findViewById(R$id.search_plate);
        this.f919r = findViewById;
        View findViewById2 = findViewById(R$id.submit_area);
        this.f921s = findViewById2;
        ImageView imageView = (ImageView) findViewById(R$id.search_button);
        this.f923t = imageView;
        ImageView imageView2 = (ImageView) findViewById(R$id.search_go_btn);
        this.f928x = imageView2;
        ImageView imageView3 = (ImageView) findViewById(R$id.search_close_btn);
        this.f930y = imageView3;
        ImageView imageView4 = (ImageView) findViewById(R$id.search_voice_btn);
        this.f912m0 = imageView4;
        ImageView imageView5 = (ImageView) findViewById(R$id.search_mag_icon);
        this.f924t0 = imageView5;
        Drawable g10 = m0Var.g(R$styleable.SearchView_queryBackground);
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        findViewById.setBackground(g10);
        findViewById2.setBackground(m0Var.g(R$styleable.SearchView_submitBackground));
        int i11 = R$styleable.SearchView_searchIcon;
        imageView.setImageDrawable(m0Var.g(i11));
        imageView2.setImageDrawable(m0Var.g(R$styleable.SearchView_goIcon));
        imageView3.setImageDrawable(m0Var.g(R$styleable.SearchView_closeIcon));
        imageView4.setImageDrawable(m0Var.g(R$styleable.SearchView_voiceIcon));
        imageView5.setImageDrawable(m0Var.g(i11));
        this.f925u0 = m0Var.g(R$styleable.SearchView_searchHintIcon);
        imageView.setTooltipText(getResources().getString(R$string.abc_searchview_description_search));
        this.f926v0 = m0Var.m(R$styleable.SearchView_suggestionRowLayout, R$layout.abc_search_dropdown_item_icons_2line);
        this.f927w0 = m0Var.m(R$styleable.SearchView_commitIcon, 0);
        imageView.setOnClickListener(fVar);
        imageView3.setOnClickListener(fVar);
        imageView2.setOnClickListener(fVar);
        imageView4.setOnClickListener(fVar);
        searchAutoComplete.setOnClickListener(fVar);
        searchAutoComplete.addTextChangedListener(this.Y0);
        searchAutoComplete.setOnEditorActionListener(hVar);
        searchAutoComplete.setOnItemClickListener(iVar);
        searchAutoComplete.setOnItemSelectedListener(jVar);
        searchAutoComplete.setOnKeyListener(this.X0);
        searchAutoComplete.setOnFocusChangeListener(new d());
        setIconifiedByDefault(m0Var.a(R$styleable.SearchView_iconifiedByDefault, true));
        int f10 = m0Var.f(R$styleable.SearchView_android_maxWidth, -1);
        if (f10 != -1) {
            setMaxWidth(f10);
        }
        this.f932z0 = m0Var.o(R$styleable.SearchView_defaultQueryHint);
        this.J0 = m0Var.o(R$styleable.SearchView_queryHint);
        int j10 = m0Var.j(R$styleable.SearchView_android_imeOptions, -1);
        if (j10 != -1) {
            setImeOptions(j10);
        }
        int j11 = m0Var.j(R$styleable.SearchView_android_inputType, -1);
        if (j11 != -1) {
            setInputType(j11);
        }
        setFocusable(m0Var.a(R$styleable.SearchView_android_focusable, true));
        obtainStyledAttributes.recycle();
        Intent intent = new Intent("android.speech.action.WEB_SEARCH");
        this.f929x0 = intent;
        intent.addFlags(268435456);
        intent.putExtra("android.speech.extra.LANGUAGE_MODEL", "web_search");
        Intent intent2 = new Intent("android.speech.action.RECOGNIZE_SPEECH");
        this.f931y0 = intent2;
        intent2.addFlags(268435456);
        View findViewById3 = findViewById(searchAutoComplete.getDropDownAnchor());
        this.f913n0 = findViewById3;
        if (findViewById3 != null) {
            findViewById3.addOnLayoutChangeListener(new e());
        }
        A(this.F0);
        x();
    }
}
