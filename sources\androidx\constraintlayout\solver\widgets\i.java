package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.SolverVariable;
import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import w.b;
import w.c;

/* compiled from: ResolutionAnchor */
public class i extends c {

    /* renamed from: c  reason: collision with root package name */
    public ConstraintAnchor f1377c;

    /* renamed from: d  reason: collision with root package name */
    public i f1378d;

    /* renamed from: e  reason: collision with root package name */
    public float f1379e;

    /* renamed from: f  reason: collision with root package name */
    public i f1380f;

    /* renamed from: g  reason: collision with root package name */
    public float f1381g;
    public int h = 0;

    /* renamed from: i  reason: collision with root package name */
    public i f1382i;

    /* renamed from: j  reason: collision with root package name */
    public b f1383j = null;

    /* renamed from: k  reason: collision with root package name */
    public int f1384k = 1;

    /* renamed from: l  reason: collision with root package name */
    public b f1385l = null;

    public i(ConstraintAnchor constraintAnchor) {
        this.f1377c = constraintAnchor;
    }

    @Override // w.c
    public void d() {
        int i10;
        i iVar;
        i iVar2;
        i iVar3;
        i iVar4;
        i iVar5;
        i iVar6;
        float f10;
        float f11;
        float f12;
        i iVar7;
        boolean z10 = true;
        if (this.f10635b != 1 && (i10 = this.h) != 4) {
            b bVar = this.f1383j;
            if (bVar != null) {
                if (bVar.f10635b == 1) {
                    this.f1379e = ((float) this.f1384k) * bVar.f10633c;
                } else {
                    return;
                }
            }
            b bVar2 = this.f1385l;
            if (bVar2 != null) {
                if (bVar2.f10635b == 1) {
                    float f13 = bVar2.f10633c;
                } else {
                    return;
                }
            }
            if (i10 == 1 && ((iVar7 = this.f1378d) == null || iVar7.f10635b == 1)) {
                if (iVar7 == null) {
                    this.f1380f = this;
                    this.f1381g = this.f1379e;
                } else {
                    this.f1380f = iVar7.f1380f;
                    this.f1381g = iVar7.f1381g + this.f1379e;
                }
                a();
            } else if (i10 == 2 && (iVar4 = this.f1378d) != null && iVar4.f10635b == 1 && (iVar5 = this.f1382i) != null && (iVar6 = iVar5.f1378d) != null && iVar6.f10635b == 1) {
                this.f1380f = iVar4.f1380f;
                iVar5.f1380f = iVar6.f1380f;
                ConstraintAnchor constraintAnchor = this.f1377c;
                ConstraintAnchor.Type type = constraintAnchor.f1278c;
                ConstraintAnchor.Type type2 = ConstraintAnchor.Type.RIGHT;
                int i11 = 0;
                if (!(type == type2 || type == ConstraintAnchor.Type.BOTTOM)) {
                    z10 = false;
                }
                if (z10) {
                    f10 = iVar4.f1381g - iVar6.f1381g;
                } else {
                    f10 = iVar6.f1381g - iVar4.f1381g;
                }
                if (type == ConstraintAnchor.Type.LEFT || type == type2) {
                    f11 = f10 - ((float) constraintAnchor.f1277b.n());
                    f12 = this.f1377c.f1277b.V;
                } else {
                    f11 = f10 - ((float) constraintAnchor.f1277b.h());
                    f12 = this.f1377c.f1277b.W;
                }
                int b10 = this.f1377c.b();
                int b11 = this.f1382i.f1377c.b();
                ConstraintAnchor constraintAnchor2 = this.f1377c.f1279d;
                i iVar8 = this.f1382i;
                if (constraintAnchor2 == iVar8.f1377c.f1279d) {
                    f12 = 0.5f;
                    b11 = 0;
                } else {
                    i11 = b10;
                }
                float f14 = (float) i11;
                float f15 = (float) b11;
                float f16 = (f11 - f14) - f15;
                if (z10) {
                    iVar8.f1381g = (f16 * f12) + iVar8.f1378d.f1381g + f15;
                    this.f1381g = (this.f1378d.f1381g - f14) - ((1.0f - f12) * f16);
                } else {
                    this.f1381g = (f16 * f12) + this.f1378d.f1381g + f14;
                    iVar8.f1381g = (iVar8.f1378d.f1381g - f15) - ((1.0f - f12) * f16);
                }
                a();
                this.f1382i.a();
            } else if (i10 == 3 && (iVar = this.f1378d) != null && iVar.f10635b == 1 && (iVar2 = this.f1382i) != null && (iVar3 = iVar2.f1378d) != null && iVar3.f10635b == 1) {
                this.f1380f = iVar.f1380f;
                iVar2.f1380f = iVar3.f1380f;
                this.f1381g = iVar.f1381g + this.f1379e;
                iVar2.f1381g = iVar3.f1381g + iVar2.f1379e;
                a();
                this.f1382i.a();
            } else if (i10 == 5) {
                this.f1377c.f1277b.u();
            }
        }
    }

    public void e(androidx.constraintlayout.solver.c cVar) {
        SolverVariable solverVariable = this.f1377c.f1283i;
        i iVar = this.f1380f;
        if (iVar == null) {
            cVar.e(solverVariable, (int) (this.f1381g + 0.5f));
        } else {
            cVar.d(solverVariable, cVar.l(iVar.f1377c), (int) (this.f1381g + 0.5f), 6);
        }
    }

    public void f(int i10, i iVar, int i11) {
        this.h = i10;
        this.f1378d = iVar;
        this.f1379e = (float) i11;
        iVar.f10634a.add(this);
    }

    public void g(i iVar, int i10) {
        this.f1378d = iVar;
        this.f1379e = (float) i10;
        iVar.f10634a.add(this);
    }

    public void h(i iVar, int i10, b bVar) {
        this.f1378d = iVar;
        iVar.f10634a.add(this);
        this.f1383j = bVar;
        this.f1384k = i10;
        bVar.f10634a.add(this);
    }

    public void i() {
        this.f10635b = 0;
        this.f10634a.clear();
        this.f1378d = null;
        this.f1379e = Constant.VOLUME_FLOAT_MIN;
        this.f1383j = null;
        this.f1384k = 1;
        this.f1385l = null;
        this.f1380f = null;
        this.f1381g = Constant.VOLUME_FLOAT_MIN;
        this.f1382i = null;
        this.h = 0;
    }

    public void j(i iVar, float f10) {
        int i10 = this.f10635b;
        if (i10 == 0 || !(this.f1380f == iVar || this.f1381g == f10)) {
            this.f1380f = iVar;
            this.f1381g = f10;
            if (i10 == 1) {
                b();
            }
            a();
        }
    }

    public String k(int i10) {
        return i10 == 1 ? "DIRECT" : i10 == 2 ? "CENTER" : i10 == 3 ? "MATCH" : i10 == 4 ? "CHAIN" : i10 == 5 ? "BARRIER" : "UNCONNECTED";
    }

    public String toString() {
        if (this.f10635b != 1) {
            StringBuilder a10 = f.a("{ ");
            a10.append(this.f1377c);
            a10.append(" UNRESOLVED} type: ");
            a10.append(k(this.h));
            return a10.toString();
        } else if (this.f1380f == this) {
            StringBuilder a11 = f.a("[");
            a11.append(this.f1377c);
            a11.append(", RESOLVED: ");
            a11.append(this.f1381g);
            a11.append("]  type: ");
            a11.append(k(this.h));
            return a11.toString();
        } else {
            StringBuilder a12 = f.a("[");
            a12.append(this.f1377c);
            a12.append(", RESOLVED: ");
            a12.append(this.f1380f);
            a12.append(":");
            a12.append(this.f1381g);
            a12.append("] type: ");
            a12.append(k(this.h));
            return a12.toString();
        }
    }
}
