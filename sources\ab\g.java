package ab;

import java.math.BigInteger;
import java.security.spec.AlgorithmParameterSpec;

/* compiled from: ElGamalParameterSpec */
public class g implements AlgorithmParameterSpec {

    /* renamed from: a  reason: collision with root package name */
    public BigInteger f255a;

    /* renamed from: b  reason: collision with root package name */
    public BigInteger f256b;

    public g(BigInteger bigInteger, BigInteger bigInteger2) {
        this.f255a = bigInteger;
        this.f256b = bigInteger2;
    }
}
