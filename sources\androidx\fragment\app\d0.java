package androidx.fragment.app;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import java.util.ArrayList;

/* compiled from: FragmentTransaction */
public abstract class d0 {

    /* renamed from: a  reason: collision with root package name */
    public ArrayList<a> f1866a = new ArrayList<>();

    /* renamed from: b  reason: collision with root package name */
    public int f1867b;

    /* renamed from: c  reason: collision with root package name */
    public int f1868c;

    /* renamed from: d  reason: collision with root package name */
    public int f1869d;

    /* renamed from: e  reason: collision with root package name */
    public int f1870e;

    /* renamed from: f  reason: collision with root package name */
    public int f1871f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f1872g;
    @Nullable
    public String h;

    /* renamed from: i  reason: collision with root package name */
    public int f1873i;

    /* renamed from: j  reason: collision with root package name */
    public CharSequence f1874j;

    /* renamed from: k  reason: collision with root package name */
    public int f1875k;

    /* renamed from: l  reason: collision with root package name */
    public CharSequence f1876l;

    /* renamed from: m  reason: collision with root package name */
    public ArrayList<String> f1877m;

    /* renamed from: n  reason: collision with root package name */
    public ArrayList<String> f1878n;

    /* renamed from: o  reason: collision with root package name */
    public boolean f1879o = false;

    /* compiled from: FragmentTransaction */
    public static final class a {

        /* renamed from: a  reason: collision with root package name */
        public int f1880a;

        /* renamed from: b  reason: collision with root package name */
        public Fragment f1881b;

        /* renamed from: c  reason: collision with root package name */
        public int f1882c;

        /* renamed from: d  reason: collision with root package name */
        public int f1883d;

        /* renamed from: e  reason: collision with root package name */
        public int f1884e;

        /* renamed from: f  reason: collision with root package name */
        public int f1885f;

        /* renamed from: g  reason: collision with root package name */
        public Lifecycle.State f1886g;
        public Lifecycle.State h;

        public a() {
        }

        public a(int i10, Fragment fragment) {
            this.f1880a = i10;
            this.f1881b = fragment;
            Lifecycle.State state = Lifecycle.State.RESUMED;
            this.f1886g = state;
            this.h = state;
        }
    }

    public d0(@NonNull u uVar, @Nullable ClassLoader classLoader) {
    }

    public void b(a aVar) {
        this.f1866a.add(aVar);
        aVar.f1882c = this.f1867b;
        aVar.f1883d = this.f1868c;
        aVar.f1884e = this.f1869d;
        aVar.f1885f = this.f1870e;
    }

    public abstract int c();
}
