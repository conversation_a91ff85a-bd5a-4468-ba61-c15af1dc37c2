package androidx.recyclerview.widget;

import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import java.util.ArrayList;
import java.util.List;

/* compiled from: DefaultItemAnimator */
public class l extends b0 {

    /* renamed from: s  reason: collision with root package name */
    public static TimeInterpolator f2387s;
    public ArrayList<RecyclerView.w> h = new ArrayList<>();

    /* renamed from: i  reason: collision with root package name */
    public ArrayList<RecyclerView.w> f2388i = new ArrayList<>();

    /* renamed from: j  reason: collision with root package name */
    public ArrayList<b> f2389j = new ArrayList<>();

    /* renamed from: k  reason: collision with root package name */
    public ArrayList<a> f2390k = new ArrayList<>();

    /* renamed from: l  reason: collision with root package name */
    public ArrayList<ArrayList<RecyclerView.w>> f2391l = new ArrayList<>();

    /* renamed from: m  reason: collision with root package name */
    public ArrayList<ArrayList<b>> f2392m = new ArrayList<>();

    /* renamed from: n  reason: collision with root package name */
    public ArrayList<ArrayList<a>> f2393n = new ArrayList<>();

    /* renamed from: o  reason: collision with root package name */
    public ArrayList<RecyclerView.w> f2394o = new ArrayList<>();

    /* renamed from: p  reason: collision with root package name */
    public ArrayList<RecyclerView.w> f2395p = new ArrayList<>();

    /* renamed from: q  reason: collision with root package name */
    public ArrayList<RecyclerView.w> f2396q = new ArrayList<>();

    /* renamed from: r  reason: collision with root package name */
    public ArrayList<RecyclerView.w> f2397r = new ArrayList<>();

    /* compiled from: DefaultItemAnimator */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public RecyclerView.w f2398a;

        /* renamed from: b  reason: collision with root package name */
        public RecyclerView.w f2399b;

        /* renamed from: c  reason: collision with root package name */
        public int f2400c;

        /* renamed from: d  reason: collision with root package name */
        public int f2401d;

        /* renamed from: e  reason: collision with root package name */
        public int f2402e;

        /* renamed from: f  reason: collision with root package name */
        public int f2403f;

        public a(RecyclerView.w wVar, RecyclerView.w wVar2, int i10, int i11, int i12, int i13) {
            this.f2398a = wVar;
            this.f2399b = wVar2;
            this.f2400c = i10;
            this.f2401d = i11;
            this.f2402e = i12;
            this.f2403f = i13;
        }

        public String toString() {
            StringBuilder a10 = f.a("ChangeInfo{oldHolder=");
            a10.append(this.f2398a);
            a10.append(", newHolder=");
            a10.append(this.f2399b);
            a10.append(", fromX=");
            a10.append(this.f2400c);
            a10.append(", fromY=");
            a10.append(this.f2401d);
            a10.append(", toX=");
            a10.append(this.f2402e);
            a10.append(", toY=");
            return b0.b.a(a10, this.f2403f, '}');
        }
    }

    /* compiled from: DefaultItemAnimator */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public RecyclerView.w f2404a;

        /* renamed from: b  reason: collision with root package name */
        public int f2405b;

        /* renamed from: c  reason: collision with root package name */
        public int f2406c;

        /* renamed from: d  reason: collision with root package name */
        public int f2407d;

        /* renamed from: e  reason: collision with root package name */
        public int f2408e;

        public b(RecyclerView.w wVar, int i10, int i11, int i12, int i13) {
            this.f2404a = wVar;
            this.f2405b = i10;
            this.f2406c = i11;
            this.f2407d = i12;
            this.f2408e = i13;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemAnimator
    public void e(RecyclerView.w wVar) {
        View view = wVar.f2267a;
        view.animate().cancel();
        int size = this.f2389j.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            } else if (this.f2389j.get(size).f2404a == wVar) {
                view.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                view.setTranslationX(Constant.VOLUME_FLOAT_MIN);
                c(wVar);
                this.f2389j.remove(size);
            }
        }
        l(this.f2390k, wVar);
        if (this.h.remove(wVar)) {
            view.setAlpha(1.0f);
            c(wVar);
        }
        if (this.f2388i.remove(wVar)) {
            view.setAlpha(1.0f);
            c(wVar);
        }
        int size2 = this.f2393n.size();
        while (true) {
            size2--;
            if (size2 < 0) {
                break;
            }
            ArrayList<a> arrayList = this.f2393n.get(size2);
            l(arrayList, wVar);
            if (arrayList.isEmpty()) {
                this.f2393n.remove(size2);
            }
        }
        int size3 = this.f2392m.size();
        while (true) {
            size3--;
            if (size3 < 0) {
                break;
            }
            ArrayList<b> arrayList2 = this.f2392m.get(size3);
            int size4 = arrayList2.size();
            while (true) {
                size4--;
                if (size4 < 0) {
                    break;
                } else if (arrayList2.get(size4).f2404a == wVar) {
                    view.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                    view.setTranslationX(Constant.VOLUME_FLOAT_MIN);
                    c(wVar);
                    arrayList2.remove(size4);
                    if (arrayList2.isEmpty()) {
                        this.f2392m.remove(size3);
                    }
                }
            }
        }
        int size5 = this.f2391l.size();
        while (true) {
            size5--;
            if (size5 >= 0) {
                ArrayList<RecyclerView.w> arrayList3 = this.f2391l.get(size5);
                if (arrayList3.remove(wVar)) {
                    view.setAlpha(1.0f);
                    c(wVar);
                    if (arrayList3.isEmpty()) {
                        this.f2391l.remove(size5);
                    }
                }
            } else {
                this.f2396q.remove(wVar);
                this.f2394o.remove(wVar);
                this.f2397r.remove(wVar);
                this.f2395p.remove(wVar);
                k();
                return;
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemAnimator
    public void f() {
        int size = this.f2389j.size();
        while (true) {
            size--;
            if (size < 0) {
                break;
            }
            b bVar = this.f2389j.get(size);
            View view = bVar.f2404a.f2267a;
            view.setTranslationY(Constant.VOLUME_FLOAT_MIN);
            view.setTranslationX(Constant.VOLUME_FLOAT_MIN);
            c(bVar.f2404a);
            this.f2389j.remove(size);
        }
        int size2 = this.h.size();
        while (true) {
            size2--;
            if (size2 < 0) {
                break;
            }
            c(this.h.get(size2));
            this.h.remove(size2);
        }
        int size3 = this.f2388i.size();
        while (true) {
            size3--;
            if (size3 < 0) {
                break;
            }
            RecyclerView.w wVar = this.f2388i.get(size3);
            wVar.f2267a.setAlpha(1.0f);
            c(wVar);
            this.f2388i.remove(size3);
        }
        int size4 = this.f2390k.size();
        while (true) {
            size4--;
            if (size4 < 0) {
                break;
            }
            a aVar = this.f2390k.get(size4);
            RecyclerView.w wVar2 = aVar.f2398a;
            if (wVar2 != null) {
                m(aVar, wVar2);
            }
            RecyclerView.w wVar3 = aVar.f2399b;
            if (wVar3 != null) {
                m(aVar, wVar3);
            }
        }
        this.f2390k.clear();
        if (g()) {
            int size5 = this.f2392m.size();
            while (true) {
                size5--;
                if (size5 < 0) {
                    break;
                }
                ArrayList<b> arrayList = this.f2392m.get(size5);
                int size6 = arrayList.size();
                while (true) {
                    size6--;
                    if (size6 >= 0) {
                        b bVar2 = arrayList.get(size6);
                        View view2 = bVar2.f2404a.f2267a;
                        view2.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                        view2.setTranslationX(Constant.VOLUME_FLOAT_MIN);
                        c(bVar2.f2404a);
                        arrayList.remove(size6);
                        if (arrayList.isEmpty()) {
                            this.f2392m.remove(arrayList);
                        }
                    }
                }
            }
            int size7 = this.f2391l.size();
            while (true) {
                size7--;
                if (size7 < 0) {
                    break;
                }
                ArrayList<RecyclerView.w> arrayList2 = this.f2391l.get(size7);
                int size8 = arrayList2.size();
                while (true) {
                    size8--;
                    if (size8 >= 0) {
                        RecyclerView.w wVar4 = arrayList2.get(size8);
                        wVar4.f2267a.setAlpha(1.0f);
                        c(wVar4);
                        arrayList2.remove(size8);
                        if (arrayList2.isEmpty()) {
                            this.f2391l.remove(arrayList2);
                        }
                    }
                }
            }
            int size9 = this.f2393n.size();
            while (true) {
                size9--;
                if (size9 >= 0) {
                    ArrayList<a> arrayList3 = this.f2393n.get(size9);
                    int size10 = arrayList3.size();
                    while (true) {
                        size10--;
                        if (size10 >= 0) {
                            a aVar2 = arrayList3.get(size10);
                            RecyclerView.w wVar5 = aVar2.f2398a;
                            if (wVar5 != null) {
                                m(aVar2, wVar5);
                            }
                            RecyclerView.w wVar6 = aVar2.f2399b;
                            if (wVar6 != null) {
                                m(aVar2, wVar6);
                            }
                            if (arrayList3.isEmpty()) {
                                this.f2393n.remove(arrayList3);
                            }
                        }
                    }
                } else {
                    j(this.f2396q);
                    j(this.f2395p);
                    j(this.f2394o);
                    j(this.f2397r);
                    d();
                    return;
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.ItemAnimator
    public boolean g() {
        return !this.f2388i.isEmpty() || !this.f2390k.isEmpty() || !this.f2389j.isEmpty() || !this.h.isEmpty() || !this.f2395p.isEmpty() || !this.f2396q.isEmpty() || !this.f2394o.isEmpty() || !this.f2397r.isEmpty() || !this.f2392m.isEmpty() || !this.f2391l.isEmpty() || !this.f2393n.isEmpty();
    }

    @Override // androidx.recyclerview.widget.b0
    public boolean i(RecyclerView.w wVar, int i10, int i11, int i12, int i13) {
        View view = wVar.f2267a;
        int translationX = i10 + ((int) view.getTranslationX());
        int translationY = i11 + ((int) wVar.f2267a.getTranslationY());
        n(wVar);
        int i14 = i12 - translationX;
        int i15 = i13 - translationY;
        if (i14 == 0 && i15 == 0) {
            c(wVar);
            return false;
        }
        if (i14 != 0) {
            view.setTranslationX((float) (-i14));
        }
        if (i15 != 0) {
            view.setTranslationY((float) (-i15));
        }
        this.f2389j.add(new b(wVar, translationX, translationY, i12, i13));
        return true;
    }

    public void j(List<RecyclerView.w> list) {
        int size = list.size();
        while (true) {
            size--;
            if (size >= 0) {
                list.get(size).f2267a.animate().cancel();
            } else {
                return;
            }
        }
    }

    public void k() {
        if (!g()) {
            d();
        }
    }

    public final void l(List<a> list, RecyclerView.w wVar) {
        int size = list.size();
        while (true) {
            size--;
            if (size >= 0) {
                a aVar = list.get(size);
                if (m(aVar, wVar) && aVar.f2398a == null && aVar.f2399b == null) {
                    list.remove(aVar);
                }
            } else {
                return;
            }
        }
    }

    public final boolean m(a aVar, RecyclerView.w wVar) {
        if (aVar.f2399b == wVar) {
            aVar.f2399b = null;
        } else if (aVar.f2398a != wVar) {
            return false;
        } else {
            aVar.f2398a = null;
        }
        wVar.f2267a.setAlpha(1.0f);
        wVar.f2267a.setTranslationX(Constant.VOLUME_FLOAT_MIN);
        wVar.f2267a.setTranslationY(Constant.VOLUME_FLOAT_MIN);
        c(wVar);
        return true;
    }

    public final void n(RecyclerView.w wVar) {
        if (f2387s == null) {
            f2387s = new ValueAnimator().getInterpolator();
        }
        wVar.f2267a.animate().setInterpolator(f2387s);
        e(wVar);
    }
}
