package a7;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import i7.e;
import java.util.ArrayList;
import miuix.appcompat.R$layout;

/* compiled from: ImmersionMenuAdapter */
public class a extends BaseAdapter {

    /* renamed from: a  reason: collision with root package name */
    public LayoutInflater f136a;

    /* renamed from: b  reason: collision with root package name */
    public ArrayList<MenuItem> f137b;

    /* compiled from: ImmersionMenuAdapter */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public ImageView f138a;

        /* renamed from: b  reason: collision with root package name */
        public TextView f139b;

        public b() {
        }

        public b(C0003a aVar) {
        }
    }

    public a(Context context, Menu menu) {
        this.f136a = LayoutInflater.from(context);
        ArrayList<MenuItem> arrayList = new ArrayList<>();
        this.f137b = arrayList;
        a(menu, arrayList);
    }

    public final void a(Menu menu, ArrayList<MenuItem> arrayList) {
        arrayList.clear();
        if (menu != null) {
            int size = menu.size();
            for (int i10 = 0; i10 < size; i10++) {
                MenuItem item = menu.getItem(i10);
                if (item.isVisible()) {
                    arrayList.add(item);
                }
            }
        }
    }

    public int getCount() {
        return this.f137b.size();
    }

    public Object getItem(int i10) {
        return this.f137b.get(i10);
    }

    public long getItemId(int i10) {
        return (long) i10;
    }

    public View getView(int i10, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = this.f136a.inflate(R$layout.miuix_appcompat_immersion_popup_menu_item, viewGroup, false);
            b bVar = new b(null);
            bVar.f138a = (ImageView) view.findViewById(16908294);
            bVar.f139b = (TextView) view.findViewById(16908308);
            view.setTag(bVar);
            i7.b.b(view);
        }
        e.c(view, i10, getCount());
        Object tag = view.getTag();
        if (tag != null) {
            b bVar2 = (b) tag;
            MenuItem menuItem = this.f137b.get(i10);
            if (menuItem.getIcon() != null) {
                bVar2.f138a.setImageDrawable(menuItem.getIcon());
                bVar2.f138a.setVisibility(0);
            } else {
                bVar2.f138a.setVisibility(8);
            }
            bVar2.f139b.setText(menuItem.getTitle());
        }
        return view;
    }
}
