package androidx.recyclerview.widget;

import androidx.recyclerview.widget.s;
import i0.c;
import java.util.ArrayList;
import java.util.List;

/* compiled from: AdapterHelper */
public class a implements s.a {

    /* renamed from: a  reason: collision with root package name */
    public c f2332a = new c(30);

    /* renamed from: b  reason: collision with root package name */
    public final ArrayList<b> f2333b = new ArrayList<>();

    /* renamed from: c  reason: collision with root package name */
    public final ArrayList<b> f2334c = new ArrayList<>();

    /* renamed from: d  reason: collision with root package name */
    public final AbstractC0015a f2335d;

    /* renamed from: e  reason: collision with root package name */
    public final s f2336e;

    /* renamed from: androidx.recyclerview.widget.a$a  reason: collision with other inner class name */
    /* compiled from: AdapterHelper */
    public interface AbstractC0015a {
    }

    /* compiled from: AdapterHelper */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public int f2337a;

        /* renamed from: b  reason: collision with root package name */
        public int f2338b;

        /* renamed from: c  reason: collision with root package name */
        public Object f2339c;

        /* renamed from: d  reason: collision with root package name */
        public int f2340d;

        public b(int i10, int i11, int i12, Object obj) {
            this.f2337a = i10;
            this.f2338b = i11;
            this.f2340d = i12;
            this.f2339c = obj;
        }

        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null || b.class != obj.getClass()) {
                return false;
            }
            b bVar = (b) obj;
            int i10 = this.f2337a;
            if (i10 != bVar.f2337a) {
                return false;
            }
            if (i10 == 8 && Math.abs(this.f2340d - this.f2338b) == 1 && this.f2340d == bVar.f2338b && this.f2338b == bVar.f2340d) {
                return true;
            }
            if (this.f2340d != bVar.f2340d || this.f2338b != bVar.f2338b) {
                return false;
            }
            Object obj2 = this.f2339c;
            if (obj2 != null) {
                if (!obj2.equals(bVar.f2339c)) {
                    return false;
                }
            } else if (bVar.f2339c != null) {
                return false;
            }
            return true;
        }

        public int hashCode() {
            return (((this.f2337a * 31) + this.f2338b) * 31) + this.f2340d;
        }

        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(Integer.toHexString(System.identityHashCode(this)));
            sb.append("[");
            int i10 = this.f2337a;
            sb.append(i10 != 1 ? i10 != 2 ? i10 != 4 ? i10 != 8 ? "??" : "mv" : "up" : "rm" : "add");
            sb.append(",s:");
            sb.append(this.f2338b);
            sb.append("c:");
            sb.append(this.f2340d);
            sb.append(",p:");
            sb.append(this.f2339c);
            sb.append("]");
            return sb.toString();
        }
    }

    public a(AbstractC0015a aVar) {
        this.f2335d = aVar;
        this.f2336e = new s(this);
    }

    public final boolean a(int i10) {
        int size = this.f2334c.size();
        for (int i11 = 0; i11 < size; i11++) {
            b bVar = this.f2334c.get(i11);
            int i12 = bVar.f2337a;
            if (i12 == 8) {
                if (f(bVar.f2340d, i11 + 1) == i10) {
                    return true;
                }
            } else if (i12 == 1) {
                int i13 = bVar.f2338b;
                int i14 = bVar.f2340d + i13;
                while (i13 < i14) {
                    if (f(i13, i11 + 1) == i10) {
                        return true;
                    }
                    i13++;
                }
                continue;
            } else {
                continue;
            }
        }
        return false;
    }

    public void b() {
        int size = this.f2334c.size();
        for (int i10 = 0; i10 < size; i10++) {
            ((y) this.f2335d).a(this.f2334c.get(i10));
        }
        l(this.f2334c);
    }

    public void c() {
        b();
        int size = this.f2333b.size();
        for (int i10 = 0; i10 < size; i10++) {
            b bVar = this.f2333b.get(i10);
            int i11 = bVar.f2337a;
            if (i11 == 1) {
                ((y) this.f2335d).a(bVar);
                ((y) this.f2335d).d(bVar.f2338b, bVar.f2340d);
            } else if (i11 == 2) {
                ((y) this.f2335d).a(bVar);
                AbstractC0015a aVar = this.f2335d;
                int i12 = bVar.f2338b;
                int i13 = bVar.f2340d;
                y yVar = (y) aVar;
                yVar.f2480a.R(i12, i13, true);
                RecyclerView recyclerView = yVar.f2480a;
                recyclerView.U0 = true;
                recyclerView.R0.f2248c += i13;
            } else if (i11 == 4) {
                ((y) this.f2335d).a(bVar);
                ((y) this.f2335d).c(bVar.f2338b, bVar.f2340d, bVar.f2339c);
            } else if (i11 == 8) {
                ((y) this.f2335d).a(bVar);
                ((y) this.f2335d).e(bVar.f2338b, bVar.f2340d);
            }
        }
        l(this.f2333b);
    }

    public final void d(b bVar) {
        int i10;
        int i11 = bVar.f2337a;
        if (i11 == 1 || i11 == 8) {
            throw new IllegalArgumentException("should not dispatch add or move for pre layout");
        }
        int m10 = m(bVar.f2338b, i11);
        int i12 = bVar.f2338b;
        int i13 = bVar.f2337a;
        if (i13 == 2) {
            i10 = 0;
        } else if (i13 == 4) {
            i10 = 1;
        } else {
            throw new IllegalArgumentException("op should be remove or update." + bVar);
        }
        int i14 = 1;
        for (int i15 = 1; i15 < bVar.f2340d; i15++) {
            int m11 = m((i10 * i15) + bVar.f2338b, bVar.f2337a);
            int i16 = bVar.f2337a;
            if (i16 == 2 ? m11 == m10 : i16 == 4 && m11 == m10 + 1) {
                i14++;
            } else {
                b h = h(i16, m10, i14, bVar.f2339c);
                e(h, i12);
                k(h);
                if (bVar.f2337a == 4) {
                    i12 += i14;
                }
                i14 = 1;
                m10 = m11;
            }
        }
        Object obj = bVar.f2339c;
        k(bVar);
        if (i14 > 0) {
            b h6 = h(bVar.f2337a, m10, i14, obj);
            e(h6, i12);
            k(h6);
        }
    }

    public void e(b bVar, int i10) {
        ((y) this.f2335d).a(bVar);
        int i11 = bVar.f2337a;
        if (i11 == 2) {
            AbstractC0015a aVar = this.f2335d;
            int i12 = bVar.f2340d;
            y yVar = (y) aVar;
            yVar.f2480a.R(i10, i12, true);
            RecyclerView recyclerView = yVar.f2480a;
            recyclerView.U0 = true;
            recyclerView.R0.f2248c += i12;
        } else if (i11 == 4) {
            ((y) this.f2335d).c(i10, bVar.f2340d, bVar.f2339c);
        } else {
            throw new IllegalArgumentException("only remove and update ops can be dispatched in first pass");
        }
    }

    public int f(int i10, int i11) {
        int size = this.f2334c.size();
        while (i11 < size) {
            b bVar = this.f2334c.get(i11);
            int i12 = bVar.f2337a;
            if (i12 == 8) {
                int i13 = bVar.f2338b;
                if (i13 == i10) {
                    i10 = bVar.f2340d;
                } else {
                    if (i13 < i10) {
                        i10--;
                    }
                    if (bVar.f2340d <= i10) {
                        i10++;
                    }
                }
            } else {
                int i14 = bVar.f2338b;
                if (i14 > i10) {
                    continue;
                } else if (i12 == 2) {
                    int i15 = bVar.f2340d;
                    if (i10 < i14 + i15) {
                        return -1;
                    }
                    i10 -= i15;
                } else if (i12 == 1) {
                    i10 += bVar.f2340d;
                }
            }
            i11++;
        }
        return i10;
    }

    public boolean g() {
        return this.f2333b.size() > 0;
    }

    public b h(int i10, int i11, int i12, Object obj) {
        b bVar = (b) this.f2332a.a();
        if (bVar == null) {
            return new b(i10, i11, i12, obj);
        }
        bVar.f2337a = i10;
        bVar.f2338b = i11;
        bVar.f2340d = i12;
        bVar.f2339c = obj;
        return bVar;
    }

    public final void i(b bVar) {
        this.f2334c.add(bVar);
        int i10 = bVar.f2337a;
        if (i10 == 1) {
            ((y) this.f2335d).d(bVar.f2338b, bVar.f2340d);
        } else if (i10 == 2) {
            AbstractC0015a aVar = this.f2335d;
            y yVar = (y) aVar;
            yVar.f2480a.R(bVar.f2338b, bVar.f2340d, false);
            yVar.f2480a.U0 = true;
        } else if (i10 == 4) {
            ((y) this.f2335d).c(bVar.f2338b, bVar.f2340d, bVar.f2339c);
        } else if (i10 == 8) {
            ((y) this.f2335d).e(bVar.f2338b, bVar.f2340d);
        } else {
            throw new IllegalArgumentException("Unknown update op type for " + bVar);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:172:0x0009 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:25:0x006b  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x0070  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x008e  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0092  */
    /* JADX WARNING: Removed duplicated region for block: B:34:0x009e  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x00a3  */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00d1  */
    /* JADX WARNING: Removed duplicated region for block: B:55:0x00d6  */
    /* JADX WARNING: Removed duplicated region for block: B:62:0x00f9  */
    /* JADX WARNING: Removed duplicated region for block: B:63:0x00fe  */
    /* JADX WARNING: Removed duplicated region for block: B:67:0x0117  */
    /* JADX WARNING: Removed duplicated region for block: B:68:0x0126  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void j() {
        /*
        // Method dump skipped, instructions count: 672
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.a.j():void");
    }

    public void k(b bVar) {
        bVar.f2339c = null;
        this.f2332a.b(bVar);
    }

    public void l(List<b> list) {
        int size = list.size();
        for (int i10 = 0; i10 < size; i10++) {
            k(list.get(i10));
        }
        list.clear();
    }

    public final int m(int i10, int i11) {
        int i12;
        int i13;
        for (int size = this.f2334c.size() - 1; size >= 0; size--) {
            b bVar = this.f2334c.get(size);
            int i14 = bVar.f2337a;
            if (i14 == 8) {
                int i15 = bVar.f2338b;
                int i16 = bVar.f2340d;
                if (i15 < i16) {
                    i13 = i15;
                    i12 = i16;
                } else {
                    i12 = i15;
                    i13 = i16;
                }
                if (i10 < i13 || i10 > i12) {
                    if (i10 < i15) {
                        if (i11 == 1) {
                            bVar.f2338b = i15 + 1;
                            bVar.f2340d = i16 + 1;
                        } else if (i11 == 2) {
                            bVar.f2338b = i15 - 1;
                            bVar.f2340d = i16 - 1;
                        }
                    }
                } else if (i13 == i15) {
                    if (i11 == 1) {
                        bVar.f2340d = i16 + 1;
                    } else if (i11 == 2) {
                        bVar.f2340d = i16 - 1;
                    }
                    i10++;
                } else {
                    if (i11 == 1) {
                        bVar.f2338b = i15 + 1;
                    } else if (i11 == 2) {
                        bVar.f2338b = i15 - 1;
                    }
                    i10--;
                }
            } else {
                int i17 = bVar.f2338b;
                if (i17 <= i10) {
                    if (i14 == 1) {
                        i10 -= bVar.f2340d;
                    } else if (i14 == 2) {
                        i10 += bVar.f2340d;
                    }
                } else if (i11 == 1) {
                    bVar.f2338b = i17 + 1;
                } else if (i11 == 2) {
                    bVar.f2338b = i17 - 1;
                }
            }
        }
        for (int size2 = this.f2334c.size() - 1; size2 >= 0; size2--) {
            b bVar2 = this.f2334c.get(size2);
            if (bVar2.f2337a == 8) {
                int i18 = bVar2.f2340d;
                if (i18 == bVar2.f2338b || i18 < 0) {
                    this.f2334c.remove(size2);
                    k(bVar2);
                }
            } else if (bVar2.f2340d <= 0) {
                this.f2334c.remove(size2);
                k(bVar2);
            }
        }
        return i10;
    }
}
