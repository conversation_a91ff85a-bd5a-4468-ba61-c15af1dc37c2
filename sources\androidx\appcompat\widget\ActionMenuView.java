package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewDebug;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleRes;
import androidx.appcompat.app.m;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.f;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.view.menu.i;
import androidx.appcompat.widget.ActionMenuPresenter;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.appcompat.widget.Toolbar;
import com.xiaomi.mitv.pie.EventResultPersister;

public class ActionMenuView extends LinearLayoutCompat implements d.b, i {

    /* renamed from: m0  reason: collision with root package name */
    public boolean f767m0;

    /* renamed from: n0  reason: collision with root package name */
    public int f768n0;

    /* renamed from: o0  reason: collision with root package name */
    public int f769o0;

    /* renamed from: p  reason: collision with root package name */
    public androidx.appcompat.view.menu.d f770p;

    /* renamed from: p0  reason: collision with root package name */
    public int f771p0;

    /* renamed from: q  reason: collision with root package name */
    public Context f772q;

    /* renamed from: q0  reason: collision with root package name */
    public d f773q0;

    /* renamed from: r  reason: collision with root package name */
    public int f774r = 0;

    /* renamed from: s  reason: collision with root package name */
    public boolean f775s;

    /* renamed from: t  reason: collision with root package name */
    public ActionMenuPresenter f776t;

    /* renamed from: x  reason: collision with root package name */
    public h.a f777x;

    /* renamed from: y  reason: collision with root package name */
    public d.a f778y;

    public static class LayoutParams extends LinearLayoutCompat.LayoutParams {
        @ViewDebug.ExportedProperty

        /* renamed from: c  reason: collision with root package name */
        public boolean f779c;
        @ViewDebug.ExportedProperty

        /* renamed from: d  reason: collision with root package name */
        public int f780d;
        @ViewDebug.ExportedProperty

        /* renamed from: e  reason: collision with root package name */
        public int f781e;
        @ViewDebug.ExportedProperty

        /* renamed from: f  reason: collision with root package name */
        public boolean f782f;
        @ViewDebug.ExportedProperty

        /* renamed from: g  reason: collision with root package name */
        public boolean f783g;
        public boolean h;

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public LayoutParams(LayoutParams layoutParams) {
            super(layoutParams);
            this.f779c = layoutParams.f779c;
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
            this.f779c = false;
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public interface a {
        boolean a();

        boolean b();
    }

    public static class b implements h.a {
        @Override // androidx.appcompat.view.menu.h.a
        public void a(@NonNull androidx.appcompat.view.menu.d dVar, boolean z10) {
        }

        @Override // androidx.appcompat.view.menu.h.a
        public boolean b(@NonNull androidx.appcompat.view.menu.d dVar) {
            return false;
        }
    }

    public class c implements d.a {
        public c() {
        }

        @Override // androidx.appcompat.view.menu.d.a
        public boolean a(@NonNull androidx.appcompat.view.menu.d dVar, @NonNull MenuItem menuItem) {
            d dVar2 = ActionMenuView.this.f773q0;
            if (dVar2 == null) {
                return false;
            }
            Toolbar.e eVar = Toolbar.this.f1022w0;
            if (eVar != null ? m.this.f461c.onMenuItemSelected(0, menuItem) : false) {
                return true;
            }
            return false;
        }

        @Override // androidx.appcompat.view.menu.d.a
        public void b(@NonNull androidx.appcompat.view.menu.d dVar) {
            d.a aVar = ActionMenuView.this.f778y;
            if (aVar != null) {
                aVar.b(dVar);
            }
        }
    }

    public interface d {
    }

    public ActionMenuView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        super(context, attributeSet);
        setBaselineAligned(false);
        float f10 = context.getResources().getDisplayMetrics().density;
        this.f769o0 = (int) (56.0f * f10);
        this.f771p0 = (int) (f10 * 4.0f);
        this.f772q = context;
    }

    public static int o(View view, int i10, int i11, int i12, int i13) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(View.MeasureSpec.getSize(i12) - i13, View.MeasureSpec.getMode(i12));
        ActionMenuItemView actionMenuItemView = view instanceof ActionMenuItemView ? (ActionMenuItemView) view : null;
        boolean z10 = false;
        boolean z11 = actionMenuItemView != null && actionMenuItemView.c();
        int i14 = 2;
        if (i11 <= 0 || (z11 && i11 < 2)) {
            i14 = 0;
        } else {
            view.measure(View.MeasureSpec.makeMeasureSpec(i11 * i10, EventResultPersister.GENERATE_NEW_ID), makeMeasureSpec);
            int measuredWidth = view.getMeasuredWidth();
            int i15 = measuredWidth / i10;
            if (measuredWidth % i10 != 0) {
                i15++;
            }
            if (!z11 || i15 >= 2) {
                i14 = i15;
            }
        }
        if (!layoutParams.f779c && z11) {
            z10 = true;
        }
        layoutParams.f782f = z10;
        layoutParams.f780d = i14;
        view.measure(View.MeasureSpec.makeMeasureSpec(i10 * i14, 1073741824), makeMeasureSpec);
        return i14;
    }

    @Override // androidx.appcompat.view.menu.d.b
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public boolean a(f fVar) {
        return this.f770p.r(fVar, null, 0);
    }

    @Override // androidx.appcompat.view.menu.i
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void b(androidx.appcompat.view.menu.d dVar) {
        this.f770p = dVar;
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        return false;
    }

    public Menu getMenu() {
        if (this.f770p == null) {
            Context context = getContext();
            androidx.appcompat.view.menu.d dVar = new androidx.appcompat.view.menu.d(context);
            this.f770p = dVar;
            dVar.f608e = new c();
            ActionMenuPresenter actionMenuPresenter = new ActionMenuPresenter(context);
            this.f776t = actionMenuPresenter;
            actionMenuPresenter.f745m = true;
            actionMenuPresenter.f747n = true;
            h.a aVar = this.f777x;
            if (aVar == null) {
                aVar = new b();
            }
            actionMenuPresenter.f584e = aVar;
            this.f770p.b(actionMenuPresenter, this.f772q);
            ActionMenuPresenter actionMenuPresenter2 = this.f776t;
            actionMenuPresenter2.h = this;
            this.f770p = actionMenuPresenter2.f582c;
        }
        return this.f770p;
    }

    @Nullable
    public Drawable getOverflowIcon() {
        getMenu();
        ActionMenuPresenter actionMenuPresenter = this.f776t;
        ActionMenuPresenter.d dVar = actionMenuPresenter.f742j;
        if (dVar != null) {
            return dVar.getDrawable();
        }
        if (actionMenuPresenter.f744l) {
            return actionMenuPresenter.f743k;
        }
        return null;
    }

    public int getPopupTheme() {
        return this.f774r;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int getWindowAnimations() {
        return 0;
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public LinearLayoutCompat.LayoutParams i(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    /* renamed from: l */
    public LayoutParams h() {
        LayoutParams layoutParams = new LayoutParams(-2, -2);
        layoutParams.f880b = 16;
        return layoutParams;
    }

    /* renamed from: m */
    public LayoutParams j(ViewGroup.LayoutParams layoutParams) {
        LayoutParams layoutParams2;
        if (layoutParams == null) {
            return h();
        }
        if (layoutParams instanceof LayoutParams) {
            layoutParams2 = new LayoutParams((LayoutParams) layoutParams);
        } else {
            layoutParams2 = new LayoutParams(layoutParams);
        }
        if (layoutParams2.f880b <= 0) {
            layoutParams2.f880b = 16;
        }
        return layoutParams2;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public boolean n(int i10) {
        boolean z10 = false;
        if (i10 == 0) {
            return false;
        }
        View childAt = getChildAt(i10 - 1);
        View childAt2 = getChildAt(i10);
        if (i10 < getChildCount() && (childAt instanceof a)) {
            z10 = false | ((a) childAt).a();
        }
        return (i10 <= 0 || !(childAt2 instanceof a)) ? z10 : z10 | ((a) childAt2).b();
    }

    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        ActionMenuPresenter actionMenuPresenter = this.f776t;
        if (actionMenuPresenter != null) {
            actionMenuPresenter.b(false);
            if (this.f776t.o()) {
                this.f776t.m();
                this.f776t.p();
            }
        }
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ActionMenuPresenter actionMenuPresenter = this.f776t;
        if (actionMenuPresenter != null) {
            actionMenuPresenter.j();
        }
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        int i14;
        int i15;
        if (!this.f767m0) {
            super.onLayout(z10, i10, i11, i12, i13);
            return;
        }
        int childCount = getChildCount();
        int i16 = (i13 - i11) / 2;
        int dividerWidth = getDividerWidth();
        int i17 = i12 - i10;
        int paddingRight = (i17 - getPaddingRight()) - getPaddingLeft();
        boolean b10 = q0.b(this);
        int i18 = 0;
        int i19 = 0;
        for (int i20 = 0; i20 < childCount; i20++) {
            View childAt = getChildAt(i20);
            if (childAt.getVisibility() != 8) {
                LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
                if (layoutParams.f779c) {
                    int measuredWidth = childAt.getMeasuredWidth();
                    if (n(i20)) {
                        measuredWidth += dividerWidth;
                    }
                    int measuredHeight = childAt.getMeasuredHeight();
                    if (b10) {
                        i14 = getPaddingLeft() + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin;
                        i15 = i14 + measuredWidth;
                    } else {
                        i15 = (getWidth() - getPaddingRight()) - ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
                        i14 = i15 - measuredWidth;
                    }
                    int i21 = i16 - (measuredHeight / 2);
                    childAt.layout(i14, i21, i15, measuredHeight + i21);
                    paddingRight -= measuredWidth;
                    i18 = 1;
                } else {
                    paddingRight -= (childAt.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin) + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
                    n(i20);
                    i19++;
                }
            }
        }
        if (childCount == 1 && i18 == 0) {
            View childAt2 = getChildAt(0);
            int measuredWidth2 = childAt2.getMeasuredWidth();
            int measuredHeight2 = childAt2.getMeasuredHeight();
            int i22 = (i17 / 2) - (measuredWidth2 / 2);
            int i23 = i16 - (measuredHeight2 / 2);
            childAt2.layout(i22, i23, measuredWidth2 + i22, measuredHeight2 + i23);
            return;
        }
        int i24 = i19 - (i18 ^ 1);
        int max = Math.max(0, i24 > 0 ? paddingRight / i24 : 0);
        if (b10) {
            int width = getWidth() - getPaddingRight();
            for (int i25 = 0; i25 < childCount; i25++) {
                View childAt3 = getChildAt(i25);
                LayoutParams layoutParams2 = (LayoutParams) childAt3.getLayoutParams();
                if (childAt3.getVisibility() != 8 && !layoutParams2.f779c) {
                    int i26 = width - ((ViewGroup.MarginLayoutParams) layoutParams2).rightMargin;
                    int measuredWidth3 = childAt3.getMeasuredWidth();
                    int measuredHeight3 = childAt3.getMeasuredHeight();
                    int i27 = i16 - (measuredHeight3 / 2);
                    childAt3.layout(i26 - measuredWidth3, i27, i26, measuredHeight3 + i27);
                    width = i26 - ((measuredWidth3 + ((ViewGroup.MarginLayoutParams) layoutParams2).leftMargin) + max);
                }
            }
            return;
        }
        int paddingLeft = getPaddingLeft();
        for (int i28 = 0; i28 < childCount; i28++) {
            View childAt4 = getChildAt(i28);
            LayoutParams layoutParams3 = (LayoutParams) childAt4.getLayoutParams();
            if (childAt4.getVisibility() != 8 && !layoutParams3.f779c) {
                int i29 = paddingLeft + ((ViewGroup.MarginLayoutParams) layoutParams3).leftMargin;
                int measuredWidth4 = childAt4.getMeasuredWidth();
                int measuredHeight4 = childAt4.getMeasuredHeight();
                int i30 = i16 - (measuredHeight4 / 2);
                childAt4.layout(i29, i30, i29 + measuredWidth4, measuredHeight4 + i30);
                paddingLeft = measuredWidth4 + ((ViewGroup.MarginLayoutParams) layoutParams3).rightMargin + max + i29;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r3v33, types: [boolean, int] */
    /* JADX WARN: Type inference failed for: r3v39 */
    /* JADX WARN: Type inference failed for: r3v40 */
    /* JADX WARNING: Unknown variable types count: 1 */
    @Override // androidx.appcompat.widget.LinearLayoutCompat
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r30, int r31) {
        /*
        // Method dump skipped, instructions count: 685
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ActionMenuView.onMeasure(int, int):void");
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setExpandedActionViewsExclusive(boolean z10) {
        this.f776t.f753r = z10;
    }

    public void setOnMenuItemClickListener(d dVar) {
        this.f773q0 = dVar;
    }

    public void setOverflowIcon(@Nullable Drawable drawable) {
        getMenu();
        ActionMenuPresenter actionMenuPresenter = this.f776t;
        ActionMenuPresenter.d dVar = actionMenuPresenter.f742j;
        if (dVar != null) {
            dVar.setImageDrawable(drawable);
            return;
        }
        actionMenuPresenter.f744l = true;
        actionMenuPresenter.f743k = drawable;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setOverflowReserved(boolean z10) {
        this.f775s = z10;
    }

    public void setPopupTheme(@StyleRes int i10) {
        if (this.f774r != i10) {
            this.f774r = i10;
            if (i10 == 0) {
                this.f772q = getContext();
            } else {
                this.f772q = new ContextThemeWrapper(getContext(), i10);
            }
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setPresenter(ActionMenuPresenter actionMenuPresenter) {
        this.f776t = actionMenuPresenter;
        actionMenuPresenter.h = this;
        this.f770p = actionMenuPresenter.f582c;
    }

    @Override // android.view.ViewGroup, androidx.appcompat.widget.LinearLayoutCompat
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }
}
