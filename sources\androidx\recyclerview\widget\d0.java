package androidx.recyclerview.widget;

import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.recyclerview.widget.RecyclerView;
import i0.c;
import u.d;
import u.g;

/* compiled from: ViewInfoStore */
public class d0 {
    @VisibleForTesting

    /* renamed from: a  reason: collision with root package name */
    public final g<RecyclerView.w, a> f2355a = new g<>();
    @VisibleForTesting

    /* renamed from: b  reason: collision with root package name */
    public final d<RecyclerView.w> f2356b = new d<>();

    /* compiled from: ViewInfoStore */
    public static class a {

        /* renamed from: d  reason: collision with root package name */
        public static c f2357d = new c(20);

        /* renamed from: a  reason: collision with root package name */
        public int f2358a;
        @Nullable

        /* renamed from: b  reason: collision with root package name */
        public RecyclerView.ItemAnimator.c f2359b;
        @Nullable

        /* renamed from: c  reason: collision with root package name */
        public RecyclerView.ItemAnimator.c f2360c;

        public static a a() {
            a aVar = (a) f2357d.a();
            return aVar == null ? new a() : aVar;
        }

        public static void b(a aVar) {
            aVar.f2358a = 0;
            aVar.f2359b = null;
            aVar.f2360c = null;
            f2357d.b(aVar);
        }
    }

    /* compiled from: ViewInfoStore */
    public interface b {
    }

    public void a(RecyclerView.w wVar) {
        a orDefault = this.f2355a.getOrDefault(wVar, null);
        if (orDefault == null) {
            orDefault = a.a();
            this.f2355a.put(wVar, orDefault);
        }
        orDefault.f2358a |= 1;
    }

    public void b(RecyclerView.w wVar, RecyclerView.ItemAnimator.c cVar) {
        a orDefault = this.f2355a.getOrDefault(wVar, null);
        if (orDefault == null) {
            orDefault = a.a();
            this.f2355a.put(wVar, orDefault);
        }
        orDefault.f2360c = cVar;
        orDefault.f2358a |= 8;
    }

    public void c(RecyclerView.w wVar, RecyclerView.ItemAnimator.c cVar) {
        a orDefault = this.f2355a.getOrDefault(wVar, null);
        if (orDefault == null) {
            orDefault = a.a();
            this.f2355a.put(wVar, orDefault);
        }
        orDefault.f2359b = cVar;
        orDefault.f2358a |= 4;
    }

    public boolean d(RecyclerView.w wVar) {
        a orDefault = this.f2355a.getOrDefault(wVar, null);
        if (orDefault == null || (orDefault.f2358a & 1) == 0) {
            return false;
        }
        return true;
    }

    public final RecyclerView.ItemAnimator.c e(RecyclerView.w wVar, int i10) {
        a k10;
        RecyclerView.ItemAnimator.c cVar;
        int e10 = this.f2355a.e(wVar);
        if (e10 >= 0 && (k10 = this.f2355a.k(e10)) != null) {
            int i11 = k10.f2358a;
            if ((i11 & i10) != 0) {
                int i12 = (~i10) & i11;
                k10.f2358a = i12;
                if (i10 == 4) {
                    cVar = k10.f2359b;
                } else if (i10 == 8) {
                    cVar = k10.f2360c;
                } else {
                    throw new IllegalArgumentException("Must provide flag PRE or POST");
                }
                if ((i12 & 12) == 0) {
                    this.f2355a.i(e10);
                    a.b(k10);
                }
                return cVar;
            }
        }
        return null;
    }

    public void f(RecyclerView.w wVar) {
        a orDefault = this.f2355a.getOrDefault(wVar, null);
        if (orDefault != null) {
            orDefault.f2358a &= -2;
        }
    }

    public void g(RecyclerView.w wVar) {
        int g10 = this.f2356b.g() - 1;
        while (true) {
            if (g10 < 0) {
                break;
            } else if (wVar == this.f2356b.h(g10)) {
                d<RecyclerView.w> dVar = this.f2356b;
                Object[] objArr = dVar.f10402c;
                Object obj = objArr[g10];
                Object obj2 = d.f10399e;
                if (obj != obj2) {
                    objArr[g10] = obj2;
                    dVar.f10400a = true;
                }
            } else {
                g10--;
            }
        }
        a remove = this.f2355a.remove(wVar);
        if (remove != null) {
            a.b(remove);
        }
    }
}
