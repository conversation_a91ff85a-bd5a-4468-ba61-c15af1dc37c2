package androidx.fragment.app;

import android.util.Log;
import java.io.Writer;

/* compiled from: LogWriter */
public final class q0 extends Writer {

    /* renamed from: a  reason: collision with root package name */
    public final String f1987a;

    /* renamed from: b  reason: collision with root package name */
    public StringBuilder f1988b = new StringBuilder(128);

    public q0(String str) {
        this.f1987a = str;
    }

    public final void a() {
        if (this.f1988b.length() > 0) {
            Log.d(this.f1987a, this.f1988b.toString());
            StringBuilder sb = this.f1988b;
            sb.delete(0, sb.length());
        }
    }

    @Override // java.io.Closeable, java.io.Writer, java.lang.AutoCloseable
    public void close() {
        a();
    }

    @Override // java.io.Writer, java.io.Flushable
    public void flush() {
        a();
    }

    @Override // java.io.Writer
    public void write(char[] cArr, int i10, int i11) {
        for (int i12 = 0; i12 < i11; i12++) {
            char c10 = cArr[i10 + i12];
            if (c10 == '\n') {
                a();
            } else {
                this.f1988b.append(c10);
            }
        }
    }
}
