package androidx.coordinatorlayout;

public final class R$styleable {
    public static final int[] ColorStateListItem = {16843173, 16843551, 2130968679};
    public static final int ColorStateListItem_alpha = 2;
    public static final int ColorStateListItem_android_alpha = 1;
    public static final int ColorStateListItem_android_color = 0;
    public static final int[] CoordinatorLayout = {2130969121, 2130969448};
    public static final int[] CoordinatorLayout_Layout = {16842931, 2130969131, 2130969132, 2130969133, 2130969177, 2130969186, 2130969187};
    public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
    public static final int CoordinatorLayout_Layout_layout_anchor = 1;
    public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
    public static final int CoordinatorLayout_Layout_layout_behavior = 3;
    public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
    public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
    public static final int CoordinatorLayout_Layout_layout_keyline = 6;
    public static final int CoordinatorLayout_keylines = 0;
    public static final int CoordinatorLayout_statusBarBackground = 1;
    public static final int[] FontFamily = {2130969034, 2130969035, 2130969036, 2130969037, 2130969038, 2130969039};
    public static final int[] FontFamilyFont = {16844082, 16844083, 16844095, 16844143, 16844144, 2130969032, 2130969040, 2130969041, 2130969042, 2130969588};
    public static final int FontFamilyFont_android_font = 0;
    public static final int FontFamilyFont_android_fontStyle = 2;
    public static final int FontFamilyFont_android_fontVariationSettings = 4;
    public static final int FontFamilyFont_android_fontWeight = 1;
    public static final int FontFamilyFont_android_ttcIndex = 3;
    public static final int FontFamilyFont_font = 5;
    public static final int FontFamilyFont_fontStyle = 6;
    public static final int FontFamilyFont_fontVariationSettings = 7;
    public static final int FontFamilyFont_fontWeight = 8;
    public static final int FontFamilyFont_ttcIndex = 9;
    public static final int FontFamily_fontProviderAuthority = 0;
    public static final int FontFamily_fontProviderCerts = 1;
    public static final int FontFamily_fontProviderFetchStrategy = 2;
    public static final int FontFamily_fontProviderFetchTimeout = 3;
    public static final int FontFamily_fontProviderPackage = 4;
    public static final int FontFamily_fontProviderQuery = 5;
    public static final int[] GradientColor = {16843165, 16843166, 16843169, 16843170, 16843171, 16843172, 16843265, 16843275, 16844048, 16844049, 16844050, 16844051};
    public static final int[] GradientColorItem = {16843173, 16844052};
    public static final int GradientColorItem_android_color = 0;
    public static final int GradientColorItem_android_offset = 1;
    public static final int GradientColor_android_centerColor = 7;
    public static final int GradientColor_android_centerX = 3;
    public static final int GradientColor_android_centerY = 4;
    public static final int GradientColor_android_endColor = 1;
    public static final int GradientColor_android_endX = 10;
    public static final int GradientColor_android_endY = 11;
    public static final int GradientColor_android_gradientRadius = 5;
    public static final int GradientColor_android_startColor = 0;
    public static final int GradientColor_android_startX = 8;
    public static final int GradientColor_android_startY = 9;
    public static final int GradientColor_android_tileMode = 6;
    public static final int GradientColor_android_type = 2;

    private R$styleable() {
    }
}
