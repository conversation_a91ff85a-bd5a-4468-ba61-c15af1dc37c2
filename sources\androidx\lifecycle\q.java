package androidx.lifecycle;

import androidx.annotation.Nullable;
import java.io.Closeable;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/* compiled from: ViewModel */
public abstract class q {
    @Nullable

    /* renamed from: a  reason: collision with root package name */
    public final Map<String, Object> f2090a = new HashMap();

    /* renamed from: b  reason: collision with root package name */
    public volatile boolean f2091b = false;

    public void a() {
    }

    public <T> T b(String str, T t2) {
        Object obj;
        synchronized (this.f2090a) {
            obj = this.f2090a.get(str);
            if (obj == null) {
                this.f2090a.put(str, t2);
            }
        }
        if (obj != null) {
            t2 = obj;
        }
        if (this.f2091b && (t2 instanceof Closeable)) {
            try {
                t2.close();
            } catch (IOException e10) {
                throw new RuntimeException(e10);
            }
        }
        return t2;
    }
}
