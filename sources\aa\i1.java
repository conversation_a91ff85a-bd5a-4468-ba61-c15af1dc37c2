package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERVideotexString */
public class i1 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f189a;

    public i1(byte[] bArr) {
        this.f189a = a.c(bArr);
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof i1)) {
            return false;
        }
        return a.a(this.f189a, ((i1) qVar).f189a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f189a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(21, this.f189a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f189a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f189a.length) + 1 + this.f189a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
