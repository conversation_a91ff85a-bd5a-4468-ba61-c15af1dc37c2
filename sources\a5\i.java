package a5;

import c5.r;
import java.util.Locale;

/* compiled from: NettyRuntime */
public final class i {

    /* renamed from: a  reason: collision with root package name */
    public static final a f88a = new a();

    /* compiled from: NettyRuntime */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public int f89a;

        public synchronized void a(int i10) {
            d2.a.d(i10, "availableProcessors");
            int i11 = this.f89a;
            if (i11 == 0) {
                this.f89a = i10;
            } else {
                throw new IllegalStateException(String.format(Locale.ROOT, "availableProcessors is already set to [%d], rejecting [%d]", Integer.valueOf(i11), Integer.valueOf(i10)));
            }
        }
    }

    public static int a() {
        int i10;
        a aVar = f88a;
        synchronized (aVar) {
            if (aVar.f89a == 0) {
                aVar.a(r.d("io.netty.availableProcessors", Runtime.getRuntime().availableProcessors()));
            }
            i10 = aVar.f89a;
        }
        return i10;
    }
}
