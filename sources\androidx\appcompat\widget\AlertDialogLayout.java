package androidx.appcompat.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$id;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.WeakHashMap;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class AlertDialogLayout extends LinearLayoutCompat {
    public AlertDialogLayout(@Nullable Context context, @Nullable AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public static int l(View view) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int minimumHeight = view.getMinimumHeight();
        if (minimumHeight > 0) {
            return minimumHeight;
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            if (viewGroup.getChildCount() == 1) {
                return l(viewGroup.getChildAt(0));
            }
        }
        return 0;
    }

    /* JADX WARNING: Removed duplicated region for block: B:29:0x009f  */
    @Override // androidx.appcompat.widget.LinearLayoutCompat
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onLayout(boolean r10, int r11, int r12, int r13, int r14) {
        /*
        // Method dump skipped, instructions count: 178
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.AlertDialogLayout.onLayout(boolean, int, int, int, int):void");
    }

    @Override // androidx.appcompat.widget.LinearLayoutCompat
    public void onMeasure(int i10, int i11) {
        int i12;
        int i13;
        int i14;
        int i15;
        int childCount = getChildCount();
        View view = null;
        boolean z10 = false;
        View view2 = null;
        View view3 = null;
        int i16 = 0;
        while (true) {
            if (i16 < childCount) {
                View childAt = getChildAt(i16);
                if (childAt.getVisibility() != 8) {
                    int id = childAt.getId();
                    if (id == R$id.topPanel) {
                        view = childAt;
                    } else if (id == R$id.buttonPanel) {
                        view2 = childAt;
                    } else if ((id == R$id.contentPanel || id == R$id.customPanel) && view3 == null) {
                        view3 = childAt;
                    }
                }
                i16++;
            } else {
                int mode = View.MeasureSpec.getMode(i11);
                int size = View.MeasureSpec.getSize(i11);
                int mode2 = View.MeasureSpec.getMode(i10);
                int paddingBottom = getPaddingBottom() + getPaddingTop();
                if (view != null) {
                    view.measure(i10, 0);
                    paddingBottom += view.getMeasuredHeight();
                    i12 = View.combineMeasuredStates(0, view.getMeasuredState());
                } else {
                    i12 = 0;
                }
                if (view2 != null) {
                    view2.measure(i10, 0);
                    i14 = l(view2);
                    i13 = view2.getMeasuredHeight() - i14;
                    paddingBottom += i14;
                    i12 = View.combineMeasuredStates(i12, view2.getMeasuredState());
                } else {
                    i14 = 0;
                    i13 = 0;
                }
                if (view3 != null) {
                    view3.measure(i10, mode == 0 ? 0 : View.MeasureSpec.makeMeasureSpec(Math.max(0, size - paddingBottom), mode));
                    i15 = view3.getMeasuredHeight();
                    paddingBottom += i15;
                    i12 = View.combineMeasuredStates(i12, view3.getMeasuredState());
                } else {
                    i15 = 0;
                }
                int i17 = size - paddingBottom;
                if (view2 != null) {
                    int i18 = paddingBottom - i14;
                    int min = Math.min(i17, i13);
                    if (min > 0) {
                        i17 -= min;
                        i14 += min;
                    }
                    view2.measure(i10, View.MeasureSpec.makeMeasureSpec(i14, 1073741824));
                    paddingBottom = i18 + view2.getMeasuredHeight();
                    i12 = View.combineMeasuredStates(i12, view2.getMeasuredState());
                }
                if (view3 != null && i17 > 0) {
                    view3.measure(i10, View.MeasureSpec.makeMeasureSpec(i15 + i17, mode));
                    paddingBottom = (paddingBottom - i15) + view3.getMeasuredHeight();
                    i12 = View.combineMeasuredStates(i12, view3.getMeasuredState());
                }
                int i19 = 0;
                for (int i20 = 0; i20 < childCount; i20++) {
                    View childAt2 = getChildAt(i20);
                    if (childAt2.getVisibility() != 8) {
                        i19 = Math.max(i19, childAt2.getMeasuredWidth());
                    }
                }
                setMeasuredDimension(View.resolveSizeAndState(getPaddingRight() + getPaddingLeft() + i19, i10, i12), View.resolveSizeAndState(paddingBottom, i11, 0));
                if (mode2 != 1073741824) {
                    int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 1073741824);
                    for (int i21 = 0; i21 < childCount; i21++) {
                        View childAt3 = getChildAt(i21);
                        if (childAt3.getVisibility() != 8) {
                            LinearLayoutCompat.LayoutParams layoutParams = (LinearLayoutCompat.LayoutParams) childAt3.getLayoutParams();
                            if (((ViewGroup.MarginLayoutParams) layoutParams).width == -1) {
                                int i22 = ((ViewGroup.MarginLayoutParams) layoutParams).height;
                                ((ViewGroup.MarginLayoutParams) layoutParams).height = childAt3.getMeasuredHeight();
                                measureChildWithMargins(childAt3, makeMeasureSpec, 0, i11, 0);
                                ((ViewGroup.MarginLayoutParams) layoutParams).height = i22;
                            }
                        }
                    }
                }
                z10 = true;
            }
        }
        if (!z10) {
            super.onMeasure(i10, i11);
        }
    }
}
