package androidx.appcompat.widget;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.Region;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.TransformationMethod;
import android.util.AttributeSet;
import android.util.Property;
import android.view.ActionMode;
import android.view.VelocityTracker;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.CompoundButton;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.R$attr;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.xiaomi.mitv.socialtv.common.utils.HanziToPinyin;
import j0.m;
import java.util.WeakHashMap;

public class SwitchCompat extends CompoundButton {
    public static final Property<SwitchCompat, Float> C0 = new a(Float.class, "thumbPos");
    public static final int[] D0 = {16842912};
    public ObjectAnimator A0;
    public final Rect B0;

    /* renamed from: a  reason: collision with root package name */
    public Drawable f958a;

    /* renamed from: b  reason: collision with root package name */
    public ColorStateList f959b;

    /* renamed from: c  reason: collision with root package name */
    public PorterDuff.Mode f960c;

    /* renamed from: d  reason: collision with root package name */
    public boolean f961d;

    /* renamed from: e  reason: collision with root package name */
    public boolean f962e;

    /* renamed from: f  reason: collision with root package name */
    public Drawable f963f;

    /* renamed from: g  reason: collision with root package name */
    public ColorStateList f964g;
    public PorterDuff.Mode h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f965i;

    /* renamed from: j  reason: collision with root package name */
    public boolean f966j;

    /* renamed from: k  reason: collision with root package name */
    public int f967k;

    /* renamed from: l  reason: collision with root package name */
    public int f968l;

    /* renamed from: m  reason: collision with root package name */
    public int f969m;

    /* renamed from: m0  reason: collision with root package name */
    public int f970m0;

    /* renamed from: n  reason: collision with root package name */
    public boolean f971n;

    /* renamed from: n0  reason: collision with root package name */
    public float f972n0;

    /* renamed from: o  reason: collision with root package name */
    public CharSequence f973o;

    /* renamed from: o0  reason: collision with root package name */
    public int f974o0;

    /* renamed from: p  reason: collision with root package name */
    public CharSequence f975p;

    /* renamed from: p0  reason: collision with root package name */
    public int f976p0;

    /* renamed from: q  reason: collision with root package name */
    public boolean f977q;

    /* renamed from: q0  reason: collision with root package name */
    public int f978q0;

    /* renamed from: r  reason: collision with root package name */
    public int f979r;

    /* renamed from: r0  reason: collision with root package name */
    public int f980r0;

    /* renamed from: s  reason: collision with root package name */
    public int f981s;

    /* renamed from: s0  reason: collision with root package name */
    public int f982s0;

    /* renamed from: t  reason: collision with root package name */
    public float f983t;

    /* renamed from: t0  reason: collision with root package name */
    public int f984t0;

    /* renamed from: u0  reason: collision with root package name */
    public int f985u0;

    /* renamed from: v0  reason: collision with root package name */
    public final TextPaint f986v0;

    /* renamed from: w0  reason: collision with root package name */
    public ColorStateList f987w0;

    /* renamed from: x  reason: collision with root package name */
    public float f988x;

    /* renamed from: x0  reason: collision with root package name */
    public Layout f989x0;

    /* renamed from: y  reason: collision with root package name */
    public VelocityTracker f990y;

    /* renamed from: y0  reason: collision with root package name */
    public Layout f991y0;

    /* renamed from: z0  reason: collision with root package name */
    public TransformationMethod f992z0;

    public class a extends Property<SwitchCompat, Float> {
        public a(Class cls, String str) {
            super(cls, str);
        }

        /* Return type fixed from 'java.lang.Object' to match base method */
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // android.util.Property
        public Float get(SwitchCompat switchCompat) {
            return Float.valueOf(switchCompat.f972n0);
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object, java.lang.Object] */
        @Override // android.util.Property
        public void set(SwitchCompat switchCompat, Float f10) {
            switchCompat.setThumbPosition(f10.floatValue());
        }
    }

    public SwitchCompat(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.switchStyle);
    }

    private boolean getTargetCheckedState() {
        return this.f972n0 > 0.5f;
    }

    private int getThumbOffset() {
        float f10;
        if (q0.b(this)) {
            f10 = 1.0f - this.f972n0;
        } else {
            f10 = this.f972n0;
        }
        return (int) ((f10 * ((float) getThumbScrollRange())) + 0.5f);
    }

    private int getThumbScrollRange() {
        Rect rect;
        Drawable drawable = this.f963f;
        if (drawable == null) {
            return 0;
        }
        Rect rect2 = this.B0;
        drawable.getPadding(rect2);
        Drawable drawable2 = this.f958a;
        if (drawable2 != null) {
            rect = u.b(drawable2);
        } else {
            rect = u.f1202c;
        }
        return ((((this.f974o0 - this.f978q0) - rect2.left) - rect2.right) - rect.left) - rect.right;
    }

    public final void a() {
        Drawable drawable = this.f958a;
        if (drawable == null) {
            return;
        }
        if (this.f961d || this.f962e) {
            Drawable mutate = drawable.mutate();
            this.f958a = mutate;
            if (this.f961d) {
                mutate.setTintList(this.f959b);
            }
            if (this.f962e) {
                this.f958a.setTintMode(this.f960c);
            }
            if (this.f958a.isStateful()) {
                this.f958a.setState(getDrawableState());
            }
        }
    }

    public final void b() {
        Drawable drawable = this.f963f;
        if (drawable == null) {
            return;
        }
        if (this.f965i || this.f966j) {
            Drawable mutate = drawable.mutate();
            this.f963f = mutate;
            if (this.f965i) {
                mutate.setTintList(this.f964g);
            }
            if (this.f966j) {
                this.f963f.setTintMode(this.h);
            }
            if (this.f963f.isStateful()) {
                this.f963f.setState(getDrawableState());
            }
        }
    }

    public final Layout c(CharSequence charSequence) {
        TransformationMethod transformationMethod = this.f992z0;
        if (transformationMethod != null) {
            charSequence = transformationMethod.getTransformation(charSequence, this);
        }
        TextPaint textPaint = this.f986v0;
        return new StaticLayout(charSequence, textPaint, charSequence != null ? (int) Math.ceil((double) Layout.getDesiredWidth(charSequence, textPaint)) : 0, Layout.Alignment.ALIGN_NORMAL, 1.0f, Constant.VOLUME_FLOAT_MIN, true);
    }

    public void draw(Canvas canvas) {
        Rect rect;
        int i10;
        int i11;
        Rect rect2 = this.B0;
        int i12 = this.f980r0;
        int i13 = this.f982s0;
        int i14 = this.f984t0;
        int i15 = this.f985u0;
        int thumbOffset = getThumbOffset() + i12;
        Drawable drawable = this.f958a;
        if (drawable != null) {
            rect = u.b(drawable);
        } else {
            rect = u.f1202c;
        }
        Drawable drawable2 = this.f963f;
        if (drawable2 != null) {
            drawable2.getPadding(rect2);
            int i16 = rect2.left;
            thumbOffset += i16;
            if (rect != null) {
                int i17 = rect.left;
                if (i17 > i16) {
                    i12 += i17 - i16;
                }
                int i18 = rect.top;
                int i19 = rect2.top;
                i10 = i18 > i19 ? (i18 - i19) + i13 : i13;
                int i20 = rect.right;
                int i21 = rect2.right;
                if (i20 > i21) {
                    i14 -= i20 - i21;
                }
                int i22 = rect.bottom;
                int i23 = rect2.bottom;
                if (i22 > i23) {
                    i11 = i15 - (i22 - i23);
                    this.f963f.setBounds(i12, i10, i14, i11);
                }
            } else {
                i10 = i13;
            }
            i11 = i15;
            this.f963f.setBounds(i12, i10, i14, i11);
        }
        Drawable drawable3 = this.f958a;
        if (drawable3 != null) {
            drawable3.getPadding(rect2);
            int i24 = thumbOffset - rect2.left;
            int i25 = thumbOffset + this.f978q0 + rect2.right;
            this.f958a.setBounds(i24, i13, i25, i15);
            Drawable background = getBackground();
            if (background != null) {
                background.setHotspotBounds(i24, i13, i25, i15);
            }
        }
        super.draw(canvas);
    }

    public void drawableHotspotChanged(float f10, float f11) {
        super.drawableHotspotChanged(f10, f11);
        Drawable drawable = this.f958a;
        if (drawable != null) {
            drawable.setHotspot(f10, f11);
        }
        Drawable drawable2 = this.f963f;
        if (drawable2 != null) {
            drawable2.setHotspot(f10, f11);
        }
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        int[] drawableState = getDrawableState();
        Drawable drawable = this.f958a;
        boolean z10 = false;
        if (drawable != null && drawable.isStateful()) {
            z10 = false | drawable.setState(drawableState);
        }
        Drawable drawable2 = this.f963f;
        if (drawable2 != null && drawable2.isStateful()) {
            z10 |= drawable2.setState(drawableState);
        }
        if (z10) {
            invalidate();
        }
    }

    public int getCompoundPaddingLeft() {
        if (!q0.b(this)) {
            return super.getCompoundPaddingLeft();
        }
        int compoundPaddingLeft = super.getCompoundPaddingLeft() + this.f974o0;
        return !TextUtils.isEmpty(getText()) ? compoundPaddingLeft + this.f969m : compoundPaddingLeft;
    }

    public int getCompoundPaddingRight() {
        if (q0.b(this)) {
            return super.getCompoundPaddingRight();
        }
        int compoundPaddingRight = super.getCompoundPaddingRight() + this.f974o0;
        return !TextUtils.isEmpty(getText()) ? compoundPaddingRight + this.f969m : compoundPaddingRight;
    }

    public boolean getShowText() {
        return this.f977q;
    }

    public boolean getSplitTrack() {
        return this.f971n;
    }

    public int getSwitchMinWidth() {
        return this.f968l;
    }

    public int getSwitchPadding() {
        return this.f969m;
    }

    public CharSequence getTextOff() {
        return this.f975p;
    }

    public CharSequence getTextOn() {
        return this.f973o;
    }

    public Drawable getThumbDrawable() {
        return this.f958a;
    }

    public int getThumbTextPadding() {
        return this.f967k;
    }

    @Nullable
    public ColorStateList getThumbTintList() {
        return this.f959b;
    }

    @Nullable
    public PorterDuff.Mode getThumbTintMode() {
        return this.f960c;
    }

    public Drawable getTrackDrawable() {
        return this.f963f;
    }

    @Nullable
    public ColorStateList getTrackTintList() {
        return this.f964g;
    }

    @Nullable
    public PorterDuff.Mode getTrackTintMode() {
        return this.h;
    }

    public void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable = this.f958a;
        if (drawable != null) {
            drawable.jumpToCurrentState();
        }
        Drawable drawable2 = this.f963f;
        if (drawable2 != null) {
            drawable2.jumpToCurrentState();
        }
        ObjectAnimator objectAnimator = this.A0;
        if (objectAnimator != null && objectAnimator.isStarted()) {
            this.A0.end();
            this.A0 = null;
        }
    }

    public int[] onCreateDrawableState(int i10) {
        int[] onCreateDrawableState = super.onCreateDrawableState(i10 + 1);
        if (isChecked()) {
            CompoundButton.mergeDrawableStates(onCreateDrawableState, D0);
        }
        return onCreateDrawableState;
    }

    public void onDraw(Canvas canvas) {
        int i10;
        super.onDraw(canvas);
        Rect rect = this.B0;
        Drawable drawable = this.f963f;
        if (drawable != null) {
            drawable.getPadding(rect);
        } else {
            rect.setEmpty();
        }
        int i11 = this.f982s0;
        int i12 = this.f985u0;
        int i13 = i11 + rect.top;
        int i14 = i12 - rect.bottom;
        Drawable drawable2 = this.f958a;
        if (drawable != null) {
            if (!this.f971n || drawable2 == null) {
                drawable.draw(canvas);
            } else {
                Rect b10 = u.b(drawable2);
                drawable2.copyBounds(rect);
                rect.left += b10.left;
                rect.right -= b10.right;
                int save = canvas.save();
                canvas.clipRect(rect, Region.Op.DIFFERENCE);
                drawable.draw(canvas);
                canvas.restoreToCount(save);
            }
        }
        int save2 = canvas.save();
        if (drawable2 != null) {
            drawable2.draw(canvas);
        }
        Layout layout = getTargetCheckedState() ? this.f989x0 : this.f991y0;
        if (layout != null) {
            int[] drawableState = getDrawableState();
            ColorStateList colorStateList = this.f987w0;
            if (colorStateList != null) {
                this.f986v0.setColor(colorStateList.getColorForState(drawableState, 0));
            }
            this.f986v0.drawableState = drawableState;
            if (drawable2 != null) {
                Rect bounds = drawable2.getBounds();
                i10 = bounds.left + bounds.right;
            } else {
                i10 = getWidth();
            }
            canvas.translate((float) ((i10 / 2) - (layout.getWidth() / 2)), (float) (((i13 + i14) / 2) - (layout.getHeight() / 2)));
            layout.draw(canvas);
        }
        canvas.restoreToCount(save2);
    }

    public void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onInitializeAccessibilityEvent(accessibilityEvent);
        accessibilityEvent.setClassName("android.widget.Switch");
    }

    public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        accessibilityNodeInfo.setClassName("android.widget.Switch");
        CharSequence charSequence = isChecked() ? this.f973o : this.f975p;
        if (!TextUtils.isEmpty(charSequence)) {
            CharSequence text = accessibilityNodeInfo.getText();
            if (TextUtils.isEmpty(text)) {
                accessibilityNodeInfo.setText(charSequence);
                return;
            }
            StringBuilder sb = new StringBuilder();
            sb.append(text);
            sb.append(HanziToPinyin.Token.SEPARATOR);
            sb.append(charSequence);
            accessibilityNodeInfo.setText(sb);
        }
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        int i14;
        int i15;
        int i16;
        int i17;
        int i18;
        super.onLayout(z10, i10, i11, i12, i13);
        int i19 = 0;
        if (this.f958a != null) {
            Rect rect = this.B0;
            Drawable drawable = this.f963f;
            if (drawable != null) {
                drawable.getPadding(rect);
            } else {
                rect.setEmpty();
            }
            Rect b10 = u.b(this.f958a);
            i14 = Math.max(0, b10.left - rect.left);
            i19 = Math.max(0, b10.right - rect.right);
        } else {
            i14 = 0;
        }
        if (q0.b(this)) {
            i16 = getPaddingLeft() + i14;
            i15 = ((this.f974o0 + i16) - i14) - i19;
        } else {
            i15 = (getWidth() - getPaddingRight()) - i19;
            i16 = (i15 - this.f974o0) + i14 + i19;
        }
        int gravity = getGravity() & 112;
        if (gravity == 16) {
            int paddingTop = getPaddingTop();
            int i20 = this.f976p0;
            int height = (((getHeight() + paddingTop) - getPaddingBottom()) / 2) - (i20 / 2);
            i17 = i20 + height;
            i18 = height;
        } else if (gravity != 80) {
            i18 = getPaddingTop();
            i17 = this.f976p0 + i18;
        } else {
            i17 = getHeight() - getPaddingBottom();
            i18 = i17 - this.f976p0;
        }
        this.f980r0 = i16;
        this.f982s0 = i18;
        this.f985u0 = i17;
        this.f984t0 = i15;
    }

    public void onMeasure(int i10, int i11) {
        int i12;
        int i13;
        int i14;
        if (this.f977q) {
            if (this.f989x0 == null) {
                this.f989x0 = c(this.f973o);
            }
            if (this.f991y0 == null) {
                this.f991y0 = c(this.f975p);
            }
        }
        Rect rect = this.B0;
        Drawable drawable = this.f958a;
        int i15 = 0;
        if (drawable != null) {
            drawable.getPadding(rect);
            i13 = (this.f958a.getIntrinsicWidth() - rect.left) - rect.right;
            i12 = this.f958a.getIntrinsicHeight();
        } else {
            i13 = 0;
            i12 = 0;
        }
        if (this.f977q) {
            i14 = (this.f967k * 2) + Math.max(this.f989x0.getWidth(), this.f991y0.getWidth());
        } else {
            i14 = 0;
        }
        this.f978q0 = Math.max(i14, i13);
        Drawable drawable2 = this.f963f;
        if (drawable2 != null) {
            drawable2.getPadding(rect);
            i15 = this.f963f.getIntrinsicHeight();
        } else {
            rect.setEmpty();
        }
        int i16 = rect.left;
        int i17 = rect.right;
        Drawable drawable3 = this.f958a;
        if (drawable3 != null) {
            Rect b10 = u.b(drawable3);
            i16 = Math.max(i16, b10.left);
            i17 = Math.max(i17, b10.right);
        }
        int max = Math.max(this.f968l, (this.f978q0 * 2) + i16 + i17);
        int max2 = Math.max(i15, i12);
        this.f974o0 = max;
        this.f976p0 = max2;
        super.onMeasure(i10, i11);
        if (getMeasuredHeight() < max2) {
            setMeasuredDimension(getMeasuredWidthAndState(), max2);
        }
    }

    public void onPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onPopulateAccessibilityEvent(accessibilityEvent);
        CharSequence charSequence = isChecked() ? this.f973o : this.f975p;
        if (charSequence != null) {
            accessibilityEvent.getText().add(charSequence);
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:5:0x0014, code lost:
        if (r0 != 3) goto L_0x0150;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouchEvent(android.view.MotionEvent r11) {
        /*
        // Method dump skipped, instructions count: 341
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SwitchCompat.onTouchEvent(android.view.MotionEvent):boolean");
    }

    public void setChecked(boolean z10) {
        super.setChecked(z10);
        boolean isChecked = isChecked();
        float f10 = 1.0f;
        if (getWindowToken() != null) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (isLaidOut()) {
                if (!isChecked) {
                    f10 = 0.0f;
                }
                ObjectAnimator ofFloat = ObjectAnimator.ofFloat(this, C0, f10);
                this.A0 = ofFloat;
                ofFloat.setDuration(250L);
                this.A0.setAutoCancel(true);
                this.A0.start();
                return;
            }
        }
        ObjectAnimator objectAnimator = this.A0;
        if (objectAnimator != null) {
            objectAnimator.cancel();
        }
        if (!isChecked) {
            f10 = 0.0f;
        }
        setThumbPosition(f10);
    }

    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(callback);
    }

    public void setShowText(boolean z10) {
        if (this.f977q != z10) {
            this.f977q = z10;
            requestLayout();
        }
    }

    public void setSplitTrack(boolean z10) {
        this.f971n = z10;
        invalidate();
    }

    public void setSwitchMinWidth(int i10) {
        this.f968l = i10;
        requestLayout();
    }

    public void setSwitchPadding(int i10) {
        this.f969m = i10;
        requestLayout();
    }

    public void setSwitchTypeface(Typeface typeface) {
        if ((this.f986v0.getTypeface() != null && !this.f986v0.getTypeface().equals(typeface)) || (this.f986v0.getTypeface() == null && typeface != null)) {
            this.f986v0.setTypeface(typeface);
            requestLayout();
            invalidate();
        }
    }

    public void setTextOff(CharSequence charSequence) {
        this.f975p = charSequence;
        requestLayout();
    }

    public void setTextOn(CharSequence charSequence) {
        this.f973o = charSequence;
        requestLayout();
    }

    public void setThumbDrawable(Drawable drawable) {
        Drawable drawable2 = this.f958a;
        if (drawable2 != null) {
            drawable2.setCallback(null);
        }
        this.f958a = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
        }
        requestLayout();
    }

    public void setThumbPosition(float f10) {
        this.f972n0 = f10;
        invalidate();
    }

    public void setThumbResource(int i10) {
        setThumbDrawable(m.a.a(getContext(), i10));
    }

    public void setThumbTextPadding(int i10) {
        this.f967k = i10;
        requestLayout();
    }

    public void setThumbTintList(@Nullable ColorStateList colorStateList) {
        this.f959b = colorStateList;
        this.f961d = true;
        a();
    }

    public void setThumbTintMode(@Nullable PorterDuff.Mode mode) {
        this.f960c = mode;
        this.f962e = true;
        a();
    }

    public void setTrackDrawable(Drawable drawable) {
        Drawable drawable2 = this.f963f;
        if (drawable2 != null) {
            drawable2.setCallback(null);
        }
        this.f963f = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
        }
        requestLayout();
    }

    public void setTrackResource(int i10) {
        setTrackDrawable(m.a.a(getContext(), i10));
    }

    public void setTrackTintList(@Nullable ColorStateList colorStateList) {
        this.f964g = colorStateList;
        this.f965i = true;
        b();
    }

    public void setTrackTintMode(@Nullable PorterDuff.Mode mode) {
        this.h = mode;
        this.f966j = true;
        b();
    }

    public void toggle() {
        setChecked(!isChecked());
    }

    public boolean verifyDrawable(Drawable drawable) {
        return super.verifyDrawable(drawable) || drawable == this.f958a || drawable == this.f963f;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:35:0x0129, code lost:
        if (r3 != null) goto L_0x0130;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public SwitchCompat(@androidx.annotation.NonNull android.content.Context r19, @androidx.annotation.Nullable android.util.AttributeSet r20, int r21) {
        /*
        // Method dump skipped, instructions count: 485
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.SwitchCompat.<init>(android.content.Context, android.util.AttributeSet, int):void");
    }
}
