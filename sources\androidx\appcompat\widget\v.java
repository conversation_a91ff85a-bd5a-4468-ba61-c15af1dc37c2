package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ListAdapter;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.appcompat.R$attr;
import java.lang.reflect.Field;

/* compiled from: DropDownListView */
public class v extends ListView {

    /* renamed from: a  reason: collision with root package name */
    public final Rect f1204a = new Rect();

    /* renamed from: b  reason: collision with root package name */
    public int f1205b = 0;

    /* renamed from: c  reason: collision with root package name */
    public int f1206c = 0;

    /* renamed from: d  reason: collision with root package name */
    public int f1207d = 0;

    /* renamed from: e  reason: collision with root package name */
    public int f1208e = 0;

    /* renamed from: f  reason: collision with root package name */
    public int f1209f;

    /* renamed from: g  reason: collision with root package name */
    public Field f1210g;
    public a h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f1211i;

    /* renamed from: j  reason: collision with root package name */
    public boolean f1212j;

    /* renamed from: k  reason: collision with root package name */
    public boolean f1213k;

    /* renamed from: l  reason: collision with root package name */
    public androidx.core.widget.b f1214l;

    /* renamed from: m  reason: collision with root package name */
    public b f1215m;

    /* compiled from: DropDownListView */
    public static class a extends n.a {

        /* renamed from: b  reason: collision with root package name */
        public boolean f1216b = true;

        public a(Drawable drawable) {
            super(drawable);
        }

        @Override // n.a
        public void draw(Canvas canvas) {
            if (this.f1216b) {
                this.f8963a.draw(canvas);
            }
        }

        @Override // n.a
        public void setHotspot(float f10, float f11) {
            if (this.f1216b) {
                this.f8963a.setHotspot(f10, f11);
            }
        }

        @Override // n.a
        public void setHotspotBounds(int i10, int i11, int i12, int i13) {
            if (this.f1216b) {
                this.f8963a.setHotspotBounds(i10, i11, i12, i13);
            }
        }

        @Override // n.a
        public boolean setState(int[] iArr) {
            if (this.f1216b) {
                return this.f8963a.setState(iArr);
            }
            return false;
        }

        @Override // n.a
        public boolean setVisible(boolean z10, boolean z11) {
            if (this.f1216b) {
                return super.setVisible(z10, z11);
            }
            return false;
        }
    }

    /* compiled from: DropDownListView */
    public class b implements Runnable {
        public b() {
        }

        public void run() {
            v vVar = v.this;
            vVar.f1215m = null;
            vVar.drawableStateChanged();
        }
    }

    public v(@NonNull Context context, boolean z10) {
        super(context, null, R$attr.dropDownListViewStyle);
        this.f1212j = z10;
        setCacheColorHint(0);
        try {
            Field declaredField = AbsListView.class.getDeclaredField("mIsChildViewEnabled");
            this.f1210g = declaredField;
            declaredField.setAccessible(true);
        } catch (NoSuchFieldException e10) {
            e10.printStackTrace();
        }
    }

    private void setSelectorEnabled(boolean z10) {
        a aVar = this.h;
        if (aVar != null) {
            aVar.f1216b = z10;
        }
    }

    public int a(int i10, int i11, int i12) {
        int i13;
        int listPaddingTop = getListPaddingTop();
        int listPaddingBottom = getListPaddingBottom();
        int dividerHeight = getDividerHeight();
        Drawable divider = getDivider();
        ListAdapter adapter = getAdapter();
        if (adapter == null) {
            return listPaddingTop + listPaddingBottom;
        }
        int i14 = listPaddingTop + listPaddingBottom;
        if (dividerHeight <= 0 || divider == null) {
            dividerHeight = 0;
        }
        int count = adapter.getCount();
        int i15 = 0;
        int i16 = 0;
        int i17 = 0;
        View view = null;
        while (i15 < count) {
            int itemViewType = adapter.getItemViewType(i15);
            if (itemViewType != i16) {
                view = null;
                i16 = itemViewType;
            }
            view = adapter.getView(i15, view, this);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (layoutParams == null) {
                layoutParams = generateDefaultLayoutParams();
                view.setLayoutParams(layoutParams);
            }
            int i18 = layoutParams.height;
            if (i18 > 0) {
                i13 = View.MeasureSpec.makeMeasureSpec(i18, 1073741824);
            } else {
                i13 = View.MeasureSpec.makeMeasureSpec(0, 0);
            }
            view.measure(i10, i13);
            view.forceLayout();
            if (i15 > 0) {
                i14 += dividerHeight;
            }
            i14 += view.getMeasuredHeight();
            if (i14 >= i11) {
                return (i12 < 0 || i15 <= i12 || i17 <= 0 || i14 == i11) ? i11 : i17;
            }
            if (i12 >= 0 && i15 >= i12) {
                i17 = i14;
            }
            i15++;
        }
        return i14;
    }

    /* JADX WARNING: Removed duplicated region for block: B:66:0x013f  */
    /* JADX WARNING: Removed duplicated region for block: B:68:0x0144  */
    /* JADX WARNING: Removed duplicated region for block: B:72:0x015a  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean b(android.view.MotionEvent r17, int r18) {
        /*
        // Method dump skipped, instructions count: 360
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.v.b(android.view.MotionEvent, int):boolean");
    }

    public final void c() {
        Drawable selector = getSelector();
        if (selector != null && this.f1213k && isPressed()) {
            selector.setState(getDrawableState());
        }
    }

    public void dispatchDraw(Canvas canvas) {
        Drawable selector;
        if (!this.f1204a.isEmpty() && (selector = getSelector()) != null) {
            selector.setBounds(this.f1204a);
            selector.draw(canvas);
        }
        super.dispatchDraw(canvas);
    }

    public void drawableStateChanged() {
        if (this.f1215m == null) {
            super.drawableStateChanged();
            setSelectorEnabled(true);
            c();
        }
    }

    public boolean hasFocus() {
        return this.f1212j || super.hasFocus();
    }

    public boolean hasWindowFocus() {
        return this.f1212j || super.hasWindowFocus();
    }

    public boolean isFocused() {
        return this.f1212j || super.isFocused();
    }

    public boolean isInTouchMode() {
        return (this.f1212j && this.f1211i) || super.isInTouchMode();
    }

    public void onDetachedFromWindow() {
        this.f1215m = null;
        super.onDetachedFromWindow();
    }

    public boolean onHoverEvent(@NonNull MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 10 && this.f1215m == null) {
            b bVar = new b();
            this.f1215m = bVar;
            post(bVar);
        }
        boolean onHoverEvent = super.onHoverEvent(motionEvent);
        if (actionMasked == 9 || actionMasked == 7) {
            int pointToPosition = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY());
            if (!(pointToPosition == -1 || pointToPosition == getSelectedItemPosition())) {
                View childAt = getChildAt(pointToPosition - getFirstVisiblePosition());
                if (childAt.isEnabled()) {
                    setSelectionFromTop(pointToPosition, childAt.getTop() - getTop());
                }
                c();
            }
        } else {
            setSelection(-1);
        }
        return onHoverEvent;
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        if (motionEvent.getAction() == 0) {
            this.f1209f = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY());
        }
        b bVar = this.f1215m;
        if (bVar != null) {
            v vVar = v.this;
            vVar.f1215m = null;
            vVar.removeCallbacks(bVar);
        }
        return super.onTouchEvent(motionEvent);
    }

    public void setListSelectionHidden(boolean z10) {
        this.f1211i = z10;
    }

    @Override // android.widget.AbsListView
    public void setSelector(Drawable drawable) {
        a aVar = drawable != null ? new a(drawable) : null;
        this.h = aVar;
        super.setSelector(aVar);
        Rect rect = new Rect();
        if (drawable != null) {
            drawable.getPadding(rect);
        }
        this.f1205b = rect.left;
        this.f1206c = rect.top;
        this.f1207d = rect.right;
        this.f1208e = rect.bottom;
    }
}
