package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.c;
import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;
import com.duokan.airkan.common.Constant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import w.d;

/* compiled from: ConstraintWidgetContainer */
public class e extends d {

    /* renamed from: j0  reason: collision with root package name */
    public boolean f1345j0 = false;

    /* renamed from: k0  reason: collision with root package name */
    public c f1346k0 = new c();

    /* renamed from: l0  reason: collision with root package name */
    public j f1347l0;

    /* renamed from: m0  reason: collision with root package name */
    public int f1348m0 = 0;

    /* renamed from: n0  reason: collision with root package name */
    public int f1349n0 = 0;

    /* renamed from: o0  reason: collision with root package name */
    public d[] f1350o0 = new d[4];

    /* renamed from: p0  reason: collision with root package name */
    public d[] f1351p0 = new d[4];

    /* renamed from: q0  reason: collision with root package name */
    public List<f> f1352q0 = new ArrayList();

    /* renamed from: r0  reason: collision with root package name */
    public boolean f1353r0 = false;

    /* renamed from: s0  reason: collision with root package name */
    public boolean f1354s0 = false;

    /* renamed from: t0  reason: collision with root package name */
    public boolean f1355t0 = false;

    /* renamed from: u0  reason: collision with root package name */
    public int f1356u0 = 0;

    /* renamed from: v0  reason: collision with root package name */
    public int f1357v0 = 0;

    /* renamed from: w0  reason: collision with root package name */
    public int f1358w0 = 7;

    /* renamed from: x0  reason: collision with root package name */
    public boolean f1359x0 = false;

    /* renamed from: y0  reason: collision with root package name */
    public boolean f1360y0 = false;

    /* renamed from: z0  reason: collision with root package name */
    public boolean f1361z0 = false;

    /* JADX WARN: Type inference failed for: r8v12, types: [boolean] */
    /* JADX WARN: Type inference failed for: r8v16 */
    /* JADX WARN: Type inference failed for: r8v17 */
    /* JADX WARNING: Removed duplicated region for block: B:110:0x0258  */
    /* JADX WARNING: Removed duplicated region for block: B:126:0x02bc  */
    /* JADX WARNING: Removed duplicated region for block: B:141:0x0304 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:160:0x037c  */
    /* JADX WARNING: Removed duplicated region for block: B:163:0x0399  */
    /* JADX WARNING: Removed duplicated region for block: B:164:0x03a6  */
    /* JADX WARNING: Removed duplicated region for block: B:166:0x03ab  */
    /* JADX WARNING: Unknown variable types count: 1 */
    @Override // w.d
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void F() {
        /*
        // Method dump skipped, instructions count: 1236
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.e.F():void");
    }

    public void G(ConstraintWidget constraintWidget, int i10) {
        if (i10 == 0) {
            int i11 = this.f1348m0 + 1;
            d[] dVarArr = this.f1351p0;
            if (i11 >= dVarArr.length) {
                this.f1351p0 = (d[]) Arrays.copyOf(dVarArr, dVarArr.length * 2);
            }
            d[] dVarArr2 = this.f1351p0;
            int i12 = this.f1348m0;
            dVarArr2[i12] = new d(constraintWidget, 0, this.f1345j0);
            this.f1348m0 = i12 + 1;
        } else if (i10 == 1) {
            int i13 = this.f1349n0 + 1;
            d[] dVarArr3 = this.f1350o0;
            if (i13 >= dVarArr3.length) {
                this.f1350o0 = (d[]) Arrays.copyOf(dVarArr3, dVarArr3.length * 2);
            }
            d[] dVarArr4 = this.f1350o0;
            int i14 = this.f1349n0;
            dVarArr4[i14] = new d(constraintWidget, 1, this.f1345j0);
            this.f1349n0 = i14 + 1;
        }
    }

    public boolean H(c cVar) {
        a(cVar);
        int size = this.f10636i0.size();
        for (int i10 = 0; i10 < size; i10++) {
            ConstraintWidget constraintWidget = this.f10636i0.get(i10);
            if (constraintWidget instanceof e) {
                ConstraintWidget.DimensionBehaviour[] dimensionBehaviourArr = constraintWidget.C;
                ConstraintWidget.DimensionBehaviour dimensionBehaviour = dimensionBehaviourArr[0];
                ConstraintWidget.DimensionBehaviour dimensionBehaviour2 = dimensionBehaviourArr[1];
                ConstraintWidget.DimensionBehaviour dimensionBehaviour3 = ConstraintWidget.DimensionBehaviour.WRAP_CONTENT;
                if (dimensionBehaviour == dimensionBehaviour3) {
                    constraintWidget.y(ConstraintWidget.DimensionBehaviour.FIXED);
                }
                if (dimensionBehaviour2 == dimensionBehaviour3) {
                    constraintWidget.B(ConstraintWidget.DimensionBehaviour.FIXED);
                }
                constraintWidget.a(cVar);
                if (dimensionBehaviour == dimensionBehaviour3) {
                    constraintWidget.y(dimensionBehaviour);
                }
                if (dimensionBehaviour2 == dimensionBehaviour3) {
                    constraintWidget.B(dimensionBehaviour2);
                }
            } else {
                ConstraintWidget.DimensionBehaviour dimensionBehaviour4 = this.C[0];
                ConstraintWidget.DimensionBehaviour dimensionBehaviour5 = ConstraintWidget.DimensionBehaviour.WRAP_CONTENT;
                if (dimensionBehaviour4 != dimensionBehaviour5 && constraintWidget.C[0] == ConstraintWidget.DimensionBehaviour.MATCH_PARENT) {
                    int i11 = constraintWidget.f1314s.f1280e;
                    int n10 = n() - constraintWidget.f1316u.f1280e;
                    ConstraintAnchor constraintAnchor = constraintWidget.f1314s;
                    constraintAnchor.f1283i = cVar.l(constraintAnchor);
                    ConstraintAnchor constraintAnchor2 = constraintWidget.f1316u;
                    constraintAnchor2.f1283i = cVar.l(constraintAnchor2);
                    cVar.e(constraintWidget.f1314s.f1283i, i11);
                    cVar.e(constraintWidget.f1316u.f1283i, n10);
                    constraintWidget.f1289a = 2;
                    constraintWidget.x(i11, n10);
                }
                if (this.C[1] != dimensionBehaviour5 && constraintWidget.C[1] == ConstraintWidget.DimensionBehaviour.MATCH_PARENT) {
                    int i12 = constraintWidget.f1315t.f1280e;
                    int h = h() - constraintWidget.f1317v.f1280e;
                    ConstraintAnchor constraintAnchor3 = constraintWidget.f1315t;
                    constraintAnchor3.f1283i = cVar.l(constraintAnchor3);
                    ConstraintAnchor constraintAnchor4 = constraintWidget.f1317v;
                    constraintAnchor4.f1283i = cVar.l(constraintAnchor4);
                    cVar.e(constraintWidget.f1315t.f1283i, i12);
                    cVar.e(constraintWidget.f1317v.f1283i, h);
                    if (constraintWidget.Q > 0 || constraintWidget.Y == 8) {
                        ConstraintAnchor constraintAnchor5 = constraintWidget.f1318w;
                        constraintAnchor5.f1283i = cVar.l(constraintAnchor5);
                        cVar.e(constraintWidget.f1318w.f1283i, constraintWidget.Q + i12);
                    }
                    constraintWidget.f1291b = 2;
                    constraintWidget.A(i12, h);
                }
                constraintWidget.a(cVar);
            }
        }
        if (this.f1348m0 > 0) {
            c.a(this, cVar, 0);
        }
        if (this.f1349n0 > 0) {
            c.a(this, cVar, 1);
        }
        return true;
    }

    public boolean I(int i10) {
        return (this.f1358w0 & i10) == i10;
    }

    public void J() {
        int size = this.f10636i0.size();
        s();
        for (int i10 = 0; i10 < size; i10++) {
            this.f10636i0.get(i10).s();
        }
    }

    public final void K() {
        this.f1348m0 = 0;
        this.f1349n0 = 0;
    }

    public void L() {
        i iVar = f(ConstraintAnchor.Type.LEFT).f1276a;
        i iVar2 = f(ConstraintAnchor.Type.TOP).f1276a;
        iVar.j(null, Constant.VOLUME_FLOAT_MIN);
        iVar2.j(null, Constant.VOLUME_FLOAT_MIN);
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void c(int i10) {
        super.c(i10);
        int size = this.f10636i0.size();
        for (int i11 = 0; i11 < size; i11++) {
            this.f10636i0.get(i11).c(i10);
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget, w.d
    public void r() {
        this.f1346k0.t();
        this.f1352q0.clear();
        this.f1359x0 = false;
        super.r();
    }
}
