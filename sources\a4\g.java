package a4;

import a4.f;
import android.graphics.Point;
import android.os.Handler;
import com.duokan.airkan.common.Log;
import com.xiaomi.milink.discover.core.udt.UDTDiscoverService;
import d7.d;
import miuix.appcompat.widget.Spinner;

/* compiled from: R8$$SyntheticClass */
public final /* synthetic */ class g implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ int f37a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Object f38b;

    public /* synthetic */ g(Object obj, int i10) {
        this.f37a = i10;
        this.f38b = obj;
    }

    public final void run() {
        switch (this.f37a) {
            case 0:
                f.b bVar = (f.b) this.f38b;
                f fVar = f.this;
                Handler handler = fVar.f31l;
                if (handler != null) {
                    handler.post(new d(fVar));
                } else {
                    Log.e("UDTDiscoverManager", "Handler not ready, reset JmDNS service failed!");
                }
                UDTDiscoverService.f4653l = false;
                f.this.f();
                return;
            default:
                Spinner spinner = (Spinner) this.f38b;
                Spinner.i iVar = spinner.f8500d;
                if (iVar != null && iVar.isShowing() && (spinner.f8500d instanceof Spinner.f)) {
                    Point b10 = d.b(spinner.getPopupContext());
                    spinner.b(((float) b10.x) * spinner.h, ((float) b10.y) * spinner.f8504i);
                    return;
                }
                return;
        }
    }
}
