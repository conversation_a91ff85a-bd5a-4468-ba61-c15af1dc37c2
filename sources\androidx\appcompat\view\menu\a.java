package androidx.appcompat.view.menu;

import android.content.Context;
import android.view.LayoutInflater;
import androidx.annotation.RestrictTo;
import androidx.appcompat.view.menu.h;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: BaseMenuPresenter */
public abstract class a implements h {

    /* renamed from: a  reason: collision with root package name */
    public Context f580a;

    /* renamed from: b  reason: collision with root package name */
    public Context f581b;

    /* renamed from: c  reason: collision with root package name */
    public d f582c;

    /* renamed from: d  reason: collision with root package name */
    public LayoutInflater f583d;

    /* renamed from: e  reason: collision with root package name */
    public h.a f584e;

    /* renamed from: f  reason: collision with root package name */
    public int f585f;

    /* renamed from: g  reason: collision with root package name */
    public int f586g;
    public i h;

    /* renamed from: i  reason: collision with root package name */
    public int f587i;

    public a(Context context, int i10, int i11) {
        this.f580a = context;
        this.f583d = LayoutInflater.from(context);
        this.f585f = i10;
        this.f586g = i11;
    }

    @Override // androidx.appcompat.view.menu.h
    public int getId() {
        return this.f587i;
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean h(d dVar, f fVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean i(d dVar, f fVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.h
    public void k(h.a aVar) {
        this.f584e = aVar;
    }
}
