package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.RadioButton;
import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import m.a;

public class AppCompatRadioButton extends RadioButton {

    /* renamed from: a  reason: collision with root package name */
    public final f f824a;

    /* renamed from: b  reason: collision with root package name */
    public final e f825b;

    /* renamed from: c  reason: collision with root package name */
    public final q f826c;

    public AppCompatRadioButton(Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.radioButtonStyle);
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        e eVar = this.f825b;
        if (eVar != null) {
            eVar.a();
        }
        q qVar = this.f826c;
        if (qVar != null) {
            qVar.b();
        }
    }

    public int getCompoundPaddingLeft() {
        return super.getCompoundPaddingLeft();
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public ColorStateList getSupportBackgroundTintList() {
        e eVar = this.f825b;
        if (eVar != null) {
            return eVar.b();
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        e eVar = this.f825b;
        if (eVar != null) {
            return eVar.c();
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public ColorStateList getSupportButtonTintList() {
        f fVar = this.f824a;
        if (fVar != null) {
            return fVar.f1086b;
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public PorterDuff.Mode getSupportButtonTintMode() {
        f fVar = this.f824a;
        if (fVar != null) {
            return fVar.f1087c;
        }
        return null;
    }

    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        e eVar = this.f825b;
        if (eVar != null) {
            eVar.e();
        }
    }

    public void setBackgroundResource(@DrawableRes int i10) {
        super.setBackgroundResource(i10);
        e eVar = this.f825b;
        if (eVar != null) {
            eVar.f(i10);
        }
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(Drawable drawable) {
        super.setButtonDrawable(drawable);
        f fVar = this.f824a;
        if (fVar == null) {
            return;
        }
        if (fVar.f1090f) {
            fVar.f1090f = false;
            return;
        }
        fVar.f1090f = true;
        fVar.a();
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintList(@Nullable ColorStateList colorStateList) {
        e eVar = this.f825b;
        if (eVar != null) {
            eVar.h(colorStateList);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintMode(@Nullable PorterDuff.Mode mode) {
        e eVar = this.f825b;
        if (eVar != null) {
            eVar.i(mode);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportButtonTintList(@Nullable ColorStateList colorStateList) {
        f fVar = this.f824a;
        if (fVar != null) {
            fVar.f1086b = colorStateList;
            fVar.f1088d = true;
            fVar.a();
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportButtonTintMode(@Nullable PorterDuff.Mode mode) {
        f fVar = this.f824a;
        if (fVar != null) {
            fVar.f1087c = mode;
            fVar.f1089e = true;
            fVar.a();
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public AppCompatRadioButton(Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        j0.a(context);
        h0.a(this, getContext());
        f fVar = new f(this);
        this.f824a = fVar;
        fVar.b(attributeSet, i10);
        e eVar = new e(this);
        this.f825b = eVar;
        eVar.d(attributeSet, i10);
        q qVar = new q(this);
        this.f826c = qVar;
        qVar.d(attributeSet, i10);
    }

    @Override // android.widget.CompoundButton
    public void setButtonDrawable(@DrawableRes int i10) {
        setButtonDrawable(a.a(getContext(), i10));
    }
}
