package androidx.core.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.WindowInsets;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RestrictTo;
import androidx.core.R$id;
import androidx.core.view.a;
import j0.j;
import j0.l;
import j0.m;
import j0.q;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Objects;
import java.util.WeakHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import k0.b;

public class ViewCompat {

    /* renamed from: a  reason: collision with root package name */
    public static WeakHashMap<View, m> f1593a = null;

    /* renamed from: b  reason: collision with root package name */
    public static Field f1594b;

    /* renamed from: c  reason: collision with root package name */
    public static boolean f1595c = false;

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface FocusDirection {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface FocusRealDirection {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface FocusRelativeDirection {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface NestedScrollType {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface ScrollAxis {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface ScrollIndicators {
    }

    public class a implements View.OnApplyWindowInsetsListener {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ j f1596a;

        public a(j jVar) {
            this.f1596a = jVar;
        }

        public WindowInsets onApplyWindowInsets(View view, WindowInsets windowInsets) {
            Objects.requireNonNull(windowInsets);
            return this.f1596a.a(view, new q(windowInsets)).j();
        }
    }

    @RequiresApi(21)
    public static class b {
        public static q a(@NonNull View view, @NonNull q qVar, @NonNull Rect rect) {
            WindowInsets j10 = qVar.j();
            if (j10 != null) {
                return q.k(view.computeSystemWindowInsets(j10, rect));
            }
            rect.setEmpty();
            return qVar;
        }
    }

    @RequiresApi(23)
    public static class c {
        public static WindowInsets a(View view) {
            return view.getRootWindowInsets();
        }
    }

    @RequiresApi(29)
    public static class d {
        public static void a(@NonNull View view, @NonNull Context context, @NonNull int[] iArr, @Nullable AttributeSet attributeSet, @NonNull TypedArray typedArray, int i10, int i11) {
            view.saveAttributeDataForStyleable(context, iArr, attributeSet, typedArray, i10, i11);
        }
    }

    static {
        new AtomicInteger(1);
        new WeakHashMap();
    }

    @NonNull
    public static m a(@NonNull View view) {
        if (f1593a == null) {
            f1593a = new WeakHashMap<>();
        }
        m mVar = f1593a.get(view);
        if (mVar != null) {
            return mVar;
        }
        m mVar2 = new m(view);
        f1593a.put(view, mVar2);
        return mVar2;
    }

    @NonNull
    public static q b(@NonNull View view, @NonNull q qVar) {
        WindowInsets j10 = qVar.j();
        return (j10 == null || view.dispatchApplyWindowInsets(j10).equals(j10)) ? qVar : new q(j10);
    }

    @Nullable
    public static a c(@NonNull View view) {
        View.AccessibilityDelegate d10 = d(view);
        if (d10 == null) {
            return null;
        }
        if (d10 instanceof a.C0011a) {
            return ((a.C0011a) d10).f1600a;
        }
        return new a(d10);
    }

    @Nullable
    public static View.AccessibilityDelegate d(@NonNull View view) {
        if (Build.VERSION.SDK_INT >= 29) {
            return view.getAccessibilityDelegate();
        }
        if (f1595c) {
            return null;
        }
        if (f1594b == null) {
            try {
                Field declaredField = View.class.getDeclaredField("mAccessibilityDelegate");
                f1594b = declaredField;
                declaredField.setAccessible(true);
            } catch (Throwable unused) {
                f1595c = true;
                return null;
            }
        }
        try {
            Object obj = f1594b.get(view);
            if (obj instanceof View.AccessibilityDelegate) {
                return (View.AccessibilityDelegate) obj;
            }
            return null;
        } catch (Throwable unused2) {
            f1595c = true;
            return null;
        }
    }

    @RequiresApi(19)
    public static void e(View view, int i10) {
        if (((AccessibilityManager) view.getContext().getSystemService("accessibility")).isEnabled()) {
            boolean z10 = view.getAccessibilityPaneTitle() != null;
            if (view.getAccessibilityLiveRegion() != 0 || (z10 && view.getVisibility() == 0)) {
                AccessibilityEvent obtain = AccessibilityEvent.obtain();
                obtain.setEventType(z10 ? 32 : 2048);
                obtain.setContentChangeTypes(i10);
                view.sendAccessibilityEventUnchecked(obtain);
            } else if (view.getParent() != null) {
                try {
                    view.getParent().notifySubtreeAccessibilityStateChanged(view, view, i10);
                } catch (AbstractMethodError e10) {
                    Log.e("ViewCompat", view.getParent().getClass().getSimpleName() + " does not fully implement ViewParent", e10);
                }
            }
        }
    }

    @NonNull
    public static q f(@NonNull View view, @NonNull q qVar) {
        WindowInsets j10 = qVar.j();
        if (j10 == null) {
            return qVar;
        }
        WindowInsets onApplyWindowInsets = view.onApplyWindowInsets(j10);
        return !onApplyWindowInsets.equals(j10) ? new q(onApplyWindowInsets) : qVar;
    }

    public static void g(@NonNull View view, int i10) {
        h(i10, view);
        e(view, 0);
    }

    public static void h(int i10, View view) {
        int i11 = R$id.tag_accessibility_actions;
        ArrayList arrayList = (ArrayList) view.getTag(i11);
        if (arrayList == null) {
            arrayList = new ArrayList();
            view.setTag(i11, arrayList);
        }
        for (int i12 = 0; i12 < arrayList.size(); i12++) {
            if (((b.a) arrayList.get(i12)).a() == i10) {
                arrayList.remove(i12);
                return;
            }
        }
    }

    public static void i(@NonNull View view, @NonNull b.a aVar, @Nullable CharSequence charSequence, @Nullable k0.d dVar) {
        if (dVar == null) {
            h(aVar.a(), view);
            e(view, 0);
            return;
        }
        b.a aVar2 = new b.a(null, aVar.f7281b, null, dVar, aVar.f7282c);
        a c10 = c(view);
        if (c10 == null) {
            c10 = new a();
        }
        k(view, c10);
        h(aVar2.a(), view);
        int i10 = R$id.tag_accessibility_actions;
        ArrayList arrayList = (ArrayList) view.getTag(i10);
        if (arrayList == null) {
            arrayList = new ArrayList();
            view.setTag(i10, arrayList);
        }
        arrayList.add(aVar2);
        e(view, 0);
    }

    public static void j(@NonNull View view, @NonNull @SuppressLint({"ContextFirst"}) Context context, @NonNull int[] iArr, @Nullable AttributeSet attributeSet, @NonNull TypedArray typedArray, int i10, int i11) {
        if (Build.VERSION.SDK_INT >= 29) {
            d.a(view, context, iArr, attributeSet, typedArray, i10, i11);
        }
    }

    public static void k(@NonNull View view, a aVar) {
        View.AccessibilityDelegate accessibilityDelegate;
        if (aVar == null && (d(view) instanceof a.C0011a)) {
            aVar = new a();
        }
        if (aVar == null) {
            accessibilityDelegate = null;
        } else {
            accessibilityDelegate = aVar.f1599b;
        }
        view.setAccessibilityDelegate(accessibilityDelegate);
    }

    public static void l(@NonNull View view, @Nullable j jVar) {
        if (jVar == null) {
            view.setOnApplyWindowInsetsListener(null);
        } else {
            view.setOnApplyWindowInsetsListener(new a(jVar));
        }
    }

    public static void m(@NonNull View view, l lVar) {
        view.setPointerIcon(null);
    }
}
