package androidx.appcompat.view.menu;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuBuilder */
public class d implements d0.a {

    /* renamed from: y  reason: collision with root package name */
    public static final int[] f603y = {1, 4, 5, 3, 2, 0};

    /* renamed from: a  reason: collision with root package name */
    public final Context f604a;

    /* renamed from: b  reason: collision with root package name */
    public final Resources f605b;

    /* renamed from: c  reason: collision with root package name */
    public boolean f606c;

    /* renamed from: d  reason: collision with root package name */
    public boolean f607d;

    /* renamed from: e  reason: collision with root package name */
    public a f608e;

    /* renamed from: f  reason: collision with root package name */
    public ArrayList<f> f609f;

    /* renamed from: g  reason: collision with root package name */
    public ArrayList<f> f610g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public ArrayList<f> f611i;

    /* renamed from: j  reason: collision with root package name */
    public ArrayList<f> f612j;

    /* renamed from: k  reason: collision with root package name */
    public boolean f613k;

    /* renamed from: l  reason: collision with root package name */
    public int f614l = 0;

    /* renamed from: m  reason: collision with root package name */
    public CharSequence f615m;

    /* renamed from: n  reason: collision with root package name */
    public Drawable f616n;

    /* renamed from: o  reason: collision with root package name */
    public View f617o;

    /* renamed from: p  reason: collision with root package name */
    public boolean f618p = false;

    /* renamed from: q  reason: collision with root package name */
    public boolean f619q = false;

    /* renamed from: r  reason: collision with root package name */
    public boolean f620r = false;

    /* renamed from: s  reason: collision with root package name */
    public boolean f621s = false;

    /* renamed from: t  reason: collision with root package name */
    public ArrayList<f> f622t = new ArrayList<>();

    /* renamed from: u  reason: collision with root package name */
    public CopyOnWriteArrayList<WeakReference<h>> f623u = new CopyOnWriteArrayList<>();

    /* renamed from: v  reason: collision with root package name */
    public f f624v;

    /* renamed from: w  reason: collision with root package name */
    public boolean f625w = false;

    /* renamed from: x  reason: collision with root package name */
    public boolean f626x;

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    /* compiled from: MenuBuilder */
    public interface a {
        boolean a(@NonNull d dVar, @NonNull MenuItem menuItem);

        void b(@NonNull d dVar);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    /* compiled from: MenuBuilder */
    public interface b {
        boolean a(f fVar);
    }

    public d(Context context) {
        boolean z10 = false;
        this.f604a = context;
        Resources resources = context.getResources();
        this.f605b = resources;
        this.f609f = new ArrayList<>();
        this.f610g = new ArrayList<>();
        this.h = true;
        this.f611i = new ArrayList<>();
        this.f612j = new ArrayList<>();
        this.f613k = true;
        if (resources.getConfiguration().keyboard != 1 && ViewConfiguration.get(context).shouldShowMenuShortcutsWhenKeyboardPresent()) {
            z10 = true;
        }
        this.f607d = z10;
    }

    public void A() {
        if (!this.f618p) {
            this.f618p = true;
            this.f619q = false;
            this.f620r = false;
        }
    }

    public MenuItem a(int i10, int i11, int i12, CharSequence charSequence) {
        int i13;
        int i14 = (-65536 & i12) >> 16;
        if (i14 >= 0) {
            int[] iArr = f603y;
            if (i14 < iArr.length) {
                int i15 = (iArr[i14] << 16) | (65535 & i12);
                f fVar = new f(this, i10, i11, i12, i15, charSequence, this.f614l);
                ArrayList<f> arrayList = this.f609f;
                int size = arrayList.size();
                while (true) {
                    size--;
                    if (size >= 0) {
                        if (arrayList.get(size).f633d <= i15) {
                            i13 = size + 1;
                            break;
                        }
                    } else {
                        i13 = 0;
                        break;
                    }
                }
                arrayList.add(i13, fVar);
                p(true);
                return fVar;
            }
        }
        throw new IllegalArgumentException("order does not contain a valid category.");
    }

    @Override // android.view.Menu
    public MenuItem add(CharSequence charSequence) {
        return a(0, 0, 0, charSequence);
    }

    public int addIntentOptions(int i10, int i11, int i12, ComponentName componentName, Intent[] intentArr, Intent intent, int i13, MenuItem[] menuItemArr) {
        int i14;
        PackageManager packageManager = this.f604a.getPackageManager();
        List<ResolveInfo> queryIntentActivityOptions = packageManager.queryIntentActivityOptions(componentName, intentArr, intent, 0);
        int size = queryIntentActivityOptions != null ? queryIntentActivityOptions.size() : 0;
        if ((i13 & 1) == 0) {
            removeGroup(i10);
        }
        for (int i15 = 0; i15 < size; i15++) {
            ResolveInfo resolveInfo = queryIntentActivityOptions.get(i15);
            int i16 = resolveInfo.specificIndex;
            Intent intent2 = new Intent(i16 < 0 ? intent : intentArr[i16]);
            ActivityInfo activityInfo = resolveInfo.activityInfo;
            intent2.setComponent(new ComponentName(activityInfo.applicationInfo.packageName, activityInfo.name));
            MenuItem intent3 = a(i10, i11, i12, resolveInfo.loadLabel(packageManager)).setIcon(resolveInfo.loadIcon(packageManager)).setIntent(intent2);
            if (menuItemArr != null && (i14 = resolveInfo.specificIndex) >= 0) {
                menuItemArr[i14] = intent3;
            }
        }
        return size;
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(CharSequence charSequence) {
        return addSubMenu(0, 0, 0, charSequence);
    }

    public void b(h hVar, Context context) {
        this.f623u.add(new WeakReference<>(hVar));
        hVar.d(context, this);
        this.f613k = true;
    }

    public final void c(boolean z10) {
        if (!this.f621s) {
            this.f621s = true;
            Iterator<WeakReference<h>> it = this.f623u.iterator();
            while (it.hasNext()) {
                WeakReference<h> next = it.next();
                h hVar = next.get();
                if (hVar == null) {
                    this.f623u.remove(next);
                } else {
                    hVar.a(this, z10);
                }
            }
            this.f621s = false;
        }
    }

    public void clear() {
        f fVar = this.f624v;
        if (fVar != null) {
            d(fVar);
        }
        this.f609f.clear();
        p(true);
    }

    public void clearHeader() {
        this.f616n = null;
        this.f615m = null;
        this.f617o = null;
        p(false);
    }

    public void close() {
        c(true);
    }

    public boolean d(f fVar) {
        boolean z10 = false;
        if (!this.f623u.isEmpty() && this.f624v == fVar) {
            A();
            Iterator<WeakReference<h>> it = this.f623u.iterator();
            while (it.hasNext()) {
                WeakReference<h> next = it.next();
                h hVar = next.get();
                if (hVar == null) {
                    this.f623u.remove(next);
                } else {
                    z10 = hVar.h(this, fVar);
                    if (z10) {
                        break;
                    }
                }
            }
            z();
            if (z10) {
                this.f624v = null;
            }
        }
        return z10;
    }

    public boolean e(@NonNull d dVar, @NonNull MenuItem menuItem) {
        a aVar = this.f608e;
        return aVar != null && aVar.a(dVar, menuItem);
    }

    public boolean f(f fVar) {
        boolean z10 = false;
        if (this.f623u.isEmpty()) {
            return false;
        }
        A();
        Iterator<WeakReference<h>> it = this.f623u.iterator();
        while (it.hasNext()) {
            WeakReference<h> next = it.next();
            h hVar = next.get();
            if (hVar == null) {
                this.f623u.remove(next);
            } else {
                z10 = hVar.i(this, fVar);
                if (z10) {
                    break;
                }
            }
        }
        z();
        if (z10) {
            this.f624v = fVar;
        }
        return z10;
    }

    public MenuItem findItem(int i10) {
        MenuItem findItem;
        int size = size();
        for (int i11 = 0; i11 < size; i11++) {
            f fVar = this.f609f.get(i11);
            if (fVar.f630a == i10) {
                return fVar;
            }
            if (fVar.hasSubMenu() && (findItem = fVar.f643o.findItem(i10)) != null) {
                return findItem;
            }
        }
        return null;
    }

    public f g(int i10, KeyEvent keyEvent) {
        char c10;
        ArrayList<f> arrayList = this.f622t;
        arrayList.clear();
        h(arrayList, i10, keyEvent);
        if (arrayList.isEmpty()) {
            return null;
        }
        int metaState = keyEvent.getMetaState();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        keyEvent.getKeyData(keyData);
        int size = arrayList.size();
        if (size == 1) {
            return arrayList.get(0);
        }
        boolean n10 = n();
        for (int i11 = 0; i11 < size; i11++) {
            f fVar = arrayList.get(i11);
            if (n10) {
                c10 = fVar.f638j;
            } else {
                c10 = fVar.h;
            }
            char[] cArr = keyData.meta;
            if ((c10 == cArr[0] && (metaState & 2) == 0) || ((c10 == cArr[2] && (metaState & 2) != 0) || (n10 && c10 == '\b' && i10 == 67))) {
                return fVar;
            }
        }
        return null;
    }

    public MenuItem getItem(int i10) {
        return this.f609f.get(i10);
    }

    /* access modifiers changed from: package-private */
    public void h(List<f> list, int i10, KeyEvent keyEvent) {
        char c10;
        int i11;
        boolean n10 = n();
        int modifiers = keyEvent.getModifiers();
        KeyCharacterMap.KeyData keyData = new KeyCharacterMap.KeyData();
        if (keyEvent.getKeyData(keyData) || i10 == 67) {
            int size = this.f609f.size();
            for (int i12 = 0; i12 < size; i12++) {
                f fVar = this.f609f.get(i12);
                if (fVar.hasSubMenu()) {
                    fVar.f643o.h(list, i10, keyEvent);
                }
                if (n10) {
                    c10 = fVar.f638j;
                } else {
                    c10 = fVar.h;
                }
                if (n10) {
                    i11 = fVar.f639k;
                } else {
                    i11 = fVar.f637i;
                }
                if (((modifiers & 69647) == (i11 & 69647)) && c10 != 0) {
                    char[] cArr = keyData.meta;
                    if ((c10 == cArr[0] || c10 == cArr[2] || (n10 && c10 == '\b' && i10 == 67)) && fVar.isEnabled()) {
                        list.add(fVar);
                    }
                }
            }
        }
    }

    public boolean hasVisibleItems() {
        if (this.f626x) {
            return true;
        }
        int size = size();
        for (int i10 = 0; i10 < size; i10++) {
            if (this.f609f.get(i10).isVisible()) {
                return true;
            }
        }
        return false;
    }

    public void i() {
        ArrayList<f> l10 = l();
        if (this.f613k) {
            Iterator<WeakReference<h>> it = this.f623u.iterator();
            boolean z10 = false;
            while (it.hasNext()) {
                WeakReference<h> next = it.next();
                h hVar = next.get();
                if (hVar == null) {
                    this.f623u.remove(next);
                } else {
                    z10 |= hVar.c();
                }
            }
            if (z10) {
                this.f611i.clear();
                this.f612j.clear();
                int size = l10.size();
                for (int i10 = 0; i10 < size; i10++) {
                    f fVar = l10.get(i10);
                    if (fVar.g()) {
                        this.f611i.add(fVar);
                    } else {
                        this.f612j.add(fVar);
                    }
                }
            } else {
                this.f611i.clear();
                this.f612j.clear();
                this.f612j.addAll(l());
            }
            this.f613k = false;
        }
    }

    public boolean isShortcutKey(int i10, KeyEvent keyEvent) {
        return g(i10, keyEvent) != null;
    }

    public String j() {
        return "android:menu:actionviewstates";
    }

    public d k() {
        return this;
    }

    @NonNull
    public ArrayList<f> l() {
        if (!this.h) {
            return this.f610g;
        }
        this.f610g.clear();
        int size = this.f609f.size();
        for (int i10 = 0; i10 < size; i10++) {
            f fVar = this.f609f.get(i10);
            if (fVar.isVisible()) {
                this.f610g.add(fVar);
            }
        }
        this.h = false;
        this.f613k = true;
        return this.f610g;
    }

    public boolean m() {
        return this.f625w;
    }

    /* access modifiers changed from: package-private */
    public boolean n() {
        return this.f606c;
    }

    public boolean o() {
        return this.f607d;
    }

    public void p(boolean z10) {
        if (!this.f618p) {
            if (z10) {
                this.h = true;
                this.f613k = true;
            }
            if (!this.f623u.isEmpty()) {
                A();
                Iterator<WeakReference<h>> it = this.f623u.iterator();
                while (it.hasNext()) {
                    WeakReference<h> next = it.next();
                    h hVar = next.get();
                    if (hVar == null) {
                        this.f623u.remove(next);
                    } else {
                        hVar.b(z10);
                    }
                }
                z();
                return;
            }
            return;
        }
        this.f619q = true;
        if (z10) {
            this.f620r = true;
        }
    }

    public boolean performIdentifierAction(int i10, int i11) {
        return q(findItem(i10), i11);
    }

    public boolean performShortcut(int i10, KeyEvent keyEvent, int i11) {
        f g10 = g(i10, keyEvent);
        boolean r10 = g10 != null ? r(g10, null, i11) : false;
        if ((i11 & 2) != 0) {
            c(true);
        }
        return r10;
    }

    public boolean q(MenuItem menuItem, int i10) {
        return r(menuItem, null, i10);
    }

    /* JADX WARNING: Removed duplicated region for block: B:30:0x0056  */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x0062  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean r(android.view.MenuItem r7, androidx.appcompat.view.menu.h r8, int r9) {
        /*
        // Method dump skipped, instructions count: 211
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.d.r(android.view.MenuItem, androidx.appcompat.view.menu.h, int):boolean");
    }

    public void removeGroup(int i10) {
        int size = size();
        int i11 = 0;
        while (true) {
            if (i11 >= size) {
                i11 = -1;
                break;
            } else if (this.f609f.get(i11).f631b == i10) {
                break;
            } else {
                i11++;
            }
        }
        if (i11 >= 0) {
            int size2 = this.f609f.size() - i11;
            int i12 = 0;
            while (true) {
                int i13 = i12 + 1;
                if (i12 >= size2 || this.f609f.get(i11).f631b != i10) {
                    p(true);
                } else {
                    s(i11, false);
                    i12 = i13;
                }
            }
            p(true);
        }
    }

    public void removeItem(int i10) {
        int size = size();
        int i11 = 0;
        while (true) {
            if (i11 >= size) {
                i11 = -1;
                break;
            } else if (this.f609f.get(i11).f630a == i10) {
                break;
            } else {
                i11++;
            }
        }
        s(i11, true);
    }

    public final void s(int i10, boolean z10) {
        if (i10 >= 0 && i10 < this.f609f.size()) {
            this.f609f.remove(i10);
            if (z10) {
                p(true);
            }
        }
    }

    public void setGroupCheckable(int i10, boolean z10, boolean z11) {
        int size = this.f609f.size();
        for (int i11 = 0; i11 < size; i11++) {
            f fVar = this.f609f.get(i11);
            if (fVar.f631b == i10) {
                fVar.k(z11);
                fVar.setCheckable(z10);
            }
        }
    }

    public void setGroupDividerEnabled(boolean z10) {
        this.f625w = z10;
    }

    public void setGroupEnabled(int i10, boolean z10) {
        int size = this.f609f.size();
        for (int i11 = 0; i11 < size; i11++) {
            f fVar = this.f609f.get(i11);
            if (fVar.f631b == i10) {
                fVar.setEnabled(z10);
            }
        }
    }

    public void setGroupVisible(int i10, boolean z10) {
        int size = this.f609f.size();
        boolean z11 = false;
        for (int i11 = 0; i11 < size; i11++) {
            f fVar = this.f609f.get(i11);
            if (fVar.f631b == i10 && fVar.m(z10)) {
                z11 = true;
            }
        }
        if (z11) {
            p(true);
        }
    }

    public void setQwertyMode(boolean z10) {
        this.f606c = z10;
        p(false);
    }

    public int size() {
        return this.f609f.size();
    }

    public void t(h hVar) {
        Iterator<WeakReference<h>> it = this.f623u.iterator();
        while (it.hasNext()) {
            WeakReference<h> next = it.next();
            h hVar2 = next.get();
            if (hVar2 == null || hVar2 == hVar) {
                this.f623u.remove(next);
            }
        }
    }

    public void u(Bundle bundle) {
        MenuItem findItem;
        if (bundle != null) {
            SparseArray<Parcelable> sparseParcelableArray = bundle.getSparseParcelableArray(j());
            int size = size();
            for (int i10 = 0; i10 < size; i10++) {
                MenuItem item = getItem(i10);
                View actionView = item.getActionView();
                if (!(actionView == null || actionView.getId() == -1)) {
                    actionView.restoreHierarchyState(sparseParcelableArray);
                }
                if (item.hasSubMenu()) {
                    ((k) item.getSubMenu()).u(bundle);
                }
            }
            int i11 = bundle.getInt("android:menu:expandedactionview");
            if (i11 > 0 && (findItem = findItem(i11)) != null) {
                findItem.expandActionView();
            }
        }
    }

    public void v(Bundle bundle) {
        Parcelable parcelable;
        SparseArray sparseParcelableArray = bundle.getSparseParcelableArray("android:menu:presenters");
        if (sparseParcelableArray != null && !this.f623u.isEmpty()) {
            Iterator<WeakReference<h>> it = this.f623u.iterator();
            while (it.hasNext()) {
                WeakReference<h> next = it.next();
                h hVar = next.get();
                if (hVar == null) {
                    this.f623u.remove(next);
                } else {
                    int id = hVar.getId();
                    if (id > 0 && (parcelable = (Parcelable) sparseParcelableArray.get(id)) != null) {
                        hVar.e(parcelable);
                    }
                }
            }
        }
    }

    public void w(Bundle bundle) {
        int size = size();
        SparseArray<? extends Parcelable> sparseArray = null;
        for (int i10 = 0; i10 < size; i10++) {
            MenuItem item = getItem(i10);
            View actionView = item.getActionView();
            if (!(actionView == null || actionView.getId() == -1)) {
                if (sparseArray == null) {
                    sparseArray = new SparseArray<>();
                }
                actionView.saveHierarchyState(sparseArray);
                if (item.isActionViewExpanded()) {
                    bundle.putInt("android:menu:expandedactionview", item.getItemId());
                }
            }
            if (item.hasSubMenu()) {
                ((k) item.getSubMenu()).w(bundle);
            }
        }
        if (sparseArray != null) {
            bundle.putSparseParcelableArray(j(), sparseArray);
        }
    }

    public void x(Bundle bundle) {
        Parcelable g10;
        if (!this.f623u.isEmpty()) {
            SparseArray<? extends Parcelable> sparseArray = new SparseArray<>();
            Iterator<WeakReference<h>> it = this.f623u.iterator();
            while (it.hasNext()) {
                WeakReference<h> next = it.next();
                h hVar = next.get();
                if (hVar == null) {
                    this.f623u.remove(next);
                } else {
                    int id = hVar.getId();
                    if (id > 0 && (g10 = hVar.g()) != null) {
                        sparseArray.put(id, g10);
                    }
                }
            }
            bundle.putSparseParcelableArray("android:menu:presenters", sparseArray);
        }
    }

    public final void y(int i10, CharSequence charSequence, int i11, Drawable drawable, View view) {
        Resources resources = this.f605b;
        if (view != null) {
            this.f617o = view;
            this.f615m = null;
            this.f616n = null;
        } else {
            if (i10 > 0) {
                this.f615m = resources.getText(i10);
            } else if (charSequence != null) {
                this.f615m = charSequence;
            }
            if (i11 > 0) {
                Context context = this.f604a;
                Object obj = z.a.f11008a;
                this.f616n = context.getDrawable(i11);
            } else if (drawable != null) {
                this.f616n = drawable;
            }
            this.f617o = null;
        }
        p(false);
    }

    public void z() {
        this.f618p = false;
        if (this.f619q) {
            this.f619q = false;
            p(this.f620r);
        }
    }

    @Override // android.view.Menu
    public MenuItem add(int i10) {
        return a(0, 0, 0, this.f605b.getString(i10));
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i10) {
        return addSubMenu(0, 0, 0, this.f605b.getString(i10));
    }

    @Override // android.view.Menu
    public MenuItem add(int i10, int i11, int i12, CharSequence charSequence) {
        return a(i10, i11, i12, charSequence);
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i10, int i11, int i12, CharSequence charSequence) {
        f fVar = (f) a(i10, i11, i12, charSequence);
        k kVar = new k(this.f604a, this, fVar);
        fVar.f643o = kVar;
        kVar.setHeaderTitle(fVar.f634e);
        return kVar;
    }

    @Override // android.view.Menu
    public MenuItem add(int i10, int i11, int i12, int i13) {
        return a(i10, i11, i12, this.f605b.getString(i13));
    }

    @Override // android.view.Menu
    public SubMenu addSubMenu(int i10, int i11, int i12, int i13) {
        return addSubMenu(i10, i11, i12, this.f605b.getString(i13));
    }
}
