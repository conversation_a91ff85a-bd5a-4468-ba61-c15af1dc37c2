package androidx.appcompat.view.menu;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import androidx.annotation.RestrictTo;
import androidx.appcompat.view.menu.i;
import java.util.ArrayList;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuAdapter */
public class c extends BaseAdapter {

    /* renamed from: a  reason: collision with root package name */
    public d f597a;

    /* renamed from: b  reason: collision with root package name */
    public int f598b = -1;

    /* renamed from: c  reason: collision with root package name */
    public boolean f599c;

    /* renamed from: d  reason: collision with root package name */
    public final boolean f600d;

    /* renamed from: e  reason: collision with root package name */
    public final LayoutInflater f601e;

    /* renamed from: f  reason: collision with root package name */
    public final int f602f;

    public c(d dVar, LayoutInflater layoutInflater, boolean z10, int i10) {
        this.f600d = z10;
        this.f601e = layoutInflater;
        this.f597a = dVar;
        this.f602f = i10;
        a();
    }

    public void a() {
        d dVar = this.f597a;
        f fVar = dVar.f624v;
        if (fVar != null) {
            dVar.i();
            ArrayList<f> arrayList = dVar.f612j;
            int size = arrayList.size();
            for (int i10 = 0; i10 < size; i10++) {
                if (arrayList.get(i10) == fVar) {
                    this.f598b = i10;
                    return;
                }
            }
        }
        this.f598b = -1;
    }

    /* renamed from: b */
    public f getItem(int i10) {
        ArrayList<f> arrayList;
        if (this.f600d) {
            d dVar = this.f597a;
            dVar.i();
            arrayList = dVar.f612j;
        } else {
            arrayList = this.f597a.l();
        }
        int i11 = this.f598b;
        if (i11 >= 0 && i10 >= i11) {
            i10++;
        }
        return arrayList.get(i10);
    }

    public int getCount() {
        ArrayList<f> arrayList;
        if (this.f600d) {
            d dVar = this.f597a;
            dVar.i();
            arrayList = dVar.f612j;
        } else {
            arrayList = this.f597a.l();
        }
        if (this.f598b < 0) {
            return arrayList.size();
        }
        return arrayList.size() - 1;
    }

    public long getItemId(int i10) {
        return (long) i10;
    }

    public View getView(int i10, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = this.f601e.inflate(this.f602f, viewGroup, false);
        }
        int i11 = getItem(i10).f631b;
        int i12 = i10 - 1;
        ListMenuItemView listMenuItemView = (ListMenuItemView) view;
        listMenuItemView.setGroupDividerEnabled(this.f597a.m() && i11 != (i12 >= 0 ? getItem(i12).f631b : i11));
        i.a aVar = (i.a) view;
        if (this.f599c) {
            listMenuItemView.setForceShowIcon(true);
        }
        aVar.d(getItem(i10), 0);
        return view;
    }

    public void notifyDataSetChanged() {
        a();
        super.notifyDataSetChanged();
    }
}
