package androidx.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.app.Fragment;
import android.app.FragmentManager;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RestrictTo;
import androidx.lifecycle.Lifecycle;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: ReportFragment */
public class n extends Fragment {

    @RequiresApi(29)
    /* compiled from: ReportFragment */
    public static class a implements Application.ActivityLifecycleCallbacks {
        public static void registerIn(Activity activity) {
            activity.registerActivityLifecycleCallbacks(new a());
        }

        public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {
        }

        public void onActivityDestroyed(@NonNull Activity activity) {
        }

        public void onActivityPaused(@NonNull Activity activity) {
        }

        public void onActivityPostCreated(@NonNull Activity activity, @Nullable Bundle bundle) {
            n.a(activity, Lifecycle.Event.ON_CREATE);
        }

        public void onActivityPostResumed(@NonNull Activity activity) {
            n.a(activity, Lifecycle.Event.ON_RESUME);
        }

        public void onActivityPostStarted(@NonNull Activity activity) {
            n.a(activity, Lifecycle.Event.ON_START);
        }

        public void onActivityPreDestroyed(@NonNull Activity activity) {
            n.a(activity, Lifecycle.Event.ON_DESTROY);
        }

        public void onActivityPrePaused(@NonNull Activity activity) {
            n.a(activity, Lifecycle.Event.ON_PAUSE);
        }

        public void onActivityPreStopped(@NonNull Activity activity) {
            n.a(activity, Lifecycle.Event.ON_STOP);
        }

        public void onActivityResumed(@NonNull Activity activity) {
        }

        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {
        }

        public void onActivityStarted(@NonNull Activity activity) {
        }

        public void onActivityStopped(@NonNull Activity activity) {
        }
    }

    public static void a(@NonNull Activity activity, @NonNull Lifecycle.Event event) {
        if (activity instanceof i) {
            h lifecycle = ((i) activity).getLifecycle();
            lifecycle.d("handleLifecycleEvent");
            lifecycle.g(event.getTargetState());
        } else if (activity instanceof g) {
            Lifecycle lifecycle2 = ((g) activity).getLifecycle();
            if (lifecycle2 instanceof h) {
                h hVar = (h) lifecycle2;
                hVar.d("handleLifecycleEvent");
                hVar.g(event.getTargetState());
            }
        }
    }

    public static void c(Activity activity) {
        if (Build.VERSION.SDK_INT >= 29) {
            a.registerIn(activity);
        }
        FragmentManager fragmentManager = activity.getFragmentManager();
        if (fragmentManager.findFragmentByTag("androidx.lifecycle.LifecycleDispatcher.report_fragment_tag") == null) {
            fragmentManager.beginTransaction().add(new n(), "androidx.lifecycle.LifecycleDispatcher.report_fragment_tag").commit();
            fragmentManager.executePendingTransactions();
        }
    }

    public final void b(@NonNull Lifecycle.Event event) {
        if (Build.VERSION.SDK_INT < 29) {
            a(getActivity(), event);
        }
    }

    public void onActivityCreated(Bundle bundle) {
        super.onActivityCreated(bundle);
        b(Lifecycle.Event.ON_CREATE);
    }

    public void onDestroy() {
        super.onDestroy();
        b(Lifecycle.Event.ON_DESTROY);
    }

    public void onPause() {
        super.onPause();
        b(Lifecycle.Event.ON_PAUSE);
    }

    public void onResume() {
        super.onResume();
        b(Lifecycle.Event.ON_RESUME);
    }

    public void onStart() {
        super.onStart();
        b(Lifecycle.Event.ON_START);
    }

    public void onStop() {
        super.onStop();
        b(Lifecycle.Event.ON_STOP);
    }
}
