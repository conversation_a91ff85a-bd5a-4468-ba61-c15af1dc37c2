package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: DERSet */
public class c1 extends t {

    /* renamed from: c  reason: collision with root package name */
    public int f173c = -1;

    public c1() {
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        p a10 = pVar.a();
        int s10 = s();
        pVar.c(49);
        pVar.g(s10);
        Enumeration q10 = q();
        while (q10.hasMoreElements()) {
            a10.h((e) q10.nextElement());
        }
    }

    @Override // aa.q
    public int i() throws IOException {
        int s10 = s();
        return v1.a(s10) + 1 + s10;
    }

    public final int s() throws IOException {
        if (this.f173c < 0) {
            int i10 = 0;
            Enumeration q10 = q();
            while (q10.hasMoreElements()) {
                i10 += ((e) q10.nextElement()).c().l().i();
            }
            this.f173c = i10;
        }
        return this.f173c;
    }

    public c1(f fVar) {
        super(fVar, true);
    }

    public c1(f fVar, boolean z10) {
        super(fVar, z10);
    }
}
