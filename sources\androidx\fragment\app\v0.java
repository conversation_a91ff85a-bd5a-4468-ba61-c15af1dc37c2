package androidx.fragment.app;

import android.util.Log;
import android.view.ViewParent;
import org.xmlpull.v1.XmlPullParser;

public final /* synthetic */ class v0 {
    public static String a(XmlPullParser xmlPullParser, StringBuilder sb, String str) {
        sb.append(xmlPullParser.getPositionDescription());
        sb.append(str);
        return sb.toString();
    }

    public static void b(String str, ViewParent viewParent, String str2, String str3, AbstractMethodError abstractMethodError) {
        Log.e(str3, str + viewParent + str2, abstractMethodError);
    }

    public static void c(String str, String str2, String str3) {
        com.duokan.airkan.common.Log.i(str3, str + str2);
    }

    public static void d(StringBuilder sb, int i10, String str) {
        sb.append(i10);
        com.duokan.airkan.common.Log.e(str, sb.toString());
    }

    public static /* synthetic */ String e(int i10) {
        return i10 == 1 ? "NONE" : i10 == 2 ? "ADDING" : i10 == 3 ? "REMOVING" : "null";
    }
}
