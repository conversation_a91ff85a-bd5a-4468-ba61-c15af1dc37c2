package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import m.a;

public class LinearLayoutCompat extends ViewGroup {

    /* renamed from: a  reason: collision with root package name */
    public boolean f865a;

    /* renamed from: b  reason: collision with root package name */
    public int f866b;

    /* renamed from: c  reason: collision with root package name */
    public int f867c;

    /* renamed from: d  reason: collision with root package name */
    public int f868d;

    /* renamed from: e  reason: collision with root package name */
    public int f869e;

    /* renamed from: f  reason: collision with root package name */
    public int f870f;

    /* renamed from: g  reason: collision with root package name */
    public float f871g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public int[] f872i;

    /* renamed from: j  reason: collision with root package name */
    public int[] f873j;

    /* renamed from: k  reason: collision with root package name */
    public Drawable f874k;

    /* renamed from: l  reason: collision with root package name */
    public int f875l;

    /* renamed from: m  reason: collision with root package name */
    public int f876m;

    /* renamed from: n  reason: collision with root package name */
    public int f877n;

    /* renamed from: o  reason: collision with root package name */
    public int f878o;

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface DividerMode {
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface OrientationMode {
    }

    public LinearLayoutCompat(@NonNull Context context) {
        this(context, null);
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    public void f(Canvas canvas, int i10) {
        this.f874k.setBounds(getPaddingLeft() + this.f878o, i10, (getWidth() - getPaddingRight()) - this.f878o, this.f876m + i10);
        this.f874k.draw(canvas);
    }

    public void g(Canvas canvas, int i10) {
        this.f874k.setBounds(i10, getPaddingTop() + this.f878o, this.f875l + i10, (getHeight() - getPaddingBottom()) - this.f878o);
        this.f874k.draw(canvas);
    }

    public int getBaseline() {
        int i10;
        if (this.f866b < 0) {
            return super.getBaseline();
        }
        int childCount = getChildCount();
        int i11 = this.f866b;
        if (childCount > i11) {
            View childAt = getChildAt(i11);
            int baseline = childAt.getBaseline();
            if (baseline != -1) {
                int i12 = this.f867c;
                if (this.f868d == 1 && (i10 = this.f869e & 112) != 48) {
                    if (i10 == 16) {
                        i12 = a.a(((getBottom() - getTop()) - getPaddingTop()) - getPaddingBottom(), this.f870f, 2, i12);
                    } else if (i10 == 80) {
                        i12 = ((getBottom() - getTop()) - getPaddingBottom()) - this.f870f;
                    }
                }
                return i12 + ((ViewGroup.MarginLayoutParams) ((LayoutParams) childAt.getLayoutParams())).topMargin + baseline;
            } else if (this.f866b == 0) {
                return -1;
            } else {
                throw new RuntimeException("mBaselineAlignedChildIndex of LinearLayout points to a View that doesn't know how to get its baseline.");
            }
        } else {
            throw new RuntimeException("mBaselineAlignedChildIndex of LinearLayout set to an index that is out of bounds.");
        }
    }

    public int getBaselineAlignedChildIndex() {
        return this.f866b;
    }

    public Drawable getDividerDrawable() {
        return this.f874k;
    }

    public int getDividerPadding() {
        return this.f878o;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int getDividerWidth() {
        return this.f875l;
    }

    public int getGravity() {
        return this.f869e;
    }

    public int getOrientation() {
        return this.f868d;
    }

    public int getShowDividers() {
        return this.f877n;
    }

    public int getVirtualChildCount() {
        return getChildCount();
    }

    public float getWeightSum() {
        return this.f871g;
    }

    /* renamed from: h */
    public LayoutParams generateDefaultLayoutParams() {
        int i10 = this.f868d;
        if (i10 == 0) {
            return new LayoutParams(-2, -2);
        }
        if (i10 == 1) {
            return new LayoutParams(-1, -2);
        }
        return null;
    }

    /* renamed from: i */
    public LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    /* renamed from: j */
    public LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new LayoutParams(layoutParams);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public boolean k(int i10) {
        if (i10 == 0) {
            return (this.f877n & 1) != 0;
        }
        if (i10 == getChildCount()) {
            return (this.f877n & 4) != 0;
        }
        if ((this.f877n & 2) == 0) {
            return false;
        }
        for (int i11 = i10 - 1; i11 >= 0; i11--) {
            if (getChildAt(i11).getVisibility() != 8) {
                return true;
            }
        }
        return false;
    }

    public void onDraw(Canvas canvas) {
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        if (this.f874k != null) {
            int i15 = 0;
            if (this.f868d == 1) {
                int virtualChildCount = getVirtualChildCount();
                while (i15 < virtualChildCount) {
                    View childAt = getChildAt(i15);
                    if (!(childAt == null || childAt.getVisibility() == 8 || !k(i15))) {
                        f(canvas, (childAt.getTop() - ((ViewGroup.MarginLayoutParams) ((LayoutParams) childAt.getLayoutParams())).topMargin) - this.f876m);
                    }
                    i15++;
                }
                if (k(virtualChildCount)) {
                    View childAt2 = getChildAt(virtualChildCount - 1);
                    if (childAt2 == null) {
                        i14 = (getHeight() - getPaddingBottom()) - this.f876m;
                    } else {
                        i14 = childAt2.getBottom() + ((ViewGroup.MarginLayoutParams) ((LayoutParams) childAt2.getLayoutParams())).bottomMargin;
                    }
                    f(canvas, i14);
                    return;
                }
                return;
            }
            int virtualChildCount2 = getVirtualChildCount();
            boolean b10 = q0.b(this);
            while (i15 < virtualChildCount2) {
                View childAt3 = getChildAt(i15);
                if (!(childAt3 == null || childAt3.getVisibility() == 8 || !k(i15))) {
                    LayoutParams layoutParams = (LayoutParams) childAt3.getLayoutParams();
                    if (b10) {
                        i13 = childAt3.getRight() + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
                    } else {
                        i13 = (childAt3.getLeft() - ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin) - this.f875l;
                    }
                    g(canvas, i13);
                }
                i15++;
            }
            if (k(virtualChildCount2)) {
                View childAt4 = getChildAt(virtualChildCount2 - 1);
                if (childAt4 != null) {
                    LayoutParams layoutParams2 = (LayoutParams) childAt4.getLayoutParams();
                    if (b10) {
                        i12 = childAt4.getLeft() - ((ViewGroup.MarginLayoutParams) layoutParams2).leftMargin;
                        i11 = this.f875l;
                    } else {
                        i10 = childAt4.getRight() + ((ViewGroup.MarginLayoutParams) layoutParams2).rightMargin;
                        g(canvas, i10);
                    }
                } else if (b10) {
                    i10 = getPaddingLeft();
                    g(canvas, i10);
                } else {
                    i12 = getWidth() - getPaddingRight();
                    i11 = this.f875l;
                }
                i10 = i12 - i11;
                g(canvas, i10);
            }
        }
    }

    public void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onInitializeAccessibilityEvent(accessibilityEvent);
        accessibilityEvent.setClassName("androidx.appcompat.widget.LinearLayoutCompat");
    }

    public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        accessibilityNodeInfo.setClassName("androidx.appcompat.widget.LinearLayoutCompat");
    }

    /* JADX WARNING: Removed duplicated region for block: B:27:0x009d  */
    /* JADX WARNING: Removed duplicated region for block: B:56:0x0159  */
    /* JADX WARNING: Removed duplicated region for block: B:59:0x0163  */
    /* JADX WARNING: Removed duplicated region for block: B:71:0x018e  */
    /* JADX WARNING: Removed duplicated region for block: B:74:0x01a0  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onLayout(boolean r18, int r19, int r20, int r21, int r22) {
        /*
        // Method dump skipped, instructions count: 463
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.LinearLayoutCompat.onLayout(boolean, int, int, int, int):void");
    }

    /* JADX WARNING: Removed duplicated region for block: B:142:0x02fb  */
    /* JADX WARNING: Removed duplicated region for block: B:208:0x049e  */
    /* JADX WARNING: Removed duplicated region for block: B:209:0x04a3  */
    /* JADX WARNING: Removed duplicated region for block: B:212:0x04cb  */
    /* JADX WARNING: Removed duplicated region for block: B:213:0x04d0  */
    /* JADX WARNING: Removed duplicated region for block: B:216:0x04d8  */
    /* JADX WARNING: Removed duplicated region for block: B:217:0x04e6  */
    /* JADX WARNING: Removed duplicated region for block: B:219:0x04fa  */
    /* JADX WARNING: Removed duplicated region for block: B:243:0x0569  */
    /* JADX WARNING: Removed duplicated region for block: B:246:0x0574  */
    /* JADX WARNING: Removed duplicated region for block: B:274:0x060b  */
    /* JADX WARNING: Removed duplicated region for block: B:308:0x06ca  */
    /* JADX WARNING: Removed duplicated region for block: B:311:0x06e7  */
    /* JADX WARNING: Removed duplicated region for block: B:378:0x089e  */
    /* JADX WARNING: Removed duplicated region for block: B:428:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r39, int r40) {
        /*
        // Method dump skipped, instructions count: 2271
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.LinearLayoutCompat.onMeasure(int, int):void");
    }

    public void setBaselineAligned(boolean z10) {
        this.f865a = z10;
    }

    public void setBaselineAlignedChildIndex(int i10) {
        if (i10 < 0 || i10 >= getChildCount()) {
            StringBuilder a10 = f.a("base aligned child index out of range (0, ");
            a10.append(getChildCount());
            a10.append(")");
            throw new IllegalArgumentException(a10.toString());
        }
        this.f866b = i10;
    }

    public void setDividerDrawable(Drawable drawable) {
        if (drawable != this.f874k) {
            this.f874k = drawable;
            boolean z10 = false;
            if (drawable != null) {
                this.f875l = drawable.getIntrinsicWidth();
                this.f876m = drawable.getIntrinsicHeight();
            } else {
                this.f875l = 0;
                this.f876m = 0;
            }
            if (drawable == null) {
                z10 = true;
            }
            setWillNotDraw(z10);
            requestLayout();
        }
    }

    public void setDividerPadding(int i10) {
        this.f878o = i10;
    }

    public void setGravity(int i10) {
        if (this.f869e != i10) {
            if ((8388615 & i10) == 0) {
                i10 |= 8388611;
            }
            if ((i10 & 112) == 0) {
                i10 |= 48;
            }
            this.f869e = i10;
            requestLayout();
        }
    }

    public void setHorizontalGravity(int i10) {
        int i11 = i10 & 8388615;
        int i12 = this.f869e;
        if ((8388615 & i12) != i11) {
            this.f869e = i11 | (-8388616 & i12);
            requestLayout();
        }
    }

    public void setMeasureWithLargestChildEnabled(boolean z10) {
        this.h = z10;
    }

    public void setOrientation(int i10) {
        if (this.f868d != i10) {
            this.f868d = i10;
            requestLayout();
        }
    }

    public void setShowDividers(int i10) {
        if (i10 != this.f877n) {
            requestLayout();
        }
        this.f877n = i10;
    }

    public void setVerticalGravity(int i10) {
        int i11 = i10 & 112;
        int i12 = this.f869e;
        if ((i12 & 112) != i11) {
            this.f869e = i11 | (i12 & -113);
            requestLayout();
        }
    }

    public void setWeightSum(float f10) {
        this.f871g = Math.max((float) Constant.VOLUME_FLOAT_MIN, f10);
    }

    public boolean shouldDelayChildPressedState() {
        return false;
    }

    public LinearLayoutCompat(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public LinearLayoutCompat(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        Drawable drawable;
        int resourceId;
        this.f865a = true;
        this.f866b = -1;
        this.f867c = 0;
        this.f869e = 8388659;
        int[] iArr = R$styleable.LinearLayoutCompat;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i10, 0);
        ViewCompat.j(this, context, iArr, attributeSet, obtainStyledAttributes, i10, 0);
        int i11 = obtainStyledAttributes.getInt(R$styleable.LinearLayoutCompat_android_orientation, -1);
        if (i11 >= 0) {
            setOrientation(i11);
        }
        int i12 = obtainStyledAttributes.getInt(R$styleable.LinearLayoutCompat_android_gravity, -1);
        if (i12 >= 0) {
            setGravity(i12);
        }
        boolean z10 = obtainStyledAttributes.getBoolean(R$styleable.LinearLayoutCompat_android_baselineAligned, true);
        if (!z10) {
            setBaselineAligned(z10);
        }
        this.f871g = obtainStyledAttributes.getFloat(R$styleable.LinearLayoutCompat_android_weightSum, -1.0f);
        this.f866b = obtainStyledAttributes.getInt(R$styleable.LinearLayoutCompat_android_baselineAlignedChildIndex, -1);
        this.h = obtainStyledAttributes.getBoolean(R$styleable.LinearLayoutCompat_measureWithLargestChild, false);
        int i13 = R$styleable.LinearLayoutCompat_divider;
        if (!obtainStyledAttributes.hasValue(i13) || (resourceId = obtainStyledAttributes.getResourceId(i13, 0)) == 0) {
            drawable = obtainStyledAttributes.getDrawable(i13);
        } else {
            drawable = a.a(context, resourceId);
        }
        setDividerDrawable(drawable);
        this.f877n = obtainStyledAttributes.getInt(R$styleable.LinearLayoutCompat_showDividers, 0);
        this.f878o = obtainStyledAttributes.getDimensionPixelSize(R$styleable.LinearLayoutCompat_dividerPadding, 0);
        obtainStyledAttributes.recycle();
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {

        /* renamed from: a  reason: collision with root package name */
        public float f879a;

        /* renamed from: b  reason: collision with root package name */
        public int f880b;

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f880b = -1;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.LinearLayoutCompat_Layout);
            this.f879a = obtainStyledAttributes.getFloat(R$styleable.LinearLayoutCompat_Layout_android_layout_weight, Constant.VOLUME_FLOAT_MIN);
            this.f880b = obtainStyledAttributes.getInt(R$styleable.LinearLayoutCompat_Layout_android_layout_gravity, -1);
            obtainStyledAttributes.recycle();
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
            this.f880b = -1;
            this.f879a = Constant.VOLUME_FLOAT_MIN;
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
            this.f880b = -1;
        }
    }
}
