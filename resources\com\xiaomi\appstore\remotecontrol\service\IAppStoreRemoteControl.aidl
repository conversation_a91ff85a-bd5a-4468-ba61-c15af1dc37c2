package com.xiaomi.appstore.remotecontrol.service;

import com.xiaomi.appstore.remotecontrol.service.IAppStoreRemoteControlCallbackResult;

interface IAppStoreRemoteControl{
    void startAppInstallOrUpdate(in long appId, in IAppStoreRemoteControlCallbackResult cb);
    void startOtherStoreAppInstallOrUpdate(in String appUrl, in IAppStoreRemoteControlCallbackResult cb);
    
    /*
     * status:
     * =0,   uninstall
     * =1,   installing
     * =2,   installed
     * =3,   update
     * =4,   updating
     * =-1,  not ready
     */
    int checkAppStatus(in long appId);
    int checkOtherStoreAppStatus(in String appUrl, in String packageName, in int versionCode);
}