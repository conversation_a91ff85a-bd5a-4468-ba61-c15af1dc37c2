package androidx.lifecycle;

import android.app.Application;
import androidx.annotation.NonNull;
import java.lang.reflect.InvocationTargetException;

/* compiled from: ViewModelProvider */
public class r extends u {

    /* renamed from: c  reason: collision with root package name */
    public static r f2092c;

    /* renamed from: b  reason: collision with root package name */
    public Application f2093b;

    public r(@NonNull Application application) {
        this.f2093b = application;
    }

    @Override // androidx.lifecycle.u, androidx.lifecycle.s
    @NonNull
    public <T extends q> T a(@NonNull Class<T> cls) {
        if (!a.class.isAssignableFrom(cls)) {
            return (T) super.a(cls);
        }
        try {
            return cls.getConstructor(Application.class).newInstance(this.f2093b);
        } catch (NoSuchMethodException e10) {
            throw new RuntimeException("Cannot create an instance of " + cls, e10);
        } catch (IllegalAccessException e11) {
            throw new RuntimeException("Cannot create an instance of " + cls, e11);
        } catch (InstantiationException e12) {
            throw new RuntimeException("Cannot create an instance of " + cls, e12);
        } catch (InvocationTargetException e13) {
            throw new RuntimeException("Cannot create an instance of " + cls, e13);
        }
    }
}
