package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import androidx.constraintlayout.solver.widgets.b;

public class Barrier extends ConstraintHelper {

    /* renamed from: f  reason: collision with root package name */
    public int f1396f;

    /* renamed from: g  reason: collision with root package name */
    public int f1397g;
    public b h;

    public Barrier(Context context) {
        super(context);
        super.setVisibility(8);
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void b(AttributeSet attributeSet) {
        super.b(attributeSet);
        this.h = new b();
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, R$styleable.ConstraintLayout_Layout);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i10 = 0; i10 < indexCount; i10++) {
                int index = obtainStyledAttributes.getIndex(i10);
                if (index == R$styleable.ConstraintLayout_Layout_barrierDirection) {
                    setType(obtainStyledAttributes.getInt(index, 0));
                } else if (index == R$styleable.ConstraintLayout_Layout_barrierAllowsGoneWidgets) {
                    this.h.f1328m0 = obtainStyledAttributes.getBoolean(index, true);
                }
            }
        }
        this.f1401d = this.h;
        e();
    }

    public int getType() {
        return this.f1396f;
    }

    public void setAllowsGoneWidget(boolean z10) {
        this.h.f1328m0 = z10;
    }

    public void setType(int i10) {
        this.f1396f = i10;
        this.f1397g = i10;
        if (1 == getResources().getConfiguration().getLayoutDirection()) {
            int i11 = this.f1396f;
            if (i11 == 5) {
                this.f1397g = 1;
            } else if (i11 == 6) {
                this.f1397g = 0;
            }
        } else {
            int i12 = this.f1396f;
            if (i12 == 5) {
                this.f1397g = 0;
            } else if (i12 == 6) {
                this.f1397g = 1;
            }
        }
        this.h.f1326k0 = this.f1397g;
    }

    public Barrier(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        super.setVisibility(8);
    }

    public Barrier(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        super.setVisibility(8);
    }
}
