package androidx.drawerlayout;

public final class R$id {
    public static final int action_container = 2131361854;
    public static final int action_divider = 2131361857;
    public static final int action_image = 2131361858;
    public static final int action_text = 2131361866;
    public static final int actions = 2131361867;
    public static final int async = 2131361877;
    public static final int blocking = 2131361881;
    public static final int chronometer = 2131361906;
    public static final int forever = 2131361965;
    public static final int icon = 2131361980;
    public static final int icon_group = 2131361981;
    public static final int info = 2131361988;
    public static final int italic = 2131361999;
    public static final int line1 = 2131362007;
    public static final int line3 = 2131362008;
    public static final int normal = 2131362092;
    public static final int notification_background = 2131362093;
    public static final int notification_main_column = 2131362095;
    public static final int notification_main_column_container = 2131362096;
    public static final int right_icon = 2131362144;
    public static final int right_side = 2131362145;
    public static final int tag_transition_group = 2131362221;
    public static final int tag_unhandled_key_event_manager = 2131362222;
    public static final int tag_unhandled_key_listeners = 2131362223;
    public static final int text = 2131362228;
    public static final int text2 = 2131362229;
    public static final int time = 2131362243;
    public static final int title = 2131362246;

    private R$id() {
    }
}
