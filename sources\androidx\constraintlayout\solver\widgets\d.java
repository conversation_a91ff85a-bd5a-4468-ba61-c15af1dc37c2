package androidx.constraintlayout.solver.widgets;

import com.duokan.airkan.common.Constant;
import java.util.ArrayList;

/* compiled from: ChainHead */
public class d {

    /* renamed from: a  reason: collision with root package name */
    public ConstraintWidget f1329a;

    /* renamed from: b  reason: collision with root package name */
    public ConstraintWidget f1330b;

    /* renamed from: c  reason: collision with root package name */
    public ConstraintWidget f1331c;

    /* renamed from: d  reason: collision with root package name */
    public ConstraintWidget f1332d;

    /* renamed from: e  reason: collision with root package name */
    public ConstraintWidget f1333e;

    /* renamed from: f  reason: collision with root package name */
    public ConstraintWidget f1334f;

    /* renamed from: g  reason: collision with root package name */
    public ConstraintWidget f1335g;
    public ArrayList<ConstraintWidget> h;

    /* renamed from: i  reason: collision with root package name */
    public int f1336i;

    /* renamed from: j  reason: collision with root package name */
    public int f1337j;

    /* renamed from: k  reason: collision with root package name */
    public float f1338k = Constant.VOLUME_FLOAT_MIN;

    /* renamed from: l  reason: collision with root package name */
    public int f1339l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1340m = false;

    /* renamed from: n  reason: collision with root package name */
    public boolean f1341n;

    /* renamed from: o  reason: collision with root package name */
    public boolean f1342o;

    /* renamed from: p  reason: collision with root package name */
    public boolean f1343p;

    /* renamed from: q  reason: collision with root package name */
    public boolean f1344q;

    public d(ConstraintWidget constraintWidget, int i10, boolean z10) {
        this.f1329a = constraintWidget;
        this.f1339l = i10;
        this.f1340m = z10;
    }
}
