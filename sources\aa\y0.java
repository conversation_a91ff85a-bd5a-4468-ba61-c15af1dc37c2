package aa;

import java.io.IOException;
import java.io.OutputStream;

/* compiled from: DEROutputStream */
public class y0 extends p {
    public y0(OutputStream outputStream) {
        super(outputStream);
    }

    @Override // aa.p
    public p a() {
        return this;
    }

    @Override // aa.p
    public p b() {
        return this;
    }

    @Override // aa.p
    public void h(e eVar) throws IOException {
        if (eVar != null) {
            eVar.c().l().h(this);
            return;
        }
        throw new IOException("null object detected");
    }
}
