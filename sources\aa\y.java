package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: ASN1UTCTime */
public class y extends q {

    /* renamed from: a  reason: collision with root package name */
    public byte[] f246a;

    public y(byte[] bArr) {
        this.f246a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof y)) {
            return false;
        }
        return a.a(this.f246a, ((y) qVar).f246a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.c(23);
        int length = this.f246a.length;
        pVar.g(length);
        for (int i10 = 0; i10 != length; i10++) {
            pVar.c(this.f246a[i10]);
        }
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f246a);
    }

    @Override // aa.q
    public int i() {
        int length = this.f246a.length;
        return v1.a(length) + 1 + length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return c.a(this.f246a);
    }
}
