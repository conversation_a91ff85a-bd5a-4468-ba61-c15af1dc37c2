package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.l;
import com.duokan.airkan.common.Constant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class d implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ArrayList f2353a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ l f2354b;

    public d(l lVar, ArrayList arrayList) {
        this.f2354b = lVar;
        this.f2353a = arrayList;
    }

    public void run() {
        Iterator it = this.f2353a.iterator();
        while (it.hasNext()) {
            l.b bVar = (l.b) it.next();
            l lVar = this.f2354b;
            RecyclerView.w wVar = bVar.f2404a;
            int i10 = bVar.f2405b;
            int i11 = bVar.f2406c;
            int i12 = bVar.f2407d;
            int i13 = bVar.f2408e;
            Objects.requireNonNull(lVar);
            View view = wVar.f2267a;
            int i14 = i12 - i10;
            int i15 = i13 - i11;
            if (i14 != 0) {
                view.animate().translationX(Constant.VOLUME_FLOAT_MIN);
            }
            if (i15 != 0) {
                view.animate().translationY(Constant.VOLUME_FLOAT_MIN);
            }
            ViewPropertyAnimator animate = view.animate();
            lVar.f2395p.add(wVar);
            animate.setDuration(lVar.f2184e).setListener(new i(lVar, wVar, i14, view, i15, animate)).start();
        }
        this.f2353a.clear();
        this.f2354b.f2392m.remove(this.f2353a);
    }
}
