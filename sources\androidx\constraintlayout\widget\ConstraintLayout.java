package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;
import androidx.constraintlayout.solver.widgets.e;
import androidx.constraintlayout.solver.widgets.g;
import com.duokan.airkan.common.AirkanDef;
import com.duokan.airkan.common.Constant;
import com.google.protobuf.Reader;
import com.xiaomi.onetrack.util.z;
import java.util.ArrayList;
import java.util.HashMap;

public class ConstraintLayout extends ViewGroup {

    /* renamed from: a  reason: collision with root package name */
    public SparseArray<View> f1403a = new SparseArray<>();

    /* renamed from: b  reason: collision with root package name */
    public ArrayList<ConstraintHelper> f1404b = new ArrayList<>(4);

    /* renamed from: c  reason: collision with root package name */
    public final ArrayList<ConstraintWidget> f1405c = new ArrayList<>(100);

    /* renamed from: d  reason: collision with root package name */
    public e f1406d = new e();

    /* renamed from: e  reason: collision with root package name */
    public int f1407e = 0;

    /* renamed from: f  reason: collision with root package name */
    public int f1408f = 0;

    /* renamed from: g  reason: collision with root package name */
    public int f1409g = Reader.READ_DONE;
    public int h = Reader.READ_DONE;

    /* renamed from: i  reason: collision with root package name */
    public boolean f1410i = true;

    /* renamed from: j  reason: collision with root package name */
    public int f1411j = 7;

    /* renamed from: k  reason: collision with root package name */
    public a f1412k = null;

    /* renamed from: l  reason: collision with root package name */
    public int f1413l = -1;

    /* renamed from: m  reason: collision with root package name */
    public HashMap<String, Integer> f1414m = new HashMap<>();

    /* renamed from: n  reason: collision with root package name */
    public int f1415n = -1;

    /* renamed from: o  reason: collision with root package name */
    public int f1416o = -1;

    public ConstraintLayout(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        e(attributeSet);
    }

    /* renamed from: a */
    public LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-2, -2);
    }

    @Override // android.view.ViewGroup
    public void addView(View view, int i10, ViewGroup.LayoutParams layoutParams) {
        super.addView(view, i10, layoutParams);
    }

    public Object b(int i10, Object obj) {
        if (i10 != 0 || !(obj instanceof String)) {
            return null;
        }
        String str = (String) obj;
        HashMap<String, Integer> hashMap = this.f1414m;
        if (hashMap == null || !hashMap.containsKey(str)) {
            return null;
        }
        return this.f1414m.get(str);
    }

    public final ConstraintWidget c(int i10) {
        if (i10 == 0) {
            return this.f1406d;
        }
        View view = this.f1403a.get(i10);
        if (view == null && (view = findViewById(i10)) != null && view != this && view.getParent() == this) {
            onViewAdded(view);
        }
        if (view == this) {
            return this.f1406d;
        }
        if (view == null) {
            return null;
        }
        return ((LayoutParams) view.getLayoutParams()).f1437k0;
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    public final ConstraintWidget d(View view) {
        if (view == this) {
            return this.f1406d;
        }
        if (view == null) {
            return null;
        }
        return ((LayoutParams) view.getLayoutParams()).f1437k0;
    }

    public void dispatchDraw(Canvas canvas) {
        Object tag;
        super.dispatchDraw(canvas);
        if (isInEditMode()) {
            int childCount = getChildCount();
            float width = (float) getWidth();
            float height = (float) getHeight();
            for (int i10 = 0; i10 < childCount; i10++) {
                View childAt = getChildAt(i10);
                if (!(childAt.getVisibility() == 8 || (tag = childAt.getTag()) == null || !(tag instanceof String))) {
                    String[] split = ((String) tag).split(z.f5836b);
                    if (split.length == 4) {
                        int parseInt = Integer.parseInt(split[0]);
                        int parseInt2 = Integer.parseInt(split[1]);
                        int parseInt3 = Integer.parseInt(split[2]);
                        int i11 = (int) ((((float) parseInt) / 1080.0f) * width);
                        int i12 = (int) ((((float) parseInt2) / 1920.0f) * height);
                        Paint paint = new Paint();
                        paint.setColor(-65536);
                        float f10 = (float) i11;
                        float f11 = (float) i12;
                        float f12 = (float) (i11 + ((int) ((((float) parseInt3) / 1080.0f) * width)));
                        canvas.drawLine(f10, f11, f12, f11, paint);
                        float parseInt4 = (float) (i12 + ((int) ((((float) Integer.parseInt(split[3])) / 1920.0f) * height)));
                        canvas.drawLine(f12, f11, f12, parseInt4, paint);
                        canvas.drawLine(f12, parseInt4, f10, parseInt4, paint);
                        canvas.drawLine(f10, parseInt4, f10, f11, paint);
                        paint.setColor(-16711936);
                        canvas.drawLine(f10, f11, f12, parseInt4, paint);
                        canvas.drawLine(f10, parseInt4, f12, f11, paint);
                    }
                }
            }
        }
    }

    public final void e(AttributeSet attributeSet) {
        this.f1406d.X = this;
        this.f1403a.put(getId(), this);
        this.f1412k = null;
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, R$styleable.ConstraintLayout_Layout);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i10 = 0; i10 < indexCount; i10++) {
                int index = obtainStyledAttributes.getIndex(i10);
                if (index == R$styleable.ConstraintLayout_Layout_android_minWidth) {
                    this.f1407e = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1407e);
                } else if (index == R$styleable.ConstraintLayout_Layout_android_minHeight) {
                    this.f1408f = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1408f);
                } else if (index == R$styleable.ConstraintLayout_Layout_android_maxWidth) {
                    this.f1409g = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1409g);
                } else if (index == R$styleable.ConstraintLayout_Layout_android_maxHeight) {
                    this.h = obtainStyledAttributes.getDimensionPixelOffset(index, this.h);
                } else if (index == R$styleable.ConstraintLayout_Layout_layout_optimizationLevel) {
                    this.f1411j = obtainStyledAttributes.getInt(index, this.f1411j);
                } else if (index == R$styleable.ConstraintLayout_Layout_constraintSet) {
                    int resourceId = obtainStyledAttributes.getResourceId(index, 0);
                    try {
                        a aVar = new a();
                        this.f1412k = aVar;
                        aVar.i(getContext(), resourceId);
                    } catch (Resources.NotFoundException unused) {
                        this.f1412k = null;
                    }
                    this.f1413l = resourceId;
                }
            }
            obtainStyledAttributes.recycle();
        }
        this.f1406d.f1358w0 = this.f1411j;
    }

    /* JADX WARNING: Removed duplicated region for block: B:110:0x01e0  */
    /* JADX WARNING: Removed duplicated region for block: B:134:0x023d  */
    /* JADX WARNING: Removed duplicated region for block: B:144:0x026a  */
    /* JADX WARNING: Removed duplicated region for block: B:146:0x026e  */
    /* JADX WARNING: Removed duplicated region for block: B:149:0x0273  */
    /* JADX WARNING: Removed duplicated region for block: B:150:0x027b  */
    /* JADX WARNING: Removed duplicated region for block: B:152:0x0283  */
    /* JADX WARNING: Removed duplicated region for block: B:153:0x028b  */
    /* JADX WARNING: Removed duplicated region for block: B:156:0x0297  */
    /* JADX WARNING: Removed duplicated region for block: B:159:0x02a1  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void f(int r24, int r25) {
        /*
        // Method dump skipped, instructions count: 701
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.f(int, int):void");
    }

    public void g(int i10, Object obj, Object obj2) {
        if (i10 == 0 && (obj instanceof String) && (obj2 instanceof Integer)) {
            if (this.f1414m == null) {
                this.f1414m = new HashMap<>();
            }
            String str = (String) obj;
            int indexOf = str.indexOf("/");
            if (indexOf != -1) {
                str = str.substring(indexOf + 1);
            }
            this.f1414m.put(str, Integer.valueOf(((Integer) obj2).intValue()));
        }
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    public int getMaxHeight() {
        return this.h;
    }

    public int getMaxWidth() {
        return this.f1409g;
    }

    public int getMinHeight() {
        return this.f1408f;
    }

    public int getMinWidth() {
        return this.f1407e;
    }

    public int getOptimizationLevel() {
        return this.f1406d.f1358w0;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        View content;
        int childCount = getChildCount();
        boolean isInEditMode = isInEditMode();
        for (int i14 = 0; i14 < childCount; i14++) {
            View childAt = getChildAt(i14);
            LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
            ConstraintWidget constraintWidget = layoutParams.f1437k0;
            if ((childAt.getVisibility() != 8 || layoutParams.X || layoutParams.Y || isInEditMode) && !layoutParams.Z) {
                int i15 = constraintWidget.M + constraintWidget.O;
                int i16 = constraintWidget.N + constraintWidget.P;
                int n10 = constraintWidget.n() + i15;
                int h6 = constraintWidget.h() + i16;
                childAt.layout(i15, i16, n10, h6);
                if ((childAt instanceof Placeholder) && (content = ((Placeholder) childAt).getContent()) != null) {
                    content.setVisibility(0);
                    content.layout(i15, i16, n10, h6);
                }
            }
        }
        int size = this.f1404b.size();
        if (size > 0) {
            for (int i17 = 0; i17 < size; i17++) {
                this.f1404b.get(i17).c(this);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:341:0x06ab, code lost:
        if (r8.H != 1) goto L_0x06b1;
     */
    /* JADX WARNING: Removed duplicated region for block: B:190:0x037c  */
    /* JADX WARNING: Removed duplicated region for block: B:193:0x0392 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:199:0x03ae  */
    /* JADX WARNING: Removed duplicated region for block: B:202:0x03c6  */
    /* JADX WARNING: Removed duplicated region for block: B:209:0x03e7  */
    /* JADX WARNING: Removed duplicated region for block: B:212:0x03ff  */
    /* JADX WARNING: Removed duplicated region for block: B:219:0x0420  */
    /* JADX WARNING: Removed duplicated region for block: B:297:0x059b  */
    /* JADX WARNING: Removed duplicated region for block: B:353:0x06c4  */
    /* JADX WARNING: Removed duplicated region for block: B:373:0x0716  */
    /* JADX WARNING: Removed duplicated region for block: B:376:0x0725  */
    /* JADX WARNING: Removed duplicated region for block: B:378:0x0729  */
    /* JADX WARNING: Removed duplicated region for block: B:453:0x086b  */
    /* JADX WARNING: Removed duplicated region for block: B:456:0x0884  */
    /* JADX WARNING: Removed duplicated region for block: B:556:0x0a69  */
    /* JADX WARNING: Removed duplicated region for block: B:559:0x0a9d  */
    /* JADX WARNING: Removed duplicated region for block: B:562:0x0aa2  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r39, int r40) {
        /*
        // Method dump skipped, instructions count: 2731
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.onMeasure(int, int):void");
    }

    public void onViewAdded(View view) {
        super.onViewAdded(view);
        ConstraintWidget d10 = d(view);
        if ((view instanceof Guideline) && !(d10 instanceof g)) {
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            g gVar = new g();
            layoutParams.f1437k0 = gVar;
            layoutParams.X = true;
            gVar.F(layoutParams.R);
        }
        if (view instanceof ConstraintHelper) {
            ConstraintHelper constraintHelper = (ConstraintHelper) view;
            constraintHelper.e();
            ((LayoutParams) view.getLayoutParams()).Y = true;
            if (!this.f1404b.contains(constraintHelper)) {
                this.f1404b.add(constraintHelper);
            }
        }
        this.f1403a.put(view.getId(), view);
        this.f1410i = true;
    }

    public void onViewRemoved(View view) {
        super.onViewRemoved(view);
        this.f1403a.remove(view.getId());
        ConstraintWidget d10 = d(view);
        this.f1406d.f10636i0.remove(d10);
        d10.D = null;
        this.f1404b.remove(view);
        this.f1405c.remove(d10);
        this.f1410i = true;
    }

    public void removeView(View view) {
        super.removeView(view);
    }

    public void requestLayout() {
        super.requestLayout();
        this.f1410i = true;
        this.f1415n = -1;
        this.f1416o = -1;
    }

    public void setConstraintSet(a aVar) {
        this.f1412k = aVar;
    }

    public void setId(int i10) {
        this.f1403a.remove(getId());
        super.setId(i10);
        this.f1403a.put(getId(), this);
    }

    public void setMaxHeight(int i10) {
        if (i10 != this.h) {
            this.h = i10;
            requestLayout();
        }
    }

    public void setMaxWidth(int i10) {
        if (i10 != this.f1409g) {
            this.f1409g = i10;
            requestLayout();
        }
    }

    public void setMinHeight(int i10) {
        if (i10 != this.f1408f) {
            this.f1408f = i10;
            requestLayout();
        }
    }

    public void setMinWidth(int i10) {
        if (i10 != this.f1407e) {
            this.f1407e = i10;
            requestLayout();
        }
    }

    public void setOptimizationLevel(int i10) {
        this.f1406d.f1358w0 = i10;
    }

    public boolean shouldDelayChildPressedState() {
        return false;
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new LayoutParams(layoutParams);
    }

    public ConstraintLayout(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        e(attributeSet);
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {
        public float A = 0.5f;
        public String B = null;
        public int C = 1;
        public float D = -1.0f;
        public float E = -1.0f;
        public int F = 0;
        public int G = 0;
        public int H = 0;
        public int I = 0;
        public int J = 0;
        public int K = 0;
        public int L = 0;
        public int M = 0;
        public float N = 1.0f;
        public float O = 1.0f;
        public int P = -1;
        public int Q = -1;
        public int R = -1;
        public boolean S = false;
        public boolean T = false;
        public boolean U = true;
        public boolean V = true;
        public boolean W = false;
        public boolean X = false;
        public boolean Y = false;
        public boolean Z = false;

        /* renamed from: a  reason: collision with root package name */
        public int f1417a = -1;

        /* renamed from: a0  reason: collision with root package name */
        public int f1418a0 = -1;

        /* renamed from: b  reason: collision with root package name */
        public int f1419b = -1;

        /* renamed from: b0  reason: collision with root package name */
        public int f1420b0 = -1;

        /* renamed from: c  reason: collision with root package name */
        public float f1421c = -1.0f;

        /* renamed from: c0  reason: collision with root package name */
        public int f1422c0 = -1;

        /* renamed from: d  reason: collision with root package name */
        public int f1423d = -1;

        /* renamed from: d0  reason: collision with root package name */
        public int f1424d0 = -1;

        /* renamed from: e  reason: collision with root package name */
        public int f1425e = -1;

        /* renamed from: e0  reason: collision with root package name */
        public int f1426e0 = -1;

        /* renamed from: f  reason: collision with root package name */
        public int f1427f = -1;

        /* renamed from: f0  reason: collision with root package name */
        public int f1428f0 = -1;

        /* renamed from: g  reason: collision with root package name */
        public int f1429g = -1;

        /* renamed from: g0  reason: collision with root package name */
        public float f1430g0 = 0.5f;
        public int h = -1;

        /* renamed from: h0  reason: collision with root package name */
        public int f1431h0;

        /* renamed from: i  reason: collision with root package name */
        public int f1432i = -1;

        /* renamed from: i0  reason: collision with root package name */
        public int f1433i0;

        /* renamed from: j  reason: collision with root package name */
        public int f1434j = -1;

        /* renamed from: j0  reason: collision with root package name */
        public float f1435j0;

        /* renamed from: k  reason: collision with root package name */
        public int f1436k = -1;

        /* renamed from: k0  reason: collision with root package name */
        public ConstraintWidget f1437k0 = new ConstraintWidget();

        /* renamed from: l  reason: collision with root package name */
        public int f1438l = -1;

        /* renamed from: m  reason: collision with root package name */
        public int f1439m = -1;

        /* renamed from: n  reason: collision with root package name */
        public int f1440n = 0;

        /* renamed from: o  reason: collision with root package name */
        public float f1441o = Constant.VOLUME_FLOAT_MIN;

        /* renamed from: p  reason: collision with root package name */
        public int f1442p = -1;

        /* renamed from: q  reason: collision with root package name */
        public int f1443q = -1;

        /* renamed from: r  reason: collision with root package name */
        public int f1444r = -1;

        /* renamed from: s  reason: collision with root package name */
        public int f1445s = -1;

        /* renamed from: t  reason: collision with root package name */
        public int f1446t = -1;

        /* renamed from: u  reason: collision with root package name */
        public int f1447u = -1;

        /* renamed from: v  reason: collision with root package name */
        public int f1448v = -1;

        /* renamed from: w  reason: collision with root package name */
        public int f1449w = -1;

        /* renamed from: x  reason: collision with root package name */
        public int f1450x = -1;

        /* renamed from: y  reason: collision with root package name */
        public int f1451y = -1;

        /* renamed from: z  reason: collision with root package name */
        public float f1452z = 0.5f;

        public static class a {

            /* renamed from: a  reason: collision with root package name */
            public static final SparseIntArray f1453a;

            static {
                SparseIntArray sparseIntArray = new SparseIntArray();
                f1453a = sparseIntArray;
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintLeft_toLeftOf, 8);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintLeft_toRightOf, 9);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintRight_toLeftOf, 10);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintRight_toRightOf, 11);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintTop_toTopOf, 12);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintTop_toBottomOf, 13);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintBottom_toTopOf, 14);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintBottom_toBottomOf, 15);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf, 16);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintCircle, 2);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintCircleRadius, 3);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintCircleAngle, 4);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_editor_absoluteX, 49);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_editor_absoluteY, 50);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintGuide_begin, 5);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintGuide_end, 6);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintGuide_percent, 7);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_android_orientation, 1);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintStart_toEndOf, 17);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintStart_toStartOf, 18);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintEnd_toStartOf, 19);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintEnd_toEndOf, 20);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginLeft, 21);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginTop, 22);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginRight, 23);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginBottom, 24);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginStart, 25);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_goneMarginEnd, 26);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHorizontal_bias, 29);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintVertical_bias, 30);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintDimensionRatio, 44);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHorizontal_weight, 45);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintVertical_weight, 46);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle, 47);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintVertical_chainStyle, 48);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constrainedWidth, 27);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constrainedHeight, 28);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintWidth_default, 31);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHeight_default, 32);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintWidth_min, 33);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintWidth_max, 34);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintWidth_percent, 35);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHeight_min, 36);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHeight_max, 37);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintHeight_percent, 38);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintLeft_creator, 39);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintTop_creator, 40);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintRight_creator, 41);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintBottom_creator, 42);
                sparseIntArray.append(R$styleable.ConstraintLayout_Layout_layout_constraintBaseline_creator, 43);
            }
        }

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            int i10;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ConstraintLayout_Layout);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i11 = 0; i11 < indexCount; i11++) {
                int index = obtainStyledAttributes.getIndex(i11);
                int i12 = a.f1453a.get(index);
                switch (i12) {
                    case 1:
                        this.R = obtainStyledAttributes.getInt(index, this.R);
                        break;
                    case 2:
                        int resourceId = obtainStyledAttributes.getResourceId(index, this.f1439m);
                        this.f1439m = resourceId;
                        if (resourceId == -1) {
                            this.f1439m = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 3:
                        this.f1440n = obtainStyledAttributes.getDimensionPixelSize(index, this.f1440n);
                        break;
                    case 4:
                        float f10 = obtainStyledAttributes.getFloat(index, this.f1441o) % 360.0f;
                        this.f1441o = f10;
                        if (f10 < Constant.VOLUME_FLOAT_MIN) {
                            this.f1441o = (360.0f - f10) % 360.0f;
                            break;
                        } else {
                            break;
                        }
                    case 5:
                        this.f1417a = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1417a);
                        break;
                    case 6:
                        this.f1419b = obtainStyledAttributes.getDimensionPixelOffset(index, this.f1419b);
                        break;
                    case 7:
                        this.f1421c = obtainStyledAttributes.getFloat(index, this.f1421c);
                        break;
                    case 8:
                        int resourceId2 = obtainStyledAttributes.getResourceId(index, this.f1423d);
                        this.f1423d = resourceId2;
                        if (resourceId2 == -1) {
                            this.f1423d = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 9:
                        int resourceId3 = obtainStyledAttributes.getResourceId(index, this.f1425e);
                        this.f1425e = resourceId3;
                        if (resourceId3 == -1) {
                            this.f1425e = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 10:
                        int resourceId4 = obtainStyledAttributes.getResourceId(index, this.f1427f);
                        this.f1427f = resourceId4;
                        if (resourceId4 == -1) {
                            this.f1427f = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 11:
                        int resourceId5 = obtainStyledAttributes.getResourceId(index, this.f1429g);
                        this.f1429g = resourceId5;
                        if (resourceId5 == -1) {
                            this.f1429g = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 12:
                        int resourceId6 = obtainStyledAttributes.getResourceId(index, this.h);
                        this.h = resourceId6;
                        if (resourceId6 == -1) {
                            this.h = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 13:
                        int resourceId7 = obtainStyledAttributes.getResourceId(index, this.f1432i);
                        this.f1432i = resourceId7;
                        if (resourceId7 == -1) {
                            this.f1432i = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 14:
                        int resourceId8 = obtainStyledAttributes.getResourceId(index, this.f1434j);
                        this.f1434j = resourceId8;
                        if (resourceId8 == -1) {
                            this.f1434j = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 15:
                        int resourceId9 = obtainStyledAttributes.getResourceId(index, this.f1436k);
                        this.f1436k = resourceId9;
                        if (resourceId9 == -1) {
                            this.f1436k = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 16:
                        int resourceId10 = obtainStyledAttributes.getResourceId(index, this.f1438l);
                        this.f1438l = resourceId10;
                        if (resourceId10 == -1) {
                            this.f1438l = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 17:
                        int resourceId11 = obtainStyledAttributes.getResourceId(index, this.f1442p);
                        this.f1442p = resourceId11;
                        if (resourceId11 == -1) {
                            this.f1442p = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 18:
                        int resourceId12 = obtainStyledAttributes.getResourceId(index, this.f1443q);
                        this.f1443q = resourceId12;
                        if (resourceId12 == -1) {
                            this.f1443q = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 19:
                        int resourceId13 = obtainStyledAttributes.getResourceId(index, this.f1444r);
                        this.f1444r = resourceId13;
                        if (resourceId13 == -1) {
                            this.f1444r = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 20:
                        int resourceId14 = obtainStyledAttributes.getResourceId(index, this.f1445s);
                        this.f1445s = resourceId14;
                        if (resourceId14 == -1) {
                            this.f1445s = obtainStyledAttributes.getInt(index, -1);
                            break;
                        } else {
                            break;
                        }
                    case 21:
                        this.f1446t = obtainStyledAttributes.getDimensionPixelSize(index, this.f1446t);
                        break;
                    case 22:
                        this.f1447u = obtainStyledAttributes.getDimensionPixelSize(index, this.f1447u);
                        break;
                    case 23:
                        this.f1448v = obtainStyledAttributes.getDimensionPixelSize(index, this.f1448v);
                        break;
                    case 24:
                        this.f1449w = obtainStyledAttributes.getDimensionPixelSize(index, this.f1449w);
                        break;
                    case 25:
                        this.f1450x = obtainStyledAttributes.getDimensionPixelSize(index, this.f1450x);
                        break;
                    case 26:
                        this.f1451y = obtainStyledAttributes.getDimensionPixelSize(index, this.f1451y);
                        break;
                    case 27:
                        this.S = obtainStyledAttributes.getBoolean(index, this.S);
                        break;
                    case 28:
                        this.T = obtainStyledAttributes.getBoolean(index, this.T);
                        break;
                    case 29:
                        this.f1452z = obtainStyledAttributes.getFloat(index, this.f1452z);
                        break;
                    case 30:
                        this.A = obtainStyledAttributes.getFloat(index, this.A);
                        break;
                    case 31:
                        int i13 = obtainStyledAttributes.getInt(index, 0);
                        this.H = i13;
                        if (i13 == 1) {
                            Log.e("ConstraintLayout", "layout_constraintWidth_default=\"wrap\" is deprecated.\nUse layout_width=\"WRAP_CONTENT\" and layout_constrainedWidth=\"true\" instead.");
                            break;
                        } else {
                            break;
                        }
                    case 32:
                        int i14 = obtainStyledAttributes.getInt(index, 0);
                        this.I = i14;
                        if (i14 == 1) {
                            Log.e("ConstraintLayout", "layout_constraintHeight_default=\"wrap\" is deprecated.\nUse layout_height=\"WRAP_CONTENT\" and layout_constrainedHeight=\"true\" instead.");
                            break;
                        } else {
                            break;
                        }
                    case 33:
                        try {
                            this.J = obtainStyledAttributes.getDimensionPixelSize(index, this.J);
                            break;
                        } catch (Exception unused) {
                            if (obtainStyledAttributes.getInt(index, this.J) == -2) {
                                this.J = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 34:
                        try {
                            this.L = obtainStyledAttributes.getDimensionPixelSize(index, this.L);
                            break;
                        } catch (Exception unused2) {
                            if (obtainStyledAttributes.getInt(index, this.L) == -2) {
                                this.L = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 35:
                        this.N = Math.max((float) Constant.VOLUME_FLOAT_MIN, obtainStyledAttributes.getFloat(index, this.N));
                        break;
                    case 36:
                        try {
                            this.K = obtainStyledAttributes.getDimensionPixelSize(index, this.K);
                            break;
                        } catch (Exception unused3) {
                            if (obtainStyledAttributes.getInt(index, this.K) == -2) {
                                this.K = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 37:
                        try {
                            this.M = obtainStyledAttributes.getDimensionPixelSize(index, this.M);
                            break;
                        } catch (Exception unused4) {
                            if (obtainStyledAttributes.getInt(index, this.M) == -2) {
                                this.M = -2;
                                break;
                            } else {
                                break;
                            }
                        }
                    case 38:
                        this.O = Math.max((float) Constant.VOLUME_FLOAT_MIN, obtainStyledAttributes.getFloat(index, this.O));
                        break;
                    default:
                        switch (i12) {
                            case 44:
                                String string = obtainStyledAttributes.getString(index);
                                this.B = string;
                                this.C = -1;
                                if (string == null) {
                                    break;
                                } else {
                                    int length = string.length();
                                    int indexOf = this.B.indexOf(44);
                                    if (indexOf <= 0 || indexOf >= length - 1) {
                                        i10 = 0;
                                    } else {
                                        String substring = this.B.substring(0, indexOf);
                                        if (substring.equalsIgnoreCase(AirkanDef.JSON_KEY_W)) {
                                            this.C = 0;
                                        } else if (substring.equalsIgnoreCase("H")) {
                                            this.C = 1;
                                        }
                                        i10 = indexOf + 1;
                                    }
                                    int indexOf2 = this.B.indexOf(58);
                                    if (indexOf2 >= 0 && indexOf2 < length - 1) {
                                        String substring2 = this.B.substring(i10, indexOf2);
                                        String substring3 = this.B.substring(indexOf2 + 1);
                                        if (substring2.length() > 0 && substring3.length() > 0) {
                                            try {
                                                float parseFloat = Float.parseFloat(substring2);
                                                float parseFloat2 = Float.parseFloat(substring3);
                                                if (parseFloat > Constant.VOLUME_FLOAT_MIN && parseFloat2 > Constant.VOLUME_FLOAT_MIN) {
                                                    if (this.C == 1) {
                                                        Math.abs(parseFloat2 / parseFloat);
                                                        break;
                                                    } else {
                                                        Math.abs(parseFloat / parseFloat2);
                                                        break;
                                                    }
                                                }
                                            } catch (NumberFormatException unused5) {
                                                break;
                                            }
                                        }
                                    } else {
                                        String substring4 = this.B.substring(i10);
                                        if (substring4.length() <= 0) {
                                            break;
                                        } else {
                                            Float.parseFloat(substring4);
                                            continue;
                                        }
                                    }
                                }
                                break;
                            case 45:
                                this.D = obtainStyledAttributes.getFloat(index, this.D);
                                continue;
                            case 46:
                                this.E = obtainStyledAttributes.getFloat(index, this.E);
                                continue;
                            case 47:
                                this.F = obtainStyledAttributes.getInt(index, 0);
                                continue;
                            case 48:
                                this.G = obtainStyledAttributes.getInt(index, 0);
                                continue;
                            case 49:
                                this.P = obtainStyledAttributes.getDimensionPixelOffset(index, this.P);
                                continue;
                            case 50:
                                this.Q = obtainStyledAttributes.getDimensionPixelOffset(index, this.Q);
                                continue;
                        }
                }
            }
            obtainStyledAttributes.recycle();
            a();
        }

        public void a() {
            this.X = false;
            this.U = true;
            this.V = true;
            int i10 = ((ViewGroup.MarginLayoutParams) this).width;
            if (i10 == -2 && this.S) {
                this.U = false;
                this.H = 1;
            }
            int i11 = ((ViewGroup.MarginLayoutParams) this).height;
            if (i11 == -2 && this.T) {
                this.V = false;
                this.I = 1;
            }
            if (i10 == 0 || i10 == -1) {
                this.U = false;
                if (i10 == 0 && this.H == 1) {
                    ((ViewGroup.MarginLayoutParams) this).width = -2;
                    this.S = true;
                }
            }
            if (i11 == 0 || i11 == -1) {
                this.V = false;
                if (i11 == 0 && this.I == 1) {
                    ((ViewGroup.MarginLayoutParams) this).height = -2;
                    this.T = true;
                }
            }
            if (this.f1421c != -1.0f || this.f1417a != -1 || this.f1419b != -1) {
                this.X = true;
                this.U = true;
                this.V = true;
                if (!(this.f1437k0 instanceof g)) {
                    this.f1437k0 = new g();
                }
                ((g) this.f1437k0).F(this.R);
            }
        }

        /* JADX WARNING: Removed duplicated region for block: B:14:0x004c  */
        /* JADX WARNING: Removed duplicated region for block: B:17:0x0053  */
        /* JADX WARNING: Removed duplicated region for block: B:20:0x005a  */
        /* JADX WARNING: Removed duplicated region for block: B:23:0x0060  */
        /* JADX WARNING: Removed duplicated region for block: B:26:0x0066  */
        /* JADX WARNING: Removed duplicated region for block: B:33:0x007c  */
        /* JADX WARNING: Removed duplicated region for block: B:34:0x0084  */
        @android.annotation.TargetApi(17)
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public void resolveLayoutDirection(int r7) {
            /*
            // Method dump skipped, instructions count: 265
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.resolveLayoutDirection(int):void");
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }
}
