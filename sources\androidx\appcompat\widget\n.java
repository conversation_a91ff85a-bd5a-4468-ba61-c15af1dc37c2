package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.view.View;
import androidx.appcompat.widget.AppCompatSpinner;
import q.f;

/* compiled from: AppCompatSpinner */
public class n extends x {

    /* renamed from: j  reason: collision with root package name */
    public final /* synthetic */ AppCompatSpinner.d f1147j;

    /* renamed from: k  reason: collision with root package name */
    public final /* synthetic */ AppCompatSpinner f1148k;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public n(AppCompatSpinner appCompatSpinner, View view, AppCompatSpinner.d dVar) {
        super(view);
        this.f1148k = appCompatSpinner;
        this.f1147j = dVar;
    }

    @Override // androidx.appcompat.widget.x
    public f b() {
        return this.f1147j;
    }

    @Override // androidx.appcompat.widget.x
    @SuppressLint({"SyntheticAccessor"})
    public boolean c() {
        if (this.f1148k.getInternalPopup().isShowing()) {
            return true;
        }
        this.f1148k.b();
        return true;
    }
}
