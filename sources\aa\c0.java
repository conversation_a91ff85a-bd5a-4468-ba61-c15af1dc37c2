package aa;

import java.io.IOException;
import java.util.Enumeration;
import java.util.Vector;

/* compiled from: BEROctetString */
public class c0 extends n {

    /* renamed from: b  reason: collision with root package name */
    public n[] f170b;

    /* compiled from: BEROctetString */
    public class a implements Enumeration {

        /* renamed from: a  reason: collision with root package name */
        public int f171a = 0;

        public a() {
        }

        public boolean hasMoreElements() {
            return this.f171a < c0.this.f170b.length;
        }

        @Override // java.util.Enumeration
        public Object nextElement() {
            n[] nVarArr = c0.this.f170b;
            int i10 = this.f171a;
            this.f171a = i10 + 1;
            return nVarArr[i10];
        }
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public c0(aa.n[] r4) {
        /*
            r3 = this;
            java.io.ByteArrayOutputStream r0 = new java.io.ByteArrayOutputStream
            r0.<init>()
            r1 = 0
        L_0x0006:
            int r2 = r4.length
            if (r1 == r2) goto L_0x004e
            r2 = r4[r1]     // Catch:{ ClassCastException -> 0x002d, IOException -> 0x0015 }
            aa.w0 r2 = (aa.w0) r2     // Catch:{ ClassCastException -> 0x002d, IOException -> 0x0015 }
            byte[] r2 = r2.f205a     // Catch:{ ClassCastException -> 0x002d, IOException -> 0x0015 }
            r0.write(r2)     // Catch:{ ClassCastException -> 0x002d, IOException -> 0x0015 }
            int r1 = r1 + 1
            goto L_0x0006
        L_0x0015:
            r4 = move-exception
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r1 = "exception converting octets "
            java.lang.StringBuilder r1 = com.duokan.airkan.server.f.a(r1)
            java.lang.String r4 = r4.toString()
            r1.append(r4)
            java.lang.String r4 = r1.toString()
            r0.<init>(r4)
            throw r0
        L_0x002d:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            r4 = r4[r1]
            java.lang.Class r4 = r4.getClass()
            java.lang.String r4 = r4.getName()
            r2.append(r4)
            java.lang.String r4 = " found in input should only contain DEROctetString"
            r2.append(r4)
            java.lang.String r4 = r2.toString()
            r0.<init>(r4)
            throw r0
        L_0x004e:
            byte[] r0 = r0.toByteArray()
            r3.<init>(r0)
            r3.f170b = r4
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: aa.c0.<init>(aa.n[]):void");
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.c(36);
        pVar.c(128);
        Enumeration p10 = p();
        while (p10.hasMoreElements()) {
            pVar.h((e) p10.nextElement());
        }
        pVar.c(0);
        pVar.c(0);
    }

    @Override // aa.q
    public int i() throws IOException {
        Enumeration p10 = p();
        int i10 = 0;
        while (p10.hasMoreElements()) {
            i10 += ((e) p10.nextElement()).c().i();
        }
        return i10 + 2 + 2;
    }

    @Override // aa.q
    public boolean k() {
        return true;
    }

    @Override // aa.n
    public byte[] o() {
        return this.f205a;
    }

    public Enumeration p() {
        if (this.f170b != null) {
            return new a();
        }
        Vector vector = new Vector();
        int i10 = 0;
        while (true) {
            byte[] bArr = this.f205a;
            if (i10 >= bArr.length) {
                return vector.elements();
            }
            int i11 = i10 + 1000;
            int length = (i11 > bArr.length ? bArr.length : i11) - i10;
            byte[] bArr2 = new byte[length];
            System.arraycopy(bArr, i10, bArr2, 0, length);
            vector.addElement(new w0(bArr2));
            i10 = i11;
        }
    }

    public c0(byte[] bArr) {
        super(bArr);
    }
}
