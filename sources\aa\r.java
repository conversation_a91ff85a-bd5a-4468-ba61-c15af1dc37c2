package aa;

import com.duokan.airkan.server.f;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Vector;
import mb.a;

/* compiled from: ASN1Sequence */
public abstract class r extends q implements Iterable {

    /* renamed from: a  reason: collision with root package name */
    public Vector f220a = new Vector();

    public r() {
    }

    public static r n(Object obj) {
        if (obj == null || (obj instanceof r)) {
            return (r) obj;
        }
        if (obj instanceof s) {
            return n(((s) obj).c());
        }
        if (obj instanceof byte[]) {
            try {
                return n(q.j((byte[]) obj));
            } catch (IOException e10) {
                StringBuilder a10 = f.a("failed to construct sequence from byte[]: ");
                a10.append(e10.getMessage());
                throw new IllegalArgumentException(a10.toString());
            }
        } else {
            if (obj instanceof e) {
                q c10 = ((e) obj).c();
                if (c10 instanceof r) {
                    return (r) c10;
                }
            }
            StringBuilder a11 = f.a("unknown object in getInstance: ");
            a11.append(obj.getClass().getName());
            throw new IllegalArgumentException(a11.toString());
        }
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof r)) {
            return false;
        }
        r rVar = (r) qVar;
        if (size() != rVar.size()) {
            return false;
        }
        Enumeration q10 = q();
        Enumeration q11 = rVar.q();
        while (q10.hasMoreElements()) {
            e o3 = o(q10);
            e o10 = o(q11);
            q c10 = o3.c();
            q c11 = o10.c();
            if (c10 != c11 && !c10.equals(c11)) {
                return false;
            }
        }
        return true;
    }

    @Override // aa.l
    public int hashCode() {
        Enumeration q10 = q();
        int size = size();
        while (q10.hasMoreElements()) {
            size = (size * 17) ^ o(q10).hashCode();
        }
        return size;
    }

    @Override // java.lang.Iterable
    public Iterator<e> iterator() {
        return new a.C0123a(r());
    }

    @Override // aa.q
    public boolean k() {
        return true;
    }

    @Override // aa.q
    public q l() {
        a1 a1Var = new a1();
        a1Var.f220a = this.f220a;
        return a1Var;
    }

    @Override // aa.q
    public q m() {
        m1 m1Var = new m1();
        m1Var.f220a = this.f220a;
        return m1Var;
    }

    public final e o(Enumeration enumeration) {
        return (e) enumeration.nextElement();
    }

    public e p(int i10) {
        return (e) this.f220a.elementAt(i10);
    }

    public Enumeration q() {
        return this.f220a.elements();
    }

    public e[] r() {
        e[] eVarArr = new e[size()];
        for (int i10 = 0; i10 != size(); i10++) {
            eVarArr[i10] = p(i10);
        }
        return eVarArr;
    }

    public int size() {
        return this.f220a.size();
    }

    public String toString() {
        return this.f220a.toString();
    }

    public r(f fVar) {
        for (int i10 = 0; i10 != fVar.b(); i10++) {
            this.f220a.addElement(fVar.a(i10));
        }
    }

    public r(e[] eVarArr) {
        for (int i10 = 0; i10 != eVarArr.length; i10++) {
            this.f220a.addElement(eVarArr[i10]);
        }
    }
}
