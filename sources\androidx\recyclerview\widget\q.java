package androidx.recyclerview.widget;

import android.content.Context;
import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;

/* compiled from: LinearSmoothScroller */
public class q extends RecyclerView.s {

    /* renamed from: i  reason: collision with root package name */
    public final LinearInterpolator f2462i = new LinearInterpolator();

    /* renamed from: j  reason: collision with root package name */
    public final DecelerateInterpolator f2463j = new DecelerateInterpolator();

    /* renamed from: k  reason: collision with root package name */
    public PointF f2464k;

    /* renamed from: l  reason: collision with root package name */
    public final DisplayMetrics f2465l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f2466m = false;

    /* renamed from: n  reason: collision with root package name */
    public float f2467n;

    /* renamed from: o  reason: collision with root package name */
    public int f2468o = 0;

    /* renamed from: p  reason: collision with root package name */
    public int f2469p = 0;

    public q(Context context) {
        this.f2465l = context.getResources().getDisplayMetrics();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.s
    public void c(View view, RecyclerView.t tVar, RecyclerView.s.a aVar) {
        int i10;
        int i11;
        int i12;
        PointF pointF = this.f2464k;
        int i13 = 0;
        int i14 = (pointF == null || pointF.x == Constant.VOLUME_FLOAT_MIN) ? 0 : i12 > 0 ? 1 : -1;
        RecyclerView.j jVar = this.f2234c;
        if (jVar == null || !jVar.e()) {
            i10 = 0;
        } else {
            RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
            i10 = e(jVar.C(view) - ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin, jVar.F(view) + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin, jVar.N(), jVar.f2210n - jVar.O(), i14);
        }
        PointF pointF2 = this.f2464k;
        int i15 = (pointF2 == null || pointF2.y == Constant.VOLUME_FLOAT_MIN) ? 0 : i11 > 0 ? 1 : -1;
        RecyclerView.j jVar2 = this.f2234c;
        if (jVar2 != null && jVar2.f()) {
            RecyclerView.LayoutParams layoutParams2 = (RecyclerView.LayoutParams) view.getLayoutParams();
            i13 = e(jVar2.G(view) - ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin, jVar2.A(view) + ((ViewGroup.MarginLayoutParams) layoutParams2).bottomMargin, jVar2.P(), jVar2.f2211o - jVar2.M(), i15);
        }
        int g10 = g((int) Math.sqrt((double) ((i13 * i13) + (i10 * i10))));
        if (g10 > 0) {
            aVar.b(-i10, -i13, g10, this.f2463j);
        }
    }

    public int e(int i10, int i11, int i12, int i13, int i14) {
        if (i14 == -1) {
            return i12 - i10;
        }
        if (i14 == 0) {
            int i15 = i12 - i10;
            if (i15 > 0) {
                return i15;
            }
            int i16 = i13 - i11;
            if (i16 < 0) {
                return i16;
            }
            return 0;
        } else if (i14 == 1) {
            return i13 - i11;
        } else {
            throw new IllegalArgumentException("snap preference should be one of the constants defined in SmoothScroller, starting with SNAP_");
        }
    }

    public float f(DisplayMetrics displayMetrics) {
        return 25.0f / ((float) displayMetrics.densityDpi);
    }

    public int g(int i10) {
        return (int) Math.ceil(((double) h(i10)) / 0.3356d);
    }

    public int h(int i10) {
        float abs = (float) Math.abs(i10);
        if (!this.f2466m) {
            this.f2467n = f(this.f2465l);
            this.f2466m = true;
        }
        return (int) Math.ceil((double) (abs * this.f2467n));
    }
}
