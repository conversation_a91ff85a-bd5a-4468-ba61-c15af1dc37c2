package androidx.recyclerview.widget;

import android.view.View;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class ViewBoundsCheck {

    /* renamed from: a  reason: collision with root package name */
    public final b f2325a;

    /* renamed from: b  reason: collision with root package name */
    public a f2326b = new a();

    @Retention(RetentionPolicy.SOURCE)
    public @interface ViewBounds {
    }

    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public int f2327a = 0;

        /* renamed from: b  reason: collision with root package name */
        public int f2328b;

        /* renamed from: c  reason: collision with root package name */
        public int f2329c;

        /* renamed from: d  reason: collision with root package name */
        public int f2330d;

        /* renamed from: e  reason: collision with root package name */
        public int f2331e;

        public boolean a() {
            int i10 = this.f2327a;
            if ((i10 & 7) != 0 && (i10 & (b(this.f2330d, this.f2328b) << 0)) == 0) {
                return false;
            }
            int i11 = this.f2327a;
            if ((i11 & 112) != 0 && (i11 & (b(this.f2330d, this.f2329c) << 4)) == 0) {
                return false;
            }
            int i12 = this.f2327a;
            if ((i12 & 1792) != 0 && (i12 & (b(this.f2331e, this.f2328b) << 8)) == 0) {
                return false;
            }
            int i13 = this.f2327a;
            if ((i13 & 28672) == 0 || (i13 & (b(this.f2331e, this.f2329c) << 12)) != 0) {
                return true;
            }
            return false;
        }

        public int b(int i10, int i11) {
            if (i10 > i11) {
                return 1;
            }
            return i10 == i11 ? 2 : 4;
        }
    }

    public interface b {
        int a();

        int b(View view);

        View c(int i10);

        int d();

        int e(View view);
    }

    public ViewBoundsCheck(b bVar) {
        this.f2325a = bVar;
    }

    public View a(int i10, int i11, int i12, int i13) {
        int d10 = this.f2325a.d();
        int a10 = this.f2325a.a();
        int i14 = i11 > i10 ? 1 : -1;
        View view = null;
        while (i10 != i11) {
            View c10 = this.f2325a.c(i10);
            int b10 = this.f2325a.b(c10);
            int e10 = this.f2325a.e(c10);
            a aVar = this.f2326b;
            aVar.f2328b = d10;
            aVar.f2329c = a10;
            aVar.f2330d = b10;
            aVar.f2331e = e10;
            if (i12 != 0) {
                aVar.f2327a = 0;
                aVar.f2327a = i12 | 0;
                if (aVar.a()) {
                    return c10;
                }
            }
            if (i13 != 0) {
                a aVar2 = this.f2326b;
                aVar2.f2327a = 0;
                aVar2.f2327a = i13 | 0;
                if (aVar2.a()) {
                    view = c10;
                }
            }
            i10 += i14;
        }
        return view;
    }

    public boolean b(View view, int i10) {
        a aVar = this.f2326b;
        int d10 = this.f2325a.d();
        int a10 = this.f2325a.a();
        int b10 = this.f2325a.b(view);
        int e10 = this.f2325a.e(view);
        aVar.f2328b = d10;
        aVar.f2329c = a10;
        aVar.f2330d = b10;
        aVar.f2331e = e10;
        if (i10 == 0) {
            return false;
        }
        a aVar2 = this.f2326b;
        aVar2.f2327a = 0;
        aVar2.f2327a = 0 | i10;
        return aVar2.a();
    }
}
