syntax = "proto3";

package com.xiaomi.idm.service.ipcameral.proto;

option java_package = "com.xiaomi.idm.service.ipcameral.proto";
option java_outer_classname = "IPCameraServiceProto";

  message GetIpcSkeletonInfo {
  int32 aid = 1;
  string appId = 2;
  string serviceToken = 3;
}
  message IPCResponse {
  int32 code = 1;
  string message = 2;
  string response = 3;
}
  message SkeletonEvent {
  bytes bytes = 1;
}