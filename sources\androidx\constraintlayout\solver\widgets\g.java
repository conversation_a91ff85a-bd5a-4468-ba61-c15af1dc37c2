package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.SolverVariable;
import androidx.constraintlayout.solver.b;
import androidx.constraintlayout.solver.c;
import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;

/* compiled from: Guideline */
public class g extends ConstraintWidget {

    /* renamed from: i0  reason: collision with root package name */
    public float f1370i0 = -1.0f;

    /* renamed from: j0  reason: collision with root package name */
    public int f1371j0 = -1;

    /* renamed from: k0  reason: collision with root package name */
    public int f1372k0 = -1;

    /* renamed from: l0  reason: collision with root package name */
    public ConstraintAnchor f1373l0 = this.f1315t;

    /* renamed from: m0  reason: collision with root package name */
    public int f1374m0;

    /* compiled from: Guideline */
    public static /* synthetic */ class a {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int[] f1375a;

        /* JADX WARNING: Can't wrap try/catch for region: R(18:0|1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|(3:17|18|20)) */
        /* JADX WARNING: Failed to process nested try/catch */
        /* JADX WARNING: Missing exception handler attribute for start block: B:11:0x003e */
        /* JADX WARNING: Missing exception handler attribute for start block: B:13:0x0049 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:15:0x0054 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:17:0x0060 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:3:0x0012 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:5:0x001d */
        /* JADX WARNING: Missing exception handler attribute for start block: B:7:0x0028 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:9:0x0033 */
        static {
            /*
            // Method dump skipped, instructions count: 109
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.g.a.<clinit>():void");
        }
    }

    public g() {
        this.f1374m0 = 0;
        this.B.clear();
        this.B.add(this.f1373l0);
        int length = this.A.length;
        for (int i10 = 0; i10 < length; i10++) {
            this.A[i10] = this.f1373l0;
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void E(c cVar) {
        if (this.D != null) {
            int o3 = cVar.o(this.f1373l0);
            if (this.f1374m0 == 1) {
                this.I = o3;
                this.J = 0;
                w(this.D.h());
                C(0);
                return;
            }
            this.I = 0;
            this.J = o3;
            C(this.D.n());
            w(0);
        }
    }

    public void F(int i10) {
        if (this.f1374m0 != i10) {
            this.f1374m0 = i10;
            this.B.clear();
            if (this.f1374m0 == 1) {
                this.f1373l0 = this.f1314s;
            } else {
                this.f1373l0 = this.f1315t;
            }
            this.B.add(this.f1373l0);
            int length = this.A.length;
            for (int i11 = 0; i11 < length; i11++) {
                this.A[i11] = this.f1373l0;
            }
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void a(c cVar) {
        e eVar = (e) this.D;
        if (eVar != null) {
            ConstraintAnchor f10 = eVar.f(ConstraintAnchor.Type.LEFT);
            ConstraintAnchor f11 = eVar.f(ConstraintAnchor.Type.RIGHT);
            ConstraintWidget constraintWidget = this.D;
            boolean z10 = true;
            boolean z11 = constraintWidget != null && constraintWidget.C[0] == ConstraintWidget.DimensionBehaviour.WRAP_CONTENT;
            if (this.f1374m0 == 0) {
                f10 = eVar.f(ConstraintAnchor.Type.TOP);
                f11 = eVar.f(ConstraintAnchor.Type.BOTTOM);
                ConstraintWidget constraintWidget2 = this.D;
                if (constraintWidget2 == null || constraintWidget2.C[1] != ConstraintWidget.DimensionBehaviour.WRAP_CONTENT) {
                    z10 = false;
                }
                z11 = z10;
            }
            if (this.f1371j0 != -1) {
                SolverVariable l10 = cVar.l(this.f1373l0);
                cVar.d(l10, cVar.l(f10), this.f1371j0, 6);
                if (z11) {
                    cVar.f(cVar.l(f11), l10, 0, 5);
                }
            } else if (this.f1372k0 != -1) {
                SolverVariable l11 = cVar.l(this.f1373l0);
                SolverVariable l12 = cVar.l(f11);
                cVar.d(l11, l12, -this.f1372k0, 6);
                if (z11) {
                    cVar.f(l11, cVar.l(f10), 0, 5);
                    cVar.f(l12, l11, 0, 5);
                }
            } else if (this.f1370i0 != -1.0f) {
                SolverVariable l13 = cVar.l(this.f1373l0);
                SolverVariable l14 = cVar.l(f10);
                SolverVariable l15 = cVar.l(f11);
                float f12 = this.f1370i0;
                b m10 = cVar.m();
                m10.f1260c.h(l13, -1.0f);
                m10.f1260c.h(l14, 1.0f - f12);
                m10.f1260c.h(l15, f12);
                cVar.c(m10);
            }
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public boolean b() {
        return true;
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void c(int i10) {
        ConstraintWidget constraintWidget = this.D;
        if (constraintWidget != null) {
            if (this.f1374m0 == 1) {
                this.f1315t.f1276a.f(1, constraintWidget.f1315t.f1276a, 0);
                this.f1317v.f1276a.f(1, constraintWidget.f1315t.f1276a, 0);
                int i11 = this.f1371j0;
                if (i11 != -1) {
                    this.f1314s.f1276a.f(1, constraintWidget.f1314s.f1276a, i11);
                    this.f1316u.f1276a.f(1, constraintWidget.f1314s.f1276a, this.f1371j0);
                    return;
                }
                int i12 = this.f1372k0;
                if (i12 != -1) {
                    this.f1314s.f1276a.f(1, constraintWidget.f1316u.f1276a, -i12);
                    this.f1316u.f1276a.f(1, constraintWidget.f1316u.f1276a, -this.f1372k0);
                } else if (this.f1370i0 != -1.0f && constraintWidget.i() == ConstraintWidget.DimensionBehaviour.FIXED) {
                    int i13 = (int) (((float) constraintWidget.E) * this.f1370i0);
                    this.f1314s.f1276a.f(1, constraintWidget.f1314s.f1276a, i13);
                    this.f1316u.f1276a.f(1, constraintWidget.f1314s.f1276a, i13);
                }
            } else {
                this.f1314s.f1276a.f(1, constraintWidget.f1314s.f1276a, 0);
                this.f1316u.f1276a.f(1, constraintWidget.f1314s.f1276a, 0);
                int i14 = this.f1371j0;
                if (i14 != -1) {
                    this.f1315t.f1276a.f(1, constraintWidget.f1315t.f1276a, i14);
                    this.f1317v.f1276a.f(1, constraintWidget.f1315t.f1276a, this.f1371j0);
                    return;
                }
                int i15 = this.f1372k0;
                if (i15 != -1) {
                    this.f1315t.f1276a.f(1, constraintWidget.f1317v.f1276a, -i15);
                    this.f1317v.f1276a.f(1, constraintWidget.f1317v.f1276a, -this.f1372k0);
                } else if (this.f1370i0 != -1.0f && constraintWidget.m() == ConstraintWidget.DimensionBehaviour.FIXED) {
                    int i16 = (int) (((float) constraintWidget.F) * this.f1370i0);
                    this.f1315t.f1276a.f(1, constraintWidget.f1315t.f1276a, i16);
                    this.f1317v.f1276a.f(1, constraintWidget.f1315t.f1276a, i16);
                }
            }
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public ConstraintAnchor f(ConstraintAnchor.Type type) {
        switch (a.f1375a[type.ordinal()]) {
            case 1:
            case 2:
                if (this.f1374m0 == 1) {
                    return this.f1373l0;
                }
                break;
            case 3:
            case 4:
                if (this.f1374m0 == 0) {
                    return this.f1373l0;
                }
                break;
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
                return null;
        }
        throw new AssertionError(type.name());
    }
}
