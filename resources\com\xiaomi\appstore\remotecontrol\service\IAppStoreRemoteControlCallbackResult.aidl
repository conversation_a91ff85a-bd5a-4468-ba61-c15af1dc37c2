package com.xiaomi.appstore.remotecontrol.service;

import com.xiaomi.appstore.remotecontrol.service.AppStoreRemoteControlProgress;

/*
 * public class AppStoreRemoteControlProgress {
 *  public int status;
 *  public int reason;
 *  public int currBytes;
 *  public int totalBytes;
 * }
 * status:��
 * STATUS_WAITING     = 0
 * STATUS_CONNECTING  = 1
 * STATUS_PENDING     = 2
 * STATUS_DOWNLOADING = 3
 * STATUS_PAUSED      = 4
 * STATUS_VERIFYING   = 5
 * STATUS_INSTALLING  = 6
 * STATUS_TASK_FAIL   = 7
 * STATUS_TASK_SUCCESS= 8
*/

interface IAppStoreRemoteControlCallbackResult {

	void onProgressUpdate(in long appId, in AppStoreRemoteControlProgress progress);
	void onOtherStoreAppProgressUpdate(in String appUrl, in AppStoreRemoteControlProgress progress);
}