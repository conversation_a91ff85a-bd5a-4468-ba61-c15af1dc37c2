package androidx.appcompat.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewPropertyAnimator;
import android.view.Window;
import android.view.WindowInsets;
import android.widget.OverScroller;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$id;
import androidx.appcompat.app.p;
import androidx.appcompat.view.menu.h;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.g;
import j0.h;
import j0.i;
import j0.m;
import j0.q;
import java.util.Objects;
import java.util.WeakHashMap;

@SuppressLint({"UnknownNullness"})
@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ActionBarOverlayLayout extends ViewGroup implements s, g, h {

    /* renamed from: r0  reason: collision with root package name */
    public static final int[] f712r0 = {R$attr.actionBarSize, 16842841};

    /* renamed from: a  reason: collision with root package name */
    public int f713a;

    /* renamed from: b  reason: collision with root package name */
    public int f714b = 0;

    /* renamed from: c  reason: collision with root package name */
    public ContentFrameLayout f715c;

    /* renamed from: d  reason: collision with root package name */
    public ActionBarContainer f716d;

    /* renamed from: e  reason: collision with root package name */
    public t f717e;

    /* renamed from: f  reason: collision with root package name */
    public Drawable f718f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f719g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f720i;

    /* renamed from: j  reason: collision with root package name */
    public boolean f721j;

    /* renamed from: k  reason: collision with root package name */
    public boolean f722k;

    /* renamed from: l  reason: collision with root package name */
    public int f723l;

    /* renamed from: m  reason: collision with root package name */
    public int f724m;

    /* renamed from: m0  reason: collision with root package name */
    public ViewPropertyAnimator f725m0;

    /* renamed from: n  reason: collision with root package name */
    public final Rect f726n = new Rect();

    /* renamed from: n0  reason: collision with root package name */
    public final AnimatorListenerAdapter f727n0;

    /* renamed from: o  reason: collision with root package name */
    public final Rect f728o = new Rect();

    /* renamed from: o0  reason: collision with root package name */
    public final Runnable f729o0;

    /* renamed from: p  reason: collision with root package name */
    public final Rect f730p = new Rect();

    /* renamed from: p0  reason: collision with root package name */
    public final Runnable f731p0;
    @NonNull

    /* renamed from: q  reason: collision with root package name */
    public q f732q;

    /* renamed from: q0  reason: collision with root package name */
    public final i f733q0;
    @NonNull

    /* renamed from: r  reason: collision with root package name */
    public q f734r;
    @NonNull

    /* renamed from: s  reason: collision with root package name */
    public q f735s;
    @NonNull

    /* renamed from: t  reason: collision with root package name */
    public q f736t;

    /* renamed from: x  reason: collision with root package name */
    public d f737x;

    /* renamed from: y  reason: collision with root package name */
    public OverScroller f738y;

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {
        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    public class a extends AnimatorListenerAdapter {
        public a() {
        }

        public void onAnimationCancel(Animator animator) {
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f725m0 = null;
            actionBarOverlayLayout.f722k = false;
        }

        public void onAnimationEnd(Animator animator) {
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f725m0 = null;
            actionBarOverlayLayout.f722k = false;
        }
    }

    public class b implements Runnable {
        public b() {
        }

        public void run() {
            ActionBarOverlayLayout.this.q();
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f725m0 = actionBarOverlayLayout.f716d.animate().translationY(Constant.VOLUME_FLOAT_MIN).setListener(ActionBarOverlayLayout.this.f727n0);
        }
    }

    public class c implements Runnable {
        public c() {
        }

        public void run() {
            ActionBarOverlayLayout.this.q();
            ActionBarOverlayLayout actionBarOverlayLayout = ActionBarOverlayLayout.this;
            actionBarOverlayLayout.f725m0 = actionBarOverlayLayout.f716d.animate().translationY((float) (-ActionBarOverlayLayout.this.f716d.getHeight())).setListener(ActionBarOverlayLayout.this.f727n0);
        }
    }

    public interface d {
    }

    public ActionBarOverlayLayout(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        super(context, attributeSet);
        new Rect();
        new Rect();
        new Rect();
        new Rect();
        q qVar = q.f6919b;
        this.f732q = qVar;
        this.f734r = qVar;
        this.f735s = qVar;
        this.f736t = qVar;
        this.f727n0 = new a();
        this.f729o0 = new b();
        this.f731p0 = new c();
        r(context);
        this.f733q0 = new i();
    }

    @Override // androidx.appcompat.widget.s
    public void a(Menu menu, h.a aVar) {
        s();
        this.f717e.a(menu, aVar);
    }

    @Override // androidx.appcompat.widget.s
    public boolean b() {
        s();
        return this.f717e.b();
    }

    @Override // androidx.appcompat.widget.s
    public void c() {
        s();
        this.f717e.c();
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    @Override // androidx.appcompat.widget.s
    public boolean d() {
        s();
        return this.f717e.d();
    }

    public void draw(Canvas canvas) {
        int i10;
        super.draw(canvas);
        if (this.f718f != null && !this.f719g) {
            if (this.f716d.getVisibility() == 0) {
                i10 = (int) (this.f716d.getTranslationY() + ((float) this.f716d.getBottom()) + 0.5f);
            } else {
                i10 = 0;
            }
            this.f718f.setBounds(0, i10, getWidth(), this.f718f.getIntrinsicHeight() + i10);
            this.f718f.draw(canvas);
        }
    }

    @Override // androidx.appcompat.widget.s
    public boolean e() {
        s();
        return this.f717e.e();
    }

    @Override // androidx.appcompat.widget.s
    public boolean f() {
        s();
        return this.f717e.f();
    }

    public boolean fitSystemWindows(Rect rect) {
        return super.fitSystemWindows(rect);
    }

    @Override // androidx.appcompat.widget.s
    public boolean g() {
        s();
        return this.f717e.g();
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-1, -1);
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    public int getActionBarHideOffset() {
        ActionBarContainer actionBarContainer = this.f716d;
        if (actionBarContainer != null) {
            return -((int) actionBarContainer.getTranslationY());
        }
        return 0;
    }

    public int getNestedScrollAxes() {
        return this.f733q0.a();
    }

    public CharSequence getTitle() {
        s();
        return this.f717e.getTitle();
    }

    @Override // j0.g
    public void h(View view, View view2, int i10, int i11) {
        if (i11 == 0) {
            onNestedScrollAccepted(view, view2, i10);
        }
    }

    @Override // j0.g
    public void i(View view, int i10) {
        if (i10 == 0) {
            onStopNestedScroll(view);
        }
    }

    @Override // j0.g
    public void j(View view, int i10, int i11, int[] iArr, int i12) {
        if (i12 == 0) {
            onNestedPreScroll(view, i10, i11, iArr);
        }
    }

    @Override // androidx.appcompat.widget.s
    public void k(int i10) {
        s();
        if (i10 == 2) {
            this.f717e.t();
        } else if (i10 == 5) {
            this.f717e.u();
        } else if (i10 == 109) {
            setOverlayMode(true);
        }
    }

    @Override // androidx.appcompat.widget.s
    public void l() {
        s();
        this.f717e.h();
    }

    @Override // j0.h
    public void m(View view, int i10, int i11, int i12, int i13, int i14, int[] iArr) {
        if (i14 == 0) {
            onNestedScroll(view, i10, i11, i12, i13);
        }
    }

    @Override // j0.g
    public void n(View view, int i10, int i11, int i12, int i13, int i14) {
        if (i14 == 0) {
            onNestedScroll(view, i10, i11, i12, i13);
        }
    }

    @Override // j0.g
    public boolean o(View view, View view2, int i10, int i11) {
        return i11 == 0 && onStartNestedScroll(view, view2, i10);
    }

    @RequiresApi(21)
    public WindowInsets onApplyWindowInsets(@NonNull WindowInsets windowInsets) {
        s();
        Objects.requireNonNull(windowInsets);
        q qVar = new q(windowInsets);
        boolean p10 = p(this.f716d, new Rect(qVar.c(), qVar.e(), qVar.d(), qVar.b()), true, true, false, true);
        Rect rect = this.f726n;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        ViewCompat.b.a(this, qVar, rect);
        Rect rect2 = this.f726n;
        q h6 = qVar.f6920a.h(rect2.left, rect2.top, rect2.right, rect2.bottom);
        this.f732q = h6;
        boolean z10 = true;
        if (!this.f734r.equals(h6)) {
            this.f734r = this.f732q;
            p10 = true;
        }
        if (!this.f728o.equals(this.f726n)) {
            this.f728o.set(this.f726n);
        } else {
            z10 = p10;
        }
        if (z10) {
            requestLayout();
        }
        return qVar.f6920a.a().a().f6920a.b().j();
    }

    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        r(getContext());
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        requestApplyInsets();
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        q();
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        int childCount = getChildCount();
        int paddingLeft = getPaddingLeft();
        int paddingTop = getPaddingTop();
        for (int i14 = 0; i14 < childCount; i14++) {
            View childAt = getChildAt(i14);
            if (childAt.getVisibility() != 8) {
                LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
                int measuredWidth = childAt.getMeasuredWidth();
                int measuredHeight = childAt.getMeasuredHeight();
                int i15 = ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin + paddingLeft;
                int i16 = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin + paddingTop;
                childAt.layout(i15, i16, measuredWidth + i15, measuredHeight + i16);
            }
        }
    }

    public void onMeasure(int i10, int i11) {
        int i12;
        q.c cVar;
        s();
        measureChildWithMargins(this.f716d, i10, 0, i11, 0);
        LayoutParams layoutParams = (LayoutParams) this.f716d.getLayoutParams();
        int max = Math.max(0, this.f716d.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin);
        int max2 = Math.max(0, this.f716d.getMeasuredHeight() + ((ViewGroup.MarginLayoutParams) layoutParams).topMargin + ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin);
        int combineMeasuredStates = View.combineMeasuredStates(0, this.f716d.getMeasuredState());
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        boolean z10 = (getWindowSystemUiVisibility() & 256) != 0;
        if (z10) {
            i12 = this.f713a;
            if (this.f720i && this.f716d.getTabContainer() != null) {
                i12 += this.f713a;
            }
        } else {
            i12 = this.f716d.getVisibility() != 8 ? this.f716d.getMeasuredHeight() : 0;
        }
        this.f730p.set(this.f726n);
        q qVar = this.f732q;
        this.f735s = qVar;
        if (this.h || z10) {
            b0.c a10 = b0.c.a(qVar.c(), this.f735s.e() + i12, this.f735s.d(), this.f735s.b() + 0);
            q qVar2 = this.f735s;
            if (Build.VERSION.SDK_INT >= 29) {
                cVar = new q.b(qVar2);
            } else {
                cVar = new q.a(qVar2);
            }
            cVar.c(a10);
            this.f735s = cVar.a();
        } else {
            Rect rect = this.f730p;
            rect.top += i12;
            rect.bottom += 0;
            this.f735s = qVar.f6920a.h(0, i12, 0, 0);
        }
        p(this.f715c, this.f730p, true, true, true, true);
        if (!this.f736t.equals(this.f735s)) {
            q qVar3 = this.f735s;
            this.f736t = qVar3;
            ViewCompat.b(this.f715c, qVar3);
        }
        measureChildWithMargins(this.f715c, i10, 0, i11, 0);
        LayoutParams layoutParams2 = (LayoutParams) this.f715c.getLayoutParams();
        int max3 = Math.max(max, this.f715c.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) layoutParams2).leftMargin + ((ViewGroup.MarginLayoutParams) layoutParams2).rightMargin);
        int max4 = Math.max(max2, this.f715c.getMeasuredHeight() + ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin + ((ViewGroup.MarginLayoutParams) layoutParams2).bottomMargin);
        int combineMeasuredStates2 = View.combineMeasuredStates(combineMeasuredStates, this.f715c.getMeasuredState());
        setMeasuredDimension(View.resolveSizeAndState(Math.max(getPaddingRight() + getPaddingLeft() + max3, getSuggestedMinimumWidth()), i10, combineMeasuredStates2), View.resolveSizeAndState(Math.max(getPaddingBottom() + getPaddingTop() + max4, getSuggestedMinimumHeight()), i11, combineMeasuredStates2 << 16));
    }

    public boolean onNestedFling(View view, float f10, float f11, boolean z10) {
        boolean z11 = false;
        if (!this.f721j || !z10) {
            return false;
        }
        this.f738y.fling(0, 0, 0, (int) f11, 0, 0, EventResultPersister.GENERATE_NEW_ID, Reader.READ_DONE);
        if (this.f738y.getFinalY() > this.f716d.getHeight()) {
            z11 = true;
        }
        if (z11) {
            q();
            this.f731p0.run();
        } else {
            q();
            this.f729o0.run();
        }
        this.f722k = true;
        return true;
    }

    public boolean onNestedPreFling(View view, float f10, float f11) {
        return false;
    }

    public void onNestedPreScroll(View view, int i10, int i11, int[] iArr) {
    }

    public void onNestedScroll(View view, int i10, int i11, int i12, int i13) {
        int i14 = this.f723l + i11;
        this.f723l = i14;
        setActionBarHideOffset(i14);
    }

    public void onNestedScrollAccepted(View view, View view2, int i10) {
        p pVar;
        p.h hVar;
        this.f733q0.f6907a = i10;
        this.f723l = getActionBarHideOffset();
        q();
        d dVar = this.f737x;
        if (dVar != null && (hVar = (pVar = (p) dVar).f501u) != null) {
            hVar.a();
            pVar.f501u = null;
        }
    }

    public boolean onStartNestedScroll(View view, View view2, int i10) {
        if ((i10 & 2) == 0 || this.f716d.getVisibility() != 0) {
            return false;
        }
        return this.f721j;
    }

    public void onStopNestedScroll(View view) {
        if (this.f721j && !this.f722k) {
            if (this.f723l <= this.f716d.getHeight()) {
                q();
                postDelayed(this.f729o0, 600);
            } else {
                q();
                postDelayed(this.f731p0, 600);
            }
        }
        d dVar = this.f737x;
        if (dVar != null) {
            Objects.requireNonNull(dVar);
        }
    }

    public void onWindowSystemUiVisibilityChanged(int i10) {
        super.onWindowSystemUiVisibilityChanged(i10);
        s();
        int i11 = this.f724m ^ i10;
        this.f724m = i10;
        boolean z10 = (i10 & 4) == 0;
        boolean z11 = (i10 & 256) != 0;
        d dVar = this.f737x;
        if (dVar != null) {
            ((p) dVar).f496p = !z11;
            if (z10 || !z11) {
                p pVar = (p) dVar;
                if (pVar.f498r) {
                    pVar.f498r = false;
                    pVar.t(true);
                }
            } else {
                p pVar2 = (p) dVar;
                if (!pVar2.f498r) {
                    pVar2.f498r = true;
                    pVar2.t(true);
                }
            }
        }
        if ((i11 & 256) != 0 && this.f737x != null) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            requestApplyInsets();
        }
    }

    public void onWindowVisibilityChanged(int i10) {
        super.onWindowVisibilityChanged(i10);
        this.f714b = i10;
        d dVar = this.f737x;
        if (dVar != null) {
            ((p) dVar).f495o = i10;
        }
    }

    public final boolean p(@NonNull View view, @NonNull Rect rect, boolean z10, boolean z11, boolean z12, boolean z13) {
        boolean z14;
        int i10;
        int i11;
        int i12;
        int i13;
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        if (!z10 || ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin == (i13 = rect.left)) {
            z14 = false;
        } else {
            ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = i13;
            z14 = true;
        }
        if (z11 && ((ViewGroup.MarginLayoutParams) layoutParams).topMargin != (i12 = rect.top)) {
            ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = i12;
            z14 = true;
        }
        if (z13 && ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin != (i11 = rect.right)) {
            ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin = i11;
            z14 = true;
        }
        if (!z12 || ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin == (i10 = rect.bottom)) {
            return z14;
        }
        ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin = i10;
        return true;
    }

    public void q() {
        removeCallbacks(this.f729o0);
        removeCallbacks(this.f731p0);
        ViewPropertyAnimator viewPropertyAnimator = this.f725m0;
        if (viewPropertyAnimator != null) {
            viewPropertyAnimator.cancel();
        }
    }

    public final void r(Context context) {
        TypedArray obtainStyledAttributes = getContext().getTheme().obtainStyledAttributes(f712r0);
        boolean z10 = false;
        this.f713a = obtainStyledAttributes.getDimensionPixelSize(0, 0);
        Drawable drawable = obtainStyledAttributes.getDrawable(1);
        this.f718f = drawable;
        setWillNotDraw(drawable == null);
        obtainStyledAttributes.recycle();
        if (context.getApplicationInfo().targetSdkVersion < 19) {
            z10 = true;
        }
        this.f719g = z10;
        this.f738y = new OverScroller(context);
    }

    public void s() {
        t tVar;
        if (this.f715c == null) {
            this.f715c = (ContentFrameLayout) findViewById(R$id.action_bar_activity_content);
            this.f716d = (ActionBarContainer) findViewById(R$id.action_bar_container);
            View findViewById = findViewById(R$id.action_bar);
            if (findViewById instanceof t) {
                tVar = (t) findViewById;
            } else if (findViewById instanceof Toolbar) {
                tVar = ((Toolbar) findViewById).getWrapper();
            } else {
                StringBuilder a10 = f.a("Can't make a decor toolbar out of ");
                a10.append(findViewById.getClass().getSimpleName());
                throw new IllegalStateException(a10.toString());
            }
            this.f717e = tVar;
        }
    }

    public void setActionBarHideOffset(int i10) {
        q();
        this.f716d.setTranslationY((float) (-Math.max(0, Math.min(i10, this.f716d.getHeight()))));
    }

    public void setActionBarVisibilityCallback(d dVar) {
        this.f737x = dVar;
        if (getWindowToken() != null) {
            ((p) this.f737x).f495o = this.f714b;
            int i10 = this.f724m;
            if (i10 != 0) {
                onWindowSystemUiVisibilityChanged(i10);
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                requestApplyInsets();
            }
        }
    }

    public void setHasNonEmbeddedTabs(boolean z10) {
        this.f720i = z10;
    }

    public void setHideOnContentScrollEnabled(boolean z10) {
        if (z10 != this.f721j) {
            this.f721j = z10;
            if (!z10) {
                q();
                setActionBarHideOffset(0);
            }
        }
    }

    public void setIcon(int i10) {
        s();
        this.f717e.setIcon(i10);
    }

    public void setLogo(int i10) {
        s();
        this.f717e.q(i10);
    }

    public void setOverlayMode(boolean z10) {
        this.h = z10;
        this.f719g = z10 && getContext().getApplicationInfo().targetSdkVersion < 19;
    }

    public void setShowingForActionMode(boolean z10) {
    }

    public void setUiOptions(int i10) {
    }

    @Override // androidx.appcompat.widget.s
    public void setWindowCallback(Window.Callback callback) {
        s();
        this.f717e.setWindowCallback(callback);
    }

    @Override // androidx.appcompat.widget.s
    public void setWindowTitle(CharSequence charSequence) {
        s();
        this.f717e.setWindowTitle(charSequence);
    }

    public boolean shouldDelayChildPressedState() {
        return false;
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new LayoutParams(layoutParams);
    }

    public void setIcon(Drawable drawable) {
        s();
        this.f717e.setIcon(drawable);
    }
}
