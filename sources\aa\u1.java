package aa;

import java.io.InputStream;

/* compiled from: LimitedInputStream */
public abstract class u1 extends InputStream {

    /* renamed from: a  reason: collision with root package name */
    public final InputStream f235a;

    /* renamed from: b  reason: collision with root package name */
    public int f236b;

    public u1(InputStream inputStream, int i10) {
        this.f235a = inputStream;
        this.f236b = i10;
    }

    public int a() {
        return this.f236b;
    }

    public void d(boolean z10) {
        InputStream inputStream = this.f235a;
        if (inputStream instanceof r1) {
            r1 r1Var = (r1) inputStream;
            r1Var.f225f = z10;
            r1Var.e();
        }
    }
}
