package androidx.core.view;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;
import androidx.annotation.RequiresApi;
import androidx.annotation.RestrictTo;
import androidx.core.R$id;
import j0.m;
import java.util.Collections;
import java.util.List;
import java.util.WeakHashMap;
import k0.b;
import k0.c;

/* compiled from: AccessibilityDelegateCompat */
public class a {

    /* renamed from: c  reason: collision with root package name */
    public static final View.AccessibilityDelegate f1597c = new View.AccessibilityDelegate();

    /* renamed from: a  reason: collision with root package name */
    public final View.AccessibilityDelegate f1598a;

    /* renamed from: b  reason: collision with root package name */
    public final View.AccessibilityDelegate f1599b;

    /* renamed from: androidx.core.view.a$a  reason: collision with other inner class name */
    /* compiled from: AccessibilityDelegateCompat */
    public static final class C0011a extends View.AccessibilityDelegate {

        /* renamed from: a  reason: collision with root package name */
        public final a f1600a;

        public C0011a(a aVar) {
            this.f1600a = aVar;
        }

        public boolean dispatchPopulateAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            return this.f1600a.a(view, accessibilityEvent);
        }

        @RequiresApi(16)
        public AccessibilityNodeProvider getAccessibilityNodeProvider(View view) {
            c b10 = this.f1600a.b(view);
            if (b10 != null) {
                return (AccessibilityNodeProvider) b10.f7286a;
            }
            return null;
        }

        public void onInitializeAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            this.f1600a.c(view, accessibilityEvent);
        }

        public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfo accessibilityNodeInfo) {
            b bVar = new b(accessibilityNodeInfo);
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            Boolean valueOf = Boolean.valueOf(view.isScreenReaderFocusable());
            accessibilityNodeInfo.setScreenReaderFocusable(valueOf == null ? false : valueOf.booleanValue());
            Boolean valueOf2 = Boolean.valueOf(view.isAccessibilityHeading());
            accessibilityNodeInfo.setHeading(valueOf2 == null ? false : valueOf2.booleanValue());
            accessibilityNodeInfo.setPaneTitle(view.getAccessibilityPaneTitle());
            this.f1600a.d(view, bVar);
            accessibilityNodeInfo.getText();
            List list = (List) view.getTag(R$id.tag_accessibility_actions);
            if (list == null) {
                list = Collections.emptyList();
            }
            for (int i10 = 0; i10 < list.size(); i10++) {
                bVar.a((b.a) list.get(i10));
            }
        }

        public void onPopulateAccessibilityEvent(View view, AccessibilityEvent accessibilityEvent) {
            this.f1600a.e(view, accessibilityEvent);
        }

        public boolean onRequestSendAccessibilityEvent(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
            return this.f1600a.f(viewGroup, view, accessibilityEvent);
        }

        public boolean performAccessibilityAction(View view, int i10, Bundle bundle) {
            return this.f1600a.g(view, i10, bundle);
        }

        public void sendAccessibilityEvent(View view, int i10) {
            this.f1600a.h(view, i10);
        }

        public void sendAccessibilityEventUnchecked(View view, AccessibilityEvent accessibilityEvent) {
            this.f1600a.i(view, accessibilityEvent);
        }
    }

    public a() {
        this.f1598a = f1597c;
        this.f1599b = new C0011a(this);
    }

    public boolean a(View view, AccessibilityEvent accessibilityEvent) {
        return this.f1598a.dispatchPopulateAccessibilityEvent(view, accessibilityEvent);
    }

    public c b(View view) {
        AccessibilityNodeProvider accessibilityNodeProvider = this.f1598a.getAccessibilityNodeProvider(view);
        if (accessibilityNodeProvider != null) {
            return new c(accessibilityNodeProvider);
        }
        return null;
    }

    public void c(View view, AccessibilityEvent accessibilityEvent) {
        this.f1598a.onInitializeAccessibilityEvent(view, accessibilityEvent);
    }

    public void d(View view, b bVar) {
        this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
    }

    public void e(View view, AccessibilityEvent accessibilityEvent) {
        this.f1598a.onPopulateAccessibilityEvent(view, accessibilityEvent);
    }

    public boolean f(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
        return this.f1598a.onRequestSendAccessibilityEvent(viewGroup, view, accessibilityEvent);
    }

    /* JADX WARNING: Removed duplicated region for block: B:29:0x0072  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean g(android.view.View r9, int r10, android.os.Bundle r11) {
        /*
        // Method dump skipped, instructions count: 200
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.view.a.g(android.view.View, int, android.os.Bundle):boolean");
    }

    public void h(View view, int i10) {
        this.f1598a.sendAccessibilityEvent(view, i10);
    }

    public void i(View view, AccessibilityEvent accessibilityEvent) {
        this.f1598a.sendAccessibilityEventUnchecked(view, accessibilityEvent);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public a(View.AccessibilityDelegate accessibilityDelegate) {
        this.f1598a = accessibilityDelegate;
        this.f1599b = new C0011a(this);
    }
}
