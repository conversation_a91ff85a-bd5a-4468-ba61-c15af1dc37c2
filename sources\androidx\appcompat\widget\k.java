package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import m.a;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: AppCompatImageHelper */
public class k {
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final ImageView f1130a;

    /* renamed from: b  reason: collision with root package name */
    public k0 f1131b;

    public k(@NonNull ImageView imageView) {
        this.f1130a = imageView;
    }

    public void a() {
        k0 k0Var;
        Drawable drawable = this.f1130a.getDrawable();
        if (drawable != null) {
            int[] iArr = u.f1200a;
        }
        if (drawable != null && (k0Var = this.f1131b) != null) {
            g.f(drawable, k0Var, this.f1130a.getDrawableState());
        }
    }

    public void b(AttributeSet attributeSet, int i10) {
        int m10;
        Context context = this.f1130a.getContext();
        int[] iArr = R$styleable.AppCompatImageView;
        m0 r10 = m0.r(context, attributeSet, iArr, i10, 0);
        ImageView imageView = this.f1130a;
        ViewCompat.j(imageView, imageView.getContext(), iArr, attributeSet, r10.f1145b, i10, 0);
        try {
            Drawable drawable = this.f1130a.getDrawable();
            if (!(drawable != null || (m10 = r10.m(R$styleable.AppCompatImageView_srcCompat, -1)) == -1 || (drawable = a.a(this.f1130a.getContext(), m10)) == null)) {
                this.f1130a.setImageDrawable(drawable);
            }
            if (drawable != null) {
                int[] iArr2 = u.f1200a;
            }
            int i11 = R$styleable.AppCompatImageView_tint;
            if (r10.p(i11)) {
                this.f1130a.setImageTintList(r10.c(i11));
            }
            int i12 = R$styleable.AppCompatImageView_tintMode;
            if (r10.p(i12)) {
                this.f1130a.setImageTintMode(u.c(r10.j(i12, -1), null));
            }
            r10.f1145b.recycle();
        } catch (Throwable th) {
            r10.f1145b.recycle();
            throw th;
        }
    }

    public void c(int i10) {
        if (i10 != 0) {
            Drawable a10 = a.a(this.f1130a.getContext(), i10);
            if (a10 != null) {
                int[] iArr = u.f1200a;
            }
            this.f1130a.setImageDrawable(a10);
        } else {
            this.f1130a.setImageDrawable(null);
        }
        a();
    }

    public void d(ColorStateList colorStateList) {
        if (this.f1131b == null) {
            this.f1131b = new k0();
        }
        k0 k0Var = this.f1131b;
        k0Var.f1132a = colorStateList;
        k0Var.f1135d = true;
        a();
    }

    public void e(PorterDuff.Mode mode) {
        if (this.f1131b == null) {
            this.f1131b = new k0();
        }
        k0 k0Var = this.f1131b;
        k0Var.f1133b = mode;
        k0Var.f1134c = true;
        a();
    }
}
