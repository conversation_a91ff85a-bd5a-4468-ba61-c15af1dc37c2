package androidx.appcompat.app;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewStub;
import android.view.Window;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$styleable;
import androidx.core.widget.NestedScrollView;
import java.lang.ref.WeakReference;

public class AlertController {
    public NestedScrollView A;
    public int B = 0;
    public Drawable C;
    public ImageView D;
    public TextView E;
    public TextView F;
    public View G;
    public ListAdapter H;
    public int I = -1;
    public int J;
    public int K;
    public int L;
    public int M;
    public int N;
    public int O;
    public boolean P;
    public Handler Q;
    public final View.OnClickListener R = new a();

    /* renamed from: a  reason: collision with root package name */
    public final Context f329a;

    /* renamed from: b  reason: collision with root package name */
    public final k f330b;

    /* renamed from: c  reason: collision with root package name */
    public final Window f331c;

    /* renamed from: d  reason: collision with root package name */
    public final int f332d;

    /* renamed from: e  reason: collision with root package name */
    public CharSequence f333e;

    /* renamed from: f  reason: collision with root package name */
    public CharSequence f334f;

    /* renamed from: g  reason: collision with root package name */
    public ListView f335g;
    public View h;

    /* renamed from: i  reason: collision with root package name */
    public int f336i;

    /* renamed from: j  reason: collision with root package name */
    public int f337j;

    /* renamed from: k  reason: collision with root package name */
    public int f338k;

    /* renamed from: l  reason: collision with root package name */
    public int f339l;

    /* renamed from: m  reason: collision with root package name */
    public int f340m;

    /* renamed from: n  reason: collision with root package name */
    public boolean f341n = false;

    /* renamed from: o  reason: collision with root package name */
    public Button f342o;

    /* renamed from: p  reason: collision with root package name */
    public CharSequence f343p;

    /* renamed from: q  reason: collision with root package name */
    public Message f344q;

    /* renamed from: r  reason: collision with root package name */
    public Drawable f345r;

    /* renamed from: s  reason: collision with root package name */
    public Button f346s;

    /* renamed from: t  reason: collision with root package name */
    public CharSequence f347t;

    /* renamed from: u  reason: collision with root package name */
    public Message f348u;

    /* renamed from: v  reason: collision with root package name */
    public Drawable f349v;

    /* renamed from: w  reason: collision with root package name */
    public Button f350w;

    /* renamed from: x  reason: collision with root package name */
    public CharSequence f351x;

    /* renamed from: y  reason: collision with root package name */
    public Message f352y;

    /* renamed from: z  reason: collision with root package name */
    public Drawable f353z;

    public static class RecycleListView extends ListView {

        /* renamed from: a  reason: collision with root package name */
        public final int f354a;

        /* renamed from: b  reason: collision with root package name */
        public final int f355b;

        public RecycleListView(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.RecycleListView);
            this.f355b = obtainStyledAttributes.getDimensionPixelOffset(R$styleable.RecycleListView_paddingBottomNoButtons, -1);
            this.f354a = obtainStyledAttributes.getDimensionPixelOffset(R$styleable.RecycleListView_paddingTopNoTitle, -1);
        }
    }

    public class a implements View.OnClickListener {
        public a() {
        }

        public void onClick(View view) {
            Message message;
            Message message2;
            Message message3;
            Message message4;
            AlertController alertController = AlertController.this;
            if (view == alertController.f342o && (message4 = alertController.f344q) != null) {
                message = Message.obtain(message4);
            } else if (view != alertController.f346s || (message3 = alertController.f348u) == null) {
                message = (view != alertController.f350w || (message2 = alertController.f352y) == null) ? null : Message.obtain(message2);
            } else {
                message = Message.obtain(message3);
            }
            if (message != null) {
                message.sendToTarget();
            }
            AlertController alertController2 = AlertController.this;
            alertController2.Q.obtainMessage(1, alertController2.f330b).sendToTarget();
        }
    }

    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public final Context f357a;

        /* renamed from: b  reason: collision with root package name */
        public final LayoutInflater f358b;

        /* renamed from: c  reason: collision with root package name */
        public Drawable f359c;

        /* renamed from: d  reason: collision with root package name */
        public CharSequence f360d;

        /* renamed from: e  reason: collision with root package name */
        public View f361e;

        /* renamed from: f  reason: collision with root package name */
        public DialogInterface.OnKeyListener f362f;

        /* renamed from: g  reason: collision with root package name */
        public ListAdapter f363g;
        public DialogInterface.OnClickListener h;

        /* renamed from: i  reason: collision with root package name */
        public boolean f364i;

        /* renamed from: j  reason: collision with root package name */
        public int f365j = -1;

        public b(Context context) {
            this.f357a = context;
            this.f358b = (LayoutInflater) context.getSystemService("layout_inflater");
        }
    }

    public static final class c extends Handler {

        /* renamed from: a  reason: collision with root package name */
        public WeakReference<DialogInterface> f366a;

        public c(DialogInterface dialogInterface) {
            this.f366a = new WeakReference<>(dialogInterface);
        }

        public void handleMessage(Message message) {
            int i10 = message.what;
            if (i10 == -3 || i10 == -2 || i10 == -1) {
                ((DialogInterface.OnClickListener) message.obj).onClick(this.f366a.get(), message.what);
            } else if (i10 == 1) {
                ((DialogInterface) message.obj).dismiss();
            }
        }
    }

    public static class d extends ArrayAdapter<CharSequence> {
        public d(Context context, int i10, int i11, CharSequence[] charSequenceArr) {
            super(context, i10, i11, (Object[]) null);
        }

        public long getItemId(int i10) {
            return (long) i10;
        }

        public boolean hasStableIds() {
            return true;
        }
    }

    public AlertController(Context context, k kVar, Window window) {
        this.f329a = context;
        this.f330b = kVar;
        this.f331c = window;
        this.Q = new c(kVar);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(null, R$styleable.AlertDialog, R$attr.alertDialogStyle, 0);
        this.J = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_android_layout, 0);
        this.K = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_buttonPanelSideLayout, 0);
        this.L = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_listLayout, 0);
        this.M = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_multiChoiceItemLayout, 0);
        this.N = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_singleChoiceItemLayout, 0);
        this.O = obtainStyledAttributes.getResourceId(R$styleable.AlertDialog_listItemLayout, 0);
        this.P = obtainStyledAttributes.getBoolean(R$styleable.AlertDialog_showTitle, true);
        this.f332d = obtainStyledAttributes.getDimensionPixelSize(R$styleable.AlertDialog_buttonIconDimen, 0);
        obtainStyledAttributes.recycle();
        kVar.a().u(1);
    }

    public static boolean a(View view) {
        if (view.onCheckIsTextEditor()) {
            return true;
        }
        if (!(view instanceof ViewGroup)) {
            return false;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        while (childCount > 0) {
            childCount--;
            if (a(viewGroup.getChildAt(childCount))) {
                return true;
            }
        }
        return false;
    }

    public static void c(View view, View view2, View view3) {
        int i10 = 0;
        if (view2 != null) {
            view2.setVisibility(view.canScrollVertically(-1) ? 0 : 4);
        }
        if (view3 != null) {
            if (!view.canScrollVertically(1)) {
                i10 = 4;
            }
            view3.setVisibility(i10);
        }
    }

    public final void b(Button button) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) button.getLayoutParams();
        layoutParams.gravity = 1;
        layoutParams.weight = 0.5f;
        button.setLayoutParams(layoutParams);
    }

    @Nullable
    public final ViewGroup d(@Nullable View view, @Nullable View view2) {
        if (view == null) {
            if (view2 instanceof ViewStub) {
                view2 = ((ViewStub) view2).inflate();
            }
            return (ViewGroup) view2;
        }
        if (view2 != null) {
            ViewParent parent = view2.getParent();
            if (parent instanceof ViewGroup) {
                ((ViewGroup) parent).removeView(view2);
            }
        }
        if (view instanceof ViewStub) {
            view = ((ViewStub) view).inflate();
        }
        return (ViewGroup) view;
    }
}
