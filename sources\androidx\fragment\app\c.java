package androidx.fragment.app;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.b;
import androidx.fragment.app.t0;

/* compiled from: DefaultSpecialEffectsController */
public class c extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ViewGroup f1857a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ View f1858b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ boolean f1859c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ t0.b f1860d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ b.C0013b f1861e;

    public c(b bVar, ViewGroup viewGroup, View view, boolean z10, t0.b bVar2, b.C0013b bVar3) {
        this.f1857a = viewGroup;
        this.f1858b = view;
        this.f1859c = z10;
        this.f1860d = bVar2;
        this.f1861e = bVar3;
    }

    public void onAnimationEnd(Animator animator) {
        this.f1857a.endViewTransition(this.f1858b);
        if (this.f1859c) {
            x0.a(this.f1860d.f2006a, this.f1858b);
        }
        this.f1861e.a();
    }
}
