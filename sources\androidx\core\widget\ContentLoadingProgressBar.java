package androidx.core.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.Objects;

public class ContentLoadingProgressBar extends ProgressBar {

    /* renamed from: a  reason: collision with root package name */
    public final Runnable f1601a = new a();

    /* renamed from: b  reason: collision with root package name */
    public final Runnable f1602b = new b();

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            Objects.requireNonNull(ContentLoadingProgressBar.this);
            Objects.requireNonNull(ContentLoadingProgressBar.this);
            ContentLoadingProgressBar.this.setVisibility(8);
        }
    }

    public class b implements Runnable {
        public b() {
        }

        public void run() {
            Objects.requireNonNull(ContentLoadingProgressBar.this);
            Objects.requireNonNull(ContentLoadingProgressBar.this);
            ContentLoadingProgressBar contentLoadingProgressBar = ContentLoadingProgressBar.this;
            System.currentTimeMillis();
            Objects.requireNonNull(contentLoadingProgressBar);
            ContentLoadingProgressBar.this.setVisibility(0);
        }
    }

    public ContentLoadingProgressBar(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        super(context, attributeSet, 0);
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        removeCallbacks(this.f1601a);
        removeCallbacks(this.f1602b);
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(this.f1601a);
        removeCallbacks(this.f1602b);
    }
}
