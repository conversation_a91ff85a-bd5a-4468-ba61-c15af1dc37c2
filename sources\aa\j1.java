package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERVisibleString */
public class j1 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f194a;

    public j1(byte[] bArr) {
        this.f194a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof j1)) {
            return false;
        }
        return a.a(this.f194a, ((j1) qVar).f194a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f194a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(26, this.f194a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f194a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f194a.length) + 1 + this.f194a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
