package androidx.appcompat.widget;

import android.animation.ArgbEvaluator;
import android.view.View;
import android.view.ViewParent;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import com.duokan.airkan.common.Constant;
import java.io.File;
import k6.c;
import m8.k;
import o9.b;
import q6.d;
import q6.h;
import s0.q;
import s6.a;

/* compiled from: AppCompatHintHelper */
public class j implements k, b {

    /* renamed from: a  reason: collision with root package name */
    public static final int[] f1126a = {-1, 0, 0, -1, -2, -1, -1, -1, -1, -1, -1, -1};

    /* renamed from: b  reason: collision with root package name */
    public static final int[] f1127b = {1, -2, 0, 2, 0, -2, 0, 2, 1, 0, 0, 0, -2, 1, 0, -2, -3, -1, -1, -1, -1, -1, -1, -1};

    /* renamed from: c  reason: collision with root package name */
    public static final int[] f1128c = {-1, 1, -1, -3, -1, 1, -1, -3, -2, -1, -1, -1, 1, -2, -1, 1, 2};

    public /* synthetic */ j(Object obj) {
    }

    public static void b(int[] iArr, int[] iArr2, int[] iArr3) {
        if (q.b(12, iArr, iArr2, iArr3) != 0 || (iArr3[11] == -1 && q.e0(12, iArr3, f1126a))) {
            c(iArr3);
        }
    }

    public static void c(int[] iArr) {
        long j10 = (((long) iArr[0]) & 4294967295L) + 1;
        iArr[0] = (int) j10;
        long j11 = ((((long) iArr[1]) & 4294967295L) - 1) + (j10 >> 32);
        iArr[1] = (int) j11;
        long j12 = j11 >> 32;
        if (j12 != 0) {
            long j13 = j12 + (((long) iArr[2]) & 4294967295L);
            iArr[2] = (int) j13;
            j12 = j13 >> 32;
        }
        long j14 = (((long) iArr[3]) & 4294967295L) + 1 + j12;
        iArr[3] = (int) j14;
        long j15 = (4294967295L & ((long) iArr[4])) + 1 + (j14 >> 32);
        iArr[4] = (int) j15;
        if ((j15 >> 32) != 0) {
            q.m0(12, iArr, 5);
        }
    }

    public static double d(c cVar, q6.b bVar, double d10) {
        float f10;
        if (bVar instanceof d) {
            return (double) ((d) bVar).a((float) d10);
        }
        double signum = Math.signum(d10);
        double abs = Math.abs(d10);
        if (abs == 1000000.0d) {
            ArgbEvaluator argbEvaluator = a.f10193a;
            if (bVar == h.f9777i) {
                bVar = h.f9780l;
            } else if (bVar == h.f9778j) {
                bVar = h.f9779k;
            } else if (!(bVar == h.f9780l || bVar == h.f9779k)) {
                bVar = null;
            }
            if (bVar == null) {
                f10 = Constant.VOLUME_FLOAT_MIN;
            } else {
                f10 = cVar.h(bVar);
            }
            return signum * ((double) f10);
        }
        double d11 = bVar instanceof q6.c ? (double) cVar.d((q6.c) bVar) : (double) cVar.h(bVar);
        if (abs == 1000100.0d) {
            d11 *= signum;
        }
        return d11;
    }

    public static double e(c cVar, q6.b bVar, double d10) {
        if (d10 == 2.147483647E9d) {
            return (double) cVar.d((q6.c) bVar);
        }
        if (d10 == 3.4028234663852886E38d) {
            return (double) cVar.h(bVar);
        }
        return d(cVar, bVar, d10);
    }

    public static boolean f(o6.c cVar) {
        if (g(cVar.f9217f.f9053j)) {
            return false;
        }
        n6.b bVar = cVar.f9217f;
        bVar.f9052i = bVar.f9053j;
        cVar.f9217f.f9053j = Double.MAX_VALUE;
        return true;
    }

    public static boolean g(double d10) {
        return d10 == Double.MAX_VALUE || d10 == 3.4028234663852886E38d || d10 == 2.147483647E9d;
    }

    public static void h(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] iArr4 = new int[24];
        q.U0(iArr, iArr2, iArr4);
        k(iArr4, iArr3);
    }

    public static InputConnection i(InputConnection inputConnection, EditorInfo editorInfo, View view) {
        if (inputConnection != null && editorInfo.hintText == null) {
            ViewParent parent = view.getParent();
            while (true) {
                if (!(parent instanceof View)) {
                    break;
                } else if (parent instanceof r0) {
                    editorInfo.hintText = ((r0) parent).a();
                    break;
                } else {
                    parent = parent.getParent();
                }
            }
        }
        return inputConnection;
    }

    /* JADX WARNING: Failed to process nested try/catch */
    /* JADX WARNING: Missing exception handler attribute for start block: B:14:0x0026 */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0057 A[SYNTHETIC, Splitter:B:28:0x0057] */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x005c A[SYNTHETIC, Splitter:B:32:0x005c] */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x0066 A[SYNTHETIC, Splitter:B:40:0x0066] */
    /* JADX WARNING: Removed duplicated region for block: B:44:0x006b A[SYNTHETIC, Splitter:B:44:0x006b] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static java.lang.String j(java.lang.String r7) {
        /*
        // Method dump skipped, instructions count: 111
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.j.j(java.lang.String):java.lang.String");
    }

    public static void k(int[] iArr, int[] iArr2) {
        long j10 = ((long) iArr[16]) & 4294967295L;
        long j11 = ((long) iArr[17]) & 4294967295L;
        long j12 = ((long) iArr[18]) & 4294967295L;
        long j13 = ((long) iArr[19]) & 4294967295L;
        long j14 = ((long) iArr[20]) & 4294967295L;
        long j15 = ((long) iArr[21]) & 4294967295L;
        long j16 = ((long) iArr[22]) & 4294967295L;
        long j17 = ((long) iArr[23]) & 4294967295L;
        long j18 = ((((long) iArr[12]) & 4294967295L) + j14) - 1;
        long j19 = (((long) iArr[13]) & 4294967295L) + j16;
        long j20 = (((long) iArr[14]) & 4294967295L) + j16 + j17;
        long j21 = (((long) iArr[15]) & 4294967295L) + j17;
        long j22 = j11 + j15;
        long j23 = j15 - j17;
        long j24 = j16 - j17;
        long j25 = (((long) iArr[0]) & 4294967295L) + j18 + j23 + 0;
        iArr2[0] = (int) j25;
        long j26 = (((((long) iArr[1]) & 4294967295L) + j17) - j18) + j19 + (j25 >> 32);
        iArr2[1] = (int) j26;
        long j27 = (((((long) iArr[2]) & 4294967295L) - j15) - j19) + j20 + (j26 >> 32);
        iArr2[2] = (int) j27;
        long j28 = (((((long) iArr[3]) & 4294967295L) + j18) - j20) + j21 + j23 + (j27 >> 32);
        iArr2[3] = (int) j28;
        long j29 = ((((((((long) iArr[4]) & 4294967295L) + j10) + j15) + j18) + j19) - j21) + j23 + (j28 >> 32);
        iArr2[4] = (int) j29;
        long j30 = ((((long) iArr[5]) & 4294967295L) - j10) + j19 + j20 + j22 + (j29 >> 32);
        iArr2[5] = (int) j30;
        long j31 = (((((long) iArr[6]) & 4294967295L) + j12) - j11) + j20 + j21 + (j30 >> 32);
        iArr2[6] = (int) j31;
        long j32 = ((((((long) iArr[7]) & 4294967295L) + j10) + j13) - j12) + j21 + (j31 >> 32);
        iArr2[7] = (int) j32;
        long j33 = (((((((long) iArr[8]) & 4294967295L) + j10) + j11) + j14) - j13) + (j32 >> 32);
        iArr2[8] = (int) j33;
        long j34 = (((((long) iArr[9]) & 4294967295L) + j12) - j14) + j22 + (j33 >> 32);
        iArr2[9] = (int) j34;
        long j35 = ((((((long) iArr[10]) & 4294967295L) + j12) + j13) - j23) + j24 + (j34 >> 32);
        iArr2[10] = (int) j35;
        long j36 = ((((((long) iArr[11]) & 4294967295L) + j13) + j14) - j24) + (j35 >> 32);
        iArr2[11] = (int) j36;
        l((int) ((j36 >> 32) + 1), iArr2);
    }

    public static void l(int i10, int[] iArr) {
        long j10;
        if (i10 != 0) {
            long j11 = ((long) i10) & 4294967295L;
            long j12 = (((long) iArr[0]) & 4294967295L) + j11 + 0;
            iArr[0] = (int) j12;
            long j13 = ((((long) iArr[1]) & 4294967295L) - j11) + (j12 >> 32);
            iArr[1] = (int) j13;
            long j14 = j13 >> 32;
            if (j14 != 0) {
                long j15 = j14 + (((long) iArr[2]) & 4294967295L);
                iArr[2] = (int) j15;
                j14 = j15 >> 32;
            }
            long j16 = (((long) iArr[3]) & 4294967295L) + j11 + j14;
            iArr[3] = (int) j16;
            long j17 = (4294967295L & ((long) iArr[4])) + j11 + (j16 >> 32);
            iArr[4] = (int) j17;
            j10 = j17 >> 32;
        } else {
            j10 = 0;
        }
        if ((j10 != 0 && q.m0(12, iArr, 5) != 0) || (iArr[11] == -1 && q.e0(12, iArr, f1126a))) {
            c(iArr);
        }
    }

    public static int m(int i10) {
        if (i10 > 1073741824) {
            throw new IllegalArgumentException(h.a("There is no larger power of 2 int for value:", i10, " since it exceeds 2^31."));
        } else if (i10 >= 0) {
            return 1 << (32 - Integer.numberOfLeadingZeros(i10 - 1));
        } else {
            throw new IllegalArgumentException(h.a("Given value:", i10, ". Expecting value >= 0."));
        }
    }

    public static void n(int[] iArr, int[] iArr2) {
        int[] iArr3 = new int[24];
        q.A1(iArr, iArr3);
        k(iArr3, iArr2);
    }

    public static void o(int[] iArr, int i10, int[] iArr2) {
        int[] iArr3 = new int[24];
        q.A1(iArr, iArr3);
        k(iArr3, iArr2);
        while (true) {
            i10--;
            if (i10 > 0) {
                q.A1(iArr2, iArr3);
                k(iArr3, iArr2);
            } else {
                return;
            }
        }
    }

    public static void p(int[] iArr, int[] iArr2, int[] iArr3) {
        if (q.B1(12, iArr, iArr2, iArr3) != 0) {
            long j10 = (((long) iArr3[0]) & 4294967295L) - 1;
            iArr3[0] = (int) j10;
            long j11 = (((long) iArr3[1]) & 4294967295L) + 1 + (j10 >> 32);
            iArr3[1] = (int) j11;
            long j12 = j11 >> 32;
            if (j12 != 0) {
                long j13 = j12 + (((long) iArr3[2]) & 4294967295L);
                iArr3[2] = (int) j13;
                j12 = j13 >> 32;
            }
            long j14 = ((((long) iArr3[3]) & 4294967295L) - 1) + j12;
            iArr3[3] = (int) j14;
            long j15 = ((4294967295L & ((long) iArr3[4])) - 1) + (j14 >> 32);
            iArr3[4] = (int) j15;
            if ((j15 >> 32) != 0) {
                q.C(12, iArr3, 5);
            }
        }
    }

    @Override // o9.b
    public void a(File file) {
        StringBuffer a10 = i.a("Cannot read file : ");
        a10.append(file.getName());
        throw new RuntimeException(a10.toString());
    }
}
