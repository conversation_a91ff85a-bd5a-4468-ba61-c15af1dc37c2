package aa;

import androidx.activity.result.c;
import com.duokan.airkan.server.f;
import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/* compiled from: ASN1ObjectIdentifier */
public class m extends q {

    /* renamed from: c  reason: collision with root package name */
    public static final Map f198c = new HashMap();

    /* renamed from: a  reason: collision with root package name */
    public final String f199a;

    /* renamed from: b  reason: collision with root package name */
    public byte[] f200b;

    /* compiled from: ASN1ObjectIdentifier */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public int f201a;

        /* renamed from: b  reason: collision with root package name */
        public final byte[] f202b;

        public a(byte[] bArr) {
            this.f201a = mb.a.d(bArr);
            this.f202b = bArr;
        }

        public boolean equals(Object obj) {
            if (obj instanceof a) {
                return mb.a.a(this.f202b, ((a) obj).f202b);
            }
            return false;
        }

        public int hashCode() {
            return this.f201a;
        }
    }

    public m(byte[] bArr) {
        StringBuffer stringBuffer = new StringBuffer();
        boolean z10 = true;
        long j10 = 0;
        BigInteger bigInteger = null;
        for (int i10 = 0; i10 != bArr.length; i10++) {
            int i11 = bArr[i10] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP;
            if (j10 <= 72057594037927808L) {
                long j11 = j10 + ((long) (i11 & 127));
                if ((i11 & 128) == 0) {
                    if (z10) {
                        if (j11 < 40) {
                            stringBuffer.append('0');
                        } else if (j11 < 80) {
                            stringBuffer.append('1');
                            j11 -= 40;
                        } else {
                            stringBuffer.append('2');
                            j11 -= 80;
                        }
                        z10 = false;
                    }
                    stringBuffer.append('.');
                    stringBuffer.append(j11);
                    j10 = 0;
                } else {
                    j10 = j11 << 7;
                }
            } else {
                BigInteger or = (bigInteger == null ? BigInteger.valueOf(j10) : bigInteger).or(BigInteger.valueOf((long) (i11 & 127)));
                if ((i11 & 128) == 0) {
                    if (z10) {
                        stringBuffer.append('2');
                        or = or.subtract(BigInteger.valueOf(80));
                        z10 = false;
                    }
                    stringBuffer.append('.');
                    stringBuffer.append(or);
                    j10 = 0;
                    bigInteger = null;
                } else {
                    bigInteger = or.shiftLeft(7);
                }
            }
        }
        this.f199a = stringBuffer.toString();
        this.f200b = mb.a.c(bArr);
    }

    public static m o(byte[] bArr) {
        a aVar = new a(bArr);
        Map map = f198c;
        synchronized (map) {
            m mVar = (m) ((HashMap) map).get(aVar);
            if (mVar != null) {
                return mVar;
            }
            return new m(bArr);
        }
    }

    public static m q(Object obj) {
        if (obj == null || (obj instanceof m)) {
            return (m) obj;
        }
        if (obj instanceof e) {
            e eVar = (e) obj;
            if (eVar.c() instanceof m) {
                return (m) eVar.c();
            }
        }
        if (obj instanceof byte[]) {
            try {
                return (m) q.j((byte[]) obj);
            } catch (IOException e10) {
                StringBuilder a10 = f.a("failed to construct object identifier from byte[]: ");
                a10.append(e10.getMessage());
                throw new IllegalArgumentException(a10.toString());
            }
        } else {
            StringBuilder a11 = f.a("illegal object in getInstance: ");
            a11.append(obj.getClass().getName());
            throw new IllegalArgumentException(a11.toString());
        }
    }

    public static boolean s(String str, int i10) {
        boolean z10;
        char charAt;
        int length = str.length();
        do {
            z10 = false;
            while (true) {
                length--;
                if (length < i10) {
                    return z10;
                }
                charAt = str.charAt(length);
                if ('0' <= charAt && charAt <= '9') {
                    z10 = true;
                }
            }
            if (charAt != '.') {
                break;
            }
        } while (z10);
        return false;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (qVar == this) {
            return true;
        }
        if (!(qVar instanceof m)) {
            return false;
        }
        return this.f199a.equals(((m) qVar).f199a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        byte[] p10 = p();
        pVar.c(6);
        pVar.g(p10.length);
        pVar.f212a.write(p10);
    }

    @Override // aa.l
    public int hashCode() {
        return this.f199a.hashCode();
    }

    @Override // aa.q
    public int i() throws IOException {
        int length = p().length;
        return v1.a(length) + 1 + length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public final void n(ByteArrayOutputStream byteArrayOutputStream) {
        int i10;
        String str;
        int i11;
        String str2;
        String str3;
        String str4 = this.f199a;
        int indexOf = str4.indexOf(46, 0);
        if (indexOf == -1) {
            str = str4.substring(0);
            i10 = -1;
        } else {
            i10 = indexOf + 1;
            str = str4.substring(0, indexOf);
        }
        int parseInt = Integer.parseInt(str) * 40;
        if (i10 == -1) {
            i11 = i10;
            str2 = null;
        } else {
            int indexOf2 = str4.indexOf(46, i10);
            if (indexOf2 == -1) {
                str2 = str4.substring(i10);
                i11 = -1;
            } else {
                str2 = str4.substring(i10, indexOf2);
                i11 = indexOf2 + 1;
            }
        }
        if (str2.length() <= 18) {
            t(byteArrayOutputStream, ((long) parseInt) + Long.parseLong(str2));
        } else {
            u(byteArrayOutputStream, new BigInteger(str2).add(BigInteger.valueOf((long) parseInt)));
        }
        while (true) {
            if (i11 != -1) {
                if (i11 == -1) {
                    str3 = null;
                } else {
                    int indexOf3 = str4.indexOf(46, i11);
                    if (indexOf3 == -1) {
                        str3 = str4.substring(i11);
                        i11 = -1;
                    } else {
                        String substring = str4.substring(i11, indexOf3);
                        i11 = indexOf3 + 1;
                        str3 = substring;
                    }
                }
                if (str3.length() <= 18) {
                    t(byteArrayOutputStream, Long.parseLong(str3));
                } else {
                    u(byteArrayOutputStream, new BigInteger(str3));
                }
            } else {
                return;
            }
        }
    }

    public final synchronized byte[] p() {
        if (this.f200b == null) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            n(byteArrayOutputStream);
            this.f200b = byteArrayOutputStream.toByteArray();
        }
        return this.f200b;
    }

    public m r() {
        Map map = f198c;
        synchronized (map) {
            a aVar = new a(p());
            m mVar = (m) ((HashMap) map).get(aVar);
            if (mVar != null) {
                return mVar;
            }
            ((HashMap) map).put(aVar, this);
            return this;
        }
    }

    public final void t(ByteArrayOutputStream byteArrayOutputStream, long j10) {
        byte[] bArr = new byte[9];
        int i10 = 8;
        bArr[8] = (byte) (((int) j10) & 127);
        while (j10 >= 128) {
            j10 >>= 7;
            i10--;
            bArr[i10] = (byte) ((((int) j10) & 127) | 128);
        }
        byteArrayOutputStream.write(bArr, i10, 9 - i10);
    }

    public String toString() {
        return this.f199a;
    }

    public final void u(ByteArrayOutputStream byteArrayOutputStream, BigInteger bigInteger) {
        int bitLength = (bigInteger.bitLength() + 6) / 7;
        if (bitLength == 0) {
            byteArrayOutputStream.write(0);
            return;
        }
        byte[] bArr = new byte[bitLength];
        int i10 = bitLength - 1;
        for (int i11 = i10; i11 >= 0; i11--) {
            bArr[i11] = (byte) ((bigInteger.intValue() & 127) | 128);
            bigInteger = bigInteger.shiftRight(7);
        }
        bArr[i10] = (byte) (bArr[i10] & Byte.MAX_VALUE);
        byteArrayOutputStream.write(bArr, 0, bitLength);
    }

    public m(String str) {
        char charAt;
        if (str != null) {
            boolean z10 = false;
            if (str.length() >= 3 && str.charAt(1) == '.' && (charAt = str.charAt(0)) >= '0' && charAt <= '2') {
                z10 = s(str, 2);
            }
            if (z10) {
                this.f199a = str;
                return;
            }
            throw new IllegalArgumentException(c.a("string ", str, " not an OID"));
        }
        throw new IllegalArgumentException("'identifier' cannot be null");
    }

    public m(m mVar, String str) {
        if (s(str, 0)) {
            this.f199a = mVar.f199a + "." + str;
            return;
        }
        throw new IllegalArgumentException(c.a("string ", str, " not a valid OID branch"));
    }
}
