package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.WeakHashMap;

/* compiled from: AppCompatBackgroundHelper */
public class e {
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final View f1075a;

    /* renamed from: b  reason: collision with root package name */
    public final g f1076b;

    /* renamed from: c  reason: collision with root package name */
    public int f1077c = -1;

    /* renamed from: d  reason: collision with root package name */
    public k0 f1078d;

    /* renamed from: e  reason: collision with root package name */
    public k0 f1079e;

    /* renamed from: f  reason: collision with root package name */
    public k0 f1080f;

    public e(@NonNull View view) {
        this.f1075a = view;
        this.f1076b = g.a();
    }

    public void a() {
        Drawable background = this.f1075a.getBackground();
        if (background != null) {
            boolean z10 = true;
            if (this.f1078d != null) {
                if (this.f1080f == null) {
                    this.f1080f = new k0();
                }
                k0 k0Var = this.f1080f;
                k0Var.f1132a = null;
                k0Var.f1135d = false;
                k0Var.f1133b = null;
                k0Var.f1134c = false;
                View view = this.f1075a;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                ColorStateList backgroundTintList = view.getBackgroundTintList();
                if (backgroundTintList != null) {
                    k0Var.f1135d = true;
                    k0Var.f1132a = backgroundTintList;
                }
                PorterDuff.Mode backgroundTintMode = this.f1075a.getBackgroundTintMode();
                if (backgroundTintMode != null) {
                    k0Var.f1134c = true;
                    k0Var.f1133b = backgroundTintMode;
                }
                if (k0Var.f1135d || k0Var.f1134c) {
                    g.f(background, k0Var, this.f1075a.getDrawableState());
                } else {
                    z10 = false;
                }
                if (z10) {
                    return;
                }
            }
            k0 k0Var2 = this.f1079e;
            if (k0Var2 != null) {
                g.f(background, k0Var2, this.f1075a.getDrawableState());
                return;
            }
            k0 k0Var3 = this.f1078d;
            if (k0Var3 != null) {
                g.f(background, k0Var3, this.f1075a.getDrawableState());
            }
        }
    }

    public ColorStateList b() {
        k0 k0Var = this.f1079e;
        if (k0Var != null) {
            return k0Var.f1132a;
        }
        return null;
    }

    public PorterDuff.Mode c() {
        k0 k0Var = this.f1079e;
        if (k0Var != null) {
            return k0Var.f1133b;
        }
        return null;
    }

    public void d(@Nullable AttributeSet attributeSet, int i10) {
        Context context = this.f1075a.getContext();
        int[] iArr = R$styleable.ViewBackgroundHelper;
        m0 r10 = m0.r(context, attributeSet, iArr, i10, 0);
        View view = this.f1075a;
        ViewCompat.j(view, view.getContext(), iArr, attributeSet, r10.f1145b, i10, 0);
        try {
            int i11 = R$styleable.ViewBackgroundHelper_android_background;
            if (r10.p(i11)) {
                this.f1077c = r10.m(i11, -1);
                ColorStateList d10 = this.f1076b.d(this.f1075a.getContext(), this.f1077c);
                if (d10 != null) {
                    g(d10);
                }
            }
            int i12 = R$styleable.ViewBackgroundHelper_backgroundTint;
            if (r10.p(i12)) {
                this.f1075a.setBackgroundTintList(r10.c(i12));
            }
            int i13 = R$styleable.ViewBackgroundHelper_backgroundTintMode;
            if (r10.p(i13)) {
                this.f1075a.setBackgroundTintMode(u.c(r10.j(i13, -1), null));
            }
            r10.f1145b.recycle();
        } catch (Throwable th) {
            r10.f1145b.recycle();
            throw th;
        }
    }

    public void e() {
        this.f1077c = -1;
        g(null);
        a();
    }

    public void f(int i10) {
        this.f1077c = i10;
        g gVar = this.f1076b;
        g(gVar != null ? gVar.d(this.f1075a.getContext(), i10) : null);
        a();
    }

    public void g(ColorStateList colorStateList) {
        if (colorStateList != null) {
            if (this.f1078d == null) {
                this.f1078d = new k0();
            }
            k0 k0Var = this.f1078d;
            k0Var.f1132a = colorStateList;
            k0Var.f1135d = true;
        } else {
            this.f1078d = null;
        }
        a();
    }

    public void h(ColorStateList colorStateList) {
        if (this.f1079e == null) {
            this.f1079e = new k0();
        }
        k0 k0Var = this.f1079e;
        k0Var.f1132a = colorStateList;
        k0Var.f1135d = true;
        a();
    }

    public void i(PorterDuff.Mode mode) {
        if (this.f1079e == null) {
            this.f1079e = new k0();
        }
        k0 k0Var = this.f1079e;
        k0Var.f1133b = mode;
        k0Var.f1134c = true;
        a();
    }
}
