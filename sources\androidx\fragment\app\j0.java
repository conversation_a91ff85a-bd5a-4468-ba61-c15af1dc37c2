package androidx.fragment.app;

import android.graphics.Rect;
import android.view.View;
import androidx.fragment.app.k0;
import java.util.ArrayList;
import u.a;

/* compiled from: FragmentTransition */
public class j0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ m0 f1924a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ a f1925b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ Object f1926c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ k0.b f1927d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1928e;

    /* renamed from: f  reason: collision with root package name */
    public final /* synthetic */ View f1929f;

    /* renamed from: g  reason: collision with root package name */
    public final /* synthetic */ Fragment f1930g;
    public final /* synthetic */ Fragment h;

    /* renamed from: i  reason: collision with root package name */
    public final /* synthetic */ boolean f1931i;

    /* renamed from: j  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1932j;

    /* renamed from: k  reason: collision with root package name */
    public final /* synthetic */ Object f1933k;

    /* renamed from: l  reason: collision with root package name */
    public final /* synthetic */ Rect f1934l;

    public j0(m0 m0Var, a aVar, Object obj, k0.b bVar, ArrayList arrayList, View view, Fragment fragment, Fragment fragment2, boolean z10, ArrayList arrayList2, Object obj2, Rect rect) {
        this.f1924a = m0Var;
        this.f1925b = aVar;
        this.f1926c = obj;
        this.f1927d = bVar;
        this.f1928e = arrayList;
        this.f1929f = view;
        this.f1930g = fragment;
        this.h = fragment2;
        this.f1931i = z10;
        this.f1932j = arrayList2;
        this.f1933k = obj2;
        this.f1934l = rect;
    }

    public void run() {
        a<String, View> e10 = k0.e(this.f1924a, this.f1925b, this.f1926c, this.f1927d);
        if (e10 != null) {
            this.f1928e.addAll(e10.values());
            this.f1928e.add(this.f1929f);
        }
        k0.c(this.f1930g, this.h, this.f1931i, e10, false);
        Object obj = this.f1926c;
        if (obj != null) {
            this.f1924a.x(obj, this.f1932j, this.f1928e);
            View k10 = k0.k(e10, this.f1927d, this.f1933k, this.f1931i);
            if (k10 != null) {
                this.f1924a.j(k10, this.f1934l);
            }
        }
    }
}
