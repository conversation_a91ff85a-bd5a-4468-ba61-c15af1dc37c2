package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERNumericString */
public class v0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f240a;

    public v0(byte[] bArr) {
        this.f240a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof v0)) {
            return false;
        }
        return a.a(this.f240a, ((v0) qVar).f240a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f240a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(18, this.f240a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f240a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f240a.length) + 1 + this.f240a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
