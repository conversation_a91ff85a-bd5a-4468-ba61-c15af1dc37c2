package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.view.MotionEvent;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.VisibleForTesting;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;
import java.util.List;
import java.util.WeakHashMap;

@VisibleForTesting
/* compiled from: FastScroller */
public class m extends RecyclerView.i implements RecyclerView.m {
    public static final int[] D = {16842919};
    public static final int[] E = new int[0];
    public int A;
    public final Runnable B;
    public final RecyclerView.n C;

    /* renamed from: a  reason: collision with root package name */
    public final int f2409a;

    /* renamed from: b  reason: collision with root package name */
    public final int f2410b;

    /* renamed from: c  reason: collision with root package name */
    public final StateListDrawable f2411c;

    /* renamed from: d  reason: collision with root package name */
    public final Drawable f2412d;

    /* renamed from: e  reason: collision with root package name */
    public final int f2413e;

    /* renamed from: f  reason: collision with root package name */
    public final int f2414f;

    /* renamed from: g  reason: collision with root package name */
    public final StateListDrawable f2415g;
    public final Drawable h;

    /* renamed from: i  reason: collision with root package name */
    public final int f2416i;

    /* renamed from: j  reason: collision with root package name */
    public final int f2417j;
    @VisibleForTesting

    /* renamed from: k  reason: collision with root package name */
    public int f2418k;
    @VisibleForTesting

    /* renamed from: l  reason: collision with root package name */
    public int f2419l;
    @VisibleForTesting

    /* renamed from: m  reason: collision with root package name */
    public float f2420m;
    @VisibleForTesting

    /* renamed from: n  reason: collision with root package name */
    public int f2421n;
    @VisibleForTesting

    /* renamed from: o  reason: collision with root package name */
    public int f2422o;
    @VisibleForTesting

    /* renamed from: p  reason: collision with root package name */
    public float f2423p;

    /* renamed from: q  reason: collision with root package name */
    public int f2424q = 0;

    /* renamed from: r  reason: collision with root package name */
    public int f2425r = 0;

    /* renamed from: s  reason: collision with root package name */
    public RecyclerView f2426s;

    /* renamed from: t  reason: collision with root package name */
    public boolean f2427t = false;

    /* renamed from: u  reason: collision with root package name */
    public boolean f2428u = false;

    /* renamed from: v  reason: collision with root package name */
    public int f2429v = 0;

    /* renamed from: w  reason: collision with root package name */
    public int f2430w = 0;

    /* renamed from: x  reason: collision with root package name */
    public final int[] f2431x = new int[2];

    /* renamed from: y  reason: collision with root package name */
    public final int[] f2432y = new int[2];

    /* renamed from: z  reason: collision with root package name */
    public final ValueAnimator f2433z;

    /* compiled from: FastScroller */
    public class a implements Runnable {
        public a() {
        }

        public void run() {
            m mVar = m.this;
            int i10 = mVar.A;
            if (i10 == 1) {
                mVar.f2433z.cancel();
            } else if (i10 != 2) {
                return;
            }
            mVar.A = 3;
            ValueAnimator valueAnimator = mVar.f2433z;
            valueAnimator.setFloatValues(((Float) valueAnimator.getAnimatedValue()).floatValue(), 0.0f);
            mVar.f2433z.setDuration((long) 500);
            mVar.f2433z.start();
        }
    }

    /* compiled from: FastScroller */
    public class b extends RecyclerView.n {
        public b() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.n
        public void b(RecyclerView recyclerView, int i10, int i11) {
            m mVar = m.this;
            int computeHorizontalScrollOffset = recyclerView.computeHorizontalScrollOffset();
            int computeVerticalScrollOffset = recyclerView.computeVerticalScrollOffset();
            int computeVerticalScrollRange = mVar.f2426s.computeVerticalScrollRange();
            int i12 = mVar.f2425r;
            mVar.f2427t = computeVerticalScrollRange - i12 > 0 && i12 >= mVar.f2409a;
            int computeHorizontalScrollRange = mVar.f2426s.computeHorizontalScrollRange();
            int i13 = mVar.f2424q;
            boolean z10 = computeHorizontalScrollRange - i13 > 0 && i13 >= mVar.f2409a;
            mVar.f2428u = z10;
            boolean z11 = mVar.f2427t;
            if (z11 || z10) {
                if (z11) {
                    float f10 = (float) i12;
                    mVar.f2419l = (int) ((((f10 / 2.0f) + ((float) computeVerticalScrollOffset)) * f10) / ((float) computeVerticalScrollRange));
                    mVar.f2418k = Math.min(i12, (i12 * i12) / computeVerticalScrollRange);
                }
                if (mVar.f2428u) {
                    float f11 = (float) computeHorizontalScrollOffset;
                    float f12 = (float) i13;
                    mVar.f2422o = (int) ((((f12 / 2.0f) + f11) * f12) / ((float) computeHorizontalScrollRange));
                    mVar.f2421n = Math.min(i13, (i13 * i13) / computeHorizontalScrollRange);
                }
                int i14 = mVar.f2429v;
                if (i14 == 0 || i14 == 1) {
                    mVar.j(1);
                }
            } else if (mVar.f2429v != 0) {
                mVar.j(0);
            }
        }
    }

    /* compiled from: FastScroller */
    public class c extends AnimatorListenerAdapter {

        /* renamed from: a  reason: collision with root package name */
        public boolean f2436a = false;

        public c() {
        }

        public void onAnimationCancel(Animator animator) {
            this.f2436a = true;
        }

        public void onAnimationEnd(Animator animator) {
            if (this.f2436a) {
                this.f2436a = false;
            } else if (((Float) m.this.f2433z.getAnimatedValue()).floatValue() == Constant.VOLUME_FLOAT_MIN) {
                m mVar = m.this;
                mVar.A = 0;
                mVar.j(0);
            } else {
                m mVar2 = m.this;
                mVar2.A = 2;
                mVar2.f2426s.invalidate();
            }
        }
    }

    /* compiled from: FastScroller */
    public class d implements ValueAnimator.AnimatorUpdateListener {
        public d() {
        }

        public void onAnimationUpdate(ValueAnimator valueAnimator) {
            int floatValue = (int) (((Float) valueAnimator.getAnimatedValue()).floatValue() * 255.0f);
            m.this.f2411c.setAlpha(floatValue);
            m.this.f2412d.setAlpha(floatValue);
            m.this.f2426s.invalidate();
        }
    }

    public m(RecyclerView recyclerView, StateListDrawable stateListDrawable, Drawable drawable, StateListDrawable stateListDrawable2, Drawable drawable2, int i10, int i11, int i12) {
        boolean z10 = false;
        ValueAnimator ofFloat = ValueAnimator.ofFloat(Constant.VOLUME_FLOAT_MIN, 1.0f);
        this.f2433z = ofFloat;
        this.A = 0;
        this.B = new a();
        b bVar = new b();
        this.C = bVar;
        this.f2411c = stateListDrawable;
        this.f2412d = drawable;
        this.f2415g = stateListDrawable2;
        this.h = drawable2;
        this.f2413e = Math.max(i10, stateListDrawable.getIntrinsicWidth());
        this.f2414f = Math.max(i10, drawable.getIntrinsicWidth());
        this.f2416i = Math.max(i10, stateListDrawable2.getIntrinsicWidth());
        this.f2417j = Math.max(i10, drawable2.getIntrinsicWidth());
        this.f2409a = i11;
        this.f2410b = i12;
        stateListDrawable.setAlpha(255);
        drawable.setAlpha(255);
        ofFloat.addListener(new c());
        ofFloat.addUpdateListener(new d());
        RecyclerView recyclerView2 = this.f2426s;
        if (recyclerView2 != recyclerView) {
            if (recyclerView2 != null) {
                RecyclerView.j jVar = recyclerView2.f2155l;
                if (jVar != null) {
                    jVar.d("Cannot remove item decoration during a scroll  or layout");
                }
                recyclerView2.f2158n.remove(this);
                if (recyclerView2.f2158n.isEmpty()) {
                    recyclerView2.setWillNotDraw(recyclerView2.getOverScrollMode() == 2 ? true : z10);
                }
                recyclerView2.Q();
                recyclerView2.requestLayout();
                RecyclerView recyclerView3 = this.f2426s;
                recyclerView3.f2160o.remove(this);
                if (recyclerView3.f2162p == this) {
                    recyclerView3.f2162p = null;
                }
                List<RecyclerView.n> list = this.f2426s.T0;
                if (list != null) {
                    list.remove(bVar);
                }
                f();
            }
            this.f2426s = recyclerView;
            recyclerView.g(this);
            this.f2426s.f2160o.add(this);
            this.f2426s.h(bVar);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.m
    public boolean a(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
        int i10 = this.f2429v;
        if (i10 == 1) {
            boolean h6 = h(motionEvent.getX(), motionEvent.getY());
            boolean g10 = g(motionEvent.getX(), motionEvent.getY());
            if (motionEvent.getAction() == 0 && (h6 || g10)) {
                if (g10) {
                    this.f2430w = 1;
                    this.f2423p = (float) ((int) motionEvent.getX());
                } else if (h6) {
                    this.f2430w = 2;
                    this.f2420m = (float) ((int) motionEvent.getY());
                }
                j(2);
                return true;
            }
        } else if (i10 == 2) {
            return true;
        }
        return false;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.m
    public void b(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
        if (this.f2429v != 0) {
            if (motionEvent.getAction() == 0) {
                boolean h6 = h(motionEvent.getX(), motionEvent.getY());
                boolean g10 = g(motionEvent.getX(), motionEvent.getY());
                if (h6 || g10) {
                    if (g10) {
                        this.f2430w = 1;
                        this.f2423p = (float) ((int) motionEvent.getX());
                    } else if (h6) {
                        this.f2430w = 2;
                        this.f2420m = (float) ((int) motionEvent.getY());
                    }
                    j(2);
                }
            } else if (motionEvent.getAction() == 1 && this.f2429v == 2) {
                this.f2420m = Constant.VOLUME_FLOAT_MIN;
                this.f2423p = Constant.VOLUME_FLOAT_MIN;
                j(1);
                this.f2430w = 0;
            } else if (motionEvent.getAction() == 2 && this.f2429v == 2) {
                k();
                if (this.f2430w == 1) {
                    float x8 = motionEvent.getX();
                    int[] iArr = this.f2432y;
                    int i10 = this.f2410b;
                    iArr[0] = i10;
                    iArr[1] = this.f2424q - i10;
                    float max = Math.max((float) iArr[0], Math.min((float) iArr[1], x8));
                    if (Math.abs(((float) this.f2422o) - max) >= 2.0f) {
                        int i11 = i(this.f2423p, max, iArr, this.f2426s.computeHorizontalScrollRange(), this.f2426s.computeHorizontalScrollOffset(), this.f2424q);
                        if (i11 != 0) {
                            this.f2426s.scrollBy(i11, 0);
                        }
                        this.f2423p = max;
                    }
                }
                if (this.f2430w == 2) {
                    float y10 = motionEvent.getY();
                    int[] iArr2 = this.f2431x;
                    int i12 = this.f2410b;
                    iArr2[0] = i12;
                    iArr2[1] = this.f2425r - i12;
                    float max2 = Math.max((float) iArr2[0], Math.min((float) iArr2[1], y10));
                    if (Math.abs(((float) this.f2419l) - max2) >= 2.0f) {
                        int i13 = i(this.f2420m, max2, iArr2, this.f2426s.computeVerticalScrollRange(), this.f2426s.computeVerticalScrollOffset(), this.f2425r);
                        if (i13 != 0) {
                            this.f2426s.scrollBy(0, i13);
                        }
                        this.f2420m = max2;
                    }
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.m
    public void c(boolean z10) {
    }

    @Override // androidx.recyclerview.widget.RecyclerView.i
    public void e(Canvas canvas, RecyclerView recyclerView, RecyclerView.t tVar) {
        if (this.f2424q != this.f2426s.getWidth() || this.f2425r != this.f2426s.getHeight()) {
            this.f2424q = this.f2426s.getWidth();
            this.f2425r = this.f2426s.getHeight();
            j(0);
        } else if (this.A != 0) {
            if (this.f2427t) {
                int i10 = this.f2424q;
                int i11 = this.f2413e;
                int i12 = i10 - i11;
                int i13 = this.f2419l;
                int i14 = this.f2418k;
                int i15 = i13 - (i14 / 2);
                this.f2411c.setBounds(0, 0, i11, i14);
                this.f2412d.setBounds(0, 0, this.f2414f, this.f2425r);
                RecyclerView recyclerView2 = this.f2426s;
                WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
                boolean z10 = true;
                if (recyclerView2.getLayoutDirection() != 1) {
                    z10 = false;
                }
                if (z10) {
                    this.f2412d.draw(canvas);
                    canvas.translate((float) this.f2413e, (float) i15);
                    canvas.scale(-1.0f, 1.0f);
                    this.f2411c.draw(canvas);
                    canvas.scale(1.0f, 1.0f);
                    canvas.translate((float) (-this.f2413e), (float) (-i15));
                } else {
                    canvas.translate((float) i12, Constant.VOLUME_FLOAT_MIN);
                    this.f2412d.draw(canvas);
                    canvas.translate(Constant.VOLUME_FLOAT_MIN, (float) i15);
                    this.f2411c.draw(canvas);
                    canvas.translate((float) (-i12), (float) (-i15));
                }
            }
            if (this.f2428u) {
                int i16 = this.f2425r;
                int i17 = this.f2416i;
                int i18 = i16 - i17;
                int i19 = this.f2422o;
                int i20 = this.f2421n;
                int i21 = i19 - (i20 / 2);
                this.f2415g.setBounds(0, 0, i20, i17);
                this.h.setBounds(0, 0, this.f2424q, this.f2417j);
                canvas.translate(Constant.VOLUME_FLOAT_MIN, (float) i18);
                this.h.draw(canvas);
                canvas.translate((float) i21, Constant.VOLUME_FLOAT_MIN);
                this.f2415g.draw(canvas);
                canvas.translate((float) (-i21), (float) (-i18));
            }
        }
    }

    public final void f() {
        this.f2426s.removeCallbacks(this.B);
    }

    @VisibleForTesting
    public boolean g(float f10, float f11) {
        if (f11 >= ((float) (this.f2425r - this.f2416i))) {
            int i10 = this.f2422o;
            int i11 = this.f2421n;
            return f10 >= ((float) (i10 - (i11 / 2))) && f10 <= ((float) ((i11 / 2) + i10));
        }
    }

    @VisibleForTesting
    public boolean h(float f10, float f11) {
        RecyclerView recyclerView = this.f2426s;
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        if (recyclerView.getLayoutDirection() == 1) {
            if (f10 > ((float) (this.f2413e / 2))) {
                return false;
            }
        } else if (f10 < ((float) (this.f2424q - this.f2413e))) {
            return false;
        }
        int i10 = this.f2419l;
        int i11 = this.f2418k / 2;
        return f11 >= ((float) (i10 - i11)) && f11 <= ((float) (i11 + i10));
    }

    public final int i(float f10, float f11, int[] iArr, int i10, int i11, int i12) {
        int i13 = iArr[1] - iArr[0];
        if (i13 == 0) {
            return 0;
        }
        int i14 = i10 - i12;
        int i15 = (int) (((f11 - f10) / ((float) i13)) * ((float) i14));
        int i16 = i11 + i15;
        if (i16 >= i14 || i16 < 0) {
            return 0;
        }
        return i15;
    }

    public void j(int i10) {
        if (i10 == 2 && this.f2429v != 2) {
            this.f2411c.setState(D);
            f();
        }
        if (i10 == 0) {
            this.f2426s.invalidate();
        } else {
            k();
        }
        if (this.f2429v == 2 && i10 != 2) {
            this.f2411c.setState(E);
            f();
            this.f2426s.postDelayed(this.B, (long) 1200);
        } else if (i10 == 1) {
            f();
            this.f2426s.postDelayed(this.B, (long) 1500);
        }
        this.f2429v = i10;
    }

    public void k() {
        int i10 = this.A;
        if (i10 != 0) {
            if (i10 == 3) {
                this.f2433z.cancel();
            } else {
                return;
            }
        }
        this.A = 1;
        ValueAnimator valueAnimator = this.f2433z;
        valueAnimator.setFloatValues(((Float) valueAnimator.getAnimatedValue()).floatValue(), 1.0f);
        this.f2433z.setDuration(500L);
        this.f2433z.setStartDelay(0);
        this.f2433z.start();
    }
}
