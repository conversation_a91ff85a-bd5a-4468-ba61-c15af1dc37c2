package androidx.appcompat.widget;

import android.content.Context;
import android.content.ContextWrapper;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: TintContextWrapper */
public class j0 extends ContextWrapper {

    /* renamed from: a  reason: collision with root package name */
    public static final Object f1129a = new Object();

    public static Context a(@NonNull Context context) {
        if (!(context instanceof j0) && !(context.getResources() instanceof l0)) {
            context.getResources();
            int i10 = p0.f1170a;
        }
        return context;
    }
}
