package androidx.cardview;

public final class R$styleable {
    public static final int[] CardView = {16843071, 16843072, 2130968770, 2130968771, 2130968772, 2130968774, 2130968775, 2130968776, 2130968890, 2130968891, 2130968892, 2130968893, 2130968894};
    public static final int CardView_android_minHeight = 1;
    public static final int CardView_android_minWidth = 0;
    public static final int CardView_cardBackgroundColor = 2;
    public static final int CardView_cardCornerRadius = 3;
    public static final int CardView_cardElevation = 4;
    public static final int CardView_cardMaxElevation = 5;
    public static final int CardView_cardPreventCornerOverlap = 6;
    public static final int CardView_cardUseCompatPadding = 7;
    public static final int CardView_contentPadding = 8;
    public static final int CardView_contentPaddingBottom = 9;
    public static final int CardView_contentPaddingLeft = 10;
    public static final int CardView_contentPaddingRight = 11;
    public static final int CardView_contentPaddingTop = 12;

    private R$styleable() {
    }
}
