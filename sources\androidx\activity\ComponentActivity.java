package androidx.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.os.Trace;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.d;
import androidx.annotation.CallSuper;
import androidx.annotation.ContentView;
import androidx.annotation.LayoutRes;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import androidx.lifecycle.h;
import androidx.lifecycle.n;
import androidx.lifecycle.p;
import androidx.lifecycle.runtime.R$id;
import androidx.lifecycle.s;
import androidx.lifecycle.w;
import androidx.lifecycle.x;
import com.duokan.airkan.server.f;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import l.a;
import y.a;

public class ComponentActivity extends y.c implements x, androidx.savedstate.c, c, d {
    private ActivityResultRegistry mActivityResultRegistry;
    @LayoutRes
    private int mContentLayoutId;
    public final k.a mContextAwareHelper;
    private s mDefaultFactory;
    private final h mLifecycleRegistry;
    private final AtomicInteger mNextLocalRequestCode;
    private final OnBackPressedDispatcher mOnBackPressedDispatcher;
    public final androidx.savedstate.b mSavedStateRegistryController;
    private w mViewModelStore;

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            try {
                ComponentActivity.super.onBackPressed();
            } catch (IllegalStateException e10) {
                if (!TextUtils.equals(e10.getMessage(), "Can not perform this action after onSaveInstanceState")) {
                    throw e10;
                }
            }
        }
    }

    public class b extends ActivityResultRegistry {

        public class a implements Runnable {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ int f272a;

            /* renamed from: b  reason: collision with root package name */
            public final /* synthetic */ a.C0113a f273b;

            public a(int i10, a.C0113a aVar) {
                this.f272a = i10;
                this.f273b = aVar;
            }

            public void run() {
                androidx.activity.result.a<O> aVar;
                b bVar = b.this;
                int i10 = this.f272a;
                T t2 = this.f273b.f7415a;
                String str = bVar.f298b.get(Integer.valueOf(i10));
                if (str != null) {
                    ActivityResultRegistry.c<?> cVar = bVar.f301e.get(str);
                    if (cVar == null || (aVar = cVar.f316a) == null) {
                        bVar.f303g.remove(str);
                        bVar.f302f.put(str, t2);
                        return;
                    }
                    aVar.a(t2);
                }
            }
        }

        /* renamed from: androidx.activity.ComponentActivity$b$b  reason: collision with other inner class name */
        public class RunnableC0007b implements Runnable {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ int f275a;

            /* renamed from: b  reason: collision with root package name */
            public final /* synthetic */ IntentSender.SendIntentException f276b;

            public RunnableC0007b(int i10, IntentSender.SendIntentException sendIntentException) {
                this.f275a = i10;
                this.f276b = sendIntentException;
            }

            public void run() {
                b.this.a(this.f275a, 0, new Intent().setAction("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST").putExtra("androidx.activity.result.contract.extra.SEND_INTENT_EXCEPTION", this.f276b));
            }
        }

        public b() {
        }

        @Override // androidx.activity.result.ActivityResultRegistry
        public <I, O> void b(int i10, @NonNull l.a<I, O> aVar, I i11, @Nullable y.b bVar) {
            ComponentActivity componentActivity = ComponentActivity.this;
            a.C0113a<O> b10 = aVar.b(componentActivity, i11);
            if (b10 != null) {
                new Handler(Looper.getMainLooper()).post(new a(i10, b10));
                return;
            }
            Intent a10 = aVar.a(componentActivity, i11);
            Bundle bundle = null;
            if (a10.hasExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE")) {
                bundle = a10.getBundleExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE");
                a10.removeExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE");
            }
            if ("androidx.activity.result.contract.action.REQUEST_PERMISSIONS".equals(a10.getAction())) {
                String[] stringArrayExtra = a10.getStringArrayExtra("androidx.activity.result.contract.extra.PERMISSIONS");
                if (stringArrayExtra != null) {
                    ArrayList arrayList = new ArrayList();
                    for (String str : stringArrayExtra) {
                        if (ComponentActivity.this.checkPermission(str, Process.myPid(), Process.myUid()) != 0) {
                            arrayList.add(str);
                        }
                    }
                    if (!arrayList.isEmpty()) {
                        String[] strArr = (String[]) arrayList.toArray(new String[0]);
                        int i12 = y.a.f10932b;
                        if (componentActivity instanceof a.AbstractC0184a) {
                            ((a.AbstractC0184a) componentActivity).validateRequestPermissionsRequestCode(i10);
                        }
                        componentActivity.requestPermissions(strArr, i10);
                    }
                }
            } else if ("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST".equals(a10.getAction())) {
                IntentSenderRequest intentSenderRequest = (IntentSenderRequest) a10.getParcelableExtra("androidx.activity.result.contract.extra.INTENT_SENDER_REQUEST");
                try {
                    IntentSender intentSender = intentSenderRequest.f320a;
                    Intent intent = intentSenderRequest.f321b;
                    int i13 = intentSenderRequest.f322c;
                    int i14 = intentSenderRequest.f323d;
                    int i15 = y.a.f10932b;
                    componentActivity.startIntentSenderForResult(intentSender, i10, intent, i13, i14, 0, bundle);
                } catch (IntentSender.SendIntentException e10) {
                    new Handler(Looper.getMainLooper()).post(new RunnableC0007b(i10, e10));
                }
            } else {
                int i16 = y.a.f10932b;
                componentActivity.startActivityForResult(a10, i10, bundle);
            }
        }
    }

    public static final class c {

        /* renamed from: a  reason: collision with root package name */
        public Object f278a;

        /* renamed from: b  reason: collision with root package name */
        public w f279b;
    }

    public ComponentActivity() {
        this.mContextAwareHelper = new k.a();
        this.mLifecycleRegistry = new h(this);
        this.mSavedStateRegistryController = new androidx.savedstate.b(this);
        this.mOnBackPressedDispatcher = new OnBackPressedDispatcher(new a());
        this.mNextLocalRequestCode = new AtomicInteger();
        this.mActivityResultRegistry = new b();
        if (getLifecycle() != null) {
            getLifecycle().a(new e() {
                /* class androidx.activity.ComponentActivity.AnonymousClass3 */

                @Override // androidx.lifecycle.e
                public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
                    if (event == Lifecycle.Event.ON_STOP) {
                        Window window = ComponentActivity.this.getWindow();
                        View peekDecorView = window != null ? window.peekDecorView() : null;
                        if (peekDecorView != null) {
                            peekDecorView.cancelPendingInputEvents();
                        }
                    }
                }
            });
            getLifecycle().a(new e() {
                /* class androidx.activity.ComponentActivity.AnonymousClass4 */

                @Override // androidx.lifecycle.e
                public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        ComponentActivity.this.mContextAwareHelper.f7263b = null;
                        if (!ComponentActivity.this.isChangingConfigurations()) {
                            ComponentActivity.this.getViewModelStore().a();
                        }
                    }
                }
            });
            getLifecycle().a(new e() {
                /* class androidx.activity.ComponentActivity.AnonymousClass5 */

                @Override // androidx.lifecycle.e
                public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
                    ComponentActivity.this.ensureViewModelStore();
                    h hVar = (h) ComponentActivity.this.getLifecycle();
                    hVar.d("removeObserver");
                    hVar.f2066a.f(this);
                }
            });
            return;
        }
        throw new IllegalStateException("getLifecycle() returned null in ComponentActivity's constructor. Please make sure you are lazily constructing your Lifecycle in the first call to getLifecycle() rather than relying on field initialization.");
    }

    private void initViewTreeOwners() {
        getWindow().getDecorView().setTag(R$id.view_tree_lifecycle_owner, this);
        getWindow().getDecorView().setTag(androidx.lifecycle.viewmodel.R$id.view_tree_view_model_store_owner, this);
        getWindow().getDecorView().setTag(androidx.savedstate.R$id.view_tree_saved_state_registry_owner, this);
    }

    public void addContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view, @SuppressLint({"UnknownNullness", "MissingNullability"}) ViewGroup.LayoutParams layoutParams) {
        initViewTreeOwners();
        super.addContentView(view, layoutParams);
    }

    public final void addOnContextAvailableListener(@NonNull k.b bVar) {
        k.a aVar = this.mContextAwareHelper;
        if (aVar.f7263b != null) {
            bVar.a(aVar.f7263b);
        }
        aVar.f7262a.add(bVar);
    }

    public void ensureViewModelStore() {
        if (this.mViewModelStore == null) {
            c cVar = (c) getLastNonConfigurationInstance();
            if (cVar != null) {
                this.mViewModelStore = cVar.f279b;
            }
            if (this.mViewModelStore == null) {
                this.mViewModelStore = new w();
            }
        }
    }

    @Override // androidx.activity.result.d
    @NonNull
    public final ActivityResultRegistry getActivityResultRegistry() {
        return this.mActivityResultRegistry;
    }

    @NonNull
    public s getDefaultViewModelProviderFactory() {
        if (getApplication() != null) {
            if (this.mDefaultFactory == null) {
                this.mDefaultFactory = new p(getApplication(), this, getIntent() != null ? getIntent().getExtras() : null);
            }
            return this.mDefaultFactory;
        }
        throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can't request ViewModel before onCreate call.");
    }

    @Nullable
    @Deprecated
    public Object getLastCustomNonConfigurationInstance() {
        c cVar = (c) getLastNonConfigurationInstance();
        if (cVar != null) {
            return cVar.f278a;
        }
        return null;
    }

    @Override // androidx.lifecycle.g, y.c
    @NonNull
    public Lifecycle getLifecycle() {
        return this.mLifecycleRegistry;
    }

    @Override // androidx.activity.c
    @NonNull
    public final OnBackPressedDispatcher getOnBackPressedDispatcher() {
        return this.mOnBackPressedDispatcher;
    }

    @Override // androidx.savedstate.c
    @NonNull
    public final androidx.savedstate.a getSavedStateRegistry() {
        return this.mSavedStateRegistryController.f2505b;
    }

    @Override // androidx.lifecycle.x
    @NonNull
    public w getViewModelStore() {
        if (getApplication() != null) {
            ensureViewModelStore();
            return this.mViewModelStore;
        }
        throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can't request ViewModel before onCreate call.");
    }

    @CallSuper
    @Deprecated
    public void onActivityResult(int i10, int i11, @Nullable Intent intent) {
        if (!this.mActivityResultRegistry.a(i10, i11, intent)) {
            super.onActivityResult(i10, i11, intent);
        }
    }

    @MainThread
    public void onBackPressed() {
        this.mOnBackPressedDispatcher.b();
    }

    @Override // y.c
    public void onCreate(@Nullable Bundle bundle) {
        this.mSavedStateRegistryController.a(bundle);
        k.a aVar = this.mContextAwareHelper;
        aVar.f7263b = this;
        for (k.b bVar : aVar.f7262a) {
            bVar.a(this);
        }
        super.onCreate(bundle);
        ActivityResultRegistry activityResultRegistry = this.mActivityResultRegistry;
        Objects.requireNonNull(activityResultRegistry);
        if (bundle != null) {
            ArrayList<Integer> integerArrayList = bundle.getIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS");
            ArrayList<String> stringArrayList = bundle.getStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS");
            if (!(stringArrayList == null || integerArrayList == null)) {
                int size = stringArrayList.size();
                for (int i10 = 0; i10 < size; i10++) {
                    int intValue = integerArrayList.get(i10).intValue();
                    String str = stringArrayList.get(i10);
                    activityResultRegistry.f298b.put(Integer.valueOf(intValue), str);
                    activityResultRegistry.f299c.put(str, Integer.valueOf(intValue));
                }
                activityResultRegistry.f297a = (Random) bundle.getSerializable("KEY_COMPONENT_ACTIVITY_RANDOM_OBJECT");
                activityResultRegistry.f303g.putAll(bundle.getBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT"));
            }
        }
        n.c(this);
        int i11 = this.mContentLayoutId;
        if (i11 != 0) {
            setContentView(i11);
        }
    }

    @CallSuper
    @Deprecated
    public void onRequestPermissionsResult(int i10, @NonNull String[] strArr, @NonNull int[] iArr) {
        if (!this.mActivityResultRegistry.a(i10, -1, new Intent().putExtra("androidx.activity.result.contract.extra.PERMISSIONS", strArr).putExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS", iArr))) {
            super.onRequestPermissionsResult(i10, strArr, iArr);
        }
    }

    @Nullable
    @Deprecated
    public Object onRetainCustomNonConfigurationInstance() {
        return null;
    }

    @Nullable
    public final Object onRetainNonConfigurationInstance() {
        c cVar;
        Object onRetainCustomNonConfigurationInstance = onRetainCustomNonConfigurationInstance();
        w wVar = this.mViewModelStore;
        if (wVar == null && (cVar = (c) getLastNonConfigurationInstance()) != null) {
            wVar = cVar.f279b;
        }
        if (wVar == null && onRetainCustomNonConfigurationInstance == null) {
            return null;
        }
        c cVar2 = new c();
        cVar2.f278a = onRetainCustomNonConfigurationInstance;
        cVar2.f279b = wVar;
        return cVar2;
    }

    @Override // y.c
    @CallSuper
    public void onSaveInstanceState(@NonNull Bundle bundle) {
        Lifecycle lifecycle = getLifecycle();
        if (lifecycle instanceof h) {
            ((h) lifecycle).i(Lifecycle.State.CREATED);
        }
        super.onSaveInstanceState(bundle);
        this.mSavedStateRegistryController.b(bundle);
        ActivityResultRegistry activityResultRegistry = this.mActivityResultRegistry;
        Objects.requireNonNull(activityResultRegistry);
        bundle.putIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS", new ArrayList<>(activityResultRegistry.f298b.keySet()));
        bundle.putStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS", new ArrayList<>(activityResultRegistry.f298b.values()));
        bundle.putBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT", (Bundle) activityResultRegistry.f303g.clone());
        bundle.putSerializable("KEY_COMPONENT_ACTIVITY_RANDOM_OBJECT", activityResultRegistry.f297a);
    }

    @Nullable
    public Context peekAvailableContext() {
        return this.mContextAwareHelper.f7263b;
    }

    @NonNull
    public final <I, O> androidx.activity.result.b<I> registerForActivityResult(@NonNull l.a<I, O> aVar, @NonNull ActivityResultRegistry activityResultRegistry, @NonNull androidx.activity.result.a<O> aVar2) {
        StringBuilder a10 = f.a("activity_rq#");
        a10.append(this.mNextLocalRequestCode.getAndIncrement());
        return activityResultRegistry.c(a10.toString(), this, aVar, aVar2);
    }

    public final void removeOnContextAvailableListener(@NonNull k.b bVar) {
        this.mContextAwareHelper.f7262a.remove(bVar);
    }

    public void reportFullyDrawn() {
        try {
            if (r0.a.a()) {
                Trace.beginSection("reportFullyDrawn() for " + getComponentName());
            }
            super.reportFullyDrawn();
        } finally {
            Trace.endSection();
        }
    }

    @Override // android.app.Activity
    public void setContentView(@LayoutRes int i10) {
        initViewTreeOwners();
        super.setContentView(i10);
    }

    @Deprecated
    public void startActivityForResult(@SuppressLint({"UnknownNullness"}) Intent intent, int i10) {
        super.startActivityForResult(intent, i10);
    }

    @Override // android.app.Activity
    @Deprecated
    public void startIntentSenderForResult(@SuppressLint({"UnknownNullness"}) IntentSender intentSender, int i10, @Nullable Intent intent, int i11, int i12, int i13) throws IntentSender.SendIntentException {
        super.startIntentSenderForResult(intentSender, i10, intent, i11, i12, i13);
    }

    @Deprecated
    public void startActivityForResult(@SuppressLint({"UnknownNullness"}) Intent intent, int i10, @Nullable Bundle bundle) {
        super.startActivityForResult(intent, i10, bundle);
    }

    @Override // android.app.Activity
    @Deprecated
    public void startIntentSenderForResult(@SuppressLint({"UnknownNullness"}) IntentSender intentSender, int i10, @Nullable Intent intent, int i11, int i12, int i13, @Nullable Bundle bundle) throws IntentSender.SendIntentException {
        super.startIntentSenderForResult(intentSender, i10, intent, i11, i12, i13, bundle);
    }

    @Override // android.app.Activity
    public void setContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view) {
        initViewTreeOwners();
        super.setContentView(view);
    }

    @NonNull
    public final <I, O> androidx.activity.result.b<I> registerForActivityResult(@NonNull l.a<I, O> aVar, @NonNull androidx.activity.result.a<O> aVar2) {
        return registerForActivityResult(aVar, this.mActivityResultRegistry, aVar2);
    }

    public void setContentView(@SuppressLint({"UnknownNullness", "MissingNullability"}) View view, @SuppressLint({"UnknownNullness", "MissingNullability"}) ViewGroup.LayoutParams layoutParams) {
        initViewTreeOwners();
        super.setContentView(view, layoutParams);
    }

    @ContentView
    public ComponentActivity(@LayoutRes int i10) {
        this();
        this.mContentLayoutId = i10;
    }
}
