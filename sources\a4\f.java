package a4;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import androidx.appcompat.widget.f0;
import b1.d;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.common.Log;
import com.duokan.airkan.common.ServiceData;
import com.duokan.airkan.common.ServiceList;
import com.duokan.airkan.server.c;
import com.xiaomi.milink.discover.core.udt.UDTDiscoverService;
import java.util.Map;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

public class f {

    /* renamed from: a  reason: collision with root package name */
    public Map<Integer, y3.a> f21a = new ConcurrentHashMap();

    /* renamed from: b  reason: collision with root package name */
    public Map<Integer, String> f22b = new ConcurrentHashMap();

    /* renamed from: c  reason: collision with root package name */
    public ServiceData f23c = new ServiceData();

    /* renamed from: d  reason: collision with root package name */
    public ServiceData f24d = new ServiceData();

    /* renamed from: e  reason: collision with root package name */
    public Map<String, o> f25e = new ConcurrentHashMap();

    /* renamed from: f  reason: collision with root package name */
    public ServiceList f26f = new ServiceList();

    /* renamed from: g  reason: collision with root package name */
    public l f27g = null;
    public final Context h;

    /* renamed from: i  reason: collision with root package name */
    public String f28i = "";

    /* renamed from: j  reason: collision with root package name */
    public TimerTask f29j = null;

    /* renamed from: k  reason: collision with root package name */
    public Timer f30k = null;

    /* renamed from: l  reason: collision with root package name */
    public Handler f31l = null;

    /* renamed from: m  reason: collision with root package name */
    public byte[] f32m = new byte[0];

    /* renamed from: n  reason: collision with root package name */
    public BroadcastReceiver f33n = new b();

    public class a extends TimerTask {
        public a() {
        }

        public void run() {
            String str;
            f fVar = f.this;
            Objects.requireNonNull(fVar);
            try {
                NetworkInfo activeNetworkInfo = ((ConnectivityManager) fVar.h.getSystemService("connectivity")).getActiveNetworkInfo();
                if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
                    str = "No active network connect";
                } else {
                    l lVar = fVar.f27g;
                    if (lVar == null) {
                        Log.i("UDTDiscoverManager", "UDTJmDNSThread not available, try to start");
                        Handler handler = fVar.f31l;
                        if (handler != null) {
                            handler.post(new c(fVar));
                            return;
                        } else {
                            Log.e("UDTDiscoverManager", "Handler not ready, start JmDNS service failed!");
                            return;
                        }
                    } else if (!lVar.d()) {
                        Log.i("UDTDiscoverManager", "UDTJmDNSThread not available, try to reset");
                        Handler handler2 = fVar.f31l;
                        if (handler2 != null) {
                            handler2.post(new d(fVar));
                            return;
                        } else {
                            Log.e("UDTDiscoverManager", "Handler not ready, reset JmDNS service failed!");
                            return;
                        }
                    } else {
                        str = "UDTJmDNSThread now available, no operation";
                    }
                }
                Log.i("UDTDiscoverManager", str);
            } catch (Exception unused) {
                Log.e("UDTDiscoverManager", "checkUDTJmDNSThread");
            }
        }
    }

    public class b extends BroadcastReceiver {

        /* renamed from: a  reason: collision with root package name */
        public volatile boolean f35a = true;

        public b() {
        }

        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i("UDTDiscoverManager", "Action: " + action);
            boolean z10 = true;
            if (action.equals(Constant.CONNECTIVITY_CHANGED_ACTION)) {
                try {
                    NetworkInfo activeNetworkInfo = ((ConnectivityManager) context.getSystemService("connectivity")).getActiveNetworkInfo();
                    if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
                        z10 = false;
                    }
                    if (!z10) {
                        return;
                    }
                    if (this.f35a) {
                        Log.i("UDTDiscoverManager", "Connectivity changed, and screen is on, reset JmDNS");
                        f fVar = f.this;
                        Handler handler = fVar.f31l;
                        if (handler != null) {
                            handler.post(new d(fVar));
                        } else {
                            Log.e("UDTDiscoverManager", "Handler not ready, reset JmDNS service failed!");
                        }
                    } else {
                        Log.i("UDTDiscoverManager", "Connectivity changed, but screen is off, no need reset JmDNS");
                    }
                } catch (Exception e10) {
                    StringBuilder a10 = com.duokan.airkan.server.f.a(" onreceived network change exception: ");
                    a10.append(e10.toString());
                    Log.w("UDTDiscoverManager", a10.toString());
                }
            } else if (action.equals("com.duokan.duokantv.DEVICE_NAME_CHANGED")) {
                Log.i("UDTDiscoverManager", "Device name changed, refresh service");
                f fVar2 = f.this;
                fVar2.f24d.name = z3.f.a(fVar2.h);
                f.this.d();
            } else if (action.equals("android.intent.action.SCREEN_ON")) {
                Log.i("UDTDiscoverManager", "Screen on, reset JmDNS");
                this.f35a = true;
                d.a(new g(this, 0));
            } else if (action.equals("android.intent.action.SCREEN_OFF")) {
                Log.i("UDTDiscoverManager", "Screen off");
                this.f35a = false;
            } else {
                Log.e("UDTDiscoverManager", "Not supported action: " + action);
            }
        }
    }

    public f(Context context) {
        this.h = context;
    }

    public final void a() {
        if (this.f30k != null) {
            this.f29j.cancel();
            this.f30k.cancel();
            this.f30k.purge();
            this.f30k = null;
            this.f29j = null;
            return;
        }
        Log.i("UDTDiscoverManager", "No JmDNSCheckTimer, no need close");
    }

    public final boolean b() {
        int i10 = 1;
        while (true) {
            l lVar = this.f27g;
            if (lVar == null || lVar.f42a.get()) {
                return true;
            }
            try {
                Thread.sleep(100);
            } catch (Exception e10) {
                c.b(e10, com.duokan.airkan.server.f.a("Exception: "), "UDTDiscoverManager");
            }
            if (i10 > 20) {
                return false;
            }
            i10++;
        }
        return true;
    }

    public final void c() {
        if (this.f27g != null) {
            for (o oVar : this.f25e.values()) {
                this.f27g.g(oVar.f65b);
            }
            return;
        }
        Log.e("UDTDiscoverManager", "UDTJmDNSThread not available, refresh listen service failed!");
    }

    public final void d() {
        boolean z10;
        l lVar = this.f27g;
        if (lVar != null) {
            lVar.h(this.f23c);
            if (this.f22b.isEmpty()) {
                Log.i("UDTDiscoverManager", "No feature regist in MiLink service!");
                return;
            }
            this.f24d.extraText = this.f28i;
            String str = "feature=";
            for (String str2 : this.f22b.values()) {
                str = p.f.a(str, str2);
            }
            StringBuilder sb = new StringBuilder();
            ServiceData serviceData = this.f24d;
            sb.append(serviceData.extraText);
            serviceData.extraText = f0.a("%c%s", new Object[]{Integer.valueOf(str.length()), str}, sb);
            ServiceData serviceData2 = new ServiceData();
            this.f23c = serviceData2;
            serviceData2.name = new String(this.f24d.name);
            this.f23c.type = new String(this.f24d.type);
            ServiceData serviceData3 = this.f23c;
            ServiceData serviceData4 = this.f24d;
            serviceData3.port = serviceData4.port;
            serviceData3.ip = new String[]{"127.0.0.1"};
            serviceData3.extraText = new String(serviceData4.extraText);
            UDTDiscoverService uDTDiscoverService = UDTDiscoverService.f4654m;
            Objects.requireNonNull(uDTDiscoverService);
            String str3 = z3.d.f11024a;
            synchronized (z3.d.class) {
                z10 = z3.d.f11033k.get();
            }
            if (!z10) {
                Log.d("SDPRegisterDevice", "enter register1");
                new z3.c(uDTDiscoverService, true).start();
            }
            this.f27g.f(this.f24d);
            return;
        }
        Log.e("UDTDiscoverManager", "UDTJmDNSThread not available, refresh milink service failed!");
    }

    public final void e() {
        if (this.f27g != null) {
            synchronized (this.f26f.getList()) {
                for (ServiceData serviceData : this.f26f.getList()) {
                    this.f27g.f(serviceData);
                }
            }
            UDTDiscoverService.f4654m.a();
            return;
        }
        Log.e("UDTDiscoverManager", "UDTJmDNSThread not available, refresh regist service failed!");
    }

    public final void f() {
        a();
        this.f29j = new a();
        try {
            Timer timer = new Timer();
            this.f30k = timer;
            timer.schedule(this.f29j, 20000, 60000);
        } catch (Exception e10) {
            c.b(e10, com.duokan.airkan.server.f.a("Exception: "), "UDTDiscoverManager");
        }
    }
}
