package a5;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.security.PrivilegedAction;

/* compiled from: NetUtil */
public final class h {

    /* renamed from: a  reason: collision with root package name */
    public static final Inet4Address f85a;

    /* renamed from: b  reason: collision with root package name */
    public static final Inet6Address f86b;

    /* renamed from: c  reason: collision with root package name */
    public static final d5.a f87c;

    /* compiled from: NetUtil */
    public static class a implements PrivilegedAction<Integer> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        /* JADX WARNING: Removed duplicated region for block: B:35:0x0083 A[Catch:{ all -> 0x009f }] */
        /* JADX WARNING: Removed duplicated region for block: B:37:0x0097  */
        /* JADX WARNING: Removed duplicated region for block: B:44:0x00a3 A[SYNTHETIC, Splitter:B:44:0x00a3] */
        @Override // java.security.PrivilegedAction
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public java.lang.Integer run() {
            /*
            // Method dump skipped, instructions count: 167
            */
            throw new UnsupportedOperationException("Method not decompiled: a5.h.a.run():java.lang.Object");
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:46:0x00e5, code lost:
        r10 = r8.nextElement();
        r6 = r7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:61:0x012d, code lost:
        if (r1 == null) goto L_0x013b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:66:0x0139, code lost:
        if (r10 != null) goto L_0x0140;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:68:0x013b, code lost:
        a5.h.f87c.debug("Using hard-coded IPv4 localhost address: {}", r3);
     */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00fb  */
    /* JADX WARNING: Removed duplicated region for block: B:55:0x011a  */
    static {
        /*
        // Method dump skipped, instructions count: 356
        */
        throw new UnsupportedOperationException("Method not decompiled: a5.h.<clinit>():void");
    }

    /* JADX INFO: finally extract failed */
    public static Integer a(String str) throws IOException {
        Integer num;
        Process start = new ProcessBuilder("sysctl", str).start();
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(start.getInputStream()));
            try {
                String readLine = bufferedReader.readLine();
                if (readLine != null && readLine.startsWith(str)) {
                    int length = readLine.length() - 1;
                    while (true) {
                        if (length <= str.length()) {
                            break;
                        } else if (!Character.isDigit(readLine.charAt(length))) {
                            num = Integer.valueOf(readLine.substring(length + 1));
                            break;
                        } else {
                            length--;
                        }
                    }
                }
                num = null;
                bufferedReader.close();
                start.destroy();
                return num;
            } catch (Throwable th) {
                bufferedReader.close();
                throw th;
            }
        } catch (Throwable th2) {
            if (start != null) {
                start.destroy();
            }
            throw th2;
        }
    }
}
