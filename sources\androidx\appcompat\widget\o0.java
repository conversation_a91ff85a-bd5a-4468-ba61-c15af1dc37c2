package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$id;
import androidx.appcompat.R$string;
import androidx.appcompat.R$styleable;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.f;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import j0.m;
import j0.o;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: ToolbarWidgetWrapper */
public class o0 implements t {

    /* renamed from: a  reason: collision with root package name */
    public Toolbar f1152a;

    /* renamed from: b  reason: collision with root package name */
    public int f1153b;

    /* renamed from: c  reason: collision with root package name */
    public View f1154c;

    /* renamed from: d  reason: collision with root package name */
    public View f1155d;

    /* renamed from: e  reason: collision with root package name */
    public Drawable f1156e;

    /* renamed from: f  reason: collision with root package name */
    public Drawable f1157f;

    /* renamed from: g  reason: collision with root package name */
    public Drawable f1158g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public CharSequence f1159i;

    /* renamed from: j  reason: collision with root package name */
    public CharSequence f1160j;

    /* renamed from: k  reason: collision with root package name */
    public CharSequence f1161k;

    /* renamed from: l  reason: collision with root package name */
    public Window.Callback f1162l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1163m;

    /* renamed from: n  reason: collision with root package name */
    public ActionMenuPresenter f1164n;

    /* renamed from: o  reason: collision with root package name */
    public int f1165o = 0;

    /* renamed from: p  reason: collision with root package name */
    public Drawable f1166p;

    /* compiled from: ToolbarWidgetWrapper */
    public class a extends o {

        /* renamed from: d  reason: collision with root package name */
        public boolean f1167d = false;

        /* renamed from: e  reason: collision with root package name */
        public final /* synthetic */ int f1168e;

        public a(int i10) {
            this.f1168e = i10;
        }

        @Override // j0.n, j0.o
        public void b(View view) {
            this.f1167d = true;
        }

        @Override // j0.n
        public void d(View view) {
            if (!this.f1167d) {
                o0.this.f1152a.setVisibility(this.f1168e);
            }
        }

        @Override // j0.n, j0.o
        public void e(View view) {
            o0.this.f1152a.setVisibility(0);
        }
    }

    public o0(Toolbar toolbar, boolean z10) {
        int i10;
        Drawable drawable;
        int i11 = R$string.abc_action_bar_up_description;
        this.f1152a = toolbar;
        this.f1159i = toolbar.getTitle();
        this.f1160j = toolbar.getSubtitle();
        this.h = this.f1159i != null;
        this.f1158g = toolbar.getNavigationIcon();
        String str = null;
        m0 r10 = m0.r(toolbar.getContext(), null, R$styleable.ActionBar, R$attr.actionBarStyle, 0);
        this.f1166p = r10.g(R$styleable.ActionBar_homeAsUpIndicator);
        if (z10) {
            CharSequence o3 = r10.o(R$styleable.ActionBar_title);
            if (!TextUtils.isEmpty(o3)) {
                this.h = true;
                this.f1159i = o3;
                if ((this.f1153b & 8) != 0) {
                    this.f1152a.setTitle(o3);
                }
            }
            CharSequence o10 = r10.o(R$styleable.ActionBar_subtitle);
            if (!TextUtils.isEmpty(o10)) {
                this.f1160j = o10;
                if ((this.f1153b & 8) != 0) {
                    this.f1152a.setSubtitle(o10);
                }
            }
            Drawable g10 = r10.g(R$styleable.ActionBar_logo);
            if (g10 != null) {
                this.f1157f = g10;
                y();
            }
            Drawable g11 = r10.g(R$styleable.ActionBar_icon);
            if (g11 != null) {
                this.f1156e = g11;
                y();
            }
            if (this.f1158g == null && (drawable = this.f1166p) != null) {
                this.f1158g = drawable;
                x();
            }
            n(r10.j(R$styleable.ActionBar_displayOptions, 0));
            int m10 = r10.m(R$styleable.ActionBar_customNavigationLayout, 0);
            if (m10 != 0) {
                View inflate = LayoutInflater.from(this.f1152a.getContext()).inflate(m10, (ViewGroup) this.f1152a, false);
                View view = this.f1155d;
                if (!(view == null || (this.f1153b & 16) == 0)) {
                    this.f1152a.removeView(view);
                }
                this.f1155d = inflate;
                if (!(inflate == null || (this.f1153b & 16) == 0)) {
                    this.f1152a.addView(inflate);
                }
                n(this.f1153b | 16);
            }
            int l10 = r10.l(R$styleable.ActionBar_height, 0);
            if (l10 > 0) {
                ViewGroup.LayoutParams layoutParams = this.f1152a.getLayoutParams();
                layoutParams.height = l10;
                this.f1152a.setLayoutParams(layoutParams);
            }
            int e10 = r10.e(R$styleable.ActionBar_contentInsetStart, -1);
            int e11 = r10.e(R$styleable.ActionBar_contentInsetEnd, -1);
            if (e10 >= 0 || e11 >= 0) {
                Toolbar toolbar2 = this.f1152a;
                int max = Math.max(e10, 0);
                int max2 = Math.max(e11, 0);
                toolbar2.d();
                toolbar2.f1018t.a(max, max2);
            }
            int m11 = r10.m(R$styleable.ActionBar_titleTextStyle, 0);
            if (m11 != 0) {
                Toolbar toolbar3 = this.f1152a;
                Context context = toolbar3.getContext();
                toolbar3.f1003l = m11;
                TextView textView = toolbar3.f994b;
                if (textView != null) {
                    textView.setTextAppearance(context, m11);
                }
            }
            int m12 = r10.m(R$styleable.ActionBar_subtitleTextStyle, 0);
            if (m12 != 0) {
                Toolbar toolbar4 = this.f1152a;
                Context context2 = toolbar4.getContext();
                toolbar4.f1004m = m12;
                TextView textView2 = toolbar4.f995c;
                if (textView2 != null) {
                    textView2.setTextAppearance(context2, m12);
                }
            }
            int m13 = r10.m(R$styleable.ActionBar_popupTheme, 0);
            if (m13 != 0) {
                this.f1152a.setPopupTheme(m13);
            }
        } else {
            if (this.f1152a.getNavigationIcon() != null) {
                i10 = 15;
                this.f1166p = this.f1152a.getNavigationIcon();
            } else {
                i10 = 11;
            }
            this.f1153b = i10;
        }
        r10.f1145b.recycle();
        if (i11 != this.f1165o) {
            this.f1165o = i11;
            if (TextUtils.isEmpty(this.f1152a.getNavigationContentDescription())) {
                int i12 = this.f1165o;
                this.f1161k = i12 != 0 ? getContext().getString(i12) : str;
                w();
            }
        }
        this.f1161k = this.f1152a.getNavigationContentDescription();
        this.f1152a.setNavigationOnClickListener(new n0(this));
    }

    @Override // androidx.appcompat.widget.t
    public void a(Menu menu, h.a aVar) {
        f fVar;
        if (this.f1164n == null) {
            ActionMenuPresenter actionMenuPresenter = new ActionMenuPresenter(this.f1152a.getContext());
            this.f1164n = actionMenuPresenter;
            actionMenuPresenter.f587i = R$id.action_menu_presenter;
        }
        ActionMenuPresenter actionMenuPresenter2 = this.f1164n;
        actionMenuPresenter2.f584e = aVar;
        Toolbar toolbar = this.f1152a;
        d dVar = (d) menu;
        if (dVar != null || toolbar.f993a != null) {
            toolbar.f();
            d dVar2 = toolbar.f993a.f770p;
            if (dVar2 != dVar) {
                if (dVar2 != null) {
                    dVar2.t(toolbar.f1027z0);
                    dVar2.t(toolbar.A0);
                }
                if (toolbar.A0 == null) {
                    toolbar.A0 = new Toolbar.d();
                }
                actionMenuPresenter2.f753r = true;
                if (dVar != null) {
                    dVar.b(actionMenuPresenter2, toolbar.f1001j);
                    dVar.b(toolbar.A0, toolbar.f1001j);
                } else {
                    actionMenuPresenter2.d(toolbar.f1001j, null);
                    Toolbar.d dVar3 = toolbar.A0;
                    d dVar4 = dVar3.f1034a;
                    if (!(dVar4 == null || (fVar = dVar3.f1035b) == null)) {
                        dVar4.d(fVar);
                    }
                    dVar3.f1034a = null;
                    actionMenuPresenter2.b(true);
                    toolbar.A0.b(true);
                }
                toolbar.f993a.setPopupTheme(toolbar.f1002k);
                toolbar.f993a.setPresenter(actionMenuPresenter2);
                toolbar.f1027z0 = actionMenuPresenter2;
            }
        }
    }

    @Override // androidx.appcompat.widget.t
    public boolean b() {
        return this.f1152a.o();
    }

    @Override // androidx.appcompat.widget.t
    public void c() {
        this.f1163m = true;
    }

    @Override // androidx.appcompat.widget.t
    public void collapseActionView() {
        f fVar;
        Toolbar.d dVar = this.f1152a.A0;
        if (dVar == null) {
            fVar = null;
        } else {
            fVar = dVar.f1035b;
        }
        if (fVar != null) {
            fVar.collapseActionView();
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:16:? A[RETURN, SYNTHETIC] */
    @Override // androidx.appcompat.widget.t
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean d() {
        /*
            r4 = this;
            androidx.appcompat.widget.Toolbar r0 = r4.f1152a
            androidx.appcompat.widget.ActionMenuView r0 = r0.f993a
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L_0x0022
            androidx.appcompat.widget.ActionMenuPresenter r0 = r0.f776t
            if (r0 == 0) goto L_0x001e
            androidx.appcompat.widget.ActionMenuPresenter$c r3 = r0.f757y
            if (r3 != 0) goto L_0x0019
            boolean r0 = r0.o()
            if (r0 == 0) goto L_0x0017
            goto L_0x0019
        L_0x0017:
            r0 = r2
            goto L_0x001a
        L_0x0019:
            r0 = r1
        L_0x001a:
            if (r0 == 0) goto L_0x001e
            r0 = r1
            goto L_0x001f
        L_0x001e:
            r0 = r2
        L_0x001f:
            if (r0 == 0) goto L_0x0022
            goto L_0x0023
        L_0x0022:
            r1 = r2
        L_0x0023:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.o0.d():boolean");
    }

    @Override // androidx.appcompat.widget.t
    public boolean e() {
        ActionMenuView actionMenuView = this.f1152a.f993a;
        if (actionMenuView != null) {
            ActionMenuPresenter actionMenuPresenter = actionMenuView.f776t;
            if (actionMenuPresenter != null && actionMenuPresenter.m()) {
                return true;
            }
        }
        return false;
    }

    @Override // androidx.appcompat.widget.t
    public boolean f() {
        return this.f1152a.u();
    }

    @Override // androidx.appcompat.widget.t
    public boolean g() {
        ActionMenuView actionMenuView;
        Toolbar toolbar = this.f1152a;
        return toolbar.getVisibility() == 0 && (actionMenuView = toolbar.f993a) != null && actionMenuView.f775s;
    }

    @Override // androidx.appcompat.widget.t
    public Context getContext() {
        return this.f1152a.getContext();
    }

    @Override // androidx.appcompat.widget.t
    public CharSequence getTitle() {
        return this.f1152a.getTitle();
    }

    @Override // androidx.appcompat.widget.t
    public void h() {
        ActionMenuPresenter actionMenuPresenter;
        ActionMenuView actionMenuView = this.f1152a.f993a;
        if (actionMenuView != null && (actionMenuPresenter = actionMenuView.f776t) != null) {
            actionMenuPresenter.j();
        }
    }

    @Override // androidx.appcompat.widget.t
    public void i(h.a aVar, d.a aVar2) {
        Toolbar toolbar = this.f1152a;
        toolbar.B0 = aVar;
        toolbar.C0 = aVar2;
        ActionMenuView actionMenuView = toolbar.f993a;
        if (actionMenuView != null) {
            actionMenuView.f777x = aVar;
            actionMenuView.f778y = aVar2;
        }
    }

    @Override // androidx.appcompat.widget.t
    public void j(e0 e0Var) {
        Toolbar toolbar;
        View view = this.f1154c;
        if (view != null && view.getParent() == (toolbar = this.f1152a)) {
            toolbar.removeView(this.f1154c);
        }
        this.f1154c = null;
    }

    @Override // androidx.appcompat.widget.t
    public ViewGroup k() {
        return this.f1152a;
    }

    @Override // androidx.appcompat.widget.t
    public void l(boolean z10) {
    }

    @Override // androidx.appcompat.widget.t
    public boolean m() {
        Toolbar.d dVar = this.f1152a.A0;
        return (dVar == null || dVar.f1035b == null) ? false : true;
    }

    @Override // androidx.appcompat.widget.t
    public void n(int i10) {
        View view;
        int i11 = this.f1153b ^ i10;
        this.f1153b = i10;
        if (i11 != 0) {
            if ((i11 & 4) != 0) {
                if ((i10 & 4) != 0) {
                    w();
                }
                x();
            }
            if ((i11 & 3) != 0) {
                y();
            }
            if ((i11 & 8) != 0) {
                if ((i10 & 8) != 0) {
                    this.f1152a.setTitle(this.f1159i);
                    this.f1152a.setSubtitle(this.f1160j);
                } else {
                    this.f1152a.setTitle((CharSequence) null);
                    this.f1152a.setSubtitle((CharSequence) null);
                }
            }
            if ((i11 & 16) != 0 && (view = this.f1155d) != null) {
                if ((i10 & 16) != 0) {
                    this.f1152a.addView(view);
                } else {
                    this.f1152a.removeView(view);
                }
            }
        }
    }

    @Override // androidx.appcompat.widget.t
    public int o() {
        return this.f1153b;
    }

    @Override // androidx.appcompat.widget.t
    public Menu p() {
        return this.f1152a.getMenu();
    }

    @Override // androidx.appcompat.widget.t
    public void q(int i10) {
        this.f1157f = i10 != 0 ? m.a.a(getContext(), i10) : null;
        y();
    }

    @Override // androidx.appcompat.widget.t
    public int r() {
        return 0;
    }

    @Override // androidx.appcompat.widget.t
    public m s(int i10, long j10) {
        m a10 = ViewCompat.a(this.f1152a);
        a10.a(i10 == 0 ? 1.0f : Constant.VOLUME_FLOAT_MIN);
        a10.c(j10);
        a aVar = new a(i10);
        View view = a10.f6912a.get();
        if (view != null) {
            a10.e(view, aVar);
        }
        return a10;
    }

    @Override // androidx.appcompat.widget.t
    public void setIcon(int i10) {
        this.f1156e = i10 != 0 ? m.a.a(getContext(), i10) : null;
        y();
    }

    @Override // androidx.appcompat.widget.t
    public void setVisibility(int i10) {
        this.f1152a.setVisibility(i10);
    }

    @Override // androidx.appcompat.widget.t
    public void setWindowCallback(Window.Callback callback) {
        this.f1162l = callback;
    }

    @Override // androidx.appcompat.widget.t
    public void setWindowTitle(CharSequence charSequence) {
        if (!this.h) {
            this.f1159i = charSequence;
            if ((this.f1153b & 8) != 0) {
                this.f1152a.setTitle(charSequence);
            }
        }
    }

    @Override // androidx.appcompat.widget.t
    public void t() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.t
    public void u() {
        Log.i("ToolbarWidgetWrapper", "Progress display unsupported");
    }

    @Override // androidx.appcompat.widget.t
    public void v(boolean z10) {
        this.f1152a.setCollapsible(z10);
    }

    public final void w() {
        if ((this.f1153b & 4) == 0) {
            return;
        }
        if (TextUtils.isEmpty(this.f1161k)) {
            this.f1152a.setNavigationContentDescription(this.f1165o);
        } else {
            this.f1152a.setNavigationContentDescription(this.f1161k);
        }
    }

    public final void x() {
        if ((this.f1153b & 4) != 0) {
            Toolbar toolbar = this.f1152a;
            Drawable drawable = this.f1158g;
            if (drawable == null) {
                drawable = this.f1166p;
            }
            toolbar.setNavigationIcon(drawable);
            return;
        }
        this.f1152a.setNavigationIcon((Drawable) null);
    }

    public final void y() {
        Drawable drawable;
        int i10 = this.f1153b;
        if ((i10 & 2) == 0) {
            drawable = null;
        } else if ((i10 & 1) != 0) {
            drawable = this.f1157f;
            if (drawable == null) {
                drawable = this.f1156e;
            }
        } else {
            drawable = this.f1156e;
        }
        this.f1152a.setLogo(drawable);
    }

    @Override // androidx.appcompat.widget.t
    public void setIcon(Drawable drawable) {
        this.f1156e = drawable;
        y();
    }
}
