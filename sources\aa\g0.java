package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: BERSet */
public class g0 extends t {
    public g0(e eVar) {
        super(eVar);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.c(49);
        pVar.c(128);
        Enumeration q10 = q();
        while (q10.hasMoreElements()) {
            pVar.h((e) q10.nextElement());
        }
        pVar.c(0);
        pVar.c(0);
    }

    @Override // aa.q
    public int i() throws IOException {
        Enumeration q10 = q();
        int i10 = 0;
        while (q10.hasMoreElements()) {
            i10 += ((e) q10.nextElement()).c().i();
        }
        return i10 + 2 + 2;
    }

    public g0(f fVar) {
        super(fVar, false);
    }

    public g0(e[] eVarArr) {
        super(eVarArr, false);
    }
}
