package a5;

import a5.e;
import c5.m;
import d5.a;
import io.netty.util.Signal;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/* compiled from: ConstantPool */
public abstract class f<T extends e<T>> {

    /* renamed from: a  reason: collision with root package name */
    public final ConcurrentMap<String, T> f83a = new ConcurrentHashMap();

    /* renamed from: b  reason: collision with root package name */
    public final AtomicInteger f84b = new AtomicInteger(1);

    public f() {
        a aVar = m.f3142a;
    }

    public T a(String str) {
        Signal signal;
        Objects.requireNonNull(str, "name");
        if (!str.isEmpty()) {
            T t2 = this.f83a.get(str);
            return (t2 == null && (t2 = this.f83a.putIfAbsent(str, (signal = new Signal(this.f84b.getAndIncrement(), str, null)))) == null) ? signal : t2;
        }
        throw new IllegalArgumentException("empty name");
    }
}
