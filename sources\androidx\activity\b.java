package androidx.activity;

import androidx.annotation.MainThread;
import java.util.concurrent.CopyOnWriteArrayList;

/* compiled from: OnBackPressedCallback */
public abstract class b {

    /* renamed from: a  reason: collision with root package name */
    public boolean f293a;

    /* renamed from: b  reason: collision with root package name */
    public CopyOnWriteArrayList<a> f294b = new CopyOnWriteArrayList<>();

    public b(boolean z10) {
        this.f293a = z10;
    }

    @MainThread
    public abstract void a();
}
