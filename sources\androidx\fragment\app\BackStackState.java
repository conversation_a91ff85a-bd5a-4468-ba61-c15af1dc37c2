package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import androidx.fragment.app.d0;
import java.util.ArrayList;

/* access modifiers changed from: package-private */
@SuppressLint({"BanParcelableUsage"})
public final class BackStackState implements Parcelable {
    public static final Parcelable.Creator<BackStackState> CREATOR = new a();

    /* renamed from: a  reason: collision with root package name */
    public final int[] f1707a;

    /* renamed from: b  reason: collision with root package name */
    public final ArrayList<String> f1708b;

    /* renamed from: c  reason: collision with root package name */
    public final int[] f1709c;

    /* renamed from: d  reason: collision with root package name */
    public final int[] f1710d;

    /* renamed from: e  reason: collision with root package name */
    public final int f1711e;

    /* renamed from: f  reason: collision with root package name */
    public final String f1712f;

    /* renamed from: g  reason: collision with root package name */
    public final int f1713g;
    public final int h;

    /* renamed from: i  reason: collision with root package name */
    public final CharSequence f1714i;

    /* renamed from: j  reason: collision with root package name */
    public final int f1715j;

    /* renamed from: k  reason: collision with root package name */
    public final CharSequence f1716k;

    /* renamed from: l  reason: collision with root package name */
    public final ArrayList<String> f1717l;

    /* renamed from: m  reason: collision with root package name */
    public final ArrayList<String> f1718m;

    /* renamed from: n  reason: collision with root package name */
    public final boolean f1719n;

    public class a implements Parcelable.Creator<BackStackState> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // android.os.Parcelable.Creator
        public BackStackState createFromParcel(Parcel parcel) {
            return new BackStackState(parcel);
        }

        /* Return type fixed from 'java.lang.Object[]' to match base method */
        @Override // android.os.Parcelable.Creator
        public BackStackState[] newArray(int i10) {
            return new BackStackState[i10];
        }
    }

    public BackStackState(a aVar) {
        int size = aVar.f1866a.size();
        this.f1707a = new int[(size * 5)];
        if (aVar.f1872g) {
            this.f1708b = new ArrayList<>(size);
            this.f1709c = new int[size];
            this.f1710d = new int[size];
            int i10 = 0;
            int i11 = 0;
            while (i10 < size) {
                d0.a aVar2 = aVar.f1866a.get(i10);
                int i12 = i11 + 1;
                this.f1707a[i11] = aVar2.f1880a;
                ArrayList<String> arrayList = this.f1708b;
                Fragment fragment = aVar2.f1881b;
                arrayList.add(fragment != null ? fragment.f1724e : null);
                int[] iArr = this.f1707a;
                int i13 = i12 + 1;
                iArr[i12] = aVar2.f1882c;
                int i14 = i13 + 1;
                iArr[i13] = aVar2.f1883d;
                int i15 = i14 + 1;
                iArr[i14] = aVar2.f1884e;
                iArr[i15] = aVar2.f1885f;
                this.f1709c[i10] = aVar2.f1886g.ordinal();
                this.f1710d[i10] = aVar2.h.ordinal();
                i10++;
                i11 = i15 + 1;
            }
            this.f1711e = aVar.f1871f;
            this.f1712f = aVar.h;
            this.f1713g = aVar.f1839r;
            this.h = aVar.f1873i;
            this.f1714i = aVar.f1874j;
            this.f1715j = aVar.f1875k;
            this.f1716k = aVar.f1876l;
            this.f1717l = aVar.f1877m;
            this.f1718m = aVar.f1878n;
            this.f1719n = aVar.f1879o;
            return;
        }
        throw new IllegalStateException("Not on back stack");
    }

    public int describeContents() {
        return 0;
    }

    public void writeToParcel(Parcel parcel, int i10) {
        parcel.writeIntArray(this.f1707a);
        parcel.writeStringList(this.f1708b);
        parcel.writeIntArray(this.f1709c);
        parcel.writeIntArray(this.f1710d);
        parcel.writeInt(this.f1711e);
        parcel.writeString(this.f1712f);
        parcel.writeInt(this.f1713g);
        parcel.writeInt(this.h);
        TextUtils.writeToParcel(this.f1714i, parcel, 0);
        parcel.writeInt(this.f1715j);
        TextUtils.writeToParcel(this.f1716k, parcel, 0);
        parcel.writeStringList(this.f1717l);
        parcel.writeStringList(this.f1718m);
        parcel.writeInt(this.f1719n ? 1 : 0);
    }

    public BackStackState(Parcel parcel) {
        this.f1707a = parcel.createIntArray();
        this.f1708b = parcel.createStringArrayList();
        this.f1709c = parcel.createIntArray();
        this.f1710d = parcel.createIntArray();
        this.f1711e = parcel.readInt();
        this.f1712f = parcel.readString();
        this.f1713g = parcel.readInt();
        this.h = parcel.readInt();
        this.f1714i = (CharSequence) TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel);
        this.f1715j = parcel.readInt();
        this.f1716k = (CharSequence) TextUtils.CHAR_SEQUENCE_CREATOR.createFromParcel(parcel);
        this.f1717l = parcel.createStringArrayList();
        this.f1718m = parcel.createStringArrayList();
        this.f1719n = parcel.readInt() != 0;
    }
}
