package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TabHost;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import bb.h;
import com.duokan.airkan.server.f;
import java.util.ArrayList;
import java.util.Objects;

@Deprecated
public class FragmentTabHost extends TabHost implements TabHost.OnTabChangeListener {

    /* renamed from: a  reason: collision with root package name */
    public final ArrayList<a> f1833a = new ArrayList<>();

    /* renamed from: b  reason: collision with root package name */
    public TabHost.OnTabChangeListener f1834b;

    /* renamed from: c  reason: collision with root package name */
    public boolean f1835c;

    public static class SavedState extends View.BaseSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public String f1836a;

        public class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }

        @NonNull
        public String toString() {
            StringBuilder a10 = f.a("FragmentTabHost.SavedState{");
            a10.append(Integer.toHexString(System.identityHashCode(this)));
            a10.append(" curTab=");
            return h.b(a10, this.f1836a, "}");
        }

        public void writeToParcel(Parcel parcel, int i10) {
            super.writeToParcel(parcel, i10);
            parcel.writeString(this.f1836a);
        }

        public SavedState(Parcel parcel) {
            super(parcel);
            this.f1836a = parcel.readString();
        }
    }

    public static final class a {
    }

    @Deprecated
    public FragmentTabHost(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        super(context, attributeSet);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, new int[]{16842995}, 0, 0);
        obtainStyledAttributes.getResourceId(0, 0);
        obtainStyledAttributes.recycle();
        super.setOnTabChangedListener(this);
    }

    @Nullable
    public final d0 a(@Nullable String str, @Nullable d0 d0Var) {
        if (this.f1833a.size() <= 0) {
            return null;
        }
        Objects.requireNonNull(this.f1833a.get(0));
        throw null;
    }

    @Deprecated
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        String currentTabTag = getCurrentTabTag();
        if (this.f1833a.size() <= 0) {
            this.f1835c = true;
            d0 a10 = a(currentTabTag, null);
            if (a10 != null) {
                a10.c();
                throw null;
            }
            return;
        }
        Objects.requireNonNull(this.f1833a.get(0));
        throw null;
    }

    @Deprecated
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.f1835c = false;
    }

    @Deprecated
    public void onRestoreInstanceState(@SuppressLint({"UnknownNullness"}) Parcelable parcelable) {
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.getSuperState());
        setCurrentTabByTag(savedState.f1836a);
    }

    @NonNull
    @Deprecated
    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        savedState.f1836a = getCurrentTabTag();
        return savedState;
    }

    @Deprecated
    public void onTabChanged(@Nullable String str) {
        d0 a10;
        if (this.f1835c && (a10 = a(str, null)) != null) {
            a10.c();
        }
        TabHost.OnTabChangeListener onTabChangeListener = this.f1834b;
        if (onTabChangeListener != null) {
            onTabChangeListener.onTabChanged(str);
        }
    }

    @Deprecated
    public void setOnTabChangedListener(@Nullable TabHost.OnTabChangeListener onTabChangeListener) {
        this.f1834b = onTabChangeListener;
    }

    @Deprecated
    public void setup() {
        throw new IllegalStateException("Must call setup() that takes a Context and FragmentManager");
    }
}
