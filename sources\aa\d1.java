package aa;

import java.io.IOException;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: DERSetParser */
public class d1 implements u {

    /* renamed from: a  reason: collision with root package name */
    public v f175a;

    public d1(v vVar) {
        this.f175a = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return new c1(this.f175a.c(), false);
        } catch (IOException e10) {
            throw new ASN1ParsingException(e10.getMessage(), e10);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new c1(this.f175a.c(), false);
    }
}
