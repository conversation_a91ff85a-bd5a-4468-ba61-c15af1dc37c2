package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewGroup;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;
import j0.m;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;

/* compiled from: ChildHelper */
public class c {

    /* renamed from: a  reason: collision with root package name */
    public final b f2342a;

    /* renamed from: b  reason: collision with root package name */
    public final a f2343b = new a();

    /* renamed from: c  reason: collision with root package name */
    public final List<View> f2344c = new ArrayList();

    /* compiled from: ChildHelper */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public long f2345a = 0;

        /* renamed from: b  reason: collision with root package name */
        public a f2346b;

        public void a(int i10) {
            if (i10 >= 64) {
                a aVar = this.f2346b;
                if (aVar != null) {
                    aVar.a(i10 - 64);
                    return;
                }
                return;
            }
            this.f2345a &= ~(1 << i10);
        }

        public int b(int i10) {
            a aVar = this.f2346b;
            if (aVar == null) {
                if (i10 >= 64) {
                    return Long.bitCount(this.f2345a);
                }
                return Long.bitCount(this.f2345a & ((1 << i10) - 1));
            } else if (i10 < 64) {
                return Long.bitCount(this.f2345a & ((1 << i10) - 1));
            } else {
                return Long.bitCount(this.f2345a) + aVar.b(i10 - 64);
            }
        }

        public final void c() {
            if (this.f2346b == null) {
                this.f2346b = new a();
            }
        }

        public boolean d(int i10) {
            if (i10 < 64) {
                return (this.f2345a & (1 << i10)) != 0;
            }
            c();
            return this.f2346b.d(i10 - 64);
        }

        public void e(int i10, boolean z10) {
            if (i10 >= 64) {
                c();
                this.f2346b.e(i10 - 64, z10);
                return;
            }
            long j10 = this.f2345a;
            boolean z11 = (Long.MIN_VALUE & j10) != 0;
            long j11 = (1 << i10) - 1;
            this.f2345a = ((j10 & (~j11)) << 1) | (j10 & j11);
            if (z10) {
                h(i10);
            } else {
                a(i10);
            }
            if (z11 || this.f2346b != null) {
                c();
                this.f2346b.e(0, z11);
            }
        }

        public boolean f(int i10) {
            if (i10 >= 64) {
                c();
                return this.f2346b.f(i10 - 64);
            }
            long j10 = 1 << i10;
            long j11 = this.f2345a;
            boolean z10 = (j11 & j10) != 0;
            long j12 = j11 & (~j10);
            this.f2345a = j12;
            long j13 = j10 - 1;
            this.f2345a = (j12 & j13) | Long.rotateRight((~j13) & j12, 1);
            a aVar = this.f2346b;
            if (aVar != null) {
                if (aVar.d(0)) {
                    h(63);
                }
                this.f2346b.f(0);
            }
            return z10;
        }

        public void g() {
            this.f2345a = 0;
            a aVar = this.f2346b;
            if (aVar != null) {
                aVar.g();
            }
        }

        public void h(int i10) {
            if (i10 >= 64) {
                c();
                this.f2346b.h(i10 - 64);
                return;
            }
            this.f2345a |= 1 << i10;
        }

        public String toString() {
            if (this.f2346b == null) {
                return Long.toBinaryString(this.f2345a);
            }
            return this.f2346b.toString() + "xx" + Long.toBinaryString(this.f2345a);
        }
    }

    /* compiled from: ChildHelper */
    public interface b {
    }

    public c(b bVar) {
        this.f2342a = bVar;
    }

    public void a(View view, int i10, boolean z10) {
        int i11;
        if (i10 < 0) {
            i11 = ((x) this.f2342a).b();
        } else {
            i11 = f(i10);
        }
        this.f2343b.e(i11, z10);
        if (z10) {
            i(view);
        }
        x xVar = (x) this.f2342a;
        xVar.f2479a.addView(view, i11);
        RecyclerView recyclerView = xVar.f2479a;
        Objects.requireNonNull(recyclerView);
        RecyclerView.K(view);
        List<RecyclerView.k> list = recyclerView.f2163p0;
        if (list != null) {
            int size = list.size();
            while (true) {
                size--;
                if (size >= 0) {
                    recyclerView.f2163p0.get(size).a(view);
                } else {
                    return;
                }
            }
        }
    }

    public void b(View view, int i10, ViewGroup.LayoutParams layoutParams, boolean z10) {
        int i11;
        if (i10 < 0) {
            i11 = ((x) this.f2342a).b();
        } else {
            i11 = f(i10);
        }
        this.f2343b.e(i11, z10);
        if (z10) {
            i(view);
        }
        x xVar = (x) this.f2342a;
        Objects.requireNonNull(xVar);
        RecyclerView.w K = RecyclerView.K(view);
        if (K != null) {
            if (K.n() || K.t()) {
                K.f2275j &= -257;
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append("Called attach on a child which is not detached: ");
                sb.append(K);
                throw new IllegalArgumentException(b.a(xVar.f2479a, sb));
            }
        }
        xVar.f2479a.attachViewToParent(view, i11, layoutParams);
    }

    public void c(int i10) {
        RecyclerView.w K;
        int f10 = f(i10);
        this.f2343b.f(f10);
        x xVar = (x) this.f2342a;
        View childAt = xVar.f2479a.getChildAt(f10);
        if (!(childAt == null || (K = RecyclerView.K(childAt)) == null)) {
            if (!K.n() || K.t()) {
                K.b(256);
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append("called detach on an already detached child ");
                sb.append(K);
                throw new IllegalArgumentException(b.a(xVar.f2479a, sb));
            }
        }
        xVar.f2479a.detachViewFromParent((RecyclerView) f10);
    }

    public View d(int i10) {
        return ((x) this.f2342a).a(f(i10));
    }

    public int e() {
        return ((x) this.f2342a).b() - this.f2344c.size();
    }

    public final int f(int i10) {
        if (i10 < 0) {
            return -1;
        }
        int b10 = ((x) this.f2342a).b();
        int i11 = i10;
        while (i11 < b10) {
            int b11 = i10 - (i11 - this.f2343b.b(i11));
            if (b11 == 0) {
                while (this.f2343b.d(i11)) {
                    i11++;
                }
                return i11;
            }
            i11 += b11;
        }
        return -1;
    }

    public View g(int i10) {
        return ((x) this.f2342a).f2479a.getChildAt(i10);
    }

    public int h() {
        return ((x) this.f2342a).b();
    }

    public final void i(View view) {
        this.f2344c.add(view);
        x xVar = (x) this.f2342a;
        Objects.requireNonNull(xVar);
        RecyclerView.w K = RecyclerView.K(view);
        if (K != null) {
            RecyclerView recyclerView = xVar.f2479a;
            int i10 = K.f2282q;
            if (i10 != -1) {
                K.f2281p = i10;
            } else {
                View view2 = K.f2267a;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                K.f2281p = view2.getImportantForAccessibility();
            }
            recyclerView.f0(K, 4);
        }
    }

    public int j(View view) {
        int indexOfChild = ((x) this.f2342a).f2479a.indexOfChild(view);
        if (indexOfChild != -1 && !this.f2343b.d(indexOfChild)) {
            return indexOfChild - this.f2343b.b(indexOfChild);
        }
        return -1;
    }

    public boolean k(View view) {
        return this.f2344c.contains(view);
    }

    public final boolean l(View view) {
        if (!this.f2344c.remove(view)) {
            return false;
        }
        x xVar = (x) this.f2342a;
        Objects.requireNonNull(xVar);
        RecyclerView.w K = RecyclerView.K(view);
        if (K == null) {
            return true;
        }
        xVar.f2479a.f0(K, K.f2281p);
        K.f2281p = 0;
        return true;
    }

    public String toString() {
        return this.f2343b.toString() + ", hidden list:" + this.f2344c.size();
    }
}
