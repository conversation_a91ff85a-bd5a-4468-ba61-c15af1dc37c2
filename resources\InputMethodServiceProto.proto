syntax = "proto3";

package com.xiaomi.idm.service.input.proto;

option java_package = "com.xiaomi.idm.service.input.proto";
option java_outer_classname = "InputMethodServiceProto";

  message StartInputBox {
  int32 aid = 1;
  string clientId = 2;
  int32 methodType = 3;
  int32 imeOptions = 4;
  string inputContent = 5;
  int32 inputTextLength = 6;
  int32 characterType = 7;
}
  message InputMethodResponse {
  int32 code = 1;
  string message = 2;
  string response = 3;
}
  message TextEvent {
  string inputData = 1;
}
  message InputCompleteEvent {
  int32 actionData = 1;
}