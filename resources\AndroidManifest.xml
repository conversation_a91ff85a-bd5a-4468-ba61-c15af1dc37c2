<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:sharedUserId="android.uid.system" android:versionCode="1001606607" android:versionName="3.1.609.10" android:compileSdkVersion="33" android:compileSdkVersionCodename="13" package="com.xiaomi.mi_connect_service" platformBuildVersionCode="33" platformBuildVersionName="13">
    <uses-sdk android:minSdkVersion="28" android:targetSdkVersion="29"/>
    <permission android:name="com.xiaomi.mi_connect_service.permission.RECEIVE_ENDPOINT" android:protectionLevel="normal"/>
    <permission android:name="com.xiaomi.mi_connect_service.permission.RECEIVE_NFC_NOTIFICATION" android:protectionLevel="normal"/>
    <uses-feature android:name="android.hardware.type.television" android:required="true"/>
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.NFC"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS" android:maxSdkVersion="26"/>
    <uses-permission android:name="android.permission.NETWORK_SETTINGS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="com.xiaomi.mi_connect_service.permission.RECEIVE_ENDPOINT"/>
    <uses-permission android:name="android.permission.REAL_GET_TASKS"/>
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REPLACED"/>
    <uses-permission android:name="android.permission.LOCAL_MAC_ADDRESS"/>
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission-sdk-23 android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.Manifest.permission.DEVICE_POWE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS" android:maxSdkVersion="22"/>
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" android:maxSdkVersion="22"/>
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.USE_CREDENTIALS" android:maxSdkVersion="22"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE"/>
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD"/>
    <uses-permission android:name="com.duokan.permission.START_STOP_SERVICE"/>
    <uses-permission android:name="com.duokan.permission.AIRKAN_SERVICE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.INJECT_EVENTS"/>
    <uses-permission android:name="android.permission.READ_FRAME_BUFFER"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="mitv.permission.ACCESS_INNER_APPLICATION"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <permission android:label="tvbox start and stop" android:name="com.duokan.permission.START_STOP_SERVICE" android:protectionLevel="normal" android:description="@string/send_service_start"/>
    <permission android:label="receive tvbox start and stop" android:name="com.duokan.permission.AIRKAN_SERVICE" android:protectionLevel="normal" android:description="@string/recv_service_start"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.DELETE_PACKAGES"/>
    <uses-permission android:name="android.permission.REBOOT"/>
    <permission android:name="com.xiaomi.mitv.remotecontroller.service.permission.MIPUSH_RECEIVE" android:protectionLevel="signature"/>
    <uses-permission android:name="com.xiaomi.mitv.remotecontroller.service.permission.MIPUSH_RECEIVE"/>
    <uses-permission android:name="com.android.systemui.BIND_ACCEPT"/>
    <uses-permission android:name="com.xiaomi.mitv.code.SMARTSHARE_CODE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <application android:theme="@style/AppTheme" android:label="@string/app_name" android:icon="@mipmap/ic_launcher" android:name="com.xiaomi.mi_connect_service.MyApplication" android:allowBackup="false" android:largeHeap="true" android:supportsRtl="true" android:extractNativeLibs="true" android:roundIcon="@mipmap/ic_launcher_round" android:appComponentFactory="androidx.core.app.CoreComponentFactory">
        <activity android:theme="@style/Theme.Translucent.NoTitleBar.Fullscreen" android:name="com.metv.uwb.activity.UwbVerifyCodeActivity" android:excludeFromRecents="true"/>
        <uses-library android:name="mitvmiddlewareimpl" android:required="false"/>
        <meta-data android:name="com.airkan.support3in1" android:value="true"/>
        <uses-library android:name="droidlogic.tv.software.core" android:required="false"/>
        <uses-library android:name="droidlogic.software.core" android:required="false"/>
        <provider android:name="com.duokan.airkan.startup.InitializationProvider" android:exported="false" android:authorities="com.xiaomi.mi_connect_service.airkan-startup">
            <meta-data android:name="com.duokan.airkan.AirkanInitializer" android:value="airkan.startup"/>
        </provider>
        <activity android:theme="@style/App.Theme.NoActionBar" android:name="com.duokan.airkan.config.DebugActivity" android:enabled="true" android:exported="true"/>
        <activity android:theme="@style/Theme.Transparent" android:label="@string/app_name" android:name="com.duokan.airkan.security.MilinkSecurityConfirmationActivity" android:exported="true" android:excludeFromRecents="true">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT"/>
                <action android:name="android.intent.action.MAIN"/>
            </intent-filter>
            <meta-data android:name="forbid_remote_capture" android:value="1"/>
        </activity>
        <service android:label="@string/playerservice_name" android:name="com.duokan.airkan.service.AirkanPhotoService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="duokan.airkan.tvbox.aidl.photo.IAirkanPhotoService"/>
            </intent-filter>
        </service>
        <service android:label="@string/playerservice_name" android:name="com.duokan.airkan.service.AirkanVideoService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="duokan.airkan.tvbox.aidl.video.IAirkanVideoService"/>
            </intent-filter>
        </service>
        <service android:label="@string/airkan_daemon_name" android:name="com.duokan.airkan.server.AirkanDaemon" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.duokan.airkan.security.aidl.IAirkanSecurityManager"/>
            </intent-filter>
        </service>
        <service android:name="com.duokan.airkan.wol.WakeOnLan" android:enabled="true"/>
        <service android:name="com.xiaomi.milink.discover.core.udt.UDTDiscoverService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.UDTDiscover.MiLink"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.UDTDiscover"/>
            </intent-filter>
        </service>
        <activity android:theme="@style/Theme.Transparent" android:name="com.duokan.airkan.security.MiPlaySecurityActivity"/>
        <service android:label="@string/rc_daemon_name" android:name="com.duokan.remotecontroller.service.RCDaemon" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.duokan.remotecontroller.aidl.IRCTVService"/>
            </intent-filter>
        </service>
        <service android:label="@string/milink_httpservice_name" android:name="com.duokan.httpserver.service.AirkanHttpService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.HttpService"/>
            </intent-filter>
        </service>
        <service android:label="@string/milink_httpservice_name" android:name="com.xiaomi.mitv.btrc.BluetoothRcService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mitv.btrc.BTRC_SERVICE"/>
            </intent-filter>
        </service>
        <service android:name="com.xiaomi.mitv.blerc.BlePeripheralRcService"/>
        <receiver android:name="com.duokan.airkan.videorelay.VideoChangedReceiver" android:exported="true">
            <intent-filter>
                <action android:name="mitv.action_VIDEO_CHANGED"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.mitv.action.play_state_change"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.metv.base.miaccount.AccountChangedReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.accounts.LOGIN_ACCOUNTS_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.duokan.airkan.server.StartBroadcastReceiver" android:exported="true">
            <intent-filter>
                <action android:name="com.duokan.duokanplayer.BOOT_COMPLETED"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="mitv.intent.action.tvconnect.enableservice"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.START_AIRKAN"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.START_SERVICE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.duokan.action.FACTORY_TEST_ON"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.duokan.action.FACTORY_TEST_OFF"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mitv.milink.WEIXIN_IME_INPUT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.duokan.airkan.tvbox.SHOWCASTAD_RC"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mitv.settings.CLOUD_SECURITY_CHANGED"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mitv.settings.SQUARE_MODE_CHANGE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.metv.monitor.keep_live"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mitv.milink.weak_up"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.mitv.tvhome.action.PATCHWALL_START_COMPLETED"/>
            </intent-filter>
        </receiver>
        <activity android:theme="@style/Theme.Translucent.NoTitleBar.Fullscreen" android:name="com.duokan.httpserver.service.activity.VerifyCodeActivity" android:exported="true" android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="action.airkan.http.verifycode"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <meta-data android:name="support_udt_new" android:value="true"/>
        <activity android:theme="@style/Theme.Transparent" android:label="@string/app_name" android:name="com.xiaomi.mitv.remotecontroller.service.install.ACodeActivity" android:exported="true" android:excludeFromRecents="true" android:launchMode="singleTask">
            <meta-data android:name="forbid_remote_capture" android:value="1"/>
            <intent-filter>
                <action android:name="action.phone.install.data"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity android:label="@string/app_name" android:name="com.xiaomi.mitv.remotecontroller.service.activity.TestActivity" android:excludeFromRecents="true"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.UploadService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.milink.ReceiveMilinkUDTService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.milink.MilinkOperationService" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mitv.action.MilinkOperationService"/>
            </intent-filter>
        </service>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.milink.ReceiveKeyUDTService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.receiver.GetMiliaoFriendService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.voice.VoiceServer" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mitv.remotecontroller.service.voice.SERVICE"/>
            </intent-filter>
        </service>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.MilinkOperationHandlerService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.remote.PIPManager"/>
        <receiver android:name="com.xiaomi.mitv.remotecontroller.service.receiver.ConnectChangedReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>
        <receiver android:name="com.xiaomi.mitv.remotecontroller.service.receiver.RemoteControllerServiceBroadcastReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="mitv.intent.action.tvconnect.enableservice"/>
                <action android:name="com.xiaomi.milink.action.START_AIRKAN"/>
                <action android:name="com.xiaomi.milink.action.START_UDTSERVICE"/>
                <action android:name="com.duokan.action.FACTORY_TEST_ON"/>
                <action android:name="com.duokan.action.FACTORY_TEST_OFF"/>
                <action android:name="com.xiaomi.mitv.settings.CLOUD_SECURITY_CHANGED"/>
                <action android:name="com.xiaomi.mitv.settings.SQUARE_MODE_CHANGE"/>
                <action android:name="com.xiaomi.milink.action.START_SERVICE"/>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                <action android:name="com.xiaomi.milink.action.START_RCSERVICE"/>
                <action android:name="com.xiaomi.mitv.rc.weak_up"/>
                <action android:name="com.mitv.tvhome.action.PATCHWALL_START_COMPLETED"/>
            </intent-filter>
        </receiver>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.receiver.ShowLogoutDialogService"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.receiver.UpdatePushAliasService"/>
        <receiver android:name="com.xiaomi.mitv.remotecontroller.service.receiver.AccountChangedReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.accounts.LOGIN_ACCOUNTS_CHANGED"/>
            </intent-filter>
        </receiver>
        <uses-library android:name="libEventInjector" android:required="false"/>
        <service android:name="com.xiaomi.milink.transmit.core.UDTTransmitService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.UDTTransmit"/>
            </intent-filter>
        </service>
        <service android:name="com.xiaomi.mitv.assistant.server.AssistantService" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mitv.assistant.manual.SOCIAL_SERVICE"/>
            </intent-filter>
        </service>
        <provider android:name="com.xiaomi.mitv.remotecontroller.service.provider.SocialTVProviderMerge" android:exported="true" android:multiprocess="false" android:authorities="com.xiaomi.mitv.remotecontroller.service.new.SocialTVProviderMerge"/>
        <activity android:theme="@style/Theme_Translucent_NoTitle" android:name="com.xiaomi.mitv.assistant.util.DelegateActivity" android:excludeFromRecents="true"/>
        <activity android:theme="@style/Theme_Translucent_NoTitle" android:name="com.xiaomi.mitv.assistant.ui.CaptchaActivity" android:excludeFromRecents="true">
            <meta-data android:name="forbid_remote_capture" android:value="1"/>
        </activity>
        <activity android:theme="@style/Theme_Translucent_NoTitle" android:name="com.xiaomi.mitv.assistant.ui.NotifyActivity" android:excludeFromRecents="true"/>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.RCDataService" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.milink.action.RCDataTransmitService"/>
            </intent-filter>
        </service>
        <service android:name="com.xiaomi.mitv.remotecontroller.service.service.RcMonitorService" android:exported="true"/>
        <activity android:theme="@style/Theme.Translucent.NoTitleBar.Fullscreen" android:name="com.xiaomi.mitv.remotecontroller.service.activity.UwbVerifyCodeActivity" android:excludeFromRecents="true">
            <meta-data android:name="forbid_remote_capture" android:value="1"/>
        </activity>
        <service android:name="com.metv.base.download.DownloadService"/>
        <service android:name="com.metv.base.service.AirkanBaseService" android:exported="true">
            <intent-filter>
                <action android:name="com.metv.airkan.base.airkan_base_service"/>
            </intent-filter>
        </service>
        <service android:name="androidx.room.MultiInstanceInvalidationService" android:exported="false" android:directBootAware="true"/>
        <meta-data android:name="appcompat" android:value="1"/>
        <receiver android:name="com.xiaomi.mitv.pie.InstallEventReceiver" android:permission="android.permission.INSTALL_PACKAGES" android:exported="true">
            <intent-filter>
                <action android:name="com.android.packageinstaller.ACTION_INSTALL_COMMIT.APPSTORE"/>
            </intent-filter>
        </receiver>
        <activity android:theme="@style/Theme.Translucent.NoTitleBar.Fullscreen" android:name="com.newbiz.remotecontrol.VerifyCodeActivity" android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="action.remotecontrol.verifycode"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity android:name="com.newbiz.remotecontrol.RemoteControlActivity" android:screenOrientation="sensorLandscape" android:configChanges="keyboardHidden|orientation|screenSize"/>
        <activity android:name="com.newbiz.remotecontrol.RcTestActivity" android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="action.remotecontrol.test"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <meta-data android:name="slidingwidget" android:value="1"/>
        <meta-data android:name="pickerwidget" android:value="1"/>
        <meta-data android:name="popupwidget" android:value="1"/>
        <meta-data android:name="androidbasewidget" android:value="1"/>
        <meta-data android:name="miuixbasewidget" android:value="1"/>
        <meta-data android:name="viewpager" android:value="2"/>
        <meta-data android:name="springback" android:value="2"/>
        <meta-data android:name="nest" android:value="2"/>
        <meta-data android:name="dynamicoverscroller" android:value="2"/>
        <meta-data android:name="folme" android:value="2"/>
        <meta-data android:name="haptic" android:value="1"/>
        <meta-data android:name="graphics" android:value="2"/>
        <meta-data android:name="core" android:value="2"/>
        <meta-data android:name="blurdrawable" android:value="1"/>
        <meta-data android:name="animation" android:value="1"/>
        <meta-data android:name="smooth" android:value="1"/>
        <meta-data android:name="devicebaseinfo" android:value="1"/>
    </application>
</manifest>
