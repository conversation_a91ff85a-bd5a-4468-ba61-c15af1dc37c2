package a4;

import com.duokan.airkan.common.ServiceData;

public class n implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ServiceData f62a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ l f63b;

    public n(l lVar, ServiceData serviceData) {
        this.f63b = lVar;
        this.f62a = serviceData;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:19:0x00a9, code lost:
        if (r0 == null) goto L_0x00ab;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void run() {
        /*
        // Method dump skipped, instructions count: 303
        */
        throw new UnsupportedOperationException("Method not decompiled: a4.n.run():void");
    }
}
