package ab;

import aa.m;
import ca.c;
import ca.d;
import java.security.spec.AlgorithmParameterSpec;
import ya.a;

/* compiled from: GOST3410ParameterSpec */
public class h implements AlgorithmParameterSpec, a {

    /* renamed from: a  reason: collision with root package name */
    public i f257a;

    /* renamed from: b  reason: collision with root package name */
    public String f258b;

    /* renamed from: c  reason: collision with root package name */
    public String f259c;

    /* renamed from: d  reason: collision with root package name */
    public String f260d;

    public h(String str, String str2, String str3) {
        d dVar;
        try {
            dVar = (d) c.f3238b.get(new m(str));
        } catch (IllegalArgumentException unused) {
            m mVar = (m) c.f3237a.get(str);
            if (mVar != null) {
                str = mVar.f199a;
                dVar = (d) c.f3238b.get(mVar);
            } else {
                dVar = null;
            }
        }
        if (dVar != null) {
            this.f257a = new i(dVar.f3243b.o(), dVar.f3244c.o(), dVar.f3245d.o());
            this.f258b = str;
            this.f259c = str2;
            this.f260d = str3;
            return;
        }
        throw new IllegalArgumentException("no key parameter set for passed in name/OID.");
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof h)) {
            return false;
        }
        h hVar = (h) obj;
        if (!this.f257a.equals(hVar.f257a) || !this.f259c.equals(hVar.f259c)) {
            return false;
        }
        String str = this.f260d;
        String str2 = hVar.f260d;
        if (str == str2 || (str != null && str.equals(str2))) {
            return true;
        }
        return false;
    }

    public int hashCode() {
        int hashCode = this.f257a.hashCode() ^ this.f259c.hashCode();
        String str = this.f260d;
        return hashCode ^ (str != null ? str.hashCode() : 0);
    }

    public h(i iVar) {
        this.f257a = iVar;
        this.f259c = ca.a.f3226d.f199a;
        this.f260d = null;
    }
}
