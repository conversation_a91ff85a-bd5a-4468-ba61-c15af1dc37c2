package androidx.lifecycle;

import androidx.annotation.NonNull;

/* compiled from: ViewModelProvider */
public abstract class t extends v implements s {
    @Override // androidx.lifecycle.s
    @NonNull
    public <T extends q> T a(@NonNull Class<T> cls) {
        throw new UnsupportedOperationException("create(String, Class<?>) must be called on implementaions of KeyedFactory");
    }

    @NonNull
    public abstract <T extends q> T c(@NonNull String str, @NonNull Class<T> cls);
}
