package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.ActionProvider;
import android.view.ContextMenu;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewDebug;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import d0.b;
import j0.a;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuItemImpl */
public final class f implements b {
    public j0.a A;
    public MenuItem.OnActionExpandListener B;
    public boolean C = false;
    public ContextMenu.ContextMenuInfo D;

    /* renamed from: a  reason: collision with root package name */
    public final int f630a;

    /* renamed from: b  reason: collision with root package name */
    public final int f631b;

    /* renamed from: c  reason: collision with root package name */
    public final int f632c;

    /* renamed from: d  reason: collision with root package name */
    public final int f633d;

    /* renamed from: e  reason: collision with root package name */
    public CharSequence f634e;

    /* renamed from: f  reason: collision with root package name */
    public CharSequence f635f;

    /* renamed from: g  reason: collision with root package name */
    public Intent f636g;
    public char h;

    /* renamed from: i  reason: collision with root package name */
    public int f637i = 4096;

    /* renamed from: j  reason: collision with root package name */
    public char f638j;

    /* renamed from: k  reason: collision with root package name */
    public int f639k = 4096;

    /* renamed from: l  reason: collision with root package name */
    public Drawable f640l;

    /* renamed from: m  reason: collision with root package name */
    public int f641m = 0;

    /* renamed from: n  reason: collision with root package name */
    public d f642n;

    /* renamed from: o  reason: collision with root package name */
    public k f643o;

    /* renamed from: p  reason: collision with root package name */
    public MenuItem.OnMenuItemClickListener f644p;

    /* renamed from: q  reason: collision with root package name */
    public CharSequence f645q;

    /* renamed from: r  reason: collision with root package name */
    public CharSequence f646r;

    /* renamed from: s  reason: collision with root package name */
    public ColorStateList f647s = null;

    /* renamed from: t  reason: collision with root package name */
    public PorterDuff.Mode f648t = null;

    /* renamed from: u  reason: collision with root package name */
    public boolean f649u = false;

    /* renamed from: v  reason: collision with root package name */
    public boolean f650v = false;

    /* renamed from: w  reason: collision with root package name */
    public boolean f651w = false;

    /* renamed from: x  reason: collision with root package name */
    public int f652x = 16;

    /* renamed from: y  reason: collision with root package name */
    public int f653y = 0;

    /* renamed from: z  reason: collision with root package name */
    public View f654z;

    /* compiled from: MenuItemImpl */
    public class a implements a.b {
        public a() {
        }
    }

    public f(d dVar, int i10, int i11, int i12, int i13, CharSequence charSequence, int i14) {
        this.f642n = dVar;
        this.f630a = i11;
        this.f631b = i10;
        this.f632c = i12;
        this.f633d = i13;
        this.f634e = charSequence;
        this.f653y = i14;
    }

    public static void c(StringBuilder sb, int i10, int i11, String str) {
        if ((i10 & i11) == i11) {
            sb.append(str);
        }
    }

    @Override // d0.b
    public b a(j0.a aVar) {
        j0.a aVar2 = this.A;
        if (aVar2 != null) {
            aVar2.f6900b = null;
            aVar2.f6899a = null;
        }
        this.f654z = null;
        this.A = aVar;
        this.f642n.p(true);
        j0.a aVar3 = this.A;
        if (aVar3 != null) {
            aVar3.h(new a());
        }
        return this;
    }

    @Override // d0.b
    public j0.a b() {
        return this.A;
    }

    @Override // d0.b
    public boolean collapseActionView() {
        if ((this.f653y & 8) == 0) {
            return false;
        }
        if (this.f654z == null) {
            return true;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionCollapse(this)) {
            return this.f642n.d(this);
        }
        return false;
    }

    public final Drawable d(Drawable drawable) {
        if (drawable != null && this.f651w && (this.f649u || this.f650v)) {
            drawable = drawable.mutate();
            if (this.f649u) {
                drawable.setTintList(this.f647s);
            }
            if (this.f650v) {
                drawable.setTintMode(this.f648t);
            }
            this.f651w = false;
        }
        return drawable;
    }

    public char e() {
        return this.f642n.n() ? this.f638j : this.h;
    }

    @Override // d0.b
    public boolean expandActionView() {
        if (!f()) {
            return false;
        }
        MenuItem.OnActionExpandListener onActionExpandListener = this.B;
        if (onActionExpandListener == null || onActionExpandListener.onMenuItemActionExpand(this)) {
            return this.f642n.f(this);
        }
        return false;
    }

    public boolean f() {
        j0.a aVar;
        if ((this.f653y & 8) == 0) {
            return false;
        }
        if (this.f654z == null && (aVar = this.A) != null) {
            this.f654z = aVar.d(this);
        }
        if (this.f654z != null) {
            return true;
        }
        return false;
    }

    public boolean g() {
        return (this.f652x & 32) == 32;
    }

    public ActionProvider getActionProvider() {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.getActionProvider()");
    }

    @Override // d0.b
    public View getActionView() {
        View view = this.f654z;
        if (view != null) {
            return view;
        }
        j0.a aVar = this.A;
        if (aVar == null) {
            return null;
        }
        View d10 = aVar.d(this);
        this.f654z = d10;
        return d10;
    }

    @Override // d0.b
    public int getAlphabeticModifiers() {
        return this.f639k;
    }

    public char getAlphabeticShortcut() {
        return this.f638j;
    }

    @Override // d0.b
    public CharSequence getContentDescription() {
        return this.f645q;
    }

    public int getGroupId() {
        return this.f631b;
    }

    public Drawable getIcon() {
        Drawable drawable = this.f640l;
        if (drawable != null) {
            return d(drawable);
        }
        int i10 = this.f641m;
        if (i10 == 0) {
            return null;
        }
        Drawable a10 = m.a.a(this.f642n.f604a, i10);
        this.f641m = 0;
        this.f640l = a10;
        return d(a10);
    }

    @Override // d0.b
    public ColorStateList getIconTintList() {
        return this.f647s;
    }

    @Override // d0.b
    public PorterDuff.Mode getIconTintMode() {
        return this.f648t;
    }

    public Intent getIntent() {
        return this.f636g;
    }

    @ViewDebug.CapturedViewProperty
    public int getItemId() {
        return this.f630a;
    }

    public ContextMenu.ContextMenuInfo getMenuInfo() {
        return this.D;
    }

    @Override // d0.b
    public int getNumericModifiers() {
        return this.f637i;
    }

    public char getNumericShortcut() {
        return this.h;
    }

    public int getOrder() {
        return this.f632c;
    }

    public SubMenu getSubMenu() {
        return this.f643o;
    }

    @ViewDebug.CapturedViewProperty
    public CharSequence getTitle() {
        return this.f634e;
    }

    public CharSequence getTitleCondensed() {
        CharSequence charSequence = this.f635f;
        return charSequence != null ? charSequence : this.f634e;
    }

    @Override // d0.b
    public CharSequence getTooltipText() {
        return this.f646r;
    }

    public boolean h() {
        return (this.f652x & 4) != 0;
    }

    public boolean hasSubMenu() {
        return this.f643o != null;
    }

    public b i(View view) {
        int i10;
        this.f654z = view;
        this.A = null;
        if (view != null && view.getId() == -1 && (i10 = this.f630a) > 0) {
            view.setId(i10);
        }
        d dVar = this.f642n;
        dVar.f613k = true;
        dVar.p(true);
        return this;
    }

    @Override // d0.b
    public boolean isActionViewExpanded() {
        return this.C;
    }

    public boolean isCheckable() {
        return (this.f652x & 1) == 1;
    }

    public boolean isChecked() {
        return (this.f652x & 2) == 2;
    }

    public boolean isEnabled() {
        return (this.f652x & 16) != 0;
    }

    public boolean isVisible() {
        j0.a aVar = this.A;
        return (aVar == null || !aVar.g()) ? (this.f652x & 8) == 0 : (this.f652x & 8) == 0 && this.A.b();
    }

    public void j(boolean z10) {
        int i10 = this.f652x;
        int i11 = (z10 ? 2 : 0) | (i10 & -3);
        this.f652x = i11;
        if (i10 != i11) {
            this.f642n.p(false);
        }
    }

    public void k(boolean z10) {
        this.f652x = (z10 ? 4 : 0) | (this.f652x & -5);
    }

    public void l(boolean z10) {
        if (z10) {
            this.f652x |= 32;
        } else {
            this.f652x &= -33;
        }
    }

    public boolean m(boolean z10) {
        int i10 = this.f652x;
        int i11 = (z10 ? 0 : 8) | (i10 & -9);
        this.f652x = i11;
        if (i10 != i11) {
            return true;
        }
        return false;
    }

    public boolean n() {
        return this.f642n.o() && e() != 0;
    }

    public MenuItem setActionProvider(ActionProvider actionProvider) {
        throw new UnsupportedOperationException("This is not supported, use MenuItemCompat.setActionProvider()");
    }

    @Override // d0.b, android.view.MenuItem
    public /* bridge */ /* synthetic */ MenuItem setActionView(View view) {
        i(view);
        return this;
    }

    public MenuItem setAlphabeticShortcut(char c10) {
        if (this.f638j == c10) {
            return this;
        }
        this.f638j = Character.toLowerCase(c10);
        this.f642n.p(false);
        return this;
    }

    public MenuItem setCheckable(boolean z10) {
        int i10 = this.f652x;
        int i11 = (z10 ? 1 : 0) | (i10 & -2);
        this.f652x = i11;
        if (i10 != i11) {
            this.f642n.p(false);
        }
        return this;
    }

    public MenuItem setChecked(boolean z10) {
        if ((this.f652x & 4) != 0) {
            d dVar = this.f642n;
            Objects.requireNonNull(dVar);
            int groupId = getGroupId();
            int size = dVar.f609f.size();
            dVar.A();
            for (int i10 = 0; i10 < size; i10++) {
                f fVar = dVar.f609f.get(i10);
                if (fVar.f631b == groupId && fVar.h() && fVar.isCheckable()) {
                    fVar.j(fVar == this);
                }
            }
            dVar.z();
        } else {
            j(z10);
        }
        return this;
    }

    @Override // d0.b
    public MenuItem setContentDescription(CharSequence charSequence) {
        this.f645q = charSequence;
        this.f642n.p(false);
        return this;
    }

    public MenuItem setEnabled(boolean z10) {
        if (z10) {
            this.f652x |= 16;
        } else {
            this.f652x &= -17;
        }
        this.f642n.p(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setIcon(Drawable drawable) {
        this.f641m = 0;
        this.f640l = drawable;
        this.f651w = true;
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setIconTintList(@Nullable ColorStateList colorStateList) {
        this.f647s = colorStateList;
        this.f649u = true;
        this.f651w = true;
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setIconTintMode(PorterDuff.Mode mode) {
        this.f648t = mode;
        this.f650v = true;
        this.f651w = true;
        this.f642n.p(false);
        return this;
    }

    public MenuItem setIntent(Intent intent) {
        this.f636g = intent;
        return this;
    }

    public MenuItem setNumericShortcut(char c10) {
        if (this.h == c10) {
            return this;
        }
        this.h = c10;
        this.f642n.p(false);
        return this;
    }

    public MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener onActionExpandListener) {
        this.B = onActionExpandListener;
        return this;
    }

    public MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener onMenuItemClickListener) {
        this.f644p = onMenuItemClickListener;
        return this;
    }

    public MenuItem setShortcut(char c10, char c11) {
        this.h = c10;
        this.f638j = Character.toLowerCase(c11);
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public void setShowAsAction(int i10) {
        int i11 = i10 & 3;
        if (i11 == 0 || i11 == 1 || i11 == 2) {
            this.f653y = i10;
            d dVar = this.f642n;
            dVar.f613k = true;
            dVar.p(true);
            return;
        }
        throw new IllegalArgumentException("SHOW_AS_ACTION_ALWAYS, SHOW_AS_ACTION_IF_ROOM, and SHOW_AS_ACTION_NEVER are mutually exclusive.");
    }

    @Override // d0.b
    public MenuItem setShowAsActionFlags(int i10) {
        setShowAsAction(i10);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTitle(CharSequence charSequence) {
        this.f634e = charSequence;
        this.f642n.p(false);
        k kVar = this.f643o;
        if (kVar != null) {
            kVar.setHeaderTitle(charSequence);
        }
        return this;
    }

    public MenuItem setTitleCondensed(CharSequence charSequence) {
        this.f635f = charSequence;
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setTooltipText(CharSequence charSequence) {
        this.f646r = charSequence;
        this.f642n.p(false);
        return this;
    }

    public MenuItem setVisible(boolean z10) {
        if (m(z10)) {
            d dVar = this.f642n;
            dVar.h = true;
            dVar.p(true);
        }
        return this;
    }

    public String toString() {
        CharSequence charSequence = this.f634e;
        if (charSequence != null) {
            return charSequence.toString();
        }
        return null;
    }

    @Override // d0.b, android.view.MenuItem
    public MenuItem setActionView(int i10) {
        Context context = this.f642n.f604a;
        i(LayoutInflater.from(context).inflate(i10, (ViewGroup) new LinearLayout(context), false));
        return this;
    }

    @Override // d0.b
    /* renamed from: setContentDescription  reason: collision with other method in class */
    public b m0setContentDescription(CharSequence charSequence) {
        this.f645q = charSequence;
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    /* renamed from: setTooltipText  reason: collision with other method in class */
    public b m1setTooltipText(CharSequence charSequence) {
        this.f646r = charSequence;
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setAlphabeticShortcut(char c10, int i10) {
        if (this.f638j == c10 && this.f639k == i10) {
            return this;
        }
        this.f638j = Character.toLowerCase(c10);
        this.f639k = KeyEvent.normalizeMetaState(i10);
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setNumericShortcut(char c10, int i10) {
        if (this.h == c10 && this.f637i == i10) {
            return this;
        }
        this.h = c10;
        this.f637i = KeyEvent.normalizeMetaState(i10);
        this.f642n.p(false);
        return this;
    }

    @Override // d0.b
    public MenuItem setShortcut(char c10, char c11, int i10, int i11) {
        this.h = c10;
        this.f637i = KeyEvent.normalizeMetaState(i10);
        this.f638j = Character.toLowerCase(c11);
        this.f639k = KeyEvent.normalizeMetaState(i11);
        this.f642n.p(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setIcon(int i10) {
        this.f640l = null;
        this.f641m = i10;
        this.f651w = true;
        this.f642n.p(false);
        return this;
    }

    @Override // android.view.MenuItem
    public MenuItem setTitle(int i10) {
        setTitle(this.f642n.f604a.getString(i10));
        return this;
    }
}
