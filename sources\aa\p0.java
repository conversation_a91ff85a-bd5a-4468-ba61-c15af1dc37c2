package aa;

import java.io.IOException;
import org.spongycastle.asn1.ASN1Exception;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: DERExternalParser */
public class p0 implements e, q1 {

    /* renamed from: a  reason: collision with root package name */
    public v f214a;

    public p0(v vVar) {
        this.f214a = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            throw new ASN1ParsingException("unable to get DER object", e10);
        } catch (IllegalArgumentException e11) {
            throw new ASN1ParsingException("unable to get DER object", e11);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        try {
            return new o0(this.f214a.c());
        } catch (IllegalArgumentException e10) {
            throw new ASN1Exception(e10.getMessage(), e10);
        }
    }
}
