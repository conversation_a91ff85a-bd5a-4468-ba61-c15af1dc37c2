package aa;

import com.metv.base.utils.NetworkType;
import com.xiaomi.mitv.socialtv.common.net.media.VideoConstants;
import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERUTF8String */
public class g1 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f181a;

    public g1(byte[] bArr) {
        this.f181a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof g1)) {
            return false;
        }
        return a.a(this.f181a, ((g1) qVar).f181a);
    }

    @Override // aa.w
    public String getString() {
        char c10;
        byte b10;
        int i10;
        byte[] bArr = this.f181a;
        String str = c.f7876a;
        int i11 = 0;
        int i12 = 0;
        int i13 = 0;
        while (i12 < bArr.length) {
            i13++;
            if ((bArr[i12] & 240) == 240) {
                i13++;
                i12 += 4;
            } else if ((bArr[i12] & 224) == 224) {
                i12 += 3;
            } else {
                i12 = (bArr[i12] & 192) == 192 ? i12 + 2 : i12 + 1;
            }
        }
        char[] cArr = new char[i13];
        int i14 = 0;
        while (i11 < bArr.length) {
            if ((bArr[i11] & 240) == 240) {
                int i15 = (((((bArr[i11] & 3) << 18) | ((bArr[i11 + 1] & 63) << 12)) | ((bArr[i11 + 2] & 63) << 6)) | (bArr[i11 + 3] & 63)) - VideoConstants.SEARCH_MASK_EDUCATION;
                c10 = (char) ((i15 & NetworkType.NETWORK_ETHERNET) | 56320);
                cArr[i14] = (char) (55296 | (i15 >> 10));
                i11 += 4;
                i14++;
            } else if ((bArr[i11] & 224) == 224) {
                c10 = (char) (((bArr[i11] & 15) << 12) | ((bArr[i11 + 1] & 63) << 6) | (bArr[i11 + 2] & 63));
                i11 += 3;
            } else {
                if ((bArr[i11] & 208) == 208) {
                    i10 = (bArr[i11] & 31) << 6;
                    b10 = bArr[i11 + 1];
                } else if ((bArr[i11] & 192) == 192) {
                    i10 = (bArr[i11] & 31) << 6;
                    b10 = bArr[i11 + 1];
                } else {
                    c10 = (char) (bArr[i11] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP);
                    i11++;
                }
                c10 = (char) (i10 | (b10 & 63));
                i11 += 2;
            }
            cArr[i14] = c10;
            i14++;
        }
        return new String(cArr);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(12, this.f181a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f181a);
    }

    @Override // aa.q
    public int i() throws IOException {
        return v1.a(this.f181a.length) + 1 + this.f181a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
