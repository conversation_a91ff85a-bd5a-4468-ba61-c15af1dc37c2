package androidx.appcompat.widget;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.database.DataSetObserver;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.ThemedSpinnerAdapter;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.VisibleForTesting;
import androidx.appcompat.R$attr;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.app.d;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.Objects;
import java.util.WeakHashMap;

public class AppCompatSpinner extends Spinner {

    /* renamed from: i  reason: collision with root package name */
    public static final int[] f829i = {16843505};

    /* renamed from: a  reason: collision with root package name */
    public final e f830a;

    /* renamed from: b  reason: collision with root package name */
    public final Context f831b;

    /* renamed from: c  reason: collision with root package name */
    public x f832c;

    /* renamed from: d  reason: collision with root package name */
    public SpinnerAdapter f833d;

    /* renamed from: e  reason: collision with root package name */
    public final boolean f834e;

    /* renamed from: f  reason: collision with root package name */
    public e f835f;

    /* renamed from: g  reason: collision with root package name */
    public int f836g;
    public final Rect h;

    public static class SavedState extends View.BaseSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public boolean f837a;

        public class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }

        public void writeToParcel(Parcel parcel, int i10) {
            super.writeToParcel(parcel, i10);
            parcel.writeByte(this.f837a ? (byte) 1 : 0);
        }

        public SavedState(Parcel parcel) {
            super(parcel);
            this.f837a = parcel.readByte() != 0;
        }
    }

    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        public void onGlobalLayout() {
            if (!AppCompatSpinner.this.getInternalPopup().isShowing()) {
                AppCompatSpinner.this.b();
            }
            ViewTreeObserver viewTreeObserver = AppCompatSpinner.this.getViewTreeObserver();
            if (viewTreeObserver != null) {
                viewTreeObserver.removeOnGlobalLayoutListener(this);
            }
        }
    }

    @VisibleForTesting
    public class b implements e, DialogInterface.OnClickListener {
        @VisibleForTesting

        /* renamed from: a  reason: collision with root package name */
        public androidx.appcompat.app.d f839a;

        /* renamed from: b  reason: collision with root package name */
        public ListAdapter f840b;

        /* renamed from: c  reason: collision with root package name */
        public CharSequence f841c;

        public b() {
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public int a() {
            return 0;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void b(CharSequence charSequence) {
            this.f841c = charSequence;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void c(int i10) {
            Log.e("AppCompatSpinner", "Cannot set vertical offset for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void d(int i10) {
            Log.e("AppCompatSpinner", "Cannot set horizontal (original) offset for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void dismiss() {
            androidx.appcompat.app.d dVar = this.f839a;
            if (dVar != null) {
                dVar.dismiss();
                this.f839a = null;
            }
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void e(int i10) {
            Log.e("AppCompatSpinner", "Cannot set horizontal offset for MODE_DIALOG, ignoring");
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public int f() {
            return 0;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public CharSequence g() {
            return this.f841c;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public Drawable getBackground() {
            return null;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void h(ListAdapter listAdapter) {
            this.f840b = listAdapter;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public boolean isShowing() {
            androidx.appcompat.app.d dVar = this.f839a;
            if (dVar != null) {
                return dVar.isShowing();
            }
            return false;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void m(int i10, int i11) {
            if (this.f840b != null) {
                d.a aVar = new d.a(AppCompatSpinner.this.getPopupContext());
                CharSequence charSequence = this.f841c;
                if (charSequence != null) {
                    aVar.f440a.f360d = charSequence;
                }
                ListAdapter listAdapter = this.f840b;
                int selectedItemPosition = AppCompatSpinner.this.getSelectedItemPosition();
                AlertController.b bVar = aVar.f440a;
                bVar.f363g = listAdapter;
                bVar.h = this;
                bVar.f365j = selectedItemPosition;
                bVar.f364i = true;
                androidx.appcompat.app.d a10 = aVar.a();
                this.f839a = a10;
                ListView listView = a10.f439c.f335g;
                listView.setTextDirection(i10);
                listView.setTextAlignment(i11);
                this.f839a.show();
            }
        }

        public void onClick(DialogInterface dialogInterface, int i10) {
            AppCompatSpinner.this.setSelection(i10);
            if (AppCompatSpinner.this.getOnItemClickListener() != null) {
                AppCompatSpinner.this.performItemClick(null, i10, this.f840b.getItemId(i10));
            }
            androidx.appcompat.app.d dVar = this.f839a;
            if (dVar != null) {
                dVar.dismiss();
                this.f839a = null;
            }
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void setBackgroundDrawable(Drawable drawable) {
            Log.e("AppCompatSpinner", "Cannot set popup background for MODE_DIALOG, ignoring");
        }
    }

    public static class c implements ListAdapter, SpinnerAdapter {

        /* renamed from: a  reason: collision with root package name */
        public SpinnerAdapter f843a;

        /* renamed from: b  reason: collision with root package name */
        public ListAdapter f844b;

        public c(@Nullable SpinnerAdapter spinnerAdapter, @Nullable Resources.Theme theme) {
            this.f843a = spinnerAdapter;
            if (spinnerAdapter instanceof ListAdapter) {
                this.f844b = (ListAdapter) spinnerAdapter;
            }
            if (theme == null) {
                return;
            }
            if (spinnerAdapter instanceof ThemedSpinnerAdapter) {
                ThemedSpinnerAdapter themedSpinnerAdapter = (ThemedSpinnerAdapter) spinnerAdapter;
                if (themedSpinnerAdapter.getDropDownViewTheme() != theme) {
                    themedSpinnerAdapter.setDropDownViewTheme(theme);
                }
            } else if (spinnerAdapter instanceof i0) {
                i0 i0Var = (i0) spinnerAdapter;
                if (i0Var.getDropDownViewTheme() == null) {
                    i0Var.setDropDownViewTheme(theme);
                }
            }
        }

        public boolean areAllItemsEnabled() {
            ListAdapter listAdapter = this.f844b;
            if (listAdapter != null) {
                return listAdapter.areAllItemsEnabled();
            }
            return true;
        }

        public int getCount() {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter == null) {
                return 0;
            }
            return spinnerAdapter.getCount();
        }

        public View getDropDownView(int i10, View view, ViewGroup viewGroup) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter == null) {
                return null;
            }
            return spinnerAdapter.getDropDownView(i10, view, viewGroup);
        }

        public Object getItem(int i10) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter == null) {
                return null;
            }
            return spinnerAdapter.getItem(i10);
        }

        public long getItemId(int i10) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter == null) {
                return -1;
            }
            return spinnerAdapter.getItemId(i10);
        }

        public int getItemViewType(int i10) {
            return 0;
        }

        public View getView(int i10, View view, ViewGroup viewGroup) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter == null) {
                return null;
            }
            return spinnerAdapter.getDropDownView(i10, view, viewGroup);
        }

        public int getViewTypeCount() {
            return 1;
        }

        public boolean hasStableIds() {
            SpinnerAdapter spinnerAdapter = this.f843a;
            return spinnerAdapter != null && spinnerAdapter.hasStableIds();
        }

        public boolean isEmpty() {
            return getCount() == 0;
        }

        public boolean isEnabled(int i10) {
            ListAdapter listAdapter = this.f844b;
            if (listAdapter != null) {
                return listAdapter.isEnabled(i10);
            }
            return true;
        }

        public void registerDataSetObserver(DataSetObserver dataSetObserver) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter != null) {
                spinnerAdapter.registerDataSetObserver(dataSetObserver);
            }
        }

        public void unregisterDataSetObserver(DataSetObserver dataSetObserver) {
            SpinnerAdapter spinnerAdapter = this.f843a;
            if (spinnerAdapter != null) {
                spinnerAdapter.unregisterDataSetObserver(dataSetObserver);
            }
        }
    }

    @VisibleForTesting
    public class d extends ListPopupWindow implements e {

        /* renamed from: r0  reason: collision with root package name */
        public CharSequence f845r0;

        /* renamed from: s0  reason: collision with root package name */
        public ListAdapter f846s0;

        /* renamed from: t0  reason: collision with root package name */
        public final Rect f847t0 = new Rect();

        /* renamed from: u0  reason: collision with root package name */
        public int f848u0;

        public class a implements AdapterView.OnItemClickListener {
            public a(AppCompatSpinner appCompatSpinner) {
            }

            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
                AppCompatSpinner.this.setSelection(i10);
                if (AppCompatSpinner.this.getOnItemClickListener() != null) {
                    d dVar = d.this;
                    AppCompatSpinner.this.performItemClick(view, i10, dVar.f846s0.getItemId(i10));
                }
                d.this.dismiss();
            }
        }

        public class b implements ViewTreeObserver.OnGlobalLayoutListener {
            public b() {
            }

            public void onGlobalLayout() {
                d dVar = d.this;
                AppCompatSpinner appCompatSpinner = AppCompatSpinner.this;
                Objects.requireNonNull(dVar);
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                if (!(appCompatSpinner.isAttachedToWindow() && appCompatSpinner.getGlobalVisibleRect(dVar.f847t0))) {
                    d.this.dismiss();
                    return;
                }
                d.this.q();
                d.this.j();
            }
        }

        public class c implements PopupWindow.OnDismissListener {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ ViewTreeObserver.OnGlobalLayoutListener f852a;

            public c(ViewTreeObserver.OnGlobalLayoutListener onGlobalLayoutListener) {
                this.f852a = onGlobalLayoutListener;
            }

            public void onDismiss() {
                ViewTreeObserver viewTreeObserver = AppCompatSpinner.this.getViewTreeObserver();
                if (viewTreeObserver != null) {
                    viewTreeObserver.removeGlobalOnLayoutListener(this.f852a);
                }
            }
        }

        public d(Context context, AttributeSet attributeSet, int i10) {
            super(context, attributeSet, i10);
            this.f898o = AppCompatSpinner.this;
            p(true);
            this.f900p = new a(AppCompatSpinner.this);
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void b(CharSequence charSequence) {
            this.f845r0 = charSequence;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void d(int i10) {
            this.f848u0 = i10;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public CharSequence g() {
            return this.f845r0;
        }

        @Override // androidx.appcompat.widget.ListPopupWindow, androidx.appcompat.widget.AppCompatSpinner.e
        public void h(ListAdapter listAdapter) {
            super.h(listAdapter);
            this.f846s0 = listAdapter;
        }

        @Override // androidx.appcompat.widget.AppCompatSpinner.e
        public void m(int i10, int i11) {
            ViewTreeObserver viewTreeObserver;
            boolean isShowing = isShowing();
            q();
            this.f899o0.setInputMethodMode(2);
            j();
            v vVar = this.f885c;
            vVar.setChoiceMode(1);
            vVar.setTextDirection(i10);
            vVar.setTextAlignment(i11);
            int selectedItemPosition = AppCompatSpinner.this.getSelectedItemPosition();
            v vVar2 = this.f885c;
            if (isShowing() && vVar2 != null) {
                vVar2.setListSelectionHidden(false);
                vVar2.setSelection(selectedItemPosition);
                if (vVar2.getChoiceMode() != 0) {
                    vVar2.setItemChecked(selectedItemPosition, true);
                }
            }
            if (!isShowing && (viewTreeObserver = AppCompatSpinner.this.getViewTreeObserver()) != null) {
                b bVar = new b();
                viewTreeObserver.addOnGlobalLayoutListener(bVar);
                this.f899o0.setOnDismissListener(new c(bVar));
            }
        }

        public void q() {
            int i10;
            int i11;
            Drawable background = getBackground();
            int i12 = 0;
            if (background != null) {
                background.getPadding(AppCompatSpinner.this.h);
                if (q0.b(AppCompatSpinner.this)) {
                    i11 = AppCompatSpinner.this.h.right;
                } else {
                    i11 = -AppCompatSpinner.this.h.left;
                }
                i12 = i11;
            } else {
                Rect rect = AppCompatSpinner.this.h;
                rect.right = 0;
                rect.left = 0;
            }
            int paddingLeft = AppCompatSpinner.this.getPaddingLeft();
            int paddingRight = AppCompatSpinner.this.getPaddingRight();
            int width = AppCompatSpinner.this.getWidth();
            AppCompatSpinner appCompatSpinner = AppCompatSpinner.this;
            int i13 = appCompatSpinner.f836g;
            if (i13 == -2) {
                int a10 = appCompatSpinner.a((SpinnerAdapter) this.f846s0, getBackground());
                int i14 = AppCompatSpinner.this.getContext().getResources().getDisplayMetrics().widthPixels;
                Rect rect2 = AppCompatSpinner.this.h;
                int i15 = (i14 - rect2.left) - rect2.right;
                if (a10 > i15) {
                    a10 = i15;
                }
                o(Math.max(a10, (width - paddingLeft) - paddingRight));
            } else if (i13 == -1) {
                o((width - paddingLeft) - paddingRight);
            } else {
                o(i13);
            }
            if (q0.b(AppCompatSpinner.this)) {
                i10 = (((width - paddingRight) - this.f887e) - this.f848u0) + i12;
            } else {
                i10 = paddingLeft + this.f848u0 + i12;
            }
            this.f888f = i10;
        }
    }

    @VisibleForTesting
    public interface e {
        int a();

        void b(CharSequence charSequence);

        void c(int i10);

        void d(int i10);

        void dismiss();

        void e(int i10);

        int f();

        CharSequence g();

        Drawable getBackground();

        void h(ListAdapter listAdapter);

        boolean isShowing();

        void m(int i10, int i11);

        void setBackgroundDrawable(Drawable drawable);
    }

    public AppCompatSpinner(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.spinnerStyle);
    }

    public int a(SpinnerAdapter spinnerAdapter, Drawable drawable) {
        int i10 = 0;
        if (spinnerAdapter == null) {
            return 0;
        }
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 0);
        int makeMeasureSpec2 = View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 0);
        int max = Math.max(0, getSelectedItemPosition());
        int min = Math.min(spinnerAdapter.getCount(), max + 15);
        View view = null;
        int i11 = 0;
        for (int max2 = Math.max(0, max - (15 - (min - max))); max2 < min; max2++) {
            int itemViewType = spinnerAdapter.getItemViewType(max2);
            if (itemViewType != i10) {
                view = null;
                i10 = itemViewType;
            }
            view = spinnerAdapter.getView(max2, view, this);
            if (view.getLayoutParams() == null) {
                view.setLayoutParams(new ViewGroup.LayoutParams(-2, -2));
            }
            view.measure(makeMeasureSpec, makeMeasureSpec2);
            i11 = Math.max(i11, view.getMeasuredWidth());
        }
        if (drawable == null) {
            return i11;
        }
        drawable.getPadding(this.h);
        Rect rect = this.h;
        return i11 + rect.left + rect.right;
    }

    public void b() {
        this.f835f.m(getTextDirection(), getTextAlignment());
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        e eVar = this.f830a;
        if (eVar != null) {
            eVar.a();
        }
    }

    public int getDropDownHorizontalOffset() {
        e eVar = this.f835f;
        if (eVar != null) {
            return eVar.a();
        }
        return super.getDropDownHorizontalOffset();
    }

    public int getDropDownVerticalOffset() {
        e eVar = this.f835f;
        if (eVar != null) {
            return eVar.f();
        }
        return super.getDropDownVerticalOffset();
    }

    public int getDropDownWidth() {
        if (this.f835f != null) {
            return this.f836g;
        }
        return super.getDropDownWidth();
    }

    @VisibleForTesting
    public final e getInternalPopup() {
        return this.f835f;
    }

    public Drawable getPopupBackground() {
        e eVar = this.f835f;
        if (eVar != null) {
            return eVar.getBackground();
        }
        return super.getPopupBackground();
    }

    public Context getPopupContext() {
        return this.f831b;
    }

    public CharSequence getPrompt() {
        e eVar = this.f835f;
        return eVar != null ? eVar.g() : super.getPrompt();
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public ColorStateList getSupportBackgroundTintList() {
        e eVar = this.f830a;
        if (eVar != null) {
            return eVar.b();
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        e eVar = this.f830a;
        if (eVar != null) {
            return eVar.c();
        }
        return null;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        e eVar = this.f835f;
        if (eVar != null && eVar.isShowing()) {
            this.f835f.dismiss();
        }
    }

    public void onMeasure(int i10, int i11) {
        super.onMeasure(i10, i11);
        if (this.f835f != null && View.MeasureSpec.getMode(i10) == Integer.MIN_VALUE) {
            setMeasuredDimension(Math.min(Math.max(getMeasuredWidth(), a(getAdapter(), getBackground())), View.MeasureSpec.getSize(i10)), getMeasuredHeight());
        }
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        ViewTreeObserver viewTreeObserver;
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.getSuperState());
        if (savedState.f837a && (viewTreeObserver = getViewTreeObserver()) != null) {
            viewTreeObserver.addOnGlobalLayoutListener(new a());
        }
    }

    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        e eVar = this.f835f;
        savedState.f837a = eVar != null && eVar.isShowing();
        return savedState;
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        x xVar = this.f832c;
        if (xVar == null || !xVar.onTouch(this, motionEvent)) {
            return super.onTouchEvent(motionEvent);
        }
        return true;
    }

    public boolean performClick() {
        e eVar = this.f835f;
        if (eVar == null) {
            return super.performClick();
        }
        if (eVar.isShowing()) {
            return true;
        }
        b();
        return true;
    }

    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        e eVar = this.f830a;
        if (eVar != null) {
            eVar.e();
        }
    }

    public void setBackgroundResource(@DrawableRes int i10) {
        super.setBackgroundResource(i10);
        e eVar = this.f830a;
        if (eVar != null) {
            eVar.f(i10);
        }
    }

    public void setDropDownHorizontalOffset(int i10) {
        e eVar = this.f835f;
        if (eVar != null) {
            eVar.d(i10);
            this.f835f.e(i10);
            return;
        }
        super.setDropDownHorizontalOffset(i10);
    }

    public void setDropDownVerticalOffset(int i10) {
        e eVar = this.f835f;
        if (eVar != null) {
            eVar.c(i10);
        } else {
            super.setDropDownVerticalOffset(i10);
        }
    }

    public void setDropDownWidth(int i10) {
        if (this.f835f != null) {
            this.f836g = i10;
        } else {
            super.setDropDownWidth(i10);
        }
    }

    public void setPopupBackgroundDrawable(Drawable drawable) {
        e eVar = this.f835f;
        if (eVar != null) {
            eVar.setBackgroundDrawable(drawable);
        } else {
            super.setPopupBackgroundDrawable(drawable);
        }
    }

    public void setPopupBackgroundResource(@DrawableRes int i10) {
        setPopupBackgroundDrawable(m.a.a(getPopupContext(), i10));
    }

    public void setPrompt(CharSequence charSequence) {
        e eVar = this.f835f;
        if (eVar != null) {
            eVar.b(charSequence);
        } else {
            super.setPrompt(charSequence);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintList(@Nullable ColorStateList colorStateList) {
        e eVar = this.f830a;
        if (eVar != null) {
            eVar.h(colorStateList);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintMode(@Nullable PorterDuff.Mode mode) {
        e eVar = this.f830a;
        if (eVar != null) {
            eVar.i(mode);
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:20:0x0057, code lost:
        if (r4 != null) goto L_0x0059;
     */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x00da  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public AppCompatSpinner(@androidx.annotation.NonNull android.content.Context r9, @androidx.annotation.Nullable android.util.AttributeSet r10, int r11) {
        /*
        // Method dump skipped, instructions count: 222
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.AppCompatSpinner.<init>(android.content.Context, android.util.AttributeSet, int):void");
    }

    @Override // android.widget.AbsSpinner, android.widget.Spinner
    public void setAdapter(SpinnerAdapter spinnerAdapter) {
        if (!this.f834e) {
            this.f833d = spinnerAdapter;
            return;
        }
        super.setAdapter(spinnerAdapter);
        if (this.f835f != null) {
            Context context = this.f831b;
            if (context == null) {
                context = getContext();
            }
            this.f835f.h(new c(spinnerAdapter, context.getTheme()));
        }
    }
}
