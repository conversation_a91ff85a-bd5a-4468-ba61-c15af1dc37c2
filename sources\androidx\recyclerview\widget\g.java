package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class g extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ RecyclerView.w f2365a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ ViewPropertyAnimator f2366b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ View f2367c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ l f2368d;

    public g(l lVar, RecyclerView.w wVar, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2368d = lVar;
        this.f2365a = wVar;
        this.f2366b = viewPropertyAnimator;
        this.f2367c = view;
    }

    public void onAnimationEnd(Animator animator) {
        this.f2366b.setListener(null);
        this.f2367c.setAlpha(1.0f);
        this.f2368d.c(this.f2365a);
        this.f2368d.f2396q.remove(this.f2365a);
        this.f2368d.k();
    }

    public void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2368d);
    }
}
