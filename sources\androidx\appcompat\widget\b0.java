package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$drawable;
import androidx.appcompat.widget.g;
import j0.o;
import java.lang.ref.WeakReference;
import java.util.Objects;
import java.util.WeakHashMap;
import org.xmlpull.v1.XmlPullParser;
import u.d;
import u.e;
import u.g;
import u.h;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: ResourceManagerInternal */
public final class b0 {
    public static final PorterDuff.Mode h = PorterDuff.Mode.SRC_IN;

    /* renamed from: i  reason: collision with root package name */
    public static b0 f1058i;

    /* renamed from: j  reason: collision with root package name */
    public static final a f1059j = new a(6);

    /* renamed from: a  reason: collision with root package name */
    public WeakHashMap<Context, h<ColorStateList>> f1060a;

    /* renamed from: b  reason: collision with root package name */
    public g<String, b> f1061b;

    /* renamed from: c  reason: collision with root package name */
    public h<String> f1062c;

    /* renamed from: d  reason: collision with root package name */
    public final WeakHashMap<Context, d<WeakReference<Drawable.ConstantState>>> f1063d = new WeakHashMap<>(0);

    /* renamed from: e  reason: collision with root package name */
    public TypedValue f1064e;

    /* renamed from: f  reason: collision with root package name */
    public boolean f1065f;

    /* renamed from: g  reason: collision with root package name */
    public c f1066g;

    /* compiled from: ResourceManagerInternal */
    public static class a extends e<Integer, PorterDuffColorFilter> {
        public a(int i10) {
            super(i10);
        }
    }

    /* compiled from: ResourceManagerInternal */
    public interface b {
        Drawable a(@NonNull Context context, @NonNull XmlPullParser xmlPullParser, @NonNull AttributeSet attributeSet, @Nullable Resources.Theme theme);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    /* compiled from: ResourceManagerInternal */
    public interface c {
    }

    public static synchronized b0 c() {
        b0 b0Var;
        synchronized (b0.class) {
            if (f1058i == null) {
                f1058i = new b0();
            }
            b0Var = f1058i;
        }
        return b0Var;
    }

    public static synchronized PorterDuffColorFilter g(int i10, PorterDuff.Mode mode) {
        PorterDuffColorFilter porterDuffColorFilter;
        synchronized (b0.class) {
            a aVar = f1059j;
            Objects.requireNonNull(aVar);
            int i11 = (i10 + 31) * 31;
            porterDuffColorFilter = (PorterDuffColorFilter) aVar.a(Integer.valueOf(mode.hashCode() + i11));
            if (porterDuffColorFilter == null) {
                porterDuffColorFilter = new PorterDuffColorFilter(i10, mode);
                Objects.requireNonNull(aVar);
                PorterDuffColorFilter porterDuffColorFilter2 = (PorterDuffColorFilter) aVar.b(Integer.valueOf(mode.hashCode() + i11), porterDuffColorFilter);
            }
        }
        return porterDuffColorFilter;
    }

    public final synchronized boolean a(@NonNull Context context, long j10, @NonNull Drawable drawable) {
        Drawable.ConstantState constantState = drawable.getConstantState();
        if (constantState == null) {
            return false;
        }
        d<WeakReference<Drawable.ConstantState>> dVar = this.f1063d.get(context);
        if (dVar == null) {
            dVar = new d<>();
            this.f1063d.put(context, dVar);
        }
        dVar.f(j10, new WeakReference<>(constantState));
        return true;
    }

    public final Drawable b(@NonNull Context context, @DrawableRes int i10) {
        if (this.f1064e == null) {
            this.f1064e = new TypedValue();
        }
        TypedValue typedValue = this.f1064e;
        context.getResources().getValue(i10, typedValue, true);
        long j10 = (((long) typedValue.assetCookie) << 32) | ((long) typedValue.data);
        Drawable d10 = d(context, j10);
        if (d10 != null) {
            return d10;
        }
        LayerDrawable layerDrawable = null;
        if (this.f1066g != null && i10 == R$drawable.abc_cab_background_top_material) {
            layerDrawable = new LayerDrawable(new Drawable[]{e(context, R$drawable.abc_cab_background_internal_bg), e(context, R$drawable.abc_cab_background_top_mtrl_alpha)});
        }
        if (layerDrawable != null) {
            layerDrawable.setChangingConfigurations(typedValue.changingConfigurations);
            a(context, j10, layerDrawable);
        }
        return layerDrawable;
    }

    public final synchronized Drawable d(@NonNull Context context, long j10) {
        Object[] objArr;
        Object obj;
        d<WeakReference<Drawable.ConstantState>> dVar = this.f1063d.get(context);
        if (dVar == null) {
            return null;
        }
        WeakReference<Drawable.ConstantState> e10 = dVar.e(j10, null);
        if (e10 != null) {
            Drawable.ConstantState constantState = e10.get();
            if (constantState != null) {
                return constantState.newDrawable(context.getResources());
            }
            int h6 = o.h(dVar.f10401b, dVar.f10403d, j10);
            if (h6 >= 0 && (objArr = dVar.f10402c)[h6] != (obj = d.f10399e)) {
                objArr[h6] = obj;
                dVar.f10400a = true;
            }
        }
        return null;
    }

    public synchronized Drawable e(@NonNull Context context, @DrawableRes int i10) {
        return f(context, i10, false);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0028, code lost:
        if (r0 != false) goto L_0x002a;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public synchronized android.graphics.drawable.Drawable f(@androidx.annotation.NonNull android.content.Context r5, @androidx.annotation.DrawableRes int r6, boolean r7) {
        /*
            r4 = this;
            monitor-enter(r4)
            boolean r0 = r4.f1065f     // Catch:{ all -> 0x0048 }
            if (r0 == 0) goto L_0x0006
            goto L_0x002a
        L_0x0006:
            r0 = 1
            r4.f1065f = r0     // Catch:{ all -> 0x0048 }
            int r1 = androidx.appcompat.resources.R$drawable.abc_vector_test     // Catch:{ all -> 0x0048 }
            android.graphics.drawable.Drawable r1 = r4.e(r5, r1)     // Catch:{ all -> 0x0048 }
            r2 = 0
            if (r1 == 0) goto L_0x004a
            boolean r3 = r1 instanceof t0.b     // Catch:{ all -> 0x0048 }
            if (r3 != 0) goto L_0x0028
            java.lang.String r3 = "android.graphics.drawable.VectorDrawable"
            java.lang.Class r1 = r1.getClass()     // Catch:{ all -> 0x0048 }
            java.lang.String r1 = r1.getName()     // Catch:{ all -> 0x0048 }
            boolean r1 = r3.equals(r1)     // Catch:{ all -> 0x0048 }
            if (r1 == 0) goto L_0x0027
            goto L_0x0028
        L_0x0027:
            r0 = r2
        L_0x0028:
            if (r0 == 0) goto L_0x004a
        L_0x002a:
            android.graphics.drawable.Drawable r0 = r4.i(r5, r6)     // Catch:{ all -> 0x0048 }
            if (r0 != 0) goto L_0x0034
            android.graphics.drawable.Drawable r0 = r4.b(r5, r6)     // Catch:{ all -> 0x0048 }
        L_0x0034:
            if (r0 != 0) goto L_0x003c
            java.lang.Object r0 = z.a.f11008a     // Catch:{ all -> 0x0048 }
            android.graphics.drawable.Drawable r0 = r5.getDrawable(r6)     // Catch:{ all -> 0x0048 }
        L_0x003c:
            if (r0 == 0) goto L_0x0042
            android.graphics.drawable.Drawable r0 = r4.j(r5, r6, r7, r0)     // Catch:{ all -> 0x0048 }
        L_0x0042:
            if (r0 == 0) goto L_0x0046
            int[] r5 = androidx.appcompat.widget.u.f1200a     // Catch:{ all -> 0x0048 }
        L_0x0046:
            monitor-exit(r4)
            return r0
        L_0x0048:
            r5 = move-exception
            goto L_0x0054
        L_0x004a:
            r4.f1065f = r2
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r6 = "This app has been built with an incorrect configuration. Please configure your build for VectorDrawableCompat."
            r5.<init>(r6)
            throw r5
        L_0x0054:
            monitor-exit(r4)
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.b0.f(android.content.Context, int, boolean):android.graphics.drawable.Drawable");
    }

    public synchronized ColorStateList h(@NonNull Context context, @DrawableRes int i10) {
        ColorStateList colorStateList;
        h<ColorStateList> hVar;
        WeakHashMap<Context, h<ColorStateList>> weakHashMap = this.f1060a;
        ColorStateList colorStateList2 = null;
        if (weakHashMap == null || (hVar = weakHashMap.get(context)) == null) {
            colorStateList = null;
        } else {
            colorStateList = hVar.d(i10, null);
        }
        if (colorStateList == null) {
            c cVar = this.f1066g;
            if (cVar != null) {
                colorStateList2 = ((g.a) cVar).c(context, i10);
            }
            if (colorStateList2 != null) {
                if (this.f1060a == null) {
                    this.f1060a = new WeakHashMap<>();
                }
                h<ColorStateList> hVar2 = this.f1060a.get(context);
                if (hVar2 == null) {
                    hVar2 = new h<>();
                    this.f1060a.put(context, hVar2);
                }
                hVar2.a(i10, colorStateList2);
            }
            colorStateList = colorStateList2;
        }
        return colorStateList;
    }

    /* JADX WARNING: Removed duplicated region for block: B:30:0x0079 A[Catch:{ Exception -> 0x00a7 }] */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x009f A[Catch:{ Exception -> 0x00a7 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final android.graphics.drawable.Drawable i(@androidx.annotation.NonNull android.content.Context r11, @androidx.annotation.DrawableRes int r12) {
        /*
        // Method dump skipped, instructions count: 184
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.b0.i(android.content.Context, int):android.graphics.drawable.Drawable");
    }

    public final Drawable j(@NonNull Context context, @DrawableRes int i10, boolean z10, @NonNull Drawable drawable) {
        ColorStateList h6 = h(context, i10);
        PorterDuff.Mode mode = null;
        if (h6 != null) {
            if (u.a(drawable)) {
                drawable = drawable.mutate();
            }
            drawable.setTintList(h6);
            if (this.f1066g != null && i10 == R$drawable.abc_switch_thumb_material) {
                mode = PorterDuff.Mode.MULTIPLY;
            }
            if (mode == null) {
                return drawable;
            }
            drawable.setTintMode(mode);
            return drawable;
        }
        c cVar = this.f1066g;
        if (cVar != null) {
            g.a aVar = (g.a) cVar;
            boolean z11 = true;
            if (i10 == R$drawable.abc_seekbar_track_material) {
                LayerDrawable layerDrawable = (LayerDrawable) drawable;
                Drawable findDrawableByLayerId = layerDrawable.findDrawableByLayerId(16908288);
                int i11 = R$attr.colorControlNormal;
                int c10 = h0.c(context, i11);
                PorterDuff.Mode mode2 = g.f1091b;
                aVar.d(findDrawableByLayerId, c10, mode2);
                aVar.d(layerDrawable.findDrawableByLayerId(16908303), h0.c(context, i11), mode2);
                aVar.d(layerDrawable.findDrawableByLayerId(16908301), h0.c(context, R$attr.colorControlActivated), mode2);
            } else if (i10 == R$drawable.abc_ratingbar_material || i10 == R$drawable.abc_ratingbar_indicator_material || i10 == R$drawable.abc_ratingbar_small_material) {
                LayerDrawable layerDrawable2 = (LayerDrawable) drawable;
                Drawable findDrawableByLayerId2 = layerDrawable2.findDrawableByLayerId(16908288);
                int b10 = h0.b(context, R$attr.colorControlNormal);
                PorterDuff.Mode mode3 = g.f1091b;
                aVar.d(findDrawableByLayerId2, b10, mode3);
                Drawable findDrawableByLayerId3 = layerDrawable2.findDrawableByLayerId(16908303);
                int i12 = R$attr.colorControlActivated;
                aVar.d(findDrawableByLayerId3, h0.c(context, i12), mode3);
                aVar.d(layerDrawable2.findDrawableByLayerId(16908301), h0.c(context, i12), mode3);
            } else {
                z11 = false;
            }
            if (z11) {
                return drawable;
            }
        }
        if (k(context, i10, drawable) || !z10) {
            return drawable;
        }
        return null;
    }

    /* JADX WARNING: Removed duplicated region for block: B:20:0x004e  */
    /* JADX WARNING: Removed duplicated region for block: B:27:0x006a  */
    /* JADX WARNING: Removed duplicated region for block: B:31:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean k(@androidx.annotation.NonNull android.content.Context r8, @androidx.annotation.DrawableRes int r9, @androidx.annotation.NonNull android.graphics.drawable.Drawable r10) {
        /*
        // Method dump skipped, instructions count: 112
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.b0.k(android.content.Context, int, android.graphics.drawable.Drawable):boolean");
    }
}
