package aa;

import androidx.appcompat.widget.h;
import androidx.appcompat.widget.p;
import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import mb.a;
import org.spongycastle.asn1.ASN1Exception;

/* compiled from: ASN1InputStream */
public class i extends FilterInputStream {

    /* renamed from: a  reason: collision with root package name */
    public final int f186a;

    /* renamed from: b  reason: collision with root package name */
    public final boolean f187b;

    /* renamed from: c  reason: collision with root package name */
    public final byte[][] f188c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public i(InputStream inputStream) {
        super(inputStream);
        int c10 = v1.c(inputStream);
        this.f186a = c10;
        this.f187b = false;
        this.f188c = new byte[11][];
    }

    public static q e(int i10, p1 p1Var, byte[][] bArr) throws IOException {
        int read;
        if (i10 == 10) {
            byte[] f10 = f(p1Var, bArr);
            if (f10.length > 1) {
                return new g(a.c(f10));
            }
            if (f10.length != 0) {
                int i11 = f10[0] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP;
                g[] gVarArr = g.f179b;
                if (i11 >= gVarArr.length) {
                    return new g(a.c(f10));
                }
                g gVar = gVarArr[i11];
                if (gVar == null) {
                    gVar = new g(a.c(f10));
                    gVarArr[i11] = gVar;
                }
                return gVar;
            }
            throw new IllegalArgumentException("ENUMERATED has zero length");
        } else if (i10 == 12) {
            return new g1(p1Var.e());
        } else {
            if (i10 != 30) {
                switch (i10) {
                    case 1:
                        byte[] f11 = f(p1Var, bArr);
                        byte[] bArr2 = c.f165b;
                        if (f11.length != 1) {
                            throw new IllegalArgumentException("BOOLEAN value should have 1 byte in it");
                        } else if (f11[0] == 0) {
                            return c.f167d;
                        } else {
                            if ((f11[0] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP) == 255) {
                                return c.f168e;
                            }
                            return new c(f11);
                        }
                    case 2:
                        return new j(p1Var.e(), false);
                    case 3:
                        int i12 = p1Var.f217d;
                        if (i12 >= 1) {
                            int read2 = p1Var.read();
                            int i13 = i12 - 1;
                            byte[] bArr3 = new byte[i13];
                            if (i13 != 0) {
                                if (ob.a.a(p1Var, bArr3) != i13) {
                                    throw new EOFException("EOF encountered in middle of BIT STRING");
                                } else if (read2 > 0 && read2 < 8) {
                                    int i14 = i13 - 1;
                                    if (bArr3[i14] != ((byte) (bArr3[i14] & (255 << read2)))) {
                                        return new k1(bArr3, read2);
                                    }
                                }
                            }
                            return new n0(bArr3, read2);
                        }
                        throw new IllegalArgumentException("truncated BIT STRING detected");
                    case 4:
                        return new w0(p1Var.e());
                    case 5:
                        return u0.f233a;
                    case 6:
                        return m.o(f(p1Var, bArr));
                    default:
                        switch (i10) {
                            case 18:
                                return new v0(p1Var.e());
                            case 19:
                                return new z0(p1Var.e());
                            case 20:
                                return new e1(p1Var.e());
                            case 21:
                                return new i1(p1Var.e());
                            case 22:
                                return new t0(p1Var.e());
                            case 23:
                                return new y(p1Var.e());
                            case 24:
                                return new h(p1Var.e());
                            case 25:
                                return new s0(p1Var.e());
                            case 26:
                                return new j1(p1Var.e());
                            case 27:
                                return new r0(p1Var.e());
                            case 28:
                                return new h1(p1Var.e());
                            default:
                                throw new IOException(h.a("unknown tag ", i10, " encountered"));
                        }
                }
            } else {
                int i15 = p1Var.f217d / 2;
                char[] cArr = new char[i15];
                for (int i16 = 0; i16 < i15; i16++) {
                    int read3 = p1Var.read();
                    if (read3 < 0 || (read = p1Var.read()) < 0) {
                        break;
                    }
                    cArr[i16] = (char) ((read3 << 8) | (read & 255));
                }
                return new m0(cArr);
            }
        }
    }

    public static byte[] f(p1 p1Var, byte[][] bArr) throws IOException {
        int i10 = p1Var.f217d;
        if (i10 >= bArr.length) {
            return p1Var.e();
        }
        byte[] bArr2 = bArr[i10];
        if (bArr2 == null) {
            bArr2 = new byte[i10];
            bArr[i10] = bArr2;
        }
        ob.a.a(p1Var, bArr2);
        return bArr2;
    }

    public static int g(InputStream inputStream, int i10) throws IOException {
        int read = inputStream.read();
        if (read < 0) {
            throw new EOFException("EOF found when length expected");
        } else if (read == 128) {
            return -1;
        } else {
            if (read <= 127) {
                return read;
            }
            int i11 = read & 127;
            if (i11 <= 4) {
                int i12 = 0;
                for (int i13 = 0; i13 < i11; i13++) {
                    int read2 = inputStream.read();
                    if (read2 >= 0) {
                        i12 = (i12 << 8) + read2;
                    } else {
                        throw new EOFException("EOF found reading length");
                    }
                }
                if (i12 < 0) {
                    throw new IOException("corrupted stream - negative length found");
                } else if (i12 < i10) {
                    return i12;
                } else {
                    throw new IOException("corrupted stream - out of bounds length found");
                }
            } else {
                throw new IOException(p.a("DER length more than 4 bytes: ", i11));
            }
        }
    }

    public static int i(InputStream inputStream, int i10) throws IOException {
        int i11 = i10 & 31;
        if (i11 != 31) {
            return i11;
        }
        int i12 = 0;
        int read = inputStream.read();
        if ((read & 127) != 0) {
            while (read >= 0 && (read & 128) != 0) {
                i12 = (i12 | (read & 127)) << 7;
                read = inputStream.read();
            }
            if (read >= 0) {
                return i12 | (read & 127);
            }
            throw new EOFException("EOF found inside tag value.");
        }
        throw new IOException("corrupted stream - invalid high tag number found");
    }

    public f a(p1 p1Var) throws IOException {
        i iVar = new i(p1Var);
        f fVar = new f();
        while (true) {
            q h = iVar.h();
            if (h == null) {
                return fVar;
            }
            fVar.f177a.addElement(h);
        }
    }

    public q d(int i10, int i11, int i12) throws IOException {
        boolean z10 = (i10 & 32) != 0;
        p1 p1Var = new p1(this, i12);
        if ((i10 & 64) != 0) {
            return new l0(z10, i11, p1Var.e());
        }
        if ((i10 & 128) != 0) {
            return new v(p1Var).b(z10, i11);
        }
        if (!z10) {
            return e(i11, p1Var, this.f188c);
        }
        if (i11 == 4) {
            f a10 = a(p1Var);
            int b10 = a10.b();
            n[] nVarArr = new n[b10];
            for (int i13 = 0; i13 != b10; i13++) {
                nVarArr[i13] = (n) a10.a(i13);
            }
            return new c0(nVarArr);
        } else if (i11 == 8) {
            return new o0(a(p1Var));
        } else {
            if (i11 != 16) {
                if (i11 == 17) {
                    f a11 = a(p1Var);
                    r rVar = q0.f218a;
                    return a11.b() < 1 ? q0.f219b : new n1(a11);
                }
                throw new IOException(h.a("unknown tag ", i11, " encountered"));
            } else if (this.f187b) {
                return new t1(p1Var.e());
            } else {
                f a12 = a(p1Var);
                r rVar2 = q0.f218a;
                return a12.b() < 1 ? q0.f218a : new m1(a12);
            }
        }
    }

    public q h() throws IOException {
        int read = read();
        if (read > 0) {
            int i10 = i(this, read);
            boolean z10 = (read & 32) != 0;
            int g10 = g(this, this.f186a);
            if (g10 >= 0) {
                try {
                    return d(read, i10, g10);
                } catch (IllegalArgumentException e10) {
                    throw new ASN1Exception("corrupted stream detected", e10);
                }
            } else if (z10) {
                v vVar = new v(new r1(this, this.f186a), this.f186a);
                if ((read & 64) != 0) {
                    return new z(i10, vVar.c());
                }
                if ((read & 128) != 0) {
                    return vVar.b(true, i10);
                }
                if (i10 == 4) {
                    k0 k0Var = new k0(vVar);
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    byte[] bArr = new byte[4096];
                    while (true) {
                        int read2 = k0Var.read(bArr, 0, 4096);
                        if (read2 < 0) {
                            return new c0(byteArrayOutputStream.toByteArray());
                        }
                        byteArrayOutputStream.write(bArr, 0, read2);
                    }
                } else if (i10 == 8) {
                    try {
                        return new o0(vVar.c());
                    } catch (IllegalArgumentException e11) {
                        throw new ASN1Exception(e11.getMessage(), e11);
                    }
                } else if (i10 == 16) {
                    return new e0(vVar.c());
                } else {
                    if (i10 == 17) {
                        return new g0(vVar.c());
                    }
                    throw new IOException("unknown BER object encountered");
                }
            } else {
                throw new IOException("indefinite-length primitive encoding encountered");
            }
        } else if (read != 0) {
            return null;
        } else {
            throw new IOException("unexpected end-of-contents marker");
        }
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public i(byte[] r2) {
        /*
            r1 = this;
            java.io.ByteArrayInputStream r0 = new java.io.ByteArrayInputStream
            r0.<init>(r2)
            int r2 = r2.length
            r1.<init>(r0)
            r1.f186a = r2
            r2 = 0
            r1.f187b = r2
            r2 = 11
            byte[][] r2 = new byte[r2][]
            r1.f188c = r2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: aa.i.<init>(byte[]):void");
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public i(byte[] r2, boolean r3) {
        /*
            r1 = this;
            java.io.ByteArrayInputStream r0 = new java.io.ByteArrayInputStream
            r0.<init>(r2)
            int r2 = r2.length
            r1.<init>(r0)
            r1.f186a = r2
            r1.f187b = r3
            r2 = 11
            byte[][] r2 = new byte[r2][]
            r1.f188c = r2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: aa.i.<init>(byte[], boolean):void");
    }
}
