package a4;

import com.duokan.airkan.common.Log;
import com.duokan.airkan.common.ServiceList;
import com.duokan.airkan.common.aidl.ParcelDeviceData;
import com.duokan.airkan.server.c;
import com.duokan.airkan.server.f;
import java.util.Map;
import y3.a;

public class o {

    /* renamed from: a  reason: collision with root package name */
    public Map<Integer, a> f64a;

    /* renamed from: b  reason: collision with root package name */
    public final String f65b;

    /* renamed from: c  reason: collision with root package name */
    public ServiceList f66c;

    public final void a(ParcelDeviceData parcelDeviceData) {
        for (a aVar : this.f64a.values()) {
            try {
                aVar.h(parcelDeviceData);
            } catch (Exception e10) {
                c.b(e10, f.a("Exception: "), "UDTListenManager");
            }
        }
    }

    public final void b(ParcelDeviceData parcelDeviceData) {
        for (a aVar : this.f64a.values()) {
            try {
                aVar.R(parcelDeviceData);
            } catch (Exception e10) {
                c.b(e10, f.a("Exception: "), "UDTListenManager");
            }
        }
    }

    public void c(int i10, a aVar) {
        this.f64a.put(Integer.valueOf(i10), aVar);
        Log.d("UDTListenManager", "Regist callback success, AppID: " + i10);
    }
}
