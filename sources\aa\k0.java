package aa;

import java.io.IOException;
import java.io.InputStream;

/* compiled from: ConstructedOctetStream */
public class k0 extends InputStream {

    /* renamed from: a  reason: collision with root package name */
    public final v f195a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f196b = true;

    /* renamed from: c  reason: collision with root package name */
    public InputStream f197c;

    public k0(v vVar) {
        this.f195a = vVar;
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i10, int i11) throws IOException {
        o oVar;
        int i12 = 0;
        if (this.f197c == null) {
            if (!this.f196b || (oVar = (o) this.f195a.a()) == null) {
                return -1;
            }
            this.f196b = false;
            this.f197c = oVar.a();
        }
        while (true) {
            int read = this.f197c.read(bArr, i10 + i12, i11 - i12);
            if (read >= 0) {
                i12 += read;
                if (i12 == i11) {
                    return i12;
                }
            } else {
                o oVar2 = (o) this.f195a.a();
                if (oVar2 == null) {
                    this.f197c = null;
                    if (i12 < 1) {
                        return -1;
                    }
                    return i12;
                }
                this.f197c = oVar2.a();
            }
        }
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        o oVar;
        if (this.f197c == null) {
            if (!this.f196b || (oVar = (o) this.f195a.a()) == null) {
                return -1;
            }
            this.f196b = false;
            this.f197c = oVar.a();
        }
        while (true) {
            int read = this.f197c.read();
            if (read >= 0) {
                return read;
            }
            o oVar2 = (o) this.f195a.a();
            if (oVar2 == null) {
                this.f197c = null;
                return -1;
            }
            this.f197c = oVar2.a();
        }
    }
}
