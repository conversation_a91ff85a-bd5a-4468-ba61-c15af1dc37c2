<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="a_code_note_1">按</string>
    <string name="a_code_note_2">键返回</string>
    <string name="a_code_title">应用安装验证码</string>
    <string name="abc_action_bar_home_description">转到首页</string>
    <string name="abc_action_bar_up_description">转到上一层级</string>
    <string name="abc_action_menu_overflow_description">更多选项</string>
    <string name="abc_action_mode_done">完成</string>
    <string name="abc_activity_chooser_view_see_all">查看全部</string>
    <string name="abc_activitychooserview_choose_application">选择应用</string>
    <string name="abc_capital_off">关闭</string>
    <string name="abc_capital_on">开启</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">Delete 键</string>
    <string name="abc_menu_enter_shortcut_label">Enter 键</string>
    <string name="abc_menu_function_shortcut_label">Fn+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">空格键</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">搜索…</string>
    <string name="abc_searchview_description_clear">清除查询</string>
    <string name="abc_searchview_description_query">搜索查询</string>
    <string name="abc_searchview_description_search">搜索</string>
    <string name="abc_searchview_description_submit">提交查询</string>
    <string name="abc_searchview_description_voice">语音搜索</string>
    <string name="abc_shareactionprovider_share_with">分享对象</string>
    <string name="abc_shareactionprovider_share_with_application">与%s分享</string>
    <string name="abc_toolbar_collapse_description">收起</string>
    <string name="actionbar_button_up_description">返回</string>
    <string name="afternoon">下午</string>
    <string name="airkan_app_name">Mi-Link Airkan Services</string>
    <string name="airkan_daemon_handle_progress">Airkan Handle Progress</string>
    <string name="airkan_daemon_name">Airkan TV Daemon</string>
    <string name="airkan_daemon_start">RemoteControllerService Started.</string>
    <string name="am">上午</string>
    <string name="app_name">MiUtilRes</string>
    <string name="cast_ad_name_box">小米盒子</string>
    <string name="cast_ad_name_tv">小米电视</string>
    <string name="check_auth_accept">允许</string>
    <string name="check_auth_cast_subtitle">%s的%s正在请求投屏该设备，请确认是否允许？</string>
    <string name="check_auth_cast_subtitle_unknown">一台新设备正在请求投屏该设备，请确认是否允许？</string>
    <string name="check_auth_reject">拒绝</string>
    <string name="check_auth_subtitle">%s的%s正在请求操作该设备，请确认是否允许？</string>
    <string name="check_auth_subtitle2">%s正在请求控制该设备，请确认是否允许？</string>
    <string name="check_auth_subtitle_unknown">一台新设备正在请求操作该设备，请确认是否允许？</string>
    <string name="check_auth_title">安全提醒</string>
    <string name="clearable_edittext_clear_description">清空</string>
    <string name="close">关闭</string>
    <string name="close_in_seconds_last">秒后关闭</string>
    <string name="close_in_seconds_pre" />
    <string name="confirm">确定</string>
    <string name="date_picker_label_day">日</string>
    <string name="date_picker_label_month">月</string>
    <string name="date_picker_label_year">年</string>
    <string name="date_picker_lunar">农历</string>
    <string name="date_time_picker_dialog_title">设置日期和时间</string>
    <string name="default_client_name" />
    <string name="default_device_name">小米盒子</string>
    <string name="default_music_title">音乐</string>
    <string name="default_video_title">视频</string>
    <string name="early_morning">凌晨</string>
    <string name="empty" />
    <string name="eras_ad">公元</string>
    <string name="eras_bc">公元前</string>
    <string name="evening">傍晚</string>
    <string name="fmt_date">D</string>
    <string name="fmt_date_day">d日</string>
    <string name="fmt_date_long_month">M月</string>
    <string name="fmt_date_long_month_day">M月d日</string>
    <string name="fmt_date_long_year_month">yyyy年M月</string>
    <string name="fmt_date_long_year_month_day">yyyy年M月d日</string>
    <string name="fmt_date_numeric_day">d</string>
    <string name="fmt_date_numeric_month">M</string>
    <string name="fmt_date_numeric_month_day">M-d</string>
    <string name="fmt_date_numeric_year">yyyy</string>
    <string name="fmt_date_numeric_year_month">yyyy-M</string>
    <string name="fmt_date_numeric_year_month_day">yyyy-M-d</string>
    <string name="fmt_date_short_month">M月</string>
    <string name="fmt_date_short_month_day">M月d日</string>
    <string name="fmt_date_short_year_month">yyyy年M月</string>
    <string name="fmt_date_short_year_month_day">yyyy年M月d日</string>
    <string name="fmt_date_time">D T</string>
    <string name="fmt_date_time_timezone">zD T</string>
    <string name="fmt_date_timezone">zD</string>
    <string name="fmt_date_year">yyyy年</string>
    <string name="fmt_time">T</string>
    <string name="fmt_time_12hour">h点</string>
    <string name="fmt_time_12hour_minute">h:mm</string>
    <string name="fmt_time_12hour_minute_pm">aah:mm</string>
    <string name="fmt_time_12hour_minute_second">h:mm:ss</string>
    <string name="fmt_time_12hour_minute_second_millis">h:mm:ss.S</string>
    <string name="fmt_time_12hour_minute_second_millis_pm">aah:mm:ss.S</string>
    <string name="fmt_time_12hour_minute_second_pm">aah:mm:ss</string>
    <string name="fmt_time_12hour_pm">aah点</string>
    <string name="fmt_time_24hour">H点</string>
    <string name="fmt_time_24hour_minute">H:mm</string>
    <string name="fmt_time_24hour_minute_second">H:mm:s</string>
    <string name="fmt_time_24hour_minute_second_millis">H:mm:ss.S</string>
    <string name="fmt_time_millis">S毫秒</string>
    <string name="fmt_time_minute">m分</string>
    <string name="fmt_time_minute_second">m分s秒</string>
    <string name="fmt_time_minute_second_millis">m分s.S秒</string>
    <string name="fmt_time_second">s秒</string>
    <string name="fmt_time_second_millis">s.S秒</string>
    <string name="fmt_time_timezone">zT</string>
    <string name="fmt_timezone">z</string>
    <string name="fmt_weekday">W</string>
    <string name="fmt_weekday_date">DW</string>
    <string name="fmt_weekday_date_time">DW T</string>
    <string name="fmt_weekday_date_time_timezone">zDW T</string>
    <string name="fmt_weekday_date_timezone">zDW</string>
    <string name="fmt_weekday_long">EEEE</string>
    <string name="fmt_weekday_short">E</string>
    <string name="fmt_weekday_time">W T</string>
    <string name="fmt_weekday_time_timezone">zW T</string>
    <string name="fmt_weekday_timezone">zW</string>
    <string name="friday">星期五</string>
    <string name="friday_short">周五</string>
    <string name="friday_shortest">五</string>
    <string name="midnight">半夜</string>
    <string name="mihome_remote_logout_main">您的登录账号已被远程用户授权强制退出</string>
    <string name="mihome_remote_logout_title">登录退出</string>
    <string name="miliao">米聊</string>
    <string name="milink_httpservice_name">MiLinkHttpServer</string>
    <string name="mitv_framework_res_global_attention">注意</string>
    <string name="mitv_framework_res_global_cancel">取消</string>
    <string name="mitv_framework_res_global_menu">菜单</string>
    <string name="mitv_framework_res_global_neutral">放弃</string>
    <string name="mitv_framework_res_global_ok">确定</string>
    <string name="miuix_access_state_desc">选中</string>
    <string name="miuix_alphabet_indexer_name">索引条</string>
    <string name="miuix_appcompat_action_mode_deselect_all">全不选</string>
    <string name="miuix_appcompat_action_mode_select_all">全选</string>
    <string name="miuix_appcompat_action_mode_title_empty">选择</string>
    <string name="miuix_appcompat_actionbar_immersion_button_more_description">更多</string>
    <string name="miuix_appcompat_cancel_description">取消</string>
    <string name="miuix_appcompat_confirm_description">确认</string>
    <string name="miuix_appcompat_delete_description">删除</string>
    <string name="miuix_appcompat_deselect_all">全不选</string>
    <string name="miuix_appcompat_deselect_all_description">全不选</string>
    <string name="miuix_appcompat_search_action_mode_cancel">取消</string>
    <string name="miuix_appcompat_search_input_description">搜索</string>
    <string name="miuix_appcompat_select_all">全选</string>
    <string name="miuix_appcompat_select_all_description">全选</string>
    <string name="miuix_appcompat_select_item">请选择项目</string>
    <string name="miuix_indexer_collect">收藏</string>
    <string name="miuix_indexer_selected">%s,已选中</string>
    <string name="miuix_sbl_tracking_progress_labe_pull_to_refresh">下拉刷新</string>
    <string name="miuix_sbl_tracking_progress_labe_refreshed">刷新完成</string>
    <string name="miuix_sbl_tracking_progress_labe_refreshing">正在刷新</string>
    <string name="miuix_sbl_tracking_progress_labe_release_to_refresh">松开刷新</string>
    <string name="miuix_sbl_tracking_progress_labe_up_nodata">到底了</string>
    <string name="miuix_sbl_tracking_progress_labe_up_none">我是有底线的~~~</string>
    <string name="miuix_sbl_tracking_progress_labe_up_refresh">加载中...</string>
    <string name="miuix_sbl_tracking_progress_labe_up_refresh_fail">加载失败</string>
    <string name="modify_device_name">设备名成功修改为:</string>
    <string name="monday">星期一</string>
    <string name="monday_short">周一</string>
    <string name="monday_shortest">一</string>
    <string name="month_april">四月</string>
    <string name="month_april_short">四</string>
    <string name="month_april_shortest">四</string>
    <string name="month_august">八月</string>
    <string name="month_august_short">八</string>
    <string name="month_august_shortest">八</string>
    <string name="month_december">十二月</string>
    <string name="month_december_short">十二</string>
    <string name="month_december_shortest">十二</string>
    <string name="month_february">二月</string>
    <string name="month_february_short">二</string>
    <string name="month_february_shortest">二</string>
    <string name="month_january">一月</string>
    <string name="month_january_short">一</string>
    <string name="month_january_shortest">一</string>
    <string name="month_july">七月</string>
    <string name="month_july_short">七</string>
    <string name="month_july_shortest">七</string>
    <string name="month_june">六月</string>
    <string name="month_june_short">六</string>
    <string name="month_june_shortest">六</string>
    <string name="month_march">三月</string>
    <string name="month_march_short">三</string>
    <string name="month_march_shortest">三</string>
    <string name="month_may">五月</string>
    <string name="month_may_short">五</string>
    <string name="month_may_shortest">五</string>
    <string name="month_november">十一月</string>
    <string name="month_november_short">十一</string>
    <string name="month_november_shortest">十一</string>
    <string name="month_october">十月</string>
    <string name="month_october_short">十</string>
    <string name="month_october_shortest">十</string>
    <string name="month_september">九月</string>
    <string name="month_september_short">九</string>
    <string name="month_september_shortest">九</string>
    <string name="more">更多</string>
    <string name="morning">上午</string>
    <string name="night">晚上</string>
    <string name="noon">中午</string>
    <string name="notification_des">应用安装验证码输入太多次错误，怀疑被别人恶意攻击</string>
    <string name="notification_title">注意</string>
    <string name="notify_rc_content">使用小米万能遥控App，手机上完美操控小米电视</string>
    <string name="notify_rc_title">免费送您一个电视遥控器</string>
    <string name="pc">PC</string>
    <string name="playerservice_name">Airkan TV Player Service</string>
    <string name="pm">下午</string>
    <string name="rc_daemon_name">Remote Controller TV Daemon</string>
    <string name="rc_daemon_start">Remote Controller TV Daemon Started.</string>
    <string name="recv_service_start">to receive service start broadcast</string>
    <string name="retry_too_many_times_tip">重试太多次，请过24小时以后再试</string>
    <string name="saturday">星期六</string>
    <string name="saturday_short">周六</string>
    <string name="saturday_shortest">六</string>
    <string name="screen_capturing">截屏中</string>
    <string name="search_menu_title">搜索</string>
    <string name="security_check_title">是否接受来自%1$s的投屏请求</string>
    <string name="security_confirmation_button_cancel">拒绝</string>
    <string name="security_confirmation_button_confirm">允许</string>
    <string name="security_confirmation_main">是否允许此项操作？</string>
    <string name="security_confirmation_title_box">一台陌生设备想要对盒子投屏</string>
    <string name="security_confirmation_title_tv">一台陌生设备想要对电视投屏</string>
    <string name="security_error_too_many_times">校验失败次数过多，请%1$d小时后重试</string>
    <string name="security_password_main">请按如下顺序按下遥控器方向键接受投屏</string>
    <string name="security_password_title_box">一台陌生设备想要遥控盒子</string>
    <string name="security_password_title_tv">一台陌生设备想要遥控电视</string>
    <string name="security_toast_note_1_last">分钟后重试</string>
    <string name="security_toast_note_1_pre">投屏认证太频繁，请</string>
    <string name="security_toast_note_2">投屏认证太频繁，请稍后重试</string>
    <string name="security_toast_note_3">认证太频繁，请稍后重试</string>
    <string name="security_toast_note_3_last">分钟后重试</string>
    <string name="security_toast_note_3_pre">认证太频繁，请</string>
    <string name="security_too_many_times_hint_2">投屏认证太频繁，请稍后重试</string>
    <string name="security_verifycode_title_tv">请输入连接码</string>
    <string name="send">发送</string>
    <string name="send_service_start">to send service start broadcast</string>
    <string name="share_input_default_message">好东西分享给你,快来看看吧~</string>
    <string name="solar_term_autumn_begins">立秋</string>
    <string name="solar_term_autumn_equinox">秋分</string>
    <string name="solar_term_clear_and_bright">清明</string>
    <string name="solar_term_cold_dews">寒露</string>
    <string name="solar_term_grain_buds">小满</string>
    <string name="solar_term_grain_in_ear">芒种</string>
    <string name="solar_term_grain_rain">谷雨</string>
    <string name="solar_term_great_cold">大寒</string>
    <string name="solar_term_great_heat">大暑</string>
    <string name="solar_term_heavy_snow">大雪</string>
    <string name="solar_term_hoar_frost_falls">霜降</string>
    <string name="solar_term_insects_awaken">惊蛰</string>
    <string name="solar_term_light_snow">小雪</string>
    <string name="solar_term_slight_cold">小寒</string>
    <string name="solar_term_slight_heat">小暑</string>
    <string name="solar_term_spring_begins">立春</string>
    <string name="solar_term_stopping_the_heat">处暑</string>
    <string name="solar_term_summer_begins">立夏</string>
    <string name="solar_term_summer_solstice">夏至</string>
    <string name="solar_term_the_rains">雨水</string>
    <string name="solar_term_vernal_equinox">春分</string>
    <string name="solar_term_white_dews">白露</string>
    <string name="solar_term_winter_begins">立冬</string>
    <string name="solar_term_winter_solstice">冬至</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="sunday">星期日</string>
    <string name="sunday_short">周日</string>
    <string name="sunday_shortest">日</string>
    <string name="the_anniversary_of_lifting_martial_law">解严纪念日</string>
    <string name="the_anti_aggression_day">反侵略日</string>
    <string name="the_arbor_day">植树节</string>
    <string name="the_armed_forces_day">军人节</string>
    <string name="the_armys_day">建军节</string>
    <string name="the_childrens_day">儿童节</string>
    <string name="the_chinese_youth_day">青年节</string>
    <string name="the_christmas_day">圣诞节</string>
    <string name="the_double_ninth_festival">重阳</string>
    <string name="the_dragon_boat_festival">端午</string>
    <string name="the_easter_day">复活节</string>
    <string name="the_eve_of_the_spring_festival">除夕</string>
    <string name="the_fifth_day">初五</string>
    <string name="the_fools_day">愚人节</string>
    <string name="the_forth_day">初四</string>
    <string name="the_hksar_establishment_day">香港特别行政区成立纪念日</string>
    <string name="the_international_womens_day">妇女节</string>
    <string name="the_laba_festival">腊八</string>
    <string name="the_labour_day">劳动节</string>
    <string name="the_lantern_festival">元宵</string>
    <string name="the_mid_autumn_festival">中秋</string>
    <string name="the_national_day">国庆节</string>
    <string name="the_national_father_day">国父诞辰</string>
    <string name="the_new_years_day">元旦</string>
    <string name="the_night_of_sevens">七夕</string>
    <string name="the_partys_day">建党节</string>
    <string name="the_peace_day">和平纪念</string>
    <string name="the_retrocession_day">光复节</string>
    <string name="the_second_day">初二</string>
    <string name="the_seventh_day">初七</string>
    <string name="the_sixth_day">初六</string>
    <string name="the_spirit_festival">中元</string>
    <string name="the_spring_festival">春节</string>
    <string name="the_teachers_day">教师节</string>
    <string name="the_third_day">初三</string>
    <string name="the_tw_childrens_day">儿童节</string>
    <string name="the_tw_youth_day">青年节</string>
    <string name="the_united_nations_day">联合国日</string>
    <string name="the_valentines_day">情人节</string>
    <string name="the_water_lantern_festival">下元</string>
    <string name="thursday">星期四</string>
    <string name="thursday_short">周四</string>
    <string name="thursday_shortest">四</string>
    <string name="time_picker_dialog_title">设置时间</string>
    <string name="time_picker_label_hour">时</string>
    <string name="time_picker_label_minute">分</string>
    <string name="today">今天</string>
    <string name="tomorrow">明天</string>
    <string name="tuesday">星期二</string>
    <string name="tuesday_short">周二</string>
    <string name="tuesday_shortest">二</string>
    <string name="wednesday">星期三</string>
    <string name="wednesday_short">周三</string>
    <string name="wednesday_shortest">三</string>
    <string name="weixin">微信</string>
    <string name="yesterday">昨天</string>
</resources>
