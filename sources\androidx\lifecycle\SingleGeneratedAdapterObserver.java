package androidx.lifecycle;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;

/* access modifiers changed from: package-private */
public class SingleGeneratedAdapterObserver implements e {

    /* renamed from: a  reason: collision with root package name */
    public final d f2058a;

    public SingleGeneratedAdapterObserver(d dVar) {
        this.f2058a = dVar;
    }

    @Override // androidx.lifecycle.e
    public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
        this.f2058a.a(gVar, event, false, null);
        this.f2058a.a(gVar, event, true, null);
    }
}
