package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$id;
import androidx.appcompat.R$layout;
import androidx.appcompat.R$styleable;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.i;
import androidx.core.view.ViewCompat;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.m;
import java.util.WeakHashMap;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ActionBarContextView extends b {

    /* renamed from: i  reason: collision with root package name */
    public CharSequence f700i;

    /* renamed from: j  reason: collision with root package name */
    public CharSequence f701j;

    /* renamed from: k  reason: collision with root package name */
    public View f702k;

    /* renamed from: l  reason: collision with root package name */
    public View f703l;

    /* renamed from: m  reason: collision with root package name */
    public LinearLayout f704m;

    /* renamed from: n  reason: collision with root package name */
    public TextView f705n;

    /* renamed from: o  reason: collision with root package name */
    public TextView f706o;

    /* renamed from: p  reason: collision with root package name */
    public int f707p;

    /* renamed from: q  reason: collision with root package name */
    public int f708q;

    /* renamed from: r  reason: collision with root package name */
    public boolean f709r;

    /* renamed from: s  reason: collision with root package name */
    public int f710s;

    public class a implements View.OnClickListener {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ p.a f711a;

        public a(ActionBarContextView actionBarContextView, p.a aVar) {
            this.f711a = aVar;
        }

        public void onClick(View view) {
            this.f711a.c();
        }
    }

    public ActionBarContextView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.actionModeStyle);
    }

    public void f(p.a aVar) {
        View view = this.f702k;
        if (view == null) {
            View inflate = LayoutInflater.from(getContext()).inflate(this.f710s, (ViewGroup) this, false);
            this.f702k = inflate;
            addView(inflate);
        } else if (view.getParent() == null) {
            addView(this.f702k);
        }
        this.f702k.findViewById(R$id.action_mode_close_button).setOnClickListener(new a(this, aVar));
        d dVar = (d) aVar.e();
        ActionMenuPresenter actionMenuPresenter = this.f1051d;
        if (actionMenuPresenter != null) {
            actionMenuPresenter.j();
        }
        ActionMenuPresenter actionMenuPresenter2 = new ActionMenuPresenter(getContext());
        this.f1051d = actionMenuPresenter2;
        actionMenuPresenter2.f745m = true;
        actionMenuPresenter2.f747n = true;
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(-2, -1);
        dVar.b(this.f1051d, this.f1049b);
        ActionMenuPresenter actionMenuPresenter3 = this.f1051d;
        i iVar = actionMenuPresenter3.h;
        if (iVar == null) {
            i iVar2 = (i) actionMenuPresenter3.f583d.inflate(actionMenuPresenter3.f585f, (ViewGroup) this, false);
            actionMenuPresenter3.h = iVar2;
            iVar2.b(actionMenuPresenter3.f582c);
            actionMenuPresenter3.b(true);
        }
        i iVar3 = actionMenuPresenter3.h;
        if (iVar != iVar3) {
            ((ActionMenuView) iVar3).setPresenter(actionMenuPresenter3);
        }
        ActionMenuView actionMenuView = (ActionMenuView) iVar3;
        this.f1050c = actionMenuView;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        actionMenuView.setBackground(null);
        addView(this.f1050c, layoutParams);
    }

    public final void g() {
        if (this.f704m == null) {
            LayoutInflater.from(getContext()).inflate(R$layout.abc_action_bar_title_item, this);
            LinearLayout linearLayout = (LinearLayout) getChildAt(getChildCount() - 1);
            this.f704m = linearLayout;
            this.f705n = (TextView) linearLayout.findViewById(R$id.action_bar_title);
            this.f706o = (TextView) this.f704m.findViewById(R$id.action_bar_subtitle);
            if (this.f707p != 0) {
                this.f705n.setTextAppearance(getContext(), this.f707p);
            }
            if (this.f708q != 0) {
                this.f706o.setTextAppearance(getContext(), this.f708q);
            }
        }
        this.f705n.setText(this.f700i);
        this.f706o.setText(this.f701j);
        boolean z10 = !TextUtils.isEmpty(this.f700i);
        boolean z11 = !TextUtils.isEmpty(this.f701j);
        int i10 = 0;
        this.f706o.setVisibility(z11 ? 0 : 8);
        LinearLayout linearLayout2 = this.f704m;
        if (!z10 && !z11) {
            i10 = 8;
        }
        linearLayout2.setVisibility(i10);
        if (this.f704m.getParent() == null) {
            addView(this.f704m);
        }
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new ViewGroup.MarginLayoutParams(-1, -2);
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new ViewGroup.MarginLayoutParams(getContext(), attributeSet);
    }

    @Override // androidx.appcompat.widget.b
    public /* bridge */ /* synthetic */ int getAnimatedVisibility() {
        return super.getAnimatedVisibility();
    }

    @Override // androidx.appcompat.widget.b
    public /* bridge */ /* synthetic */ int getContentHeight() {
        return super.getContentHeight();
    }

    public CharSequence getSubtitle() {
        return this.f701j;
    }

    public CharSequence getTitle() {
        return this.f700i;
    }

    public void h() {
        removeAllViews();
        this.f703l = null;
        this.f1050c = null;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ActionMenuPresenter actionMenuPresenter = this.f1051d;
        if (actionMenuPresenter != null) {
            actionMenuPresenter.m();
            this.f1051d.n();
        }
    }

    public void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        if (accessibilityEvent.getEventType() == 32) {
            accessibilityEvent.setSource(this);
            accessibilityEvent.setClassName(getClass().getName());
            accessibilityEvent.setPackageName(getContext().getPackageName());
            accessibilityEvent.setContentDescription(this.f700i);
            return;
        }
        super.onInitializeAccessibilityEvent(accessibilityEvent);
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        boolean b10 = q0.b(this);
        int paddingRight = b10 ? (i12 - i10) - getPaddingRight() : getPaddingLeft();
        int paddingTop = getPaddingTop();
        int paddingTop2 = ((i13 - i11) - getPaddingTop()) - getPaddingBottom();
        View view = this.f702k;
        if (!(view == null || view.getVisibility() == 8)) {
            ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) this.f702k.getLayoutParams();
            int i14 = b10 ? marginLayoutParams.rightMargin : marginLayoutParams.leftMargin;
            int i15 = b10 ? marginLayoutParams.leftMargin : marginLayoutParams.rightMargin;
            int i16 = b10 ? paddingRight - i14 : paddingRight + i14;
            int d10 = i16 + d(this.f702k, i16, paddingTop, paddingTop2, b10);
            paddingRight = b10 ? d10 - i15 : d10 + i15;
        }
        int i17 = paddingRight;
        LinearLayout linearLayout = this.f704m;
        if (!(linearLayout == null || this.f703l != null || linearLayout.getVisibility() == 8)) {
            i17 += d(this.f704m, i17, paddingTop, paddingTop2, b10);
        }
        View view2 = this.f703l;
        if (view2 != null) {
            d(view2, i17, paddingTop, paddingTop2, b10);
        }
        int paddingLeft = b10 ? getPaddingLeft() : (i12 - i10) - getPaddingRight();
        ActionMenuView actionMenuView = this.f1050c;
        if (actionMenuView != null) {
            d(actionMenuView, paddingLeft, paddingTop, paddingTop2, !b10);
        }
    }

    public void onMeasure(int i10, int i11) {
        int i12 = 1073741824;
        if (View.MeasureSpec.getMode(i10) != 1073741824) {
            throw new IllegalStateException(getClass().getSimpleName() + " can only be used with android:layout_width=\"match_parent\" (or fill_parent)");
        } else if (View.MeasureSpec.getMode(i11) != 0) {
            int size = View.MeasureSpec.getSize(i10);
            int i13 = this.f1052e;
            if (i13 <= 0) {
                i13 = View.MeasureSpec.getSize(i11);
            }
            int paddingBottom = getPaddingBottom() + getPaddingTop();
            int paddingLeft = (size - getPaddingLeft()) - getPaddingRight();
            int i14 = i13 - paddingBottom;
            int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(i14, EventResultPersister.GENERATE_NEW_ID);
            View view = this.f702k;
            if (view != null) {
                int c10 = c(view, paddingLeft, makeMeasureSpec, 0);
                ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) this.f702k.getLayoutParams();
                paddingLeft = c10 - (marginLayoutParams.leftMargin + marginLayoutParams.rightMargin);
            }
            ActionMenuView actionMenuView = this.f1050c;
            if (actionMenuView != null && actionMenuView.getParent() == this) {
                paddingLeft = c(this.f1050c, paddingLeft, makeMeasureSpec, 0);
            }
            LinearLayout linearLayout = this.f704m;
            if (linearLayout != null && this.f703l == null) {
                if (this.f709r) {
                    this.f704m.measure(View.MeasureSpec.makeMeasureSpec(0, 0), makeMeasureSpec);
                    int measuredWidth = this.f704m.getMeasuredWidth();
                    boolean z10 = measuredWidth <= paddingLeft;
                    if (z10) {
                        paddingLeft -= measuredWidth;
                    }
                    this.f704m.setVisibility(z10 ? 0 : 8);
                } else {
                    paddingLeft = c(linearLayout, paddingLeft, makeMeasureSpec, 0);
                }
            }
            View view2 = this.f703l;
            if (view2 != null) {
                ViewGroup.LayoutParams layoutParams = view2.getLayoutParams();
                int i15 = layoutParams.width;
                int i16 = i15 != -2 ? 1073741824 : Integer.MIN_VALUE;
                if (i15 >= 0) {
                    paddingLeft = Math.min(i15, paddingLeft);
                }
                int i17 = layoutParams.height;
                if (i17 == -2) {
                    i12 = Integer.MIN_VALUE;
                }
                if (i17 >= 0) {
                    i14 = Math.min(i17, i14);
                }
                this.f703l.measure(View.MeasureSpec.makeMeasureSpec(paddingLeft, i16), View.MeasureSpec.makeMeasureSpec(i14, i12));
            }
            if (this.f1052e <= 0) {
                int childCount = getChildCount();
                int i18 = 0;
                for (int i19 = 0; i19 < childCount; i19++) {
                    int measuredHeight = getChildAt(i19).getMeasuredHeight() + paddingBottom;
                    if (measuredHeight > i18) {
                        i18 = measuredHeight;
                    }
                }
                setMeasuredDimension(size, i18);
                return;
            }
            setMeasuredDimension(size, i13);
        } else {
            throw new IllegalStateException(getClass().getSimpleName() + " can only be used with android:layout_height=\"wrap_content\"");
        }
    }

    @Override // androidx.appcompat.widget.b
    public void setContentHeight(int i10) {
        this.f1052e = i10;
    }

    public void setCustomView(View view) {
        LinearLayout linearLayout;
        View view2 = this.f703l;
        if (view2 != null) {
            removeView(view2);
        }
        this.f703l = view;
        if (!(view == null || (linearLayout = this.f704m) == null)) {
            removeView(linearLayout);
            this.f704m = null;
        }
        if (view != null) {
            addView(view);
        }
        requestLayout();
    }

    public void setSubtitle(CharSequence charSequence) {
        this.f701j = charSequence;
        g();
    }

    public void setTitle(CharSequence charSequence) {
        this.f700i = charSequence;
        g();
    }

    public void setTitleOptional(boolean z10) {
        if (z10 != this.f709r) {
            requestLayout();
        }
        this.f709r = z10;
    }

    @Override // androidx.appcompat.widget.b
    public /* bridge */ /* synthetic */ void setVisibility(int i10) {
        super.setVisibility(i10);
    }

    public boolean shouldDelayChildPressedState() {
        return false;
    }

    public ActionBarContextView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        Drawable drawable;
        int resourceId;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ActionMode, i10, 0);
        int i11 = R$styleable.ActionMode_background;
        if (!obtainStyledAttributes.hasValue(i11) || (resourceId = obtainStyledAttributes.getResourceId(i11, 0)) == 0) {
            drawable = obtainStyledAttributes.getDrawable(i11);
        } else {
            drawable = m.a.a(context, resourceId);
        }
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        setBackground(drawable);
        this.f707p = obtainStyledAttributes.getResourceId(R$styleable.ActionMode_titleTextStyle, 0);
        this.f708q = obtainStyledAttributes.getResourceId(R$styleable.ActionMode_subtitleTextStyle, 0);
        this.f1052e = obtainStyledAttributes.getLayoutDimension(R$styleable.ActionMode_height, 0);
        this.f710s = obtainStyledAttributes.getResourceId(R$styleable.ActionMode_closeItemLayout, R$layout.abc_action_mode_close_item_material);
        obtainStyledAttributes.recycle();
    }
}
