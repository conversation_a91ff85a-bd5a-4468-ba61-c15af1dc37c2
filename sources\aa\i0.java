package aa;

import com.duokan.airkan.server.f;
import java.io.IOException;
import java.util.Enumeration;

/* compiled from: BERTaggedObject */
public class i0 extends x {
    public i0(boolean z10, int i10, e eVar) {
        super(z10, i10, eVar);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        Enumeration enumeration;
        pVar.i(160, this.f242a);
        pVar.c(128);
        if (!this.f243b) {
            e eVar = this.f244c;
            if (eVar instanceof n) {
                if (eVar instanceof c0) {
                    enumeration = ((c0) eVar).p();
                } else {
                    enumeration = new c0(((n) eVar).o()).p();
                }
            } else if (eVar instanceof r) {
                enumeration = ((r) eVar).q();
            } else if (eVar instanceof t) {
                enumeration = ((t) eVar).q();
            } else {
                StringBuilder a10 = f.a("not implemented: ");
                a10.append(this.f244c.getClass().getName());
                throw new RuntimeException(a10.toString());
            }
            while (enumeration.hasMoreElements()) {
                pVar.h((e) enumeration.nextElement());
            }
        } else {
            pVar.h(this.f244c);
        }
        pVar.c(0);
        pVar.c(0);
    }

    @Override // aa.q
    public int i() throws IOException {
        int i10 = this.f244c.c().i();
        if (this.f243b) {
            return v1.a(i10) + v1.b(this.f242a) + i10;
        }
        return v1.b(this.f242a) + (i10 - 1);
    }

    @Override // aa.q
    public boolean k() {
        if (this.f243b) {
            return true;
        }
        return this.f244c.c().l().k();
    }
}
