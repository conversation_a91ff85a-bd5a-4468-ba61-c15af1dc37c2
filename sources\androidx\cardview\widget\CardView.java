package androidx.cardview.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.Px;
import androidx.cardview.R$attr;
import androidx.cardview.R$color;
import androidx.cardview.R$style;
import androidx.cardview.R$styleable;
import com.duokan.airkan.common.Constant;
import t.b;
import t.c;
import t.d;

public class CardView extends FrameLayout {
    public static final int[] h = {16842801};

    /* renamed from: i  reason: collision with root package name */
    public static final c f1229i = new t.a();

    /* renamed from: a  reason: collision with root package name */
    public boolean f1230a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f1231b;

    /* renamed from: c  reason: collision with root package name */
    public int f1232c;

    /* renamed from: d  reason: collision with root package name */
    public int f1233d;

    /* renamed from: e  reason: collision with root package name */
    public final Rect f1234e;

    /* renamed from: f  reason: collision with root package name */
    public final Rect f1235f;

    /* renamed from: g  reason: collision with root package name */
    public final b f1236g;

    public class a implements b {

        /* renamed from: a  reason: collision with root package name */
        public Drawable f1237a;

        public a() {
        }

        public boolean a() {
            return CardView.this.getPreventCornerOverlap();
        }

        public void b(int i10, int i11, int i12, int i13) {
            CardView.this.f1235f.set(i10, i11, i12, i13);
            CardView cardView = CardView.this;
            Rect rect = cardView.f1234e;
            CardView.super.setPadding(i10 + rect.left, i11 + rect.top, i12 + rect.right, i13 + rect.bottom);
        }
    }

    public CardView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.cardViewStyle);
    }

    @NonNull
    public ColorStateList getCardBackgroundColor() {
        return ((t.a) f1229i).a(this.f1236g).h;
    }

    public float getCardElevation() {
        return CardView.this.getElevation();
    }

    @Px
    public int getContentPaddingBottom() {
        return this.f1234e.bottom;
    }

    @Px
    public int getContentPaddingLeft() {
        return this.f1234e.left;
    }

    @Px
    public int getContentPaddingRight() {
        return this.f1234e.right;
    }

    @Px
    public int getContentPaddingTop() {
        return this.f1234e.top;
    }

    public float getMaxCardElevation() {
        return ((t.a) f1229i).b(this.f1236g);
    }

    public boolean getPreventCornerOverlap() {
        return this.f1231b;
    }

    public float getRadius() {
        return ((t.a) f1229i).c(this.f1236g);
    }

    public boolean getUseCompatPadding() {
        return this.f1230a;
    }

    public void onMeasure(int i10, int i11) {
        super.onMeasure(i10, i11);
    }

    public void setCardBackgroundColor(@ColorInt int i10) {
        c cVar = f1229i;
        b bVar = this.f1236g;
        ColorStateList valueOf = ColorStateList.valueOf(i10);
        d a10 = ((t.a) cVar).a(bVar);
        a10.b(valueOf);
        a10.invalidateSelf();
    }

    public void setCardElevation(float f10) {
        CardView.this.setElevation(f10);
    }

    public void setMaxCardElevation(float f10) {
        ((t.a) f1229i).d(this.f1236g, f10);
    }

    public void setMinimumHeight(int i10) {
        this.f1233d = i10;
        super.setMinimumHeight(i10);
    }

    public void setMinimumWidth(int i10) {
        this.f1232c = i10;
        super.setMinimumWidth(i10);
    }

    public void setPadding(int i10, int i11, int i12, int i13) {
    }

    public void setPaddingRelative(int i10, int i11, int i12, int i13) {
    }

    public void setPreventCornerOverlap(boolean z10) {
        if (z10 != this.f1231b) {
            this.f1231b = z10;
            c cVar = f1229i;
            b bVar = this.f1236g;
            t.a aVar = (t.a) cVar;
            aVar.d(bVar, aVar.a(bVar).f10266e);
        }
    }

    public void setRadius(float f10) {
        d a10 = ((t.a) f1229i).a(this.f1236g);
        if (f10 != a10.f10262a) {
            a10.f10262a = f10;
            a10.c(null);
            a10.invalidateSelf();
        }
    }

    public void setUseCompatPadding(boolean z10) {
        if (this.f1230a != z10) {
            this.f1230a = z10;
            c cVar = f1229i;
            b bVar = this.f1236g;
            t.a aVar = (t.a) cVar;
            aVar.d(bVar, aVar.a(bVar).f10266e);
        }
    }

    public CardView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        ColorStateList colorStateList;
        int i11;
        Rect rect = new Rect();
        this.f1234e = rect;
        this.f1235f = new Rect();
        a aVar = new a();
        this.f1236g = aVar;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.CardView, i10, R$style.CardView);
        int i12 = R$styleable.CardView_cardBackgroundColor;
        if (obtainStyledAttributes.hasValue(i12)) {
            colorStateList = obtainStyledAttributes.getColorStateList(i12);
        } else {
            TypedArray obtainStyledAttributes2 = getContext().obtainStyledAttributes(h);
            int color = obtainStyledAttributes2.getColor(0, 0);
            obtainStyledAttributes2.recycle();
            float[] fArr = new float[3];
            Color.colorToHSV(color, fArr);
            if (fArr[2] > 0.5f) {
                i11 = getResources().getColor(R$color.cardview_light_background);
            } else {
                i11 = getResources().getColor(R$color.cardview_dark_background);
            }
            colorStateList = ColorStateList.valueOf(i11);
        }
        float dimension = obtainStyledAttributes.getDimension(R$styleable.CardView_cardCornerRadius, Constant.VOLUME_FLOAT_MIN);
        float dimension2 = obtainStyledAttributes.getDimension(R$styleable.CardView_cardElevation, Constant.VOLUME_FLOAT_MIN);
        float dimension3 = obtainStyledAttributes.getDimension(R$styleable.CardView_cardMaxElevation, Constant.VOLUME_FLOAT_MIN);
        this.f1230a = obtainStyledAttributes.getBoolean(R$styleable.CardView_cardUseCompatPadding, false);
        this.f1231b = obtainStyledAttributes.getBoolean(R$styleable.CardView_cardPreventCornerOverlap, true);
        int dimensionPixelSize = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_contentPadding, 0);
        rect.left = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_contentPaddingLeft, dimensionPixelSize);
        rect.top = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_contentPaddingTop, dimensionPixelSize);
        rect.right = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_contentPaddingRight, dimensionPixelSize);
        rect.bottom = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_contentPaddingBottom, dimensionPixelSize);
        dimension3 = dimension2 > dimension3 ? dimension2 : dimension3;
        this.f1232c = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_android_minWidth, 0);
        this.f1233d = obtainStyledAttributes.getDimensionPixelSize(R$styleable.CardView_android_minHeight, 0);
        obtainStyledAttributes.recycle();
        d dVar = new d(colorStateList, dimension);
        aVar.f1237a = dVar;
        CardView.this.setBackgroundDrawable(dVar);
        CardView cardView = CardView.this;
        cardView.setClipToOutline(true);
        cardView.setElevation(dimension2);
        ((t.a) f1229i).d(aVar, dimension3);
    }

    public void setCardBackgroundColor(@Nullable ColorStateList colorStateList) {
        d a10 = ((t.a) f1229i).a(this.f1236g);
        a10.b(colorStateList);
        a10.invalidateSelf();
    }
}
