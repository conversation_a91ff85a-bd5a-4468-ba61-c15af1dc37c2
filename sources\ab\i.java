package ab;

import java.math.BigInteger;

/* compiled from: GOST3410PublicKeyParameterSetSpec */
public class i {

    /* renamed from: a  reason: collision with root package name */
    public BigInteger f261a;

    /* renamed from: b  reason: collision with root package name */
    public BigInteger f262b;

    /* renamed from: c  reason: collision with root package name */
    public BigInteger f263c;

    public i(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
        this.f261a = bigInteger;
        this.f262b = bigInteger2;
        this.f263c = bigInteger3;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof i)) {
            return false;
        }
        i iVar = (i) obj;
        if (!this.f263c.equals(iVar.f263c) || !this.f261a.equals(iVar.f261a) || !this.f262b.equals(iVar.f262b)) {
            return false;
        }
        return true;
    }

    public int hashCode() {
        return (this.f263c.hashCode() ^ this.f261a.hashCode()) ^ this.f262b.hashCode();
    }
}
