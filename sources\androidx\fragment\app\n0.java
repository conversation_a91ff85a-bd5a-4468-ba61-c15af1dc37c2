package androidx.fragment.app;

import android.view.View;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.WeakHashMap;

/* compiled from: FragmentTransitionImpl */
public class n0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1970a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Map f1971b;

    public n0(m0 m0Var, ArrayList arrayList, Map map) {
        this.f1970a = arrayList;
        this.f1971b = map;
    }

    public void run() {
        String str;
        int size = this.f1970a.size();
        for (int i10 = 0; i10 < size; i10++) {
            View view = (View) this.f1970a.get(i10);
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            String transitionName = view.getTransitionName();
            if (transitionName != null) {
                Iterator it = this.f1971b.entrySet().iterator();
                while (true) {
                    if (!it.hasNext()) {
                        str = null;
                        break;
                    }
                    Map.Entry entry = (Map.Entry) it.next();
                    if (transitionName.equals(entry.getValue())) {
                        str = (String) entry.getKey();
                        break;
                    }
                }
                view.setTransitionName(str);
            }
        }
    }
}
