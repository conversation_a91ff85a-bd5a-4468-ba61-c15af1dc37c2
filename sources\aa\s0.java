package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERGraphicString */
public class s0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f226a;

    public s0(byte[] bArr) {
        this.f226a = a.c(bArr);
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof s0)) {
            return false;
        }
        return a.a(this.f226a, ((s0) qVar).f226a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f226a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(25, this.f226a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f226a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f226a.length) + 1 + this.f226a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
