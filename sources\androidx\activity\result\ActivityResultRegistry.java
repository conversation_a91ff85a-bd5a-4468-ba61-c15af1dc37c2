package androidx.activity.result;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import androidx.lifecycle.h;
import com.xiaomi.mitv.socialtv.common.net.media.VideoConstants;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;

public abstract class ActivityResultRegistry {

    /* renamed from: a  reason: collision with root package name */
    public Random f297a = new Random();

    /* renamed from: b  reason: collision with root package name */
    public final Map<Integer, String> f298b = new HashMap();

    /* renamed from: c  reason: collision with root package name */
    public final Map<String, Integer> f299c = new HashMap();

    /* renamed from: d  reason: collision with root package name */
    public final Map<String, d> f300d = new HashMap();

    /* renamed from: e  reason: collision with root package name */
    public final transient Map<String, c<?>> f301e = new HashMap();

    /* renamed from: f  reason: collision with root package name */
    public final Map<String, Object> f302f = new HashMap();

    /* renamed from: g  reason: collision with root package name */
    public final Bundle f303g = new Bundle();

    public class a extends b<I> {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ int f308a;

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ l.a f309b;

        /* renamed from: c  reason: collision with root package name */
        public final /* synthetic */ String f310c;

        public a(int i10, l.a aVar, String str) {
            this.f308a = i10;
            this.f309b = aVar;
            this.f310c = str;
        }

        @Override // androidx.activity.result.b
        public void a(I i10, @Nullable y.b bVar) {
            ActivityResultRegistry.this.b(this.f308a, this.f309b, i10, null);
        }

        @Override // androidx.activity.result.b
        public void b() {
            ActivityResultRegistry.this.f(this.f310c);
        }
    }

    public class b extends b<I> {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ int f312a;

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ l.a f313b;

        /* renamed from: c  reason: collision with root package name */
        public final /* synthetic */ String f314c;

        public b(int i10, l.a aVar, String str) {
            this.f312a = i10;
            this.f313b = aVar;
            this.f314c = str;
        }

        @Override // androidx.activity.result.b
        public void a(I i10, @Nullable y.b bVar) {
            ActivityResultRegistry.this.b(this.f312a, this.f313b, i10, null);
        }

        @Override // androidx.activity.result.b
        public void b() {
            ActivityResultRegistry.this.f(this.f314c);
        }
    }

    public static class c<O> {

        /* renamed from: a  reason: collision with root package name */
        public final a<O> f316a;

        /* renamed from: b  reason: collision with root package name */
        public final l.a<?, O> f317b;

        public c(a<O> aVar, l.a<?, O> aVar2) {
            this.f316a = aVar;
            this.f317b = aVar2;
        }
    }

    public static class d {

        /* renamed from: a  reason: collision with root package name */
        public final Lifecycle f318a;

        /* renamed from: b  reason: collision with root package name */
        public final ArrayList<e> f319b = new ArrayList<>();

        public d(@NonNull Lifecycle lifecycle) {
            this.f318a = lifecycle;
        }
    }

    @MainThread
    public final boolean a(int i10, int i11, @Nullable Intent intent) {
        a<O> aVar;
        String str = this.f298b.get(Integer.valueOf(i10));
        if (str == null) {
            return false;
        }
        c<?> cVar = this.f301e.get(str);
        if (cVar == null || (aVar = cVar.f316a) == null) {
            this.f302f.remove(str);
            this.f303g.putParcelable(str, new ActivityResult(i11, intent));
            return true;
        }
        aVar.a(cVar.f317b.c(i11, intent));
        return true;
    }

    @MainThread
    public abstract <I, O> void b(int i10, @NonNull l.a<I, O> aVar, @SuppressLint({"UnknownNullness"}) I i11, @Nullable y.b bVar);

    @NonNull
    public final <I, O> b<I> c(@NonNull final String str, @NonNull g gVar, @NonNull final l.a<I, O> aVar, @NonNull final a<O> aVar2) {
        Lifecycle lifecycle = gVar.getLifecycle();
        h hVar = (h) lifecycle;
        if (!hVar.f2067b.isAtLeast(Lifecycle.State.STARTED)) {
            int e10 = e(str);
            d dVar = this.f300d.get(str);
            if (dVar == null) {
                dVar = new d(lifecycle);
            }
            AnonymousClass1 r02 = new e() {
                /* class androidx.activity.result.ActivityResultRegistry.AnonymousClass1 */

                @Override // androidx.lifecycle.e
                public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
                    if (Lifecycle.Event.ON_START.equals(event)) {
                        ActivityResultRegistry.this.f301e.put(str, new c<>(aVar2, aVar));
                        if (ActivityResultRegistry.this.f302f.containsKey(str)) {
                            Object obj = ActivityResultRegistry.this.f302f.get(str);
                            ActivityResultRegistry.this.f302f.remove(str);
                            aVar2.a(obj);
                        }
                        ActivityResult activityResult = (ActivityResult) ActivityResultRegistry.this.f303g.getParcelable(str);
                        if (activityResult != null) {
                            ActivityResultRegistry.this.f303g.remove(str);
                            aVar2.a(aVar.c(activityResult.f295a, activityResult.f296b));
                        }
                    } else if (Lifecycle.Event.ON_STOP.equals(event)) {
                        ActivityResultRegistry.this.f301e.remove(str);
                    } else if (Lifecycle.Event.ON_DESTROY.equals(event)) {
                        ActivityResultRegistry.this.f(str);
                    }
                }
            };
            dVar.f318a.a(r02);
            dVar.f319b.add(r02);
            this.f300d.put(str, dVar);
            return new a(e10, aVar, str);
        }
        throw new IllegalStateException("LifecycleOwner " + gVar + " is attempting to register while current state is " + hVar.f2067b + ". LifecycleOwners must call register before they are STARTED.");
    }

    /* JADX DEBUG: Multi-variable search result rejected for r6v0, resolved type: androidx.activity.result.a<O> */
    /* JADX WARN: Multi-variable type inference failed */
    @NonNull
    public final <I, O> b<I> d(@NonNull String str, @NonNull l.a<I, O> aVar, @NonNull a<O> aVar2) {
        int e10 = e(str);
        this.f301e.put(str, new c<>(aVar2, aVar));
        if (this.f302f.containsKey(str)) {
            Object obj = this.f302f.get(str);
            this.f302f.remove(str);
            aVar2.a(obj);
        }
        ActivityResult activityResult = (ActivityResult) this.f303g.getParcelable(str);
        if (activityResult != null) {
            this.f303g.remove(str);
            aVar2.a(aVar.c(activityResult.f295a, activityResult.f296b));
        }
        return new b(e10, aVar, str);
    }

    public final int e(String str) {
        Integer num = this.f299c.get(str);
        if (num != null) {
            return num.intValue();
        }
        int nextInt = this.f297a.nextInt(2147418112);
        while (true) {
            int i10 = nextInt + VideoConstants.SEARCH_MASK_EDUCATION;
            if (this.f298b.containsKey(Integer.valueOf(i10))) {
                nextInt = this.f297a.nextInt(2147418112);
            } else {
                this.f298b.put(Integer.valueOf(i10), str);
                this.f299c.put(str, Integer.valueOf(i10));
                return i10;
            }
        }
    }

    @MainThread
    public final void f(@NonNull String str) {
        Integer remove = this.f299c.remove(str);
        if (remove != null) {
            this.f298b.remove(remove);
        }
        this.f301e.remove(str);
        if (this.f302f.containsKey(str)) {
            StringBuilder b10 = c.b("Dropping pending result for request ", str, ": ");
            b10.append(this.f302f.get(str));
            Log.w("ActivityResultRegistry", b10.toString());
            this.f302f.remove(str);
        }
        if (this.f303g.containsKey(str)) {
            StringBuilder b11 = c.b("Dropping pending result for request ", str, ": ");
            b11.append(this.f303g.getParcelable(str));
            Log.w("ActivityResultRegistry", b11.toString());
            this.f303g.remove(str);
        }
        d dVar = this.f300d.get(str);
        if (dVar != null) {
            Iterator<e> it = dVar.f319b.iterator();
            while (it.hasNext()) {
                dVar.f318a.b(it.next());
            }
            dVar.f319b.clear();
            this.f300d.remove(str);
        }
    }
}
