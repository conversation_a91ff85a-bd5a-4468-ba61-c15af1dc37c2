package androidx.fragment.app;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.view.LayoutInflater;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import i0.e;

/* compiled from: FragmentHostCallback */
public abstract class v<E> extends s {
    @Nullable

    /* renamed from: a  reason: collision with root package name */
    public final Activity f2015a;
    @NonNull

    /* renamed from: b  reason: collision with root package name */
    public final Context f2016b;
    @NonNull

    /* renamed from: c  reason: collision with root package name */
    public final Handler f2017c;

    /* renamed from: d  reason: collision with root package name */
    public final FragmentManager f2018d = new y();

    public v(@NonNull n nVar) {
        Handler handler = new Handler();
        this.f2015a = nVar;
        e.b(nVar, "context == null");
        this.f2016b = nVar;
        this.f2017c = handler;
    }

    @Nullable
    public abstract E d();

    @NonNull
    public abstract LayoutInflater e();

    public abstract boolean f(@NonNull Fragment fragment);

    public abstract void g();
}
