package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.util.TypedValue;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$color;
import androidx.appcompat.R$drawable;
import androidx.appcompat.widget.b0;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: AppCompatDrawableManager */
public final class g {

    /* renamed from: b  reason: collision with root package name */
    public static final PorterDuff.Mode f1091b = PorterDuff.Mode.SRC_IN;

    /* renamed from: c  reason: collision with root package name */
    public static g f1092c;

    /* renamed from: a  reason: collision with root package name */
    public b0 f1093a;

    /* compiled from: AppCompatDrawableManager */
    public class a implements b0.c {

        /* renamed from: a  reason: collision with root package name */
        public final int[] f1094a = {R$drawable.abc_textfield_search_default_mtrl_alpha, R$drawable.abc_textfield_default_mtrl_alpha, R$drawable.abc_ab_share_pack_mtrl_alpha};

        /* renamed from: b  reason: collision with root package name */
        public final int[] f1095b = {R$drawable.abc_ic_commit_search_api_mtrl_alpha, R$drawable.abc_seekbar_tick_mark_material, R$drawable.abc_ic_menu_share_mtrl_alpha, R$drawable.abc_ic_menu_copy_mtrl_am_alpha, R$drawable.abc_ic_menu_cut_mtrl_alpha, R$drawable.abc_ic_menu_selectall_mtrl_alpha, R$drawable.abc_ic_menu_paste_mtrl_am_alpha};

        /* renamed from: c  reason: collision with root package name */
        public final int[] f1096c = {R$drawable.abc_textfield_activated_mtrl_alpha, R$drawable.abc_textfield_search_activated_mtrl_alpha, R$drawable.abc_cab_background_top_mtrl_alpha, R$drawable.abc_text_cursor_material, R$drawable.abc_text_select_handle_left_mtrl_dark, R$drawable.abc_text_select_handle_middle_mtrl_dark, R$drawable.abc_text_select_handle_right_mtrl_dark, R$drawable.abc_text_select_handle_left_mtrl_light, R$drawable.abc_text_select_handle_middle_mtrl_light, R$drawable.abc_text_select_handle_right_mtrl_light};

        /* renamed from: d  reason: collision with root package name */
        public final int[] f1097d = {R$drawable.abc_popup_background_mtrl_mult, R$drawable.abc_cab_background_internal_bg, R$drawable.abc_menu_hardkey_panel_mtrl_mult};

        /* renamed from: e  reason: collision with root package name */
        public final int[] f1098e = {R$drawable.abc_tab_indicator_material, R$drawable.abc_textfield_search_material};

        /* renamed from: f  reason: collision with root package name */
        public final int[] f1099f = {R$drawable.abc_btn_check_material, R$drawable.abc_btn_radio_material, R$drawable.abc_btn_check_material_anim, R$drawable.abc_btn_radio_material_anim};

        public final boolean a(int[] iArr, int i10) {
            for (int i11 : iArr) {
                if (i11 == i10) {
                    return true;
                }
            }
            return false;
        }

        public final ColorStateList b(@NonNull Context context, @ColorInt int i10) {
            int c10 = h0.c(context, R$attr.colorControlHighlight);
            int b10 = h0.b(context, R$attr.colorButtonNormal);
            return new ColorStateList(new int[][]{h0.f1120b, h0.f1122d, h0.f1121c, h0.f1124f}, new int[]{b10, b0.a.a(c10, i10), b0.a.a(c10, i10), i10});
        }

        public ColorStateList c(@NonNull Context context, int i10) {
            if (i10 == R$drawable.abc_edit_text_material) {
                int i11 = R$color.abc_tint_edittext;
                ThreadLocal<TypedValue> threadLocal = m.a.f7597a;
                return context.getColorStateList(i11);
            } else if (i10 == R$drawable.abc_switch_track_mtrl_alpha) {
                int i12 = R$color.abc_tint_switch_track;
                ThreadLocal<TypedValue> threadLocal2 = m.a.f7597a;
                return context.getColorStateList(i12);
            } else if (i10 == R$drawable.abc_switch_thumb_material) {
                int[][] iArr = new int[3][];
                int[] iArr2 = new int[3];
                int i13 = R$attr.colorSwitchThumbNormal;
                ColorStateList d10 = h0.d(context, i13);
                if (d10 == null || !d10.isStateful()) {
                    iArr[0] = h0.f1120b;
                    iArr2[0] = h0.b(context, i13);
                    iArr[1] = h0.f1123e;
                    iArr2[1] = h0.c(context, R$attr.colorControlActivated);
                    iArr[2] = h0.f1124f;
                    iArr2[2] = h0.c(context, i13);
                } else {
                    iArr[0] = h0.f1120b;
                    iArr2[0] = d10.getColorForState(iArr[0], 0);
                    iArr[1] = h0.f1123e;
                    iArr2[1] = h0.c(context, R$attr.colorControlActivated);
                    iArr[2] = h0.f1124f;
                    iArr2[2] = d10.getDefaultColor();
                }
                return new ColorStateList(iArr, iArr2);
            } else if (i10 == R$drawable.abc_btn_default_mtrl_shape) {
                return b(context, h0.c(context, R$attr.colorButtonNormal));
            } else {
                if (i10 == R$drawable.abc_btn_borderless_material) {
                    return b(context, 0);
                }
                if (i10 == R$drawable.abc_btn_colored_material) {
                    return b(context, h0.c(context, R$attr.colorAccent));
                }
                if (i10 == R$drawable.abc_spinner_mtrl_am_alpha || i10 == R$drawable.abc_spinner_textfield_background_material) {
                    int i14 = R$color.abc_tint_spinner;
                    ThreadLocal<TypedValue> threadLocal3 = m.a.f7597a;
                    return context.getColorStateList(i14);
                } else if (a(this.f1095b, i10)) {
                    return h0.d(context, R$attr.colorControlNormal);
                } else {
                    if (a(this.f1098e, i10)) {
                        int i15 = R$color.abc_tint_default;
                        ThreadLocal<TypedValue> threadLocal4 = m.a.f7597a;
                        return context.getColorStateList(i15);
                    } else if (a(this.f1099f, i10)) {
                        int i16 = R$color.abc_tint_btn_checkable;
                        ThreadLocal<TypedValue> threadLocal5 = m.a.f7597a;
                        return context.getColorStateList(i16);
                    } else if (i10 != R$drawable.abc_seekbar_thumb_material) {
                        return null;
                    } else {
                        int i17 = R$color.abc_tint_seek_thumb;
                        ThreadLocal<TypedValue> threadLocal6 = m.a.f7597a;
                        return context.getColorStateList(i17);
                    }
                }
            }
        }

        public final void d(Drawable drawable, int i10, PorterDuff.Mode mode) {
            if (u.a(drawable)) {
                drawable = drawable.mutate();
            }
            if (mode == null) {
                mode = g.f1091b;
            }
            drawable.setColorFilter(g.c(i10, mode));
        }
    }

    public static synchronized g a() {
        g gVar;
        synchronized (g.class) {
            if (f1092c == null) {
                e();
            }
            gVar = f1092c;
        }
        return gVar;
    }

    public static synchronized PorterDuffColorFilter c(int i10, PorterDuff.Mode mode) {
        PorterDuffColorFilter g10;
        synchronized (g.class) {
            g10 = b0.g(i10, mode);
        }
        return g10;
    }

    public static synchronized void e() {
        synchronized (g.class) {
            if (f1092c == null) {
                g gVar = new g();
                f1092c = gVar;
                gVar.f1093a = b0.c();
                b0 b0Var = f1092c.f1093a;
                a aVar = new a();
                synchronized (b0Var) {
                    b0Var.f1066g = aVar;
                }
            }
        }
    }

    public static void f(Drawable drawable, k0 k0Var, int[] iArr) {
        PorterDuff.Mode mode = b0.h;
        if (!u.a(drawable) || drawable.mutate() == drawable) {
            boolean z10 = k0Var.f1135d;
            if (z10 || k0Var.f1134c) {
                PorterDuffColorFilter porterDuffColorFilter = null;
                ColorStateList colorStateList = z10 ? k0Var.f1132a : null;
                PorterDuff.Mode mode2 = k0Var.f1134c ? k0Var.f1133b : b0.h;
                if (!(colorStateList == null || mode2 == null)) {
                    porterDuffColorFilter = b0.g(colorStateList.getColorForState(iArr, 0), mode2);
                }
                drawable.setColorFilter(porterDuffColorFilter);
                return;
            }
            drawable.clearColorFilter();
            return;
        }
        Log.d("ResourceManagerInternal", "Mutated drawable is not the same instance as the input.");
    }

    public synchronized Drawable b(@NonNull Context context, @DrawableRes int i10) {
        return this.f1093a.e(context, i10);
    }

    public synchronized ColorStateList d(@NonNull Context context, @DrawableRes int i10) {
        return this.f1093a.h(context, i10);
    }
}
