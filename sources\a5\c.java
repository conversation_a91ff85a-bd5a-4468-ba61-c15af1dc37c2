package a5;

import androidx.appcompat.widget.f0;
import androidx.recyclerview.widget.o;
import bb.f;
import c5.j;
import c5.m;
import c5.n;
import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import com.xiaomi.mitv.socialtv.common.utils.HanziToPinyin;
import java.util.Arrays;

/* compiled from: AsciiString */
public final class c implements CharSequence, Comparable<CharSequence> {

    /* renamed from: f  reason: collision with root package name */
    public static final c f74f;

    /* renamed from: g  reason: collision with root package name */
    public static final g<CharSequence> f75g = new a();
    public static final g<CharSequence> h = new b();

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f76a;

    /* renamed from: b  reason: collision with root package name */
    public final int f77b;

    /* renamed from: c  reason: collision with root package name */
    public final int f78c;

    /* renamed from: d  reason: collision with root package name */
    public int f79d;

    /* renamed from: e  reason: collision with root package name */
    public String f80e;

    /* compiled from: AsciiString */
    public static class a implements g<CharSequence> {
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object, java.lang.Object] */
        @Override // a5.g
        public boolean equals(CharSequence charSequence, CharSequence charSequence2) {
            CharSequence charSequence3 = charSequence;
            CharSequence charSequence4 = charSequence2;
            if (charSequence3 == null || charSequence4 == null) {
                if (charSequence3 == charSequence4) {
                    return true;
                }
                return false;
            } else if (charSequence3 instanceof c) {
                return ((c) charSequence3).c(charSequence4);
            } else {
                if (charSequence4 instanceof c) {
                    return ((c) charSequence4).c(charSequence3);
                }
                if (charSequence3.length() != charSequence4.length()) {
                    return false;
                }
                for (int i10 = 0; i10 < charSequence3.length(); i10++) {
                    if (!c.d(charSequence3.charAt(i10), charSequence4.charAt(i10))) {
                        return false;
                    }
                }
            }
            return true;
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // a5.g
        public int hashCode(CharSequence charSequence) {
            return c.e(charSequence);
        }
    }

    /* compiled from: AsciiString */
    public static class b implements g<CharSequence> {
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object, java.lang.Object] */
        @Override // a5.g
        public boolean equals(CharSequence charSequence, CharSequence charSequence2) {
            CharSequence charSequence3 = charSequence;
            CharSequence charSequence4 = charSequence2;
            if (charSequence3 == null || charSequence4 == null) {
                if (charSequence3 == charSequence4) {
                    return true;
                }
                return false;
            } else if (charSequence3 instanceof c) {
                return ((c) charSequence3).b(charSequence4);
            } else {
                if (charSequence4 instanceof c) {
                    return ((c) charSequence4).b(charSequence3);
                }
                if (charSequence3.length() != charSequence4.length()) {
                    return false;
                }
                for (int i10 = 0; i10 < charSequence3.length(); i10++) {
                    if (charSequence3.charAt(i10) != charSequence4.charAt(i10)) {
                        return false;
                    }
                }
            }
            return true;
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // a5.g
        public int hashCode(CharSequence charSequence) {
            return c.e(charSequence);
        }
    }

    static {
        c cVar = new c("");
        cVar.f80e = "";
        f74f = cVar;
    }

    public c(byte[] bArr, int i10, int i11, boolean z10) {
        if (z10) {
            this.f76a = Arrays.copyOfRange(bArr, i10, i10 + i11);
            this.f77b = 0;
        } else if (!j.m(i10, i11, bArr.length)) {
            this.f76a = bArr;
            this.f77b = i10;
        } else {
            throw new IndexOutOfBoundsException(b0.b.a(o.a("expected: 0 <= start(", i10, ") <= start + length(", i11, ") <= value.length("), bArr.length, ')'));
        }
        this.f78c = i11;
    }

    public static boolean d(char c10, char c11) {
        return c10 == c11 || g(c10) == g(c11);
    }

    public static int e(CharSequence charSequence) {
        int i10;
        int i11;
        int i12;
        boolean z10 = false;
        if (charSequence == null) {
            return 0;
        }
        if (charSequence instanceof c) {
            return charSequence.hashCode();
        }
        d5.a aVar = m.f3142a;
        int length = charSequence.length();
        int i13 = length & 7;
        int i14 = -1028477387;
        if (length >= 32) {
            for (int i15 = length - 8; i15 >= i13; i15 -= 8) {
                i14 = m.j(charSequence, i15, i14);
            }
        } else if (length >= 8) {
            i14 = m.j(charSequence, length - 8, -1028477387);
            if (length >= 16) {
                i14 = m.j(charSequence, length - 16, i14);
                if (length >= 24) {
                    i14 = m.j(charSequence, length - 24, i14);
                }
            }
        }
        if (i13 == 0) {
            return i14;
        }
        int i16 = -862048943;
        if (((i13 != 2) && (i13 != 4)) && (i13 != 6)) {
            i14 = (i14 * -862048943) + (charSequence.charAt(0) & 31);
            i10 = 1;
        } else {
            i10 = 0;
        }
        if (((i13 != 1) && (i13 != 4)) && (i13 != 5)) {
            int i17 = i14 * (i10 == 0 ? -862048943 : 461845907);
            if (m.f3160t) {
                i11 = charSequence.charAt(i10 + 1) & 31;
                i12 = (charSequence.charAt(i10) & 31) << 8;
            } else {
                i11 = (charSequence.charAt(i10 + 1) & 31) << 8;
                i12 = charSequence.charAt(i10) & 31;
            }
            i14 = i17 + ((i12 | i11) & 522133279);
            i10 += 2;
        }
        if (i13 < 4) {
            return i14;
        }
        boolean z11 = i10 == 0;
        if (i10 == 3) {
            z10 = true;
        }
        if (!z10 && !z11) {
            i16 = 461845907;
        }
        return (i14 * i16) + m.k(charSequence, i10);
    }

    public static byte f(byte b10) {
        return b10 >= 65 && b10 <= 90 ? (byte) (b10 + 32) : b10;
    }

    public static char g(char c10) {
        return c10 >= 'A' && c10 <= 'Z' ? (char) (c10 + HanziToPinyin.Token.SEPARATOR) : c10;
    }

    public byte a(int i10) {
        if (i10 < 0 || i10 >= this.f78c) {
            throw new IndexOutOfBoundsException(f.b(f0.b("index: ", i10, " must be in the range [0,"), this.f78c, ")"));
        } else if (m.i()) {
            return n.k(this.f76a, i10 + this.f77b);
        } else {
            return this.f76a[i10 + this.f77b];
        }
    }

    public boolean b(CharSequence charSequence) {
        if (this == charSequence) {
            return true;
        }
        if (charSequence.length() != this.f78c) {
            return false;
        }
        if (charSequence instanceof c) {
            return equals(charSequence);
        }
        int i10 = this.f77b;
        for (int i11 = 0; i11 < charSequence.length(); i11++) {
            if (((char) (this.f76a[i10] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP)) != charSequence.charAt(i11)) {
                return false;
            }
            i10++;
        }
        return true;
    }

    public boolean c(CharSequence charSequence) {
        if (this == charSequence) {
            return true;
        }
        int length = charSequence.length();
        int i10 = this.f78c;
        if (length != i10) {
            return false;
        }
        if (charSequence instanceof c) {
            c cVar = (c) charSequence;
            int i11 = this.f77b;
            int i12 = cVar.f77b;
            int i13 = i10 + i11;
            while (i11 < i13) {
                byte b10 = this.f76a[i11];
                byte b11 = cVar.f76a[i12];
                if (!(b10 == b11 || f(b10) == f(b11))) {
                    return false;
                }
                i11++;
                i12++;
            }
            return true;
        }
        int i14 = this.f77b;
        for (int i15 = 0; i15 < i10; i15++) {
            if (!d((char) (this.f76a[i14] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP), charSequence.charAt(i15))) {
                return false;
            }
            i14++;
        }
        return true;
    }

    public char charAt(int i10) {
        return (char) (a(i10) & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP);
    }

    /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
    @Override // java.lang.Comparable
    public int compareTo(CharSequence charSequence) {
        CharSequence charSequence2 = charSequence;
        int i10 = 0;
        if (this == charSequence2) {
            return 0;
        }
        int i11 = this.f78c;
        int length = charSequence2.length();
        int min = Math.min(i11, length);
        int i12 = this.f77b;
        while (i10 < min) {
            int charAt = ((char) (this.f76a[i12] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP)) - charSequence2.charAt(i10);
            if (charAt != 0) {
                return charAt;
            }
            i10++;
            i12++;
        }
        return i11 - length;
    }

    public boolean equals(Object obj) {
        boolean z10;
        if (obj != null && obj.getClass() == c.class) {
            if (this == obj) {
                return true;
            }
            c cVar = (c) obj;
            if (this.f78c == cVar.f78c && hashCode() == cVar.hashCode()) {
                byte[] bArr = this.f76a;
                int i10 = this.f77b;
                byte[] bArr2 = cVar.f76a;
                int i11 = cVar.f77b;
                int i12 = this.f78c;
                if (!m.i() || !n.f3176m) {
                    int i13 = i12 + i10;
                    while (true) {
                        if (i10 >= i13) {
                            z10 = true;
                            break;
                        } else if (bArr[i10] != bArr2[i11]) {
                            z10 = false;
                            break;
                        } else {
                            i10++;
                            i11++;
                        }
                    }
                } else {
                    z10 = n.h(bArr, i10, bArr2, i11, i12);
                }
                if (z10) {
                    return true;
                }
            }
        }
        return false;
    }

    public int hashCode() {
        int i10;
        int i11;
        short s10;
        int i12;
        long j10;
        long j11;
        int i13 = this.f79d;
        if (i13 == 0) {
            byte[] bArr = this.f76a;
            int i14 = this.f77b;
            int i15 = this.f78c;
            if (!m.i() || !n.f3176m) {
                int i16 = i15 & 7;
                int i17 = i14 + i16;
                int i18 = -1028477387;
                for (int i19 = (i14 - 8) + i15; i19 >= i17; i19 -= 8) {
                    if (m.f3160t) {
                        j10 = ((((long) bArr[i19 + 1]) & 255) << 48) | (((long) bArr[i19]) << 56) | ((((long) bArr[i19 + 2]) & 255) << 40) | ((((long) bArr[i19 + 3]) & 255) << 32) | ((((long) bArr[i19 + 4]) & 255) << 24) | ((((long) bArr[i19 + 5]) & 255) << 16) | ((((long) bArr[i19 + 6]) & 255) << 8);
                        j11 = ((long) bArr[i19 + 7]) & 255;
                    } else {
                        j10 = ((((long) bArr[i19 + 1]) & 255) << 8) | (((long) bArr[i19]) & 255) | ((((long) bArr[i19 + 2]) & 255) << 16) | ((((long) bArr[i19 + 3]) & 255) << 24) | ((((long) bArr[i19 + 4]) & 255) << 32) | ((((long) bArr[i19 + 5]) & 255) << 40) | ((((long) bArr[i19 + 6]) & 255) << 48);
                        j11 = ((long) bArr[i19 + 7]) << 56;
                    }
                    i18 = n.x(j11 | j10, i18);
                }
                switch (i16) {
                    case 1:
                        i10 = i18 * -862048943;
                        byte b10 = bArr[i14];
                        d5.a aVar = n.f3165a;
                        i11 = b10 & 31;
                        i18 = i10 + i11;
                        break;
                    case 2:
                        i10 = i18 * -862048943;
                        s10 = m.h(bArr, i14);
                        i11 = s10 & 7967;
                        i18 = i10 + i11;
                        break;
                    case 3:
                        byte b11 = bArr[i14];
                        d5.a aVar2 = n.f3165a;
                        i10 = ((i18 * -862048943) + (b11 & 31)) * 461845907;
                        s10 = m.h(bArr, i14 + 1);
                        i11 = s10 & 7967;
                        i18 = i10 + i11;
                        break;
                    case 4:
                        i10 = i18 * -862048943;
                        i12 = m.g(bArr, i14);
                        i11 = i12 & 522133279;
                        i18 = i10 + i11;
                        break;
                    case 5:
                        byte b12 = bArr[i14];
                        d5.a aVar3 = n.f3165a;
                        i10 = ((i18 * -862048943) + (b12 & 31)) * 461845907;
                        i12 = m.g(bArr, i14 + 1);
                        i11 = i12 & 522133279;
                        i18 = i10 + i11;
                        break;
                    case 6:
                        i10 = ((i18 * -862048943) + (m.h(bArr, i14) & 7967)) * 461845907;
                        i12 = m.g(bArr, i14 + 2);
                        i11 = i12 & 522133279;
                        i18 = i10 + i11;
                        break;
                    case 7:
                        byte b13 = bArr[i14];
                        d5.a aVar4 = n.f3165a;
                        i10 = ((((i18 * -862048943) + (b13 & 31)) * 461845907) + (m.h(bArr, i14 + 1) & 7967)) * -862048943;
                        i12 = m.g(bArr, i14 + 3);
                        i11 = i12 & 522133279;
                        i18 = i10 + i11;
                        break;
                }
                i13 = i18;
            } else {
                i13 = n.w(bArr, i14, i15);
            }
            this.f79d = i13;
        }
        return i13;
    }

    public int length() {
        return this.f78c;
    }

    public CharSequence subSequence(int i10, int i11) {
        int i12 = i11 - i10;
        if (j.m(i10, i12, this.f78c)) {
            throw new IndexOutOfBoundsException(b0.b.a(o.a("expected: 0 <= start(", i10, ") <= end (", i11, ") <= length("), this.f78c, ')'));
        } else if (i10 == 0 && i11 == this.f78c) {
            return this;
        } else {
            if (i11 == i10) {
                return f74f;
            }
            return new c(this.f76a, i10 + this.f77b, i12, true);
        }
    }

    public String toString() {
        String str = this.f80e;
        if (str == null) {
            int i10 = this.f78c;
            int i11 = i10 + 0;
            if (i11 == 0) {
                str = "";
            } else if (!j.m(0, i11, i10)) {
                str = new String(this.f76a, 0, this.f77b + 0, i11);
            } else {
                throw new IndexOutOfBoundsException(b0.b.a(o.a("expected: 0 <= start(", 0, ") <= srcIdx + length(", i11, ") <= srcLen("), this.f78c, ')'));
            }
            this.f80e = str;
        }
        return str;
    }

    public c(CharSequence charSequence) {
        int length = charSequence.length();
        if (!j.m(0, length, charSequence.length())) {
            this.f76a = m.c(length);
            int i10 = 0;
            int i11 = 0;
            while (i10 < length) {
                byte[] bArr = this.f76a;
                char charAt = charSequence.charAt(i11);
                if (charAt > 255) {
                    charAt = '?';
                }
                bArr[i10] = (byte) charAt;
                i10++;
                i11++;
            }
            this.f77b = 0;
            this.f78c = length;
            return;
        }
        StringBuilder a10 = o.a("expected: 0 <= start(", 0, ") <= start + length(", length, ") <= value.length(");
        a10.append(charSequence.length());
        a10.append(')');
        throw new IndexOutOfBoundsException(a10.toString());
    }
}
