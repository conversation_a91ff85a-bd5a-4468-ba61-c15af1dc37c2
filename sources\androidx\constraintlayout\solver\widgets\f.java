package androidx.constraintlayout.solver.widgets;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import w.a;

/* compiled from: ConstraintWidgetGroup */
public class f {

    /* renamed from: a  reason: collision with root package name */
    public List<ConstraintWidget> f1362a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f1363b = false;

    /* renamed from: c  reason: collision with root package name */
    public final int[] f1364c = {-1, -1};

    /* renamed from: d  reason: collision with root package name */
    public List<ConstraintWidget> f1365d = new ArrayList();

    /* renamed from: e  reason: collision with root package name */
    public List<ConstraintWidget> f1366e = new ArrayList();

    /* renamed from: f  reason: collision with root package name */
    public HashSet<ConstraintWidget> f1367f = new HashSet<>();

    /* renamed from: g  reason: collision with root package name */
    public HashSet<ConstraintWidget> f1368g = new HashSet<>();
    public List<ConstraintWidget> h = new ArrayList();

    /* renamed from: i  reason: collision with root package name */
    public List<ConstraintWidget> f1369i = new ArrayList();

    public f(List<ConstraintWidget> list) {
        this.f1362a = list;
    }

    public void a(ConstraintWidget constraintWidget, int i10) {
        if (i10 == 0) {
            this.f1367f.add(constraintWidget);
        } else if (i10 == 1) {
            this.f1368g.add(constraintWidget);
        }
    }

    public final void b(ArrayList<ConstraintWidget> arrayList, ConstraintWidget constraintWidget) {
        ConstraintWidget constraintWidget2;
        if (!constraintWidget.f1294c0) {
            arrayList.add(constraintWidget);
            constraintWidget.f1294c0 = true;
            if (!constraintWidget.q()) {
                if (constraintWidget instanceof a) {
                    a aVar = (a) constraintWidget;
                    int i10 = aVar.f10632j0;
                    for (int i11 = 0; i11 < i10; i11++) {
                        b(arrayList, aVar.f10631i0[i11]);
                    }
                }
                int length = constraintWidget.A.length;
                for (int i12 = 0; i12 < length; i12++) {
                    ConstraintAnchor constraintAnchor = constraintWidget.A[i12].f1279d;
                    if (!(constraintAnchor == null || (constraintWidget2 = constraintAnchor.f1277b) == constraintWidget.D)) {
                        b(arrayList, constraintWidget2);
                    }
                }
            }
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:23:0x0044  */
    /* JADX WARNING: Removed duplicated region for block: B:24:0x004c  */
    /* JADX WARNING: Removed duplicated region for block: B:27:0x0067  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0083  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void c(androidx.constraintlayout.solver.widgets.ConstraintWidget r7) {
        /*
        // Method dump skipped, instructions count: 214
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.f.c(androidx.constraintlayout.solver.widgets.ConstraintWidget):void");
    }

    public f(List<ConstraintWidget> list, boolean z10) {
        this.f1362a = list;
        this.f1363b = z10;
    }
}
