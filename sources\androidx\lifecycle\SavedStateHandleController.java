package androidx.lifecycle;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.savedstate.a;
import androidx.savedstate.c;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

/* access modifiers changed from: package-private */
public final class SavedStateHandleController implements e {

    /* renamed from: a  reason: collision with root package name */
    public final String f2053a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f2054b = false;

    /* renamed from: c  reason: collision with root package name */
    public final o f2055c;

    public static final class a implements a.AbstractC0016a {
        @Override // androidx.savedstate.a.AbstractC0016a
        public void a(@NonNull c cVar) {
            if (cVar instanceof x) {
                w viewModelStore = ((x) cVar).getViewModelStore();
                androidx.savedstate.a savedStateRegistry = cVar.getSavedStateRegistry();
                Objects.requireNonNull(viewModelStore);
                Iterator it = new HashSet(viewModelStore.f2095a.keySet()).iterator();
                while (it.hasNext()) {
                    SavedStateHandleController.h(viewModelStore.f2095a.get((String) it.next()), savedStateRegistry, cVar.getLifecycle());
                }
                if (!new HashSet(viewModelStore.f2095a.keySet()).isEmpty()) {
                    savedStateRegistry.c(a.class);
                    return;
                }
                return;
            }
            throw new IllegalStateException("Internal error: OnRecreation should be registered only on componentsthat implement ViewModelStoreOwner");
        }
    }

    public SavedStateHandleController(String str, o oVar) {
        this.f2053a = str;
        this.f2055c = oVar;
    }

    public static void h(q qVar, androidx.savedstate.a aVar, Lifecycle lifecycle) {
        Object obj;
        Map<String, Object> map = qVar.f2090a;
        if (map == null) {
            obj = null;
        } else {
            synchronized (map) {
                obj = qVar.f2090a.get("androidx.lifecycle.savedstate.vm.tag");
            }
        }
        SavedStateHandleController savedStateHandleController = (SavedStateHandleController) obj;
        if (savedStateHandleController != null && !savedStateHandleController.f2054b) {
            savedStateHandleController.i(aVar, lifecycle);
            j(aVar, lifecycle);
        }
    }

    public static void j(final androidx.savedstate.a aVar, final Lifecycle lifecycle) {
        Lifecycle.State state = ((h) lifecycle).f2067b;
        if (state == Lifecycle.State.INITIALIZED || state.isAtLeast(Lifecycle.State.STARTED)) {
            aVar.c(a.class);
        } else {
            lifecycle.a(new e() {
                /* class androidx.lifecycle.SavedStateHandleController.AnonymousClass1 */

                @Override // androidx.lifecycle.e
                public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
                    if (event == Lifecycle.Event.ON_START) {
                        h hVar = (h) Lifecycle.this;
                        hVar.d("removeObserver");
                        hVar.f2066a.f(this);
                        aVar.c(a.class);
                    }
                }
            });
        }
    }

    @Override // androidx.lifecycle.e
    public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            this.f2054b = false;
            h hVar = (h) gVar.getLifecycle();
            hVar.d("removeObserver");
            hVar.f2066a.f(this);
        }
    }

    public void i(androidx.savedstate.a aVar, Lifecycle lifecycle) {
        if (!this.f2054b) {
            this.f2054b = true;
            lifecycle.a(this);
            aVar.b(this.f2053a, this.f2055c.f2081d);
            return;
        }
        throw new IllegalStateException("Already attached to lifecycleOwner");
    }
}
