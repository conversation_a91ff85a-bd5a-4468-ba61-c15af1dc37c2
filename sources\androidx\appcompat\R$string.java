package androidx.appcompat;

public final class R$string {
    public static final int abc_action_bar_home_description = 2131886083;
    public static final int abc_action_bar_up_description = 2131886084;
    public static final int abc_action_menu_overflow_description = 2131886085;
    public static final int abc_action_mode_done = 2131886086;
    public static final int abc_activity_chooser_view_see_all = 2131886087;
    public static final int abc_activitychooserview_choose_application = 2131886088;
    public static final int abc_capital_off = 2131886089;
    public static final int abc_capital_on = 2131886090;
    public static final int abc_menu_alt_shortcut_label = 2131886091;
    public static final int abc_menu_ctrl_shortcut_label = 2131886092;
    public static final int abc_menu_delete_shortcut_label = 2131886093;
    public static final int abc_menu_enter_shortcut_label = 2131886094;
    public static final int abc_menu_function_shortcut_label = 2131886095;
    public static final int abc_menu_meta_shortcut_label = 2131886096;
    public static final int abc_menu_shift_shortcut_label = 2131886097;
    public static final int abc_menu_space_shortcut_label = 2131886098;
    public static final int abc_menu_sym_shortcut_label = 2131886099;
    public static final int abc_prepend_shortcut_label = **********;
    public static final int abc_search_hint = **********;
    public static final int abc_searchview_description_clear = **********;
    public static final int abc_searchview_description_query = **********;
    public static final int abc_searchview_description_search = **********;
    public static final int abc_searchview_description_submit = **********;
    public static final int abc_searchview_description_voice = **********;
    public static final int abc_shareactionprovider_share_with = **********;
    public static final int abc_shareactionprovider_share_with_application = **********;
    public static final int abc_toolbar_collapse_description = **********;
    public static final int search_menu_title = **********;
    public static final int status_bar_notification_info_overflow = **********;

    private R$string() {
    }
}
