package androidx.recyclerview.widget;

import android.content.Context;
import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;
import com.google.protobuf.Reader;

/* compiled from: PagerSnapHelper */
public class w extends c0 {
    @Nullable

    /* renamed from: d  reason: collision with root package name */
    public v f2476d;
    @Nullable

    /* renamed from: e  reason: collision with root package name */
    public v f2477e;

    /* compiled from: PagerSnapHelper */
    public class a extends q {
        public a(Context context) {
            super(context);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.s, androidx.recyclerview.widget.q
        public void c(View view, RecyclerView.t tVar, RecyclerView.s.a aVar) {
            w wVar = w.this;
            int[] b10 = wVar.b(wVar.f2347a.getLayoutManager(), view);
            int i10 = b10[0];
            int i11 = b10[1];
            int g10 = g(Math.max(Math.abs(i10), Math.abs(i11)));
            if (g10 > 0) {
                aVar.b(i10, i11, g10, this.f2463j);
            }
        }

        @Override // androidx.recyclerview.widget.q
        public float f(DisplayMetrics displayMetrics) {
            return 100.0f / ((float) displayMetrics.densityDpi);
        }

        @Override // androidx.recyclerview.widget.q
        public int h(int i10) {
            return Math.min(100, super.h(i10));
        }
    }

    @Override // androidx.recyclerview.widget.c0
    @Nullable
    public int[] b(@NonNull RecyclerView.j jVar, @NonNull View view) {
        int[] iArr = new int[2];
        if (jVar.e()) {
            iArr[0] = g(view, i(jVar));
        } else {
            iArr[0] = 0;
        }
        if (jVar.f()) {
            iArr[1] = g(view, j(jVar));
        } else {
            iArr[1] = 0;
        }
        return iArr;
    }

    @Override // androidx.recyclerview.widget.c0
    public q c(RecyclerView.j jVar) {
        if (!(jVar instanceof RecyclerView.s.b)) {
            return null;
        }
        return new a(this.f2347a.getContext());
    }

    @Override // androidx.recyclerview.widget.c0
    @Nullable
    public View d(RecyclerView.j jVar) {
        throw null;
    }

    @Override // androidx.recyclerview.widget.c0
    public int e(RecyclerView.j jVar, int i10, int i11) {
        v vVar;
        PointF a10;
        int I = jVar.I();
        if (I == 0) {
            return -1;
        }
        View view = null;
        if (jVar.f()) {
            vVar = j(jVar);
        } else {
            vVar = jVar.e() ? i(jVar) : null;
        }
        if (vVar == null) {
            return -1;
        }
        int x8 = jVar.x();
        boolean z10 = false;
        int i12 = Integer.MAX_VALUE;
        int i13 = Integer.MIN_VALUE;
        View view2 = null;
        for (int i14 = 0; i14 < x8; i14++) {
            View w10 = jVar.w(i14);
            if (w10 != null) {
                int g10 = g(w10, vVar);
                if (g10 <= 0 && g10 > i13) {
                    view2 = w10;
                    i13 = g10;
                }
                if (g10 >= 0 && g10 < i12) {
                    view = w10;
                    i12 = g10;
                }
            }
        }
        int i15 = 1;
        boolean z11 = !jVar.e() ? i11 > 0 : i10 > 0;
        if (z11 && view != null) {
            return jVar.Q(view);
        }
        if (!(z11 || view2 == null)) {
            return jVar.Q(view2);
        }
        if (z11) {
            view = view2;
        }
        if (view == null) {
            return -1;
        }
        int Q = jVar.Q(view);
        int I2 = jVar.I();
        if ((jVar instanceof RecyclerView.s.b) && (a10 = ((RecyclerView.s.b) jVar).a(I2 - 1)) != null && (a10.x < Constant.VOLUME_FLOAT_MIN || a10.y < Constant.VOLUME_FLOAT_MIN)) {
            z10 = true;
        }
        if (z10 == z11) {
            i15 = -1;
        }
        int i16 = Q + i15;
        if (i16 < 0 || i16 >= I) {
            return -1;
        }
        return i16;
    }

    public final int g(@NonNull View view, v vVar) {
        return ((vVar.c(view) / 2) + vVar.e(view)) - ((vVar.l() / 2) + vVar.k());
    }

    @Nullable
    public final View h(RecyclerView.j jVar, v vVar) {
        int x8 = jVar.x();
        View view = null;
        if (x8 == 0) {
            return null;
        }
        int l10 = (vVar.l() / 2) + vVar.k();
        int i10 = Reader.READ_DONE;
        for (int i11 = 0; i11 < x8; i11++) {
            View w10 = jVar.w(i11);
            int abs = Math.abs(((vVar.c(w10) / 2) + vVar.e(w10)) - l10);
            if (abs < i10) {
                view = w10;
                i10 = abs;
            }
        }
        return view;
    }

    @NonNull
    public final v i(@NonNull RecyclerView.j jVar) {
        v vVar = this.f2477e;
        if (vVar == null || vVar.f2473a != jVar) {
            this.f2477e = new t(jVar);
        }
        return this.f2477e;
    }

    @NonNull
    public final v j(@NonNull RecyclerView.j jVar) {
        v vVar = this.f2476d;
        if (vVar == null || vVar.f2473a != jVar) {
            this.f2476d = new u(jVar);
        }
        return this.f2476d;
    }
}
