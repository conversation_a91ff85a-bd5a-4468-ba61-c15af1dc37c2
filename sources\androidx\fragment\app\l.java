package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.p;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.d0;
import androidx.lifecycle.g;
import androidx.lifecycle.m;
import com.duokan.airkan.server.f;

/* compiled from: DialogFragment */
public class l extends Fragment implements DialogInterface.OnCancelListener, DialogInterface.OnDismissListener {
    public Handler I0;
    public Runnable J0 = new a();
    public DialogInterface.OnCancelListener K0 = new b();
    public DialogInterface.OnDismissListener L0 = new c();
    public int M0 = 0;
    public int N0 = 0;
    public boolean O0 = true;
    public boolean P0 = true;
    public int Q0 = -1;
    public boolean R0;
    public m<g> S0 = new d();
    @Nullable
    public Dialog T0;
    public boolean U0;
    public boolean V0;
    public boolean W0 = false;

    /* compiled from: DialogFragment */
    public class a implements Runnable {
        public a() {
        }

        @SuppressLint({"SyntheticAccessor"})
        public void run() {
            l lVar = l.this;
            lVar.L0.onDismiss(lVar.T0);
        }
    }

    /* compiled from: DialogFragment */
    public class b implements DialogInterface.OnCancelListener {
        public b() {
        }

        @SuppressLint({"SyntheticAccessor"})
        public void onCancel(@Nullable DialogInterface dialogInterface) {
            l lVar = l.this;
            Dialog dialog = lVar.T0;
            if (dialog != null) {
                lVar.onCancel(dialog);
            }
        }
    }

    /* compiled from: DialogFragment */
    public class c implements DialogInterface.OnDismissListener {
        public c() {
        }

        @SuppressLint({"SyntheticAccessor"})
        public void onDismiss(@Nullable DialogInterface dialogInterface) {
            l lVar = l.this;
            Dialog dialog = lVar.T0;
            if (dialog != null) {
                lVar.onDismiss(dialog);
            }
        }
    }

    /* compiled from: DialogFragment */
    public class d implements m<g> {
        public d() {
        }
    }

    /* compiled from: DialogFragment */
    public class e extends s {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ s f1948a;

        public e(s sVar) {
            this.f1948a = sVar;
        }

        @Override // androidx.fragment.app.s
        @Nullable
        public View b(int i10) {
            Dialog dialog = l.this.T0;
            View findViewById = dialog != null ? dialog.findViewById(i10) : null;
            if (findViewById != null) {
                return findViewById;
            }
            if (this.f1948a.c()) {
                return this.f1948a.b(i10);
            }
            return null;
        }

        @Override // androidx.fragment.app.s
        public boolean c() {
            return l.this.W0 || this.f1948a.c();
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void A() {
        this.f1744s0 = true;
        if (!this.V0) {
            this.V0 = true;
        }
        this.E0.g(this.S0);
    }

    /* JADX INFO: finally extract failed */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x0046 A[Catch:{ all -> 0x006b }] */
    @Override // androidx.fragment.app.Fragment
    @androidx.annotation.NonNull
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.view.LayoutInflater B(@androidx.annotation.Nullable android.os.Bundle r8) {
        /*
        // Method dump skipped, instructions count: 194
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.l.B(android.os.Bundle):android.view.LayoutInflater");
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void D(@NonNull Bundle bundle) {
        Dialog dialog = this.T0;
        if (dialog != null) {
            Bundle onSaveInstanceState = dialog.onSaveInstanceState();
            onSaveInstanceState.putBoolean("android:dialogShowing", false);
            bundle.putBundle("android:savedDialogState", onSaveInstanceState);
        }
        int i10 = this.M0;
        if (i10 != 0) {
            bundle.putInt("android:style", i10);
        }
        int i11 = this.N0;
        if (i11 != 0) {
            bundle.putInt("android:theme", i11);
        }
        boolean z10 = this.O0;
        if (!z10) {
            bundle.putBoolean("android:cancelable", z10);
        }
        boolean z11 = this.P0;
        if (!z11) {
            bundle.putBoolean("android:showsDialog", z11);
        }
        int i12 = this.Q0;
        if (i12 != -1) {
            bundle.putInt("android:backStackId", i12);
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void E() {
        this.f1744s0 = true;
        Dialog dialog = this.T0;
        if (dialog != null) {
            this.U0 = false;
            dialog.show();
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void F() {
        this.f1744s0 = true;
        Dialog dialog = this.T0;
        if (dialog != null) {
            dialog.hide();
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void G(@Nullable Bundle bundle) {
        Bundle bundle2;
        this.f1744s0 = true;
        if (this.T0 != null && bundle != null && (bundle2 = bundle.getBundle("android:savedDialogState")) != null) {
            this.T0.onRestoreInstanceState(bundle2);
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void I(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        Bundle bundle2;
        super.I(layoutInflater, viewGroup, bundle);
        if (this.f1747u0 == null && this.T0 != null && bundle != null && (bundle2 = bundle.getBundle("android:savedDialogState")) != null) {
            this.T0.onRestoreInstanceState(bundle2);
        }
    }

    public final void W(boolean z10, boolean z11) {
        if (!this.V0) {
            this.V0 = true;
            Dialog dialog = this.T0;
            if (dialog != null) {
                dialog.setOnDismissListener(null);
                this.T0.dismiss();
                if (!z11) {
                    if (Looper.myLooper() == this.I0.getLooper()) {
                        onDismiss(this.T0);
                    } else {
                        this.I0.post(this.J0);
                    }
                }
            }
            this.U0 = true;
            if (this.Q0 >= 0) {
                FragmentManager m10 = m();
                int i10 = this.Q0;
                if (i10 >= 0) {
                    m10.A(new FragmentManager.l(null, i10, 1), false);
                    this.Q0 = -1;
                    return;
                }
                throw new IllegalArgumentException(p.a("Bad id: ", i10));
            }
            a aVar = new a(m());
            FragmentManager fragmentManager = this.f1741r;
            if (fragmentManager == null || fragmentManager == aVar.f1837p) {
                aVar.b(new d0.a(3, this));
                if (z10) {
                    aVar.e(true);
                } else {
                    aVar.c();
                }
            } else {
                StringBuilder a10 = f.a("Cannot remove Fragment attached to a different FragmentManager. Fragment ");
                a10.append(toString());
                a10.append(" is already attached to a FragmentManager.");
                throw new IllegalStateException(a10.toString());
            }
        }
    }

    @NonNull
    @MainThread
    public Dialog X(@Nullable Bundle bundle) {
        if (FragmentManager.O(3)) {
            Log.d("FragmentManager", "onCreateDialog called for DialogFragment " + this);
        }
        return new Dialog(N(), this.N0);
    }

    @NonNull
    public final Dialog Y() {
        Dialog dialog = this.T0;
        if (dialog != null) {
            return dialog;
        }
        throw new IllegalStateException("DialogFragment " + this + " does not have a Dialog.");
    }

    @Override // androidx.fragment.app.Fragment
    @NonNull
    public s a() {
        return new e(new Fragment.a());
    }

    public void onCancel(@NonNull DialogInterface dialogInterface) {
    }

    public void onDismiss(@NonNull DialogInterface dialogInterface) {
        if (!this.U0) {
            if (FragmentManager.O(3)) {
                Log.d("FragmentManager", "onDismiss called for DialogFragment " + this);
            }
            W(true, true);
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void w(@NonNull Context context) {
        super.w(context);
        this.E0.d(this.S0);
        this.V0 = false;
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void x(@Nullable Bundle bundle) {
        super.x(bundle);
        this.I0 = new Handler();
        this.P0 = this.f1732m0 == 0;
        if (bundle != null) {
            this.M0 = bundle.getInt("android:style", 0);
            this.N0 = bundle.getInt("android:theme", 0);
            this.O0 = bundle.getBoolean("android:cancelable", true);
            this.P0 = bundle.getBoolean("android:showsDialog", this.P0);
            this.Q0 = bundle.getInt("android:backStackId", -1);
        }
    }

    @Override // androidx.fragment.app.Fragment
    @MainThread
    public void z() {
        this.f1744s0 = true;
        Dialog dialog = this.T0;
        if (dialog != null) {
            this.U0 = true;
            dialog.setOnDismissListener(null);
            this.T0.dismiss();
            if (!this.V0) {
                onDismiss(this.T0);
            }
            this.T0 = null;
            this.W0 = false;
        }
    }
}
