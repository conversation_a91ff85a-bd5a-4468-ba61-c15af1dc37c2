package androidx.appcompat.app;

import android.app.Dialog;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import j0.c;
import java.lang.ref.WeakReference;
import p.a;

/* compiled from: AppCompatDialog */
public class k extends Dialog implements f {

    /* renamed from: a  reason: collision with root package name */
    public AppCompatDelegate f447a;

    /* renamed from: b  reason: collision with root package name */
    public final c f448b;

    /* compiled from: AppCompatDialog */
    public class a implements c {
        public a() {
        }

        @Override // j0.c
        public boolean superDispatchKeyEvent(KeyEvent keyEvent) {
            return k.this.b(keyEvent);
        }
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public k(android.content.Context r5, int r6) {
        /*
            r4 = this;
            r0 = 1
            if (r6 != 0) goto L_0x0014
            android.util.TypedValue r1 = new android.util.TypedValue
            r1.<init>()
            android.content.res.Resources$Theme r2 = r5.getTheme()
            int r3 = androidx.appcompat.R$attr.dialogTheme
            r2.resolveAttribute(r3, r1, r0)
            int r1 = r1.resourceId
            goto L_0x0015
        L_0x0014:
            r1 = r6
        L_0x0015:
            r4.<init>(r5, r1)
            androidx.appcompat.app.k$a r1 = new androidx.appcompat.app.k$a
            r1.<init>()
            r4.f448b = r1
            androidx.appcompat.app.AppCompatDelegate r1 = r4.a()
            if (r6 != 0) goto L_0x0035
            android.util.TypedValue r6 = new android.util.TypedValue
            r6.<init>()
            android.content.res.Resources$Theme r5 = r5.getTheme()
            int r2 = androidx.appcompat.R$attr.dialogTheme
            r5.resolveAttribute(r2, r6, r0)
            int r6 = r6.resourceId
        L_0x0035:
            r1.z(r6)
            r5 = 0
            r1.m(r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.k.<init>(android.content.Context, int):void");
    }

    public AppCompatDelegate a() {
        if (this.f447a == null) {
            u.c<WeakReference<AppCompatDelegate>> cVar = AppCompatDelegate.f367a;
            this.f447a = new AppCompatDelegateImpl(getContext(), getWindow(), this, this);
        }
        return this.f447a;
    }

    public void addContentView(View view, ViewGroup.LayoutParams layoutParams) {
        a().c(view, layoutParams);
    }

    public boolean b(KeyEvent keyEvent) {
        return super.dispatchKeyEvent(keyEvent);
    }

    public void dismiss() {
        super.dismiss();
        a().n();
    }

    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        getWindow().getDecorView();
        c cVar = this.f448b;
        if (cVar == null) {
            return false;
        }
        return cVar.superDispatchKeyEvent(keyEvent);
    }

    @Override // android.app.Dialog
    @Nullable
    public <T extends View> T findViewById(@IdRes int i10) {
        return (T) a().e(i10);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void invalidateOptionsMenu() {
        a().k();
    }

    public void onCreate(Bundle bundle) {
        a().j();
        super.onCreate(bundle);
        a().m(bundle);
    }

    public void onStop() {
        super.onStop();
        a().s();
    }

    @Override // androidx.appcompat.app.f
    public void onSupportActionModeFinished(p.a aVar) {
    }

    @Override // androidx.appcompat.app.f
    public void onSupportActionModeStarted(p.a aVar) {
    }

    @Override // androidx.appcompat.app.f
    @Nullable
    public p.a onWindowStartingSupportActionMode(a.AbstractC0146a aVar) {
        return null;
    }

    @Override // android.app.Dialog
    public void setContentView(@LayoutRes int i10) {
        a().v(i10);
    }

    @Override // android.app.Dialog
    public void setTitle(CharSequence charSequence) {
        super.setTitle(charSequence);
        a().A(charSequence);
    }

    @Override // android.app.Dialog
    public void setContentView(View view) {
        a().w(view);
    }

    public void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        a().x(view, layoutParams);
    }

    @Override // android.app.Dialog
    public void setTitle(int i10) {
        super.setTitle(i10);
        a().A(getContext().getString(i10));
    }
}
