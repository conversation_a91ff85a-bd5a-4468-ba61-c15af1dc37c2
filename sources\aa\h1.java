package aa;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import mb.a;

/* compiled from: DERUniversalString */
public class h1 extends q implements w {

    /* renamed from: b  reason: collision with root package name */
    public static final char[] f184b = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f185a;

    public h1(byte[] bArr) {
        this.f185a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof h1)) {
            return false;
        }
        return a.a(this.f185a, ((h1) qVar).f185a);
    }

    @Override // aa.w
    public String getString() {
        StringBuffer stringBuffer = new StringBuffer("#");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            new p(byteArrayOutputStream).h(this);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            for (int i10 = 0; i10 != byteArray.length; i10++) {
                char[] cArr = f184b;
                stringBuffer.append(cArr[(byteArray[i10] >>> 4) & 15]);
                stringBuffer.append(cArr[byteArray[i10] & 15]);
            }
            return stringBuffer.toString();
        } catch (IOException unused) {
            throw new RuntimeException("internal error encoding BitString");
        }
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(28, this.f185a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f185a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f185a.length) + 1 + this.f185a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
