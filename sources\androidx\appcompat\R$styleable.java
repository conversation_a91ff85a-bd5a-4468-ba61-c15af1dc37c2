package androidx.appcompat;

public final class R$styleable {
    public static final int[] ActionBar = {16842754, 16842964, 16843049, 16843093, 16843233, 16843454, 16843471, 16843472, 16843473, 16843474, 16843512, 16843513, 16843545, 16843549, 16843565, 16843658, 16843659, 2130968589, 2130968620, 2130968693, 2130968702, 2130968703, 2130968884, 2130968885, 2130968886, 2130968887, 2130968888, 2130968889, 2130968916, 2130968917, 2130968945, 2130968946, 2130968974, 2130968997, 2130968998, 2130969000, 2130969054, 2130969060, 2130969066, 2130969067, 2130969071, 2130969090, 2130969105, 2130969220, 2130969296, 2130969325, 2130969343, 2130969344, 2130969359, 2130969455, 2130969458, 2130969472, 2130969555, 2130969557, 2130969567, 2130969587};
    public static final int[] ActionBarLayout = {16842931};
    public static final int ActionBarLayout_android_layout_gravity = 0;
    public static final int ActionBar_actionBarEmbededTabsBackground = 17;
    public static final int ActionBar_actionBarStackedBackground = 18;
    public static final int ActionBar_android_background = 1;
    public static final int ActionBar_android_backgroundSplit = 16;
    public static final int ActionBar_android_backgroundStacked = 15;
    public static final int ActionBar_android_customNavigationLayout = 9;
    public static final int ActionBar_android_displayOptions = 7;
    public static final int ActionBar_android_divider = 2;
    public static final int ActionBar_android_height = 3;
    public static final int ActionBar_android_homeLayout = 13;
    public static final int ActionBar_android_icon = 0;
    public static final int ActionBar_android_itemPadding = 14;
    public static final int ActionBar_android_logo = 5;
    public static final int ActionBar_android_navigationMode = 6;
    public static final int ActionBar_android_progressBarPadding = 12;
    public static final int ActionBar_android_subtitle = 8;
    public static final int ActionBar_android_subtitleTextStyle = 11;
    public static final int ActionBar_android_title = 4;
    public static final int ActionBar_android_titleTextStyle = 10;
    public static final int ActionBar_background = 19;
    public static final int ActionBar_backgroundSplit = 20;
    public static final int ActionBar_backgroundStacked = 21;
    public static final int ActionBar_contentInsetEnd = 22;
    public static final int ActionBar_contentInsetEndWithActions = 23;
    public static final int ActionBar_contentInsetLeft = 24;
    public static final int ActionBar_contentInsetRight = 25;
    public static final int ActionBar_contentInsetStart = 26;
    public static final int ActionBar_contentInsetStartWithNavigation = 27;
    public static final int ActionBar_customNavigationLayout = 28;
    public static final int ActionBar_customViewAutoFitSystemWindow = 29;
    public static final int ActionBar_displayOptions = 30;
    public static final int ActionBar_divider = 31;
    public static final int ActionBar_elevation = 32;
    public static final int ActionBar_expandState = 33;
    public static final int ActionBar_expandSubtitleTextStyle = 34;
    public static final int ActionBar_expandTitleTextStyle = 35;
    public static final int ActionBar_height = 36;
    public static final int ActionBar_hideOnContentScroll = 37;
    public static final int ActionBar_homeAsUpIndicator = 38;
    public static final int ActionBar_homeLayout = 39;
    public static final int ActionBar_icon = 40;
    public static final int ActionBar_indeterminateProgressStyle = 41;
    public static final int ActionBar_itemPadding = 42;
    public static final int ActionBar_logo = 43;
    public static final int ActionBar_navigationMode = 44;
    public static final int ActionBar_popupTheme = 45;
    public static final int ActionBar_progressBarPadding = 46;
    public static final int ActionBar_progressBarStyle = 47;
    public static final int ActionBar_resizable = 48;
    public static final int ActionBar_subtitle = 49;
    public static final int ActionBar_subtitleTextStyle = 50;
    public static final int ActionBar_tabIndicator = 51;
    public static final int ActionBar_title = 52;
    public static final int ActionBar_titleCenter = 53;
    public static final int ActionBar_titleTextStyle = 54;
    public static final int ActionBar_translucentTabIndicator = 55;
    public static final int[] ActionMenuItemView = {16843071};
    public static final int ActionMenuItemView_android_minWidth = 0;
    public static final int[] ActionMenuView = new int[0];
    public static final int[] ActionMode = {16842964, 16843093, 16843512, 16843513, 16843659, 2130968646, 2130968693, 2130968702, 2130968845, 2130969000, 2130969054, 2130969458, 2130969567};
    public static final int ActionMode_actionModeAnim = 5;
    public static final int ActionMode_android_background = 0;
    public static final int ActionMode_android_backgroundSplit = 4;
    public static final int ActionMode_android_height = 1;
    public static final int ActionMode_android_subtitleTextStyle = 3;
    public static final int ActionMode_android_titleTextStyle = 2;
    public static final int ActionMode_background = 6;
    public static final int ActionMode_backgroundSplit = 7;
    public static final int ActionMode_closeItemLayout = 8;
    public static final int ActionMode_expandTitleTextStyle = 9;
    public static final int ActionMode_height = 10;
    public static final int ActionMode_subtitleTextStyle = 11;
    public static final int ActionMode_titleTextStyle = 12;
    public static final int[] ActivityChooserView = {2130968995, 2130969092};
    public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
    public static final int ActivityChooserView_initialActivityCount = 1;
    public static final int[] AlertDialog = {16842994, 2130968757, 2130968762, 2130969069, 2130969129, 2130969202, 2130969203, 2130969292, 2130969346, 2130969397, 2130969400};
    public static final int AlertDialog_android_layout = 0;
    public static final int AlertDialog_buttonIconDimen = 1;
    public static final int AlertDialog_buttonPanelSideLayout = 2;
    public static final int AlertDialog_horizontalProgressLayout = 3;
    public static final int AlertDialog_layout = 4;
    public static final int AlertDialog_listItemLayout = 5;
    public static final int AlertDialog_listLayout = 6;
    public static final int AlertDialog_multiChoiceItemLayout = 7;
    public static final int AlertDialog_progressLayout = 8;
    public static final int AlertDialog_showTitle = 9;
    public static final int AlertDialog_singleChoiceItemLayout = 10;
    public static final int[] AnimatedStateListDrawableCompat = {16843036, 16843156, 16843157, 16843158, 16843532, 16843533};
    public static final int AnimatedStateListDrawableCompat_android_constantSize = 3;
    public static final int AnimatedStateListDrawableCompat_android_dither = 0;
    public static final int AnimatedStateListDrawableCompat_android_enterFadeDuration = 4;
    public static final int AnimatedStateListDrawableCompat_android_exitFadeDuration = 5;
    public static final int AnimatedStateListDrawableCompat_android_variablePadding = 2;
    public static final int AnimatedStateListDrawableCompat_android_visible = 1;
    public static final int[] AnimatedStateListDrawableItem = {16842960, 16843161};
    public static final int AnimatedStateListDrawableItem_android_drawable = 1;
    public static final int AnimatedStateListDrawableItem_android_id = 0;
    public static final int[] AnimatedStateListDrawableTransition = {16843161, 16843849, 16843850, 16843851};
    public static final int AnimatedStateListDrawableTransition_android_drawable = 0;
    public static final int AnimatedStateListDrawableTransition_android_fromId = 2;
    public static final int AnimatedStateListDrawableTransition_android_reversible = 3;
    public static final int AnimatedStateListDrawableTransition_android_toId = 1;
    public static final int[] AppCompatImageView = {16843033, 2130969424, 2130969553, 2130969554};
    public static final int AppCompatImageView_android_src = 0;
    public static final int AppCompatImageView_srcCompat = 1;
    public static final int AppCompatImageView_tint = 2;
    public static final int AppCompatImageView_tintMode = 3;
    public static final int[] AppCompatSeekBar = {16843074, 2130969550, 2130969551, 2130969552};
    public static final int AppCompatSeekBar_android_thumb = 0;
    public static final int AppCompatSeekBar_tickMark = 1;
    public static final int AppCompatSeekBar_tickMarkTint = 2;
    public static final int AppCompatSeekBar_tickMarkTintMode = 3;
    public static final int[] AppCompatTextHelper = {16842804, 16843117, 16843118, 16843119, 16843120, 16843666, 16843667};
    public static final int AppCompatTextHelper_android_drawableBottom = 2;
    public static final int AppCompatTextHelper_android_drawableEnd = 6;
    public static final int AppCompatTextHelper_android_drawableLeft = 3;
    public static final int AppCompatTextHelper_android_drawableRight = 4;
    public static final int AppCompatTextHelper_android_drawableStart = 5;
    public static final int AppCompatTextHelper_android_drawableTop = 1;
    public static final int AppCompatTextHelper_android_textAppearance = 0;
    public static final int[] AppCompatTextView = {16842804, 2130968688, 2130968689, 2130968690, 2130968691, 2130968692, 2130968952, 2130968953, 2130968954, 2130968955, 2130968957, 2130968958, 2130968959, 2130968960, 2130969030, 2130969033, 2130969041, 2130969128, 2130969195, 2130969494, 2130969534};
    public static final int AppCompatTextView_android_textAppearance = 0;
    public static final int AppCompatTextView_autoSizeMaxTextSize = 1;
    public static final int AppCompatTextView_autoSizeMinTextSize = 2;
    public static final int AppCompatTextView_autoSizePresetSizes = 3;
    public static final int AppCompatTextView_autoSizeStepGranularity = 4;
    public static final int AppCompatTextView_autoSizeTextType = 5;
    public static final int AppCompatTextView_drawableBottomCompat = 6;
    public static final int AppCompatTextView_drawableEndCompat = 7;
    public static final int AppCompatTextView_drawableLeftCompat = 8;
    public static final int AppCompatTextView_drawableRightCompat = 9;
    public static final int AppCompatTextView_drawableStartCompat = 10;
    public static final int AppCompatTextView_drawableTint = 11;
    public static final int AppCompatTextView_drawableTintMode = 12;
    public static final int AppCompatTextView_drawableTopCompat = 13;
    public static final int AppCompatTextView_firstBaselineToTopHeight = 14;
    public static final int AppCompatTextView_fontFamily = 15;
    public static final int AppCompatTextView_fontVariationSettings = 16;
    public static final int AppCompatTextView_lastBaselineToBottomHeight = 17;
    public static final int AppCompatTextView_lineHeight = 18;
    public static final int AppCompatTextView_textAllCaps = 19;
    public static final int AppCompatTextView_textLocale = 20;
    public static final int[] AppCompatTheme = {16842839, 16842926, 2130968585, 2130968595, 2130968603, 2130968615, 2130968618, 2130968623, 2130968626, 2130968629, 2130968633, 2130968634, 2130968640, 2130968641, 2130968642, 2130968644, 2130968645, 2130968647, 2130968650, 2130968651, 2130968653, 2130968654, 2130968655, 2130968657, 2130968658, 2130968659, 2130968661, 2130968662, 2130968663, 2130968665, 2130968666, 2130968667, 2130968673, 2130968674, 2130968675, 2130968676, 2130968677, 2130968687, 2130968727, 2130968748, 2130968750, 2130968751, 2130968752, 2130968754, 2130968765, 2130968766, 2130968794, 2130968801, 2130968853, 2130968854, 2130968855, 2130968856, 2130968857, 2130968858, 2130968860, 2130968867, 2130968868, 2130968874, 2130968897, 2130968932, 2130968938, 2130968941, 2130968947, 2130968949, 2130968962, 2130968965, 2130968966, 2130968967, 2130968973, 2130969066, 2130969081, 2130969198, 2130969199, 2130969200, 2130969201, 2130969206, 2130969208, 2130969209, 2130969210, 2130969211, 2130969212, 2130969213, 2130969214, 2130969215, 2130969311, 2130969312, 2130969313, 2130969324, 2130969329, 2130969353, 2130969355, 2130969356, 2130969357, 2130969379, 2130969381, 2130969382, 2130969383, 2130969416, 2130969419, 2130969465, 2130969505, 2130969507, 2130969508, 2130969509, 2130969511, 2130969512, 2130969513, 2130969514, 2130969518, 2130969528, 2130969569, 2130969570, 2130969571, 2130969572, 2130969593, 2130969595, 2130969597, 2130969598, 2130969603, 2130969604, 2130969605, 2130969606, 2130969613, 2130969614, 2130969615};
    public static final int AppCompatTheme_actionBarDivider = 2;
    public static final int AppCompatTheme_actionBarItemBackground = 3;
    public static final int AppCompatTheme_actionBarPopupTheme = 4;
    public static final int AppCompatTheme_actionBarSize = 5;
    public static final int AppCompatTheme_actionBarSplitStyle = 6;
    public static final int AppCompatTheme_actionBarStyle = 7;
    public static final int AppCompatTheme_actionBarTabBarStyle = 8;
    public static final int AppCompatTheme_actionBarTabStyle = 9;
    public static final int AppCompatTheme_actionBarTabTextStyle = 10;
    public static final int AppCompatTheme_actionBarTheme = 11;
    public static final int AppCompatTheme_actionBarWidgetTheme = 12;
    public static final int AppCompatTheme_actionButtonStyle = 13;
    public static final int AppCompatTheme_actionDropDownStyle = 14;
    public static final int AppCompatTheme_actionMenuTextAppearance = 15;
    public static final int AppCompatTheme_actionMenuTextColor = 16;
    public static final int AppCompatTheme_actionModeBackground = 17;
    public static final int AppCompatTheme_actionModeCloseButtonStyle = 18;
    public static final int AppCompatTheme_actionModeCloseDrawable = 19;
    public static final int AppCompatTheme_actionModeCopyDrawable = 20;
    public static final int AppCompatTheme_actionModeCutDrawable = 21;
    public static final int AppCompatTheme_actionModeFindDrawable = 22;
    public static final int AppCompatTheme_actionModePasteDrawable = 23;
    public static final int AppCompatTheme_actionModePopupWindowStyle = 24;
    public static final int AppCompatTheme_actionModeSelectAllDrawable = 25;
    public static final int AppCompatTheme_actionModeShareDrawable = 26;
    public static final int AppCompatTheme_actionModeSplitBackground = 27;
    public static final int AppCompatTheme_actionModeStyle = 28;
    public static final int AppCompatTheme_actionModeWebSearchDrawable = 29;
    public static final int AppCompatTheme_actionOverflowButtonStyle = 30;
    public static final int AppCompatTheme_actionOverflowMenuStyle = 31;
    public static final int AppCompatTheme_activityChooserViewStyle = 32;
    public static final int AppCompatTheme_alertDialogButtonGroupStyle = 33;
    public static final int AppCompatTheme_alertDialogCenterButtons = 34;
    public static final int AppCompatTheme_alertDialogStyle = 35;
    public static final int AppCompatTheme_alertDialogTheme = 36;
    public static final int AppCompatTheme_android_windowAnimationStyle = 1;
    public static final int AppCompatTheme_android_windowIsFloating = 0;
    public static final int AppCompatTheme_autoCompleteTextViewStyle = 37;
    public static final int AppCompatTheme_borderlessButtonStyle = 38;
    public static final int AppCompatTheme_buttonBarButtonStyle = 39;
    public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 40;
    public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 41;
    public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 42;
    public static final int AppCompatTheme_buttonBarStyle = 43;
    public static final int AppCompatTheme_buttonStyle = 44;
    public static final int AppCompatTheme_buttonStyleSmall = 45;
    public static final int AppCompatTheme_checkboxStyle = 46;
    public static final int AppCompatTheme_checkedTextViewStyle = 47;
    public static final int AppCompatTheme_colorAccent = 48;
    public static final int AppCompatTheme_colorBackgroundFloating = 49;
    public static final int AppCompatTheme_colorButtonNormal = 50;
    public static final int AppCompatTheme_colorControlActivated = 51;
    public static final int AppCompatTheme_colorControlHighlight = 52;
    public static final int AppCompatTheme_colorControlNormal = 53;
    public static final int AppCompatTheme_colorError = 54;
    public static final int AppCompatTheme_colorPrimary = 55;
    public static final int AppCompatTheme_colorPrimaryDark = 56;
    public static final int AppCompatTheme_colorSwitchThumbNormal = 57;
    public static final int AppCompatTheme_controlBackground = 58;
    public static final int AppCompatTheme_dialogCornerRadius = 59;
    public static final int AppCompatTheme_dialogPreferredPadding = 60;
    public static final int AppCompatTheme_dialogTheme = 61;
    public static final int AppCompatTheme_dividerHorizontal = 62;
    public static final int AppCompatTheme_dividerVertical = 63;
    public static final int AppCompatTheme_dropDownListViewStyle = 64;
    public static final int AppCompatTheme_dropdownListPreferredItemHeight = 65;
    public static final int AppCompatTheme_editTextBackground = 66;
    public static final int AppCompatTheme_editTextColor = 67;
    public static final int AppCompatTheme_editTextStyle = 68;
    public static final int AppCompatTheme_homeAsUpIndicator = 69;
    public static final int AppCompatTheme_imageButtonStyle = 70;
    public static final int AppCompatTheme_listChoiceBackgroundIndicator = 71;
    public static final int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 72;
    public static final int AppCompatTheme_listChoiceIndicatorSingleAnimated = 73;
    public static final int AppCompatTheme_listDividerAlertDialog = 74;
    public static final int AppCompatTheme_listMenuViewStyle = 75;
    public static final int AppCompatTheme_listPopupWindowStyle = 76;
    public static final int AppCompatTheme_listPreferredItemHeight = 77;
    public static final int AppCompatTheme_listPreferredItemHeightLarge = 78;
    public static final int AppCompatTheme_listPreferredItemHeightSmall = 79;
    public static final int AppCompatTheme_listPreferredItemPaddingEnd = 80;
    public static final int AppCompatTheme_listPreferredItemPaddingLeft = 81;
    public static final int AppCompatTheme_listPreferredItemPaddingRight = 82;
    public static final int AppCompatTheme_listPreferredItemPaddingStart = 83;
    public static final int AppCompatTheme_panelBackground = 84;
    public static final int AppCompatTheme_panelMenuListTheme = 85;
    public static final int AppCompatTheme_panelMenuListWidth = 86;
    public static final int AppCompatTheme_popupMenuStyle = 87;
    public static final int AppCompatTheme_popupWindowStyle = 88;
    public static final int AppCompatTheme_radioButtonStyle = 89;
    public static final int AppCompatTheme_ratingBarStyle = 90;
    public static final int AppCompatTheme_ratingBarStyleIndicator = 91;
    public static final int AppCompatTheme_ratingBarStyleSmall = 92;
    public static final int AppCompatTheme_searchViewStyle = 93;
    public static final int AppCompatTheme_seekBarStyle = 94;
    public static final int AppCompatTheme_selectableItemBackground = 95;
    public static final int AppCompatTheme_selectableItemBackgroundBorderless = 96;
    public static final int AppCompatTheme_spinnerDropDownItemStyle = 97;
    public static final int AppCompatTheme_spinnerStyle = 98;
    public static final int AppCompatTheme_switchStyle = 99;
    public static final int AppCompatTheme_textAppearanceLargePopupMenu = 100;
    public static final int AppCompatTheme_textAppearanceListItem = 101;
    public static final int AppCompatTheme_textAppearanceListItemSecondary = 102;
    public static final int AppCompatTheme_textAppearanceListItemSmall = 103;
    public static final int AppCompatTheme_textAppearancePopupMenuHeader = 104;
    public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 105;
    public static final int AppCompatTheme_textAppearanceSearchResultTitle = 106;
    public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 107;
    public static final int AppCompatTheme_textColorAlertDialogListItem = 108;
    public static final int AppCompatTheme_textColorSearchUrl = 109;
    public static final int AppCompatTheme_toolbarNavigationButtonStyle = 110;
    public static final int AppCompatTheme_toolbarStyle = 111;
    public static final int AppCompatTheme_tooltipForegroundColor = 112;
    public static final int AppCompatTheme_tooltipFrameBackground = 113;
    public static final int AppCompatTheme_viewInflaterClass = 114;
    public static final int AppCompatTheme_windowActionBar = 115;
    public static final int AppCompatTheme_windowActionBarOverlay = 116;
    public static final int AppCompatTheme_windowActionModeOverlay = 117;
    public static final int AppCompatTheme_windowFixedHeightMajor = 118;
    public static final int AppCompatTheme_windowFixedHeightMinor = 119;
    public static final int AppCompatTheme_windowFixedWidthMajor = 120;
    public static final int AppCompatTheme_windowFixedWidthMinor = 121;
    public static final int AppCompatTheme_windowMinWidthMajor = 122;
    public static final int AppCompatTheme_windowMinWidthMinor = 123;
    public static final int AppCompatTheme_windowNoTitle = 124;
    public static final int[] ButtonBarLayout = {2130968678};
    public static final int ButtonBarLayout_allowStacking = 0;
    public static final int[] ColorStateListItem = {16843173, 16843551, 2130968679};
    public static final int ColorStateListItem_alpha = 2;
    public static final int ColorStateListItem_android_alpha = 1;
    public static final int ColorStateListItem_android_color = 0;
    public static final int[] CompoundButton = {16843015, 2130968755, 2130968767, 2130968768};
    public static final int CompoundButton_android_button = 0;
    public static final int CompoundButton_buttonCompat = 1;
    public static final int CompoundButton_buttonTint = 2;
    public static final int CompoundButton_buttonTintMode = 3;
    public static final int[] DrawerArrowToggle = {2130968684, 2130968686, 2130968710, 2130968852, 2130968956, 2130969047, 2130969413, 2130969540};
    public static final int DrawerArrowToggle_arrowHeadLength = 0;
    public static final int DrawerArrowToggle_arrowShaftLength = 1;
    public static final int DrawerArrowToggle_barLength = 2;
    public static final int DrawerArrowToggle_color = 3;
    public static final int DrawerArrowToggle_drawableSize = 4;
    public static final int DrawerArrowToggle_gapBetweenBars = 5;
    public static final int DrawerArrowToggle_spinBars = 6;
    public static final int DrawerArrowToggle_thickness = 7;
    public static final int[] FontFamily = {2130969034, 2130969035, 2130969036, 2130969037, 2130969038, 2130969039};
    public static final int[] FontFamilyFont = {16844082, 16844083, 16844095, 16844143, 16844144, 2130969032, 2130969040, 2130969041, 2130969042, 2130969588};
    public static final int FontFamilyFont_android_font = 0;
    public static final int FontFamilyFont_android_fontStyle = 2;
    public static final int FontFamilyFont_android_fontVariationSettings = 4;
    public static final int FontFamilyFont_android_fontWeight = 1;
    public static final int FontFamilyFont_android_ttcIndex = 3;
    public static final int FontFamilyFont_font = 5;
    public static final int FontFamilyFont_fontStyle = 6;
    public static final int FontFamilyFont_fontVariationSettings = 7;
    public static final int FontFamilyFont_fontWeight = 8;
    public static final int FontFamilyFont_ttcIndex = 9;
    public static final int FontFamily_fontProviderAuthority = 0;
    public static final int FontFamily_fontProviderCerts = 1;
    public static final int FontFamily_fontProviderFetchStrategy = 2;
    public static final int FontFamily_fontProviderFetchTimeout = 3;
    public static final int FontFamily_fontProviderPackage = 4;
    public static final int FontFamily_fontProviderQuery = 5;
    public static final int[] GradientColor = {16843165, 16843166, 16843169, 16843170, 16843171, 16843172, 16843265, 16843275, 16844048, 16844049, 16844050, 16844051};
    public static final int[] GradientColorItem = {16843173, 16844052};
    public static final int GradientColorItem_android_color = 0;
    public static final int GradientColorItem_android_offset = 1;
    public static final int GradientColor_android_centerColor = 7;
    public static final int GradientColor_android_centerX = 3;
    public static final int GradientColor_android_centerY = 4;
    public static final int GradientColor_android_endColor = 1;
    public static final int GradientColor_android_endX = 10;
    public static final int GradientColor_android_endY = 11;
    public static final int GradientColor_android_gradientRadius = 5;
    public static final int GradientColor_android_startColor = 0;
    public static final int GradientColor_android_startX = 8;
    public static final int GradientColor_android_startY = 9;
    public static final int GradientColor_android_tileMode = 6;
    public static final int GradientColor_android_type = 2;
    public static final int[] LinearLayoutCompat = {16842927, 16842948, 16843046, 16843047, 16843048, 2130968946, 2130968948, 2130969251, 2130969393};
    public static final int[] LinearLayoutCompat_Layout = {16842931, 16842996, 16842997, 16843137};
    public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
    public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
    public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
    public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
    public static final int LinearLayoutCompat_android_baselineAligned = 2;
    public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
    public static final int LinearLayoutCompat_android_gravity = 0;
    public static final int LinearLayoutCompat_android_orientation = 1;
    public static final int LinearLayoutCompat_android_weightSum = 4;
    public static final int LinearLayoutCompat_divider = 5;
    public static final int LinearLayoutCompat_dividerPadding = 6;
    public static final int LinearLayoutCompat_measureWithLargestChild = 7;
    public static final int LinearLayoutCompat_showDividers = 8;
    public static final int[] ListPopupWindow = {16843436, 16843437};
    public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
    public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
    public static final int[] MenuGroup = {16842766, 16842960, 16843156, 16843230, 16843231, 16843232};
    public static final int MenuGroup_android_checkableBehavior = 5;
    public static final int MenuGroup_android_enabled = 0;
    public static final int MenuGroup_android_id = 1;
    public static final int MenuGroup_android_menuCategory = 3;
    public static final int MenuGroup_android_orderInCategory = 4;
    public static final int MenuGroup_android_visible = 2;
    public static final int[] MenuItem = {16842754, 16842766, 16842960, 16843014, 16843156, 16843230, 16843231, 16843233, 16843234, 16843235, 16843236, 16843237, 16843375, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********};
    public static final int MenuItem_actionLayout = 13;
    public static final int MenuItem_actionProviderClass = 14;
    public static final int MenuItem_actionViewClass = 15;
    public static final int MenuItem_alphabeticModifiers = 16;
    public static final int MenuItem_android_alphabeticShortcut = 9;
    public static final int MenuItem_android_checkable = 11;
    public static final int MenuItem_android_checked = 3;
    public static final int MenuItem_android_enabled = 1;
    public static final int MenuItem_android_icon = 0;
    public static final int MenuItem_android_id = 2;
    public static final int MenuItem_android_menuCategory = 5;
    public static final int MenuItem_android_numericShortcut = 10;
    public static final int MenuItem_android_onClick = 12;
    public static final int MenuItem_android_orderInCategory = 6;
    public static final int MenuItem_android_title = 7;
    public static final int MenuItem_android_titleCondensed = 8;
    public static final int MenuItem_android_visible = 4;
    public static final int MenuItem_contentDescription = 17;
    public static final int MenuItem_iconTint = 18;
    public static final int MenuItem_iconTintMode = 19;
    public static final int MenuItem_numericModifiers = 20;
    public static final int MenuItem_showAsAction = 21;
    public static final int MenuItem_tooltipText = 22;
    public static final int[] MenuView = {16842926, 16843052, 16843053, 16843054, 16843055, 16843056, 16843057, 2130969334, 2130969453};
    public static final int MenuView_android_headerBackground = 4;
    public static final int MenuView_android_horizontalDivider = 2;
    public static final int MenuView_android_itemBackground = 5;
    public static final int MenuView_android_itemIconDisabledAlpha = 6;
    public static final int MenuView_android_itemTextAppearance = 1;
    public static final int MenuView_android_verticalDivider = 3;
    public static final int MenuView_android_windowAnimationStyle = 0;
    public static final int MenuView_preserveIconSpacing = 7;
    public static final int MenuView_subMenuArrow = 8;
    public static final int[] PopupWindow = {16843126, 16843465, 2130969302};
    public static final int[] PopupWindowBackgroundState = {2130969434};
    public static final int PopupWindowBackgroundState_state_above_anchor = 0;
    public static final int PopupWindow_android_popupAnimationStyle = 1;
    public static final int PopupWindow_android_popupBackground = 0;
    public static final int PopupWindow_overlapAnchor = 2;
    public static final int[] RecycleListView = {2130969303, 2130969309};
    public static final int RecycleListView_paddingBottomNoButtons = 0;
    public static final int RecycleListView_paddingTopNoTitle = 1;
    public static final int[] SearchView = {16842970, 16843039, 16843296, 16843364, 2130968838, 2130968875, 2130968924, 2130969049, 2130969080, 2130969129, 2130969347, 2130969348, 2130969377, 2130969378, 2130969454, 2130969462, 2130969594};
    public static final int SearchView_android_focusable = 0;
    public static final int SearchView_android_imeOptions = 3;
    public static final int SearchView_android_inputType = 2;
    public static final int SearchView_android_maxWidth = 1;
    public static final int SearchView_closeIcon = 4;
    public static final int SearchView_commitIcon = 5;
    public static final int SearchView_defaultQueryHint = 6;
    public static final int SearchView_goIcon = 7;
    public static final int SearchView_iconifiedByDefault = 8;
    public static final int SearchView_layout = 9;
    public static final int SearchView_queryBackground = 10;
    public static final int SearchView_queryHint = 11;
    public static final int SearchView_searchHintIcon = 12;
    public static final int SearchView_searchIcon = 13;
    public static final int SearchView_submitBackground = 14;
    public static final int SearchView_suggestionRowLayout = 15;
    public static final int SearchView_voiceIcon = 16;
    public static final int[] Spinner = {16842930, 16843126, 16843131, 16843362, 2130968963, 2130968964, 2130969325, 2130969418};
    public static final int Spinner_android_dropDownWidth = 3;
    public static final int Spinner_android_entries = 0;
    public static final int Spinner_android_popupBackground = 1;
    public static final int Spinner_android_prompt = 2;
    public static final int Spinner_dropDownMaxWidth = 4;
    public static final int Spinner_dropDownMinWidth = 5;
    public static final int Spinner_popupTheme = 6;
    public static final int Spinner_spinnerModeCompat = 7;
    public static final int[] StateListDrawable = {16843036, 16843156, 16843157, 16843158, 16843532, 16843533};
    public static final int[] StateListDrawableItem = {16843161};
    public static final int StateListDrawableItem_android_drawable = 0;
    public static final int StateListDrawable_android_constantSize = 3;
    public static final int StateListDrawable_android_dither = 0;
    public static final int StateListDrawable_android_enterFadeDuration = 4;
    public static final int StateListDrawable_android_exitFadeDuration = 5;
    public static final int StateListDrawable_android_variablePadding = 2;
    public static final int StateListDrawable_android_visible = 1;
    public static final int[] SwitchCompat = {16843044, 16843045, 16843074, 2130969396, 2130969422, 2130969463, 2130969464, 2130969466, 2130969544, 2130969545, 2130969546, 2130969579, 2130969584, 2130969585};
    public static final int SwitchCompat_android_textOff = 1;
    public static final int SwitchCompat_android_textOn = 0;
    public static final int SwitchCompat_android_thumb = 2;
    public static final int SwitchCompat_showText = 3;
    public static final int SwitchCompat_splitTrack = 4;
    public static final int SwitchCompat_switchMinWidth = 5;
    public static final int SwitchCompat_switchPadding = 6;
    public static final int SwitchCompat_switchTextAppearance = 7;
    public static final int SwitchCompat_thumbTextPadding = 8;
    public static final int SwitchCompat_thumbTint = 9;
    public static final int SwitchCompat_thumbTintMode = 10;
    public static final int SwitchCompat_track = 11;
    public static final int SwitchCompat_trackTint = 12;
    public static final int SwitchCompat_trackTintMode = 13;
    public static final int[] TextAppearance = {16842901, 16842902, 16842903, 16842904, 16842906, 16842907, 16843105, 16843106, 16843107, 16843108, 16843692, 16844165, 2130969033, 2130969041, 2130969494, 2130969534};
    public static final int TextAppearance_android_fontFamily = 10;
    public static final int TextAppearance_android_shadowColor = 6;
    public static final int TextAppearance_android_shadowDx = 7;
    public static final int TextAppearance_android_shadowDy = 8;
    public static final int TextAppearance_android_shadowRadius = 9;
    public static final int TextAppearance_android_textColor = 3;
    public static final int TextAppearance_android_textColorHint = 4;
    public static final int TextAppearance_android_textColorLink = 5;
    public static final int TextAppearance_android_textFontWeight = 11;
    public static final int TextAppearance_android_textSize = 0;
    public static final int TextAppearance_android_textStyle = 2;
    public static final int TextAppearance_android_typeface = 1;
    public static final int TextAppearance_fontFamily = 12;
    public static final int TextAppearance_fontVariationSettings = 13;
    public static final int TextAppearance_textAllCaps = 14;
    public static final int TextAppearance_textLocale = 15;
    public static final int[] Toolbar = {16842927, 16843072, 2130968756, 2130968846, 2130968847, 2130968884, 2130968885, 2130968886, 2130968887, 2130968888, 2130968889, 2130969220, 2130969221, 2130969244, 2130969252, 2130969294, 2130969295, 2130969325, 2130969455, 2130969456, 2130969457, 2130969555, 2130969559, 2130969560, 2130969561, 2130969562, 2130969563, 2130969564, 2130969565, 2130969566};
    public static final int Toolbar_android_gravity = 0;
    public static final int Toolbar_android_minHeight = 1;
    public static final int Toolbar_buttonGravity = 2;
    public static final int Toolbar_collapseContentDescription = 3;
    public static final int Toolbar_collapseIcon = 4;
    public static final int Toolbar_contentInsetEnd = 5;
    public static final int Toolbar_contentInsetEndWithActions = 6;
    public static final int Toolbar_contentInsetLeft = 7;
    public static final int Toolbar_contentInsetRight = 8;
    public static final int Toolbar_contentInsetStart = 9;
    public static final int Toolbar_contentInsetStartWithNavigation = 10;
    public static final int Toolbar_logo = 11;
    public static final int Toolbar_logoDescription = 12;
    public static final int Toolbar_maxButtonHeight = 13;
    public static final int Toolbar_menu = 14;
    public static final int Toolbar_navigationContentDescription = 15;
    public static final int Toolbar_navigationIcon = 16;
    public static final int Toolbar_popupTheme = 17;
    public static final int Toolbar_subtitle = 18;
    public static final int Toolbar_subtitleTextAppearance = 19;
    public static final int Toolbar_subtitleTextColor = 20;
    public static final int Toolbar_title = 21;
    public static final int Toolbar_titleMargin = 22;
    public static final int Toolbar_titleMarginBottom = 23;
    public static final int Toolbar_titleMarginEnd = 24;
    public static final int Toolbar_titleMarginStart = 25;
    public static final int Toolbar_titleMarginTop = 26;
    public static final int Toolbar_titleMargins = 27;
    public static final int Toolbar_titleTextAppearance = 28;
    public static final int Toolbar_titleTextColor = 29;
    public static final int[] View = {16842752, 16842970, 2130969305, 2130969308, 2130969538};
    public static final int[] ViewBackgroundHelper = {16842964, 2130968704, 2130968705};
    public static final int ViewBackgroundHelper_android_background = 0;
    public static final int ViewBackgroundHelper_backgroundTint = 1;
    public static final int ViewBackgroundHelper_backgroundTintMode = 2;
    public static final int[] ViewStubCompat = {16842960, 16842994, 16842995};
    public static final int ViewStubCompat_android_id = 0;
    public static final int ViewStubCompat_android_inflatedId = 2;
    public static final int ViewStubCompat_android_layout = 1;
    public static final int View_android_focusable = 1;
    public static final int View_android_theme = 0;
    public static final int View_paddingEnd = 2;
    public static final int View_paddingStart = 3;
    public static final int View_theme = 4;

    private R$styleable() {
    }
}
