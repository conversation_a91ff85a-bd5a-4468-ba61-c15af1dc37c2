package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.app.AppCompatDelegateImpl;
import androidx.appcompat.app.h;
import androidx.appcompat.view.menu.d;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY})
public class ContentFrameLayout extends FrameLayout {

    /* renamed from: a  reason: collision with root package name */
    public TypedValue f856a;

    /* renamed from: b  reason: collision with root package name */
    public TypedValue f857b;

    /* renamed from: c  reason: collision with root package name */
    public TypedValue f858c;

    /* renamed from: d  reason: collision with root package name */
    public TypedValue f859d;

    /* renamed from: e  reason: collision with root package name */
    public TypedValue f860e;

    /* renamed from: f  reason: collision with root package name */
    public TypedValue f861f;

    /* renamed from: g  reason: collision with root package name */
    public final Rect f862g;
    public a h;

    public interface a {
    }

    public ContentFrameLayout(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public TypedValue getFixedHeightMajor() {
        if (this.f860e == null) {
            this.f860e = new TypedValue();
        }
        return this.f860e;
    }

    public TypedValue getFixedHeightMinor() {
        if (this.f861f == null) {
            this.f861f = new TypedValue();
        }
        return this.f861f;
    }

    public TypedValue getFixedWidthMajor() {
        if (this.f858c == null) {
            this.f858c = new TypedValue();
        }
        return this.f858c;
    }

    public TypedValue getFixedWidthMinor() {
        if (this.f859d == null) {
            this.f859d = new TypedValue();
        }
        return this.f859d;
    }

    public TypedValue getMinWidthMajor() {
        if (this.f856a == null) {
            this.f856a = new TypedValue();
        }
        return this.f856a;
    }

    public TypedValue getMinWidthMinor() {
        if (this.f857b == null) {
            this.f857b = new TypedValue();
        }
        return this.f857b;
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        a aVar = this.h;
        if (aVar != null) {
            Objects.requireNonNull(aVar);
        }
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        a aVar = this.h;
        if (aVar != null) {
            AppCompatDelegateImpl appCompatDelegateImpl = ((h) aVar).f443a;
            s sVar = appCompatDelegateImpl.f376k;
            if (sVar != null) {
                sVar.l();
            }
            if (appCompatDelegateImpl.f384p != null) {
                appCompatDelegateImpl.f371e.getDecorView().removeCallbacks(appCompatDelegateImpl.f386q);
                if (appCompatDelegateImpl.f384p.isShowing()) {
                    try {
                        appCompatDelegateImpl.f384p.dismiss();
                    } catch (IllegalArgumentException unused) {
                    }
                }
                appCompatDelegateImpl.f384p = null;
            }
            appCompatDelegateImpl.L();
            d dVar = appCompatDelegateImpl.Q(0).h;
            if (dVar != null) {
                dVar.c(true);
            }
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:19:0x004a  */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x0063  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x0086  */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x00ab  */
    /* JADX WARNING: Removed duplicated region for block: B:41:0x00ae  */
    /* JADX WARNING: Removed duplicated region for block: B:46:0x00b8  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x00be  */
    /* JADX WARNING: Removed duplicated region for block: B:52:0x00cc  */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00d6  */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x00de  */
    /* JADX WARNING: Removed duplicated region for block: B:59:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r14, int r15) {
        /*
        // Method dump skipped, instructions count: 226
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ContentFrameLayout.onMeasure(int, int):void");
    }

    public void setAttachListener(a aVar) {
        this.h = aVar;
    }

    public ContentFrameLayout(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f862g = new Rect();
    }
}
