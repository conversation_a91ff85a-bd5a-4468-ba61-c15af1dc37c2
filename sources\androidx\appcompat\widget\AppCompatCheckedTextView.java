package androidx.appcompat.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.CheckedTextView;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import m.a;

public class AppCompatCheckedTextView extends CheckedTextView {

    /* renamed from: b  reason: collision with root package name */
    public static final int[] f815b = {16843016};

    /* renamed from: a  reason: collision with root package name */
    public final q f816a;

    public AppCompatCheckedTextView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 16843720);
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        q qVar = this.f816a;
        if (qVar != null) {
            qVar.b();
        }
    }

    public InputConnection onCreateInputConnection(EditorInfo editorInfo) {
        InputConnection onCreateInputConnection = super.onCreateInputConnection(editorInfo);
        j.i(onCreateInputConnection, editorInfo, this);
        return onCreateInputConnection;
    }

    @Override // android.widget.CheckedTextView
    public void setCheckMarkDrawable(@DrawableRes int i10) {
        setCheckMarkDrawable(a.a(getContext(), i10));
    }

    public void setCustomSelectionActionModeCallback(ActionMode.Callback callback) {
        super.setCustomSelectionActionModeCallback(callback);
    }

    public void setTextAppearance(Context context, int i10) {
        super.setTextAppearance(context, i10);
        q qVar = this.f816a;
        if (qVar != null) {
            qVar.e(context, i10);
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public AppCompatCheckedTextView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        j0.a(context);
        h0.a(this, getContext());
        q qVar = new q(this);
        this.f816a = qVar;
        qVar.d(attributeSet, i10);
        qVar.b();
        m0 r10 = m0.r(getContext(), attributeSet, f815b, i10, 0);
        setCheckMarkDrawable(r10.g(0));
        r10.f1145b.recycle();
    }
}
