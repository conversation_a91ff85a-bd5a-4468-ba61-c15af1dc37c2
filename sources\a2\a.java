package a2;

import android.util.Base64;
import com.duokan.airkan.server.f;
import db.e2;
import java.security.KeyPair;
import l2.b0;
import org.json.JSONException;
import org.json.JSONObject;
import y1.e;

/* compiled from: AirkanKeyPair */
public final class a {

    /* renamed from: a  reason: collision with root package name */
    public String f9a;

    /* renamed from: b  reason: collision with root package name */
    public String f10b;

    /* renamed from: c  reason: collision with root package name */
    public String f11c;

    public a() {
    }

    public static a a(JSONObject jSONObject) {
        a aVar = new a();
        try {
            aVar.f9a = jSONObject.getString("privateKey");
            jSONObject.getString("privateKeyMd5");
            aVar.f10b = jSONObject.getString("publicKey");
            aVar.f11c = jSONObject.getString("publicKeyMd5");
            return aVar;
        } catch (JSONException e10) {
            StringBuilder a10 = f.a("Error: ");
            a10.append(e10.getMessage());
            e.b(a10.toString());
            throw new RuntimeException(e10);
        }
    }

    public a(KeyPair keyPair, int i10) {
        if (keyPair != null) {
            if (i10 == 1) {
                this.f9a = e2.h(keyPair.getPrivate().getEncoded());
                this.f10b = e2.h(keyPair.getPublic().getEncoded());
            } else if (i10 == 2) {
                this.f9a = new String(Base64.encode(keyPair.getPrivate().getEncoded(), 0));
                this.f10b = new String(Base64.encode(keyPair.getPublic().getEncoded(), 0));
            }
            b0.o(this.f9a);
            this.f11c = b0.o(this.f10b);
        }
    }
}
