package aa;

import com.duokan.airkan.server.f;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: BEROctetStringParser */
public class d0 implements o {

    /* renamed from: a  reason: collision with root package name */
    public v f174a;

    public d0(v vVar) {
        this.f174a = vVar;
    }

    @Override // aa.o
    public InputStream a() {
        return new k0(this.f174a);
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            StringBuilder a10 = f.a("IOException converting stream to byte array: ");
            a10.append(e10.getMessage());
            throw new ASN1ParsingException(a10.toString(), e10);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        InputStream a10 = a();
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bArr = new byte[4096];
        while (true) {
            int read = a10.read(bArr, 0, 4096);
            if (read < 0) {
                return new c0(byteArrayOutputStream.toByteArray());
            }
            byteArrayOutputStream.write(bArr, 0, read);
        }
    }
}
