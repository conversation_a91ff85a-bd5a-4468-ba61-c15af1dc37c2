package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseIntArray;
import android.util.Xml;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Constraints;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import com.xiaomi.onetrack.util.z;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import org.xmlpull.v1.XmlPullParserException;

/* compiled from: ConstraintSet */
public class a {

    /* renamed from: b  reason: collision with root package name */
    public static final int[] f1470b = {0, 4, 8};

    /* renamed from: c  reason: collision with root package name */
    public static SparseIntArray f1471c;

    /* renamed from: a  reason: collision with root package name */
    public HashMap<Integer, C0008a> f1472a = new HashMap<>();

    /* renamed from: androidx.constraintlayout.widget.a$a  reason: collision with other inner class name */
    /* compiled from: ConstraintSet */
    public static class C0008a {
        public int A = -1;
        public int B = -1;
        public int C = -1;
        public int D = -1;
        public int E = -1;
        public int F = -1;
        public int G = -1;
        public int H = -1;
        public int I = -1;
        public int J = 0;
        public int K = -1;
        public int L = -1;
        public int M = -1;
        public int N = -1;
        public int O = -1;
        public int P = -1;
        public float Q = Constant.VOLUME_FLOAT_MIN;
        public float R = Constant.VOLUME_FLOAT_MIN;
        public int S = 0;
        public int T = 0;
        public float U = 1.0f;
        public boolean V = false;
        public float W = Constant.VOLUME_FLOAT_MIN;
        public float X = Constant.VOLUME_FLOAT_MIN;
        public float Y = Constant.VOLUME_FLOAT_MIN;
        public float Z = Constant.VOLUME_FLOAT_MIN;

        /* renamed from: a  reason: collision with root package name */
        public boolean f1473a = false;

        /* renamed from: a0  reason: collision with root package name */
        public float f1474a0 = 1.0f;

        /* renamed from: b  reason: collision with root package name */
        public int f1475b;

        /* renamed from: b0  reason: collision with root package name */
        public float f1476b0 = 1.0f;

        /* renamed from: c  reason: collision with root package name */
        public int f1477c;

        /* renamed from: c0  reason: collision with root package name */
        public float f1478c0 = Float.NaN;

        /* renamed from: d  reason: collision with root package name */
        public int f1479d;

        /* renamed from: d0  reason: collision with root package name */
        public float f1480d0 = Float.NaN;

        /* renamed from: e  reason: collision with root package name */
        public int f1481e = -1;

        /* renamed from: e0  reason: collision with root package name */
        public float f1482e0 = Constant.VOLUME_FLOAT_MIN;

        /* renamed from: f  reason: collision with root package name */
        public int f1483f = -1;

        /* renamed from: f0  reason: collision with root package name */
        public float f1484f0 = Constant.VOLUME_FLOAT_MIN;

        /* renamed from: g  reason: collision with root package name */
        public float f1485g = -1.0f;

        /* renamed from: g0  reason: collision with root package name */
        public float f1486g0 = Constant.VOLUME_FLOAT_MIN;
        public int h = -1;

        /* renamed from: h0  reason: collision with root package name */
        public boolean f1487h0 = false;

        /* renamed from: i  reason: collision with root package name */
        public int f1488i = -1;

        /* renamed from: i0  reason: collision with root package name */
        public boolean f1489i0 = false;

        /* renamed from: j  reason: collision with root package name */
        public int f1490j = -1;

        /* renamed from: j0  reason: collision with root package name */
        public int f1491j0 = 0;

        /* renamed from: k  reason: collision with root package name */
        public int f1492k = -1;

        /* renamed from: k0  reason: collision with root package name */
        public int f1493k0 = 0;

        /* renamed from: l  reason: collision with root package name */
        public int f1494l = -1;

        /* renamed from: l0  reason: collision with root package name */
        public int f1495l0 = -1;

        /* renamed from: m  reason: collision with root package name */
        public int f1496m = -1;

        /* renamed from: m0  reason: collision with root package name */
        public int f1497m0 = -1;

        /* renamed from: n  reason: collision with root package name */
        public int f1498n = -1;

        /* renamed from: n0  reason: collision with root package name */
        public int f1499n0 = -1;

        /* renamed from: o  reason: collision with root package name */
        public int f1500o = -1;

        /* renamed from: o0  reason: collision with root package name */
        public int f1501o0 = -1;

        /* renamed from: p  reason: collision with root package name */
        public int f1502p = -1;

        /* renamed from: p0  reason: collision with root package name */
        public float f1503p0 = 1.0f;

        /* renamed from: q  reason: collision with root package name */
        public int f1504q = -1;

        /* renamed from: q0  reason: collision with root package name */
        public float f1505q0 = 1.0f;

        /* renamed from: r  reason: collision with root package name */
        public int f1506r = -1;

        /* renamed from: r0  reason: collision with root package name */
        public boolean f1507r0 = false;

        /* renamed from: s  reason: collision with root package name */
        public int f1508s = -1;

        /* renamed from: s0  reason: collision with root package name */
        public int f1509s0 = -1;

        /* renamed from: t  reason: collision with root package name */
        public int f1510t = -1;

        /* renamed from: t0  reason: collision with root package name */
        public int f1511t0 = -1;

        /* renamed from: u  reason: collision with root package name */
        public float f1512u = 0.5f;

        /* renamed from: u0  reason: collision with root package name */
        public int[] f1513u0;

        /* renamed from: v  reason: collision with root package name */
        public float f1514v = 0.5f;

        /* renamed from: v0  reason: collision with root package name */
        public String f1515v0;

        /* renamed from: w  reason: collision with root package name */
        public String f1516w = null;

        /* renamed from: x  reason: collision with root package name */
        public int f1517x = -1;

        /* renamed from: y  reason: collision with root package name */
        public int f1518y = 0;

        /* renamed from: z  reason: collision with root package name */
        public float f1519z = Constant.VOLUME_FLOAT_MIN;

        public void a(ConstraintLayout.LayoutParams layoutParams) {
            layoutParams.f1423d = this.h;
            layoutParams.f1425e = this.f1488i;
            layoutParams.f1427f = this.f1490j;
            layoutParams.f1429g = this.f1492k;
            layoutParams.h = this.f1494l;
            layoutParams.f1432i = this.f1496m;
            layoutParams.f1434j = this.f1498n;
            layoutParams.f1436k = this.f1500o;
            layoutParams.f1438l = this.f1502p;
            layoutParams.f1442p = this.f1504q;
            layoutParams.f1443q = this.f1506r;
            layoutParams.f1444r = this.f1508s;
            layoutParams.f1445s = this.f1510t;
            ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin = this.D;
            ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin = this.E;
            ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = this.F;
            ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin = this.G;
            layoutParams.f1450x = this.P;
            layoutParams.f1451y = this.O;
            layoutParams.f1452z = this.f1512u;
            layoutParams.A = this.f1514v;
            layoutParams.f1439m = this.f1517x;
            layoutParams.f1440n = this.f1518y;
            layoutParams.f1441o = this.f1519z;
            layoutParams.B = this.f1516w;
            layoutParams.P = this.A;
            layoutParams.Q = this.B;
            layoutParams.E = this.Q;
            layoutParams.D = this.R;
            layoutParams.G = this.T;
            layoutParams.F = this.S;
            layoutParams.S = this.f1487h0;
            layoutParams.T = this.f1489i0;
            layoutParams.H = this.f1491j0;
            layoutParams.I = this.f1493k0;
            layoutParams.L = this.f1495l0;
            layoutParams.M = this.f1497m0;
            layoutParams.J = this.f1499n0;
            layoutParams.K = this.f1501o0;
            layoutParams.N = this.f1503p0;
            layoutParams.O = this.f1505q0;
            layoutParams.R = this.C;
            layoutParams.f1421c = this.f1485g;
            layoutParams.f1417a = this.f1481e;
            layoutParams.f1419b = this.f1483f;
            ((ViewGroup.MarginLayoutParams) layoutParams).width = this.f1475b;
            ((ViewGroup.MarginLayoutParams) layoutParams).height = this.f1477c;
            layoutParams.setMarginStart(this.I);
            layoutParams.setMarginEnd(this.H);
            layoutParams.a();
        }

        public final void b(int i10, ConstraintLayout.LayoutParams layoutParams) {
            this.f1479d = i10;
            this.h = layoutParams.f1423d;
            this.f1488i = layoutParams.f1425e;
            this.f1490j = layoutParams.f1427f;
            this.f1492k = layoutParams.f1429g;
            this.f1494l = layoutParams.h;
            this.f1496m = layoutParams.f1432i;
            this.f1498n = layoutParams.f1434j;
            this.f1500o = layoutParams.f1436k;
            this.f1502p = layoutParams.f1438l;
            this.f1504q = layoutParams.f1442p;
            this.f1506r = layoutParams.f1443q;
            this.f1508s = layoutParams.f1444r;
            this.f1510t = layoutParams.f1445s;
            this.f1512u = layoutParams.f1452z;
            this.f1514v = layoutParams.A;
            this.f1516w = layoutParams.B;
            this.f1517x = layoutParams.f1439m;
            this.f1518y = layoutParams.f1440n;
            this.f1519z = layoutParams.f1441o;
            this.A = layoutParams.P;
            this.B = layoutParams.Q;
            this.C = layoutParams.R;
            this.f1485g = layoutParams.f1421c;
            this.f1481e = layoutParams.f1417a;
            this.f1483f = layoutParams.f1419b;
            this.f1475b = ((ViewGroup.MarginLayoutParams) layoutParams).width;
            this.f1477c = ((ViewGroup.MarginLayoutParams) layoutParams).height;
            this.D = ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin;
            this.E = ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin;
            this.F = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin;
            this.G = ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
            this.Q = layoutParams.E;
            this.R = layoutParams.D;
            this.T = layoutParams.G;
            this.S = layoutParams.F;
            boolean z10 = layoutParams.S;
            this.f1487h0 = z10;
            this.f1489i0 = layoutParams.T;
            this.f1491j0 = layoutParams.H;
            this.f1493k0 = layoutParams.I;
            this.f1487h0 = z10;
            this.f1495l0 = layoutParams.L;
            this.f1497m0 = layoutParams.M;
            this.f1499n0 = layoutParams.J;
            this.f1501o0 = layoutParams.K;
            this.f1503p0 = layoutParams.N;
            this.f1505q0 = layoutParams.O;
            this.H = layoutParams.getMarginEnd();
            this.I = layoutParams.getMarginStart();
        }

        public final void c(int i10, Constraints.LayoutParams layoutParams) {
            b(i10, layoutParams);
            this.U = layoutParams.f1455l0;
            this.X = layoutParams.f1458o0;
            this.Y = layoutParams.f1459p0;
            this.Z = layoutParams.f1460q0;
            this.f1474a0 = layoutParams.f1461r0;
            this.f1476b0 = layoutParams.f1462s0;
            this.f1478c0 = layoutParams.f1463t0;
            this.f1480d0 = layoutParams.f1464u0;
            this.f1482e0 = layoutParams.f1465v0;
            this.f1484f0 = layoutParams.f1466w0;
            this.f1486g0 = Constant.VOLUME_FLOAT_MIN;
            this.W = layoutParams.f1457n0;
            this.V = layoutParams.f1456m0;
        }

        public Object clone() throws CloneNotSupportedException {
            C0008a aVar = new C0008a();
            aVar.f1473a = this.f1473a;
            aVar.f1475b = this.f1475b;
            aVar.f1477c = this.f1477c;
            aVar.f1481e = this.f1481e;
            aVar.f1483f = this.f1483f;
            aVar.f1485g = this.f1485g;
            aVar.h = this.h;
            aVar.f1488i = this.f1488i;
            aVar.f1490j = this.f1490j;
            aVar.f1492k = this.f1492k;
            aVar.f1494l = this.f1494l;
            aVar.f1496m = this.f1496m;
            aVar.f1498n = this.f1498n;
            aVar.f1500o = this.f1500o;
            aVar.f1502p = this.f1502p;
            aVar.f1504q = this.f1504q;
            aVar.f1506r = this.f1506r;
            aVar.f1508s = this.f1508s;
            aVar.f1510t = this.f1510t;
            aVar.f1512u = this.f1512u;
            aVar.f1514v = this.f1514v;
            aVar.f1516w = this.f1516w;
            aVar.A = this.A;
            aVar.B = this.B;
            aVar.f1512u = this.f1512u;
            aVar.f1512u = this.f1512u;
            aVar.f1512u = this.f1512u;
            aVar.f1512u = this.f1512u;
            aVar.f1512u = this.f1512u;
            aVar.C = this.C;
            aVar.D = this.D;
            aVar.E = this.E;
            aVar.F = this.F;
            aVar.G = this.G;
            aVar.H = this.H;
            aVar.I = this.I;
            aVar.J = this.J;
            aVar.K = this.K;
            aVar.L = this.L;
            aVar.M = this.M;
            aVar.N = this.N;
            aVar.O = this.O;
            aVar.P = this.P;
            aVar.Q = this.Q;
            aVar.R = this.R;
            aVar.S = this.S;
            aVar.T = this.T;
            aVar.U = this.U;
            aVar.V = this.V;
            aVar.W = this.W;
            aVar.X = this.X;
            aVar.Y = this.Y;
            aVar.Z = this.Z;
            aVar.f1474a0 = this.f1474a0;
            aVar.f1476b0 = this.f1476b0;
            aVar.f1478c0 = this.f1478c0;
            aVar.f1480d0 = this.f1480d0;
            aVar.f1482e0 = this.f1482e0;
            aVar.f1484f0 = this.f1484f0;
            aVar.f1486g0 = this.f1486g0;
            aVar.f1487h0 = this.f1487h0;
            aVar.f1489i0 = this.f1489i0;
            aVar.f1491j0 = this.f1491j0;
            aVar.f1493k0 = this.f1493k0;
            aVar.f1495l0 = this.f1495l0;
            aVar.f1497m0 = this.f1497m0;
            aVar.f1499n0 = this.f1499n0;
            aVar.f1501o0 = this.f1501o0;
            aVar.f1503p0 = this.f1503p0;
            aVar.f1505q0 = this.f1505q0;
            aVar.f1509s0 = this.f1509s0;
            aVar.f1511t0 = this.f1511t0;
            int[] iArr = this.f1513u0;
            if (iArr != null) {
                aVar.f1513u0 = Arrays.copyOf(iArr, iArr.length);
            }
            aVar.f1517x = this.f1517x;
            aVar.f1518y = this.f1518y;
            aVar.f1519z = this.f1519z;
            aVar.f1507r0 = this.f1507r0;
            return aVar;
        }
    }

    static {
        SparseIntArray sparseIntArray = new SparseIntArray();
        f1471c = sparseIntArray;
        sparseIntArray.append(R$styleable.ConstraintSet_layout_constraintLeft_toLeftOf, 25);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintLeft_toRightOf, 26);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintRight_toLeftOf, 29);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintRight_toRightOf, 30);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintTop_toTopOf, 36);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintTop_toBottomOf, 35);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintBottom_toTopOf, 4);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintBottom_toBottomOf, 3);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintBaseline_toBaselineOf, 1);
        f1471c.append(R$styleable.ConstraintSet_layout_editor_absoluteX, 6);
        f1471c.append(R$styleable.ConstraintSet_layout_editor_absoluteY, 7);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintGuide_begin, 17);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintGuide_end, 18);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintGuide_percent, 19);
        f1471c.append(R$styleable.ConstraintSet_android_orientation, 27);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintStart_toEndOf, 32);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintStart_toStartOf, 33);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintEnd_toStartOf, 10);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintEnd_toEndOf, 9);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginLeft, 13);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginTop, 16);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginRight, 14);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginBottom, 11);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginStart, 15);
        f1471c.append(R$styleable.ConstraintSet_layout_goneMarginEnd, 12);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintVertical_weight, 40);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHorizontal_weight, 39);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHorizontal_chainStyle, 41);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintVertical_chainStyle, 42);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHorizontal_bias, 20);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintVertical_bias, 37);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintDimensionRatio, 5);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintLeft_creator, 75);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintTop_creator, 75);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintRight_creator, 75);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintBottom_creator, 75);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintBaseline_creator, 75);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginLeft, 24);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginRight, 28);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginStart, 31);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginEnd, 8);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginTop, 34);
        f1471c.append(R$styleable.ConstraintSet_android_layout_marginBottom, 2);
        f1471c.append(R$styleable.ConstraintSet_android_layout_width, 23);
        f1471c.append(R$styleable.ConstraintSet_android_layout_height, 21);
        f1471c.append(R$styleable.ConstraintSet_android_visibility, 22);
        f1471c.append(R$styleable.ConstraintSet_android_alpha, 43);
        f1471c.append(R$styleable.ConstraintSet_android_elevation, 44);
        f1471c.append(R$styleable.ConstraintSet_android_rotationX, 45);
        f1471c.append(R$styleable.ConstraintSet_android_rotationY, 46);
        f1471c.append(R$styleable.ConstraintSet_android_rotation, 60);
        f1471c.append(R$styleable.ConstraintSet_android_scaleX, 47);
        f1471c.append(R$styleable.ConstraintSet_android_scaleY, 48);
        f1471c.append(R$styleable.ConstraintSet_android_transformPivotX, 49);
        f1471c.append(R$styleable.ConstraintSet_android_transformPivotY, 50);
        f1471c.append(R$styleable.ConstraintSet_android_translationX, 51);
        f1471c.append(R$styleable.ConstraintSet_android_translationY, 52);
        f1471c.append(R$styleable.ConstraintSet_android_translationZ, 53);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintWidth_default, 54);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHeight_default, 55);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintWidth_max, 56);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHeight_max, 57);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintWidth_min, 58);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHeight_min, 59);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintCircle, 61);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintCircleRadius, 62);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintCircleAngle, 63);
        f1471c.append(R$styleable.ConstraintSet_android_id, 38);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintWidth_percent, 69);
        f1471c.append(R$styleable.ConstraintSet_layout_constraintHeight_percent, 70);
        f1471c.append(R$styleable.ConstraintSet_chainUseRtl, 71);
        f1471c.append(R$styleable.ConstraintSet_barrierDirection, 72);
        f1471c.append(R$styleable.ConstraintSet_constraint_referenced_ids, 73);
        f1471c.append(R$styleable.ConstraintSet_barrierAllowsGoneWidgets, 74);
    }

    public void a(ConstraintLayout constraintLayout) {
        int childCount = constraintLayout.getChildCount();
        HashSet hashSet = new HashSet(this.f1472a.keySet());
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = constraintLayout.getChildAt(i10);
            int id = childAt.getId();
            if (id != -1) {
                if (this.f1472a.containsKey(Integer.valueOf(id))) {
                    hashSet.remove(Integer.valueOf(id));
                    C0008a aVar = this.f1472a.get(Integer.valueOf(id));
                    if (childAt instanceof Barrier) {
                        aVar.f1511t0 = 1;
                    }
                    int i11 = aVar.f1511t0;
                    if (i11 != -1 && i11 == 1) {
                        Barrier barrier = (Barrier) childAt;
                        barrier.setId(id);
                        barrier.setType(aVar.f1509s0);
                        barrier.setAllowsGoneWidget(aVar.f1507r0);
                        int[] iArr = aVar.f1513u0;
                        if (iArr != null) {
                            barrier.setReferencedIds(iArr);
                        } else {
                            String str = aVar.f1515v0;
                            if (str != null) {
                                int[] f10 = f(barrier, str);
                                aVar.f1513u0 = f10;
                                barrier.setReferencedIds(f10);
                            }
                        }
                    }
                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) childAt.getLayoutParams();
                    aVar.a(layoutParams);
                    childAt.setLayoutParams(layoutParams);
                    childAt.setVisibility(aVar.J);
                    childAt.setAlpha(aVar.U);
                    childAt.setRotation(aVar.X);
                    childAt.setRotationX(aVar.Y);
                    childAt.setRotationY(aVar.Z);
                    childAt.setScaleX(aVar.f1474a0);
                    childAt.setScaleY(aVar.f1476b0);
                    if (!Float.isNaN(aVar.f1478c0)) {
                        childAt.setPivotX(aVar.f1478c0);
                    }
                    if (!Float.isNaN(aVar.f1480d0)) {
                        childAt.setPivotY(aVar.f1480d0);
                    }
                    childAt.setTranslationX(aVar.f1482e0);
                    childAt.setTranslationY(aVar.f1484f0);
                    childAt.setTranslationZ(aVar.f1486g0);
                    if (aVar.V) {
                        childAt.setElevation(aVar.W);
                    }
                }
            } else {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
        }
        Iterator it = hashSet.iterator();
        while (it.hasNext()) {
            Integer num = (Integer) it.next();
            C0008a aVar2 = this.f1472a.get(num);
            int i12 = aVar2.f1511t0;
            if (i12 != -1 && i12 == 1) {
                Barrier barrier2 = new Barrier(constraintLayout.getContext());
                barrier2.setId(num.intValue());
                int[] iArr2 = aVar2.f1513u0;
                if (iArr2 != null) {
                    barrier2.setReferencedIds(iArr2);
                } else {
                    String str2 = aVar2.f1515v0;
                    if (str2 != null) {
                        int[] f11 = f(barrier2, str2);
                        aVar2.f1513u0 = f11;
                        barrier2.setReferencedIds(f11);
                    }
                }
                barrier2.setType(aVar2.f1509s0);
                ConstraintLayout.LayoutParams a10 = constraintLayout.generateDefaultLayoutParams();
                barrier2.e();
                aVar2.a(a10);
                constraintLayout.addView(barrier2, a10);
            }
            if (aVar2.f1473a) {
                View guideline = new Guideline(constraintLayout.getContext());
                guideline.setId(num.intValue());
                ConstraintLayout.LayoutParams a11 = constraintLayout.generateDefaultLayoutParams();
                aVar2.a(a11);
                constraintLayout.addView(guideline, a11);
            }
        }
    }

    public void b(int i10, int i11, int i12, int i13, int i14, int i15, int i16, float f10) {
        if (i13 < 0) {
            throw new IllegalArgumentException("margin must be > 0");
        } else if (i16 < 0) {
            throw new IllegalArgumentException("margin must be > 0");
        } else if (f10 <= Constant.VOLUME_FLOAT_MIN || f10 > 1.0f) {
            throw new IllegalArgumentException("bias must be between 0 and 1 inclusive");
        } else if (i12 == 1 || i12 == 2) {
            e(i10, 1, i11, i12, i13);
            e(i10, 2, i14, i15, i16);
            this.f1472a.get(Integer.valueOf(i10)).f1512u = f10;
        } else if (i12 == 6 || i12 == 7) {
            e(i10, 6, i11, i12, i13);
            e(i10, 7, i14, i15, i16);
            this.f1472a.get(Integer.valueOf(i10)).f1512u = f10;
        } else {
            e(i10, 3, i11, i12, i13);
            e(i10, 4, i14, i15, i16);
            this.f1472a.get(Integer.valueOf(i10)).f1514v = f10;
        }
    }

    public void c(ConstraintLayout constraintLayout) {
        int childCount = constraintLayout.getChildCount();
        this.f1472a.clear();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = constraintLayout.getChildAt(i10);
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) childAt.getLayoutParams();
            int id = childAt.getId();
            if (id != -1) {
                if (!this.f1472a.containsKey(Integer.valueOf(id))) {
                    this.f1472a.put(Integer.valueOf(id), new C0008a());
                }
                C0008a aVar = this.f1472a.get(Integer.valueOf(id));
                aVar.b(id, layoutParams);
                aVar.J = childAt.getVisibility();
                aVar.U = childAt.getAlpha();
                aVar.X = childAt.getRotation();
                aVar.Y = childAt.getRotationX();
                aVar.Z = childAt.getRotationY();
                aVar.f1474a0 = childAt.getScaleX();
                aVar.f1476b0 = childAt.getScaleY();
                float pivotX = childAt.getPivotX();
                float pivotY = childAt.getPivotY();
                if (!(((double) pivotX) == 0.0d && ((double) pivotY) == 0.0d)) {
                    aVar.f1478c0 = pivotX;
                    aVar.f1480d0 = pivotY;
                }
                aVar.f1482e0 = childAt.getTranslationX();
                aVar.f1484f0 = childAt.getTranslationY();
                aVar.f1486g0 = childAt.getTranslationZ();
                if (aVar.V) {
                    aVar.W = childAt.getElevation();
                }
                if (childAt instanceof Barrier) {
                    Barrier barrier = (Barrier) childAt;
                    aVar.f1507r0 = barrier.h.f1328m0;
                    aVar.f1513u0 = barrier.getReferencedIds();
                    aVar.f1509s0 = barrier.getType();
                }
            } else {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
        }
    }

    public void d(int i10, int i11, int i12, int i13) {
        if (!this.f1472a.containsKey(Integer.valueOf(i10))) {
            this.f1472a.put(Integer.valueOf(i10), new C0008a());
        }
        C0008a aVar = this.f1472a.get(Integer.valueOf(i10));
        switch (i11) {
            case 1:
                if (i13 == 1) {
                    aVar.h = i12;
                    aVar.f1488i = -1;
                    return;
                } else if (i13 == 2) {
                    aVar.f1488i = i12;
                    aVar.h = -1;
                    return;
                } else {
                    StringBuilder a10 = f.a("left to ");
                    a10.append(j(i13));
                    a10.append(" undefined");
                    throw new IllegalArgumentException(a10.toString());
                }
            case 2:
                if (i13 == 1) {
                    aVar.f1490j = i12;
                    aVar.f1492k = -1;
                    return;
                } else if (i13 == 2) {
                    aVar.f1492k = i12;
                    aVar.f1490j = -1;
                    return;
                } else {
                    StringBuilder a11 = f.a("right to ");
                    a11.append(j(i13));
                    a11.append(" undefined");
                    throw new IllegalArgumentException(a11.toString());
                }
            case 3:
                if (i13 == 3) {
                    aVar.f1494l = i12;
                    aVar.f1496m = -1;
                    aVar.f1502p = -1;
                    return;
                } else if (i13 == 4) {
                    aVar.f1496m = i12;
                    aVar.f1494l = -1;
                    aVar.f1502p = -1;
                    return;
                } else {
                    StringBuilder a12 = f.a("right to ");
                    a12.append(j(i13));
                    a12.append(" undefined");
                    throw new IllegalArgumentException(a12.toString());
                }
            case 4:
                if (i13 == 4) {
                    aVar.f1500o = i12;
                    aVar.f1498n = -1;
                    aVar.f1502p = -1;
                    return;
                } else if (i13 == 3) {
                    aVar.f1498n = i12;
                    aVar.f1500o = -1;
                    aVar.f1502p = -1;
                    return;
                } else {
                    StringBuilder a13 = f.a("right to ");
                    a13.append(j(i13));
                    a13.append(" undefined");
                    throw new IllegalArgumentException(a13.toString());
                }
            case 5:
                if (i13 == 5) {
                    aVar.f1502p = i12;
                    aVar.f1500o = -1;
                    aVar.f1498n = -1;
                    aVar.f1494l = -1;
                    aVar.f1496m = -1;
                    return;
                }
                StringBuilder a14 = f.a("right to ");
                a14.append(j(i13));
                a14.append(" undefined");
                throw new IllegalArgumentException(a14.toString());
            case 6:
                if (i13 == 6) {
                    aVar.f1506r = i12;
                    aVar.f1504q = -1;
                    return;
                } else if (i13 == 7) {
                    aVar.f1504q = i12;
                    aVar.f1506r = -1;
                    return;
                } else {
                    StringBuilder a15 = f.a("right to ");
                    a15.append(j(i13));
                    a15.append(" undefined");
                    throw new IllegalArgumentException(a15.toString());
                }
            case 7:
                if (i13 == 7) {
                    aVar.f1510t = i12;
                    aVar.f1508s = -1;
                    return;
                } else if (i13 == 6) {
                    aVar.f1508s = i12;
                    aVar.f1510t = -1;
                    return;
                } else {
                    StringBuilder a16 = f.a("right to ");
                    a16.append(j(i13));
                    a16.append(" undefined");
                    throw new IllegalArgumentException(a16.toString());
                }
            default:
                throw new IllegalArgumentException(j(i11) + " to " + j(i13) + " unknown");
        }
    }

    public void e(int i10, int i11, int i12, int i13, int i14) {
        if (!this.f1472a.containsKey(Integer.valueOf(i10))) {
            this.f1472a.put(Integer.valueOf(i10), new C0008a());
        }
        C0008a aVar = this.f1472a.get(Integer.valueOf(i10));
        switch (i11) {
            case 1:
                if (i13 == 1) {
                    aVar.h = i12;
                    aVar.f1488i = -1;
                } else if (i13 == 2) {
                    aVar.f1488i = i12;
                    aVar.h = -1;
                } else {
                    StringBuilder a10 = f.a("Left to ");
                    a10.append(j(i13));
                    a10.append(" undefined");
                    throw new IllegalArgumentException(a10.toString());
                }
                aVar.D = i14;
                return;
            case 2:
                if (i13 == 1) {
                    aVar.f1490j = i12;
                    aVar.f1492k = -1;
                } else if (i13 == 2) {
                    aVar.f1492k = i12;
                    aVar.f1490j = -1;
                } else {
                    StringBuilder a11 = f.a("right to ");
                    a11.append(j(i13));
                    a11.append(" undefined");
                    throw new IllegalArgumentException(a11.toString());
                }
                aVar.E = i14;
                return;
            case 3:
                if (i13 == 3) {
                    aVar.f1494l = i12;
                    aVar.f1496m = -1;
                    aVar.f1502p = -1;
                } else if (i13 == 4) {
                    aVar.f1496m = i12;
                    aVar.f1494l = -1;
                    aVar.f1502p = -1;
                } else {
                    StringBuilder a12 = f.a("right to ");
                    a12.append(j(i13));
                    a12.append(" undefined");
                    throw new IllegalArgumentException(a12.toString());
                }
                aVar.F = i14;
                return;
            case 4:
                if (i13 == 4) {
                    aVar.f1500o = i12;
                    aVar.f1498n = -1;
                    aVar.f1502p = -1;
                } else if (i13 == 3) {
                    aVar.f1498n = i12;
                    aVar.f1500o = -1;
                    aVar.f1502p = -1;
                } else {
                    StringBuilder a13 = f.a("right to ");
                    a13.append(j(i13));
                    a13.append(" undefined");
                    throw new IllegalArgumentException(a13.toString());
                }
                aVar.G = i14;
                return;
            case 5:
                if (i13 == 5) {
                    aVar.f1502p = i12;
                    aVar.f1500o = -1;
                    aVar.f1498n = -1;
                    aVar.f1494l = -1;
                    aVar.f1496m = -1;
                    return;
                }
                StringBuilder a14 = f.a("right to ");
                a14.append(j(i13));
                a14.append(" undefined");
                throw new IllegalArgumentException(a14.toString());
            case 6:
                if (i13 == 6) {
                    aVar.f1506r = i12;
                    aVar.f1504q = -1;
                } else if (i13 == 7) {
                    aVar.f1504q = i12;
                    aVar.f1506r = -1;
                } else {
                    StringBuilder a15 = f.a("right to ");
                    a15.append(j(i13));
                    a15.append(" undefined");
                    throw new IllegalArgumentException(a15.toString());
                }
                aVar.I = i14;
                return;
            case 7:
                if (i13 == 7) {
                    aVar.f1510t = i12;
                    aVar.f1508s = -1;
                } else if (i13 == 6) {
                    aVar.f1508s = i12;
                    aVar.f1510t = -1;
                } else {
                    StringBuilder a16 = f.a("right to ");
                    a16.append(j(i13));
                    a16.append(" undefined");
                    throw new IllegalArgumentException(a16.toString());
                }
                aVar.H = i14;
                return;
            default:
                throw new IllegalArgumentException(j(i11) + " to " + j(i13) + " unknown");
        }
    }

    public final int[] f(View view, String str) {
        int i10;
        Object b10;
        String[] split = str.split(z.f5836b);
        Context context = view.getContext();
        int[] iArr = new int[split.length];
        int i11 = 0;
        int i12 = 0;
        while (i11 < split.length) {
            String trim = split[i11].trim();
            try {
                i10 = R$id.class.getField(trim).getInt(null);
            } catch (Exception unused) {
                i10 = 0;
            }
            if (i10 == 0) {
                i10 = context.getResources().getIdentifier(trim, "id", context.getPackageName());
            }
            if (i10 == 0 && view.isInEditMode() && (view.getParent() instanceof ConstraintLayout) && (b10 = ((ConstraintLayout) view.getParent()).b(0, trim)) != null && (b10 instanceof Integer)) {
                i10 = ((Integer) b10).intValue();
            }
            iArr[i12] = i10;
            i11++;
            i12++;
        }
        return i12 != split.length ? Arrays.copyOf(iArr, i12) : iArr;
    }

    public final C0008a g(Context context, AttributeSet attributeSet) {
        C0008a aVar = new C0008a();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ConstraintSet);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i10 = 0; i10 < indexCount; i10++) {
            int index = obtainStyledAttributes.getIndex(i10);
            int i11 = f1471c.get(index);
            switch (i11) {
                case 1:
                    int resourceId = obtainStyledAttributes.getResourceId(index, aVar.f1502p);
                    if (resourceId == -1) {
                        resourceId = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1502p = resourceId;
                    break;
                case 2:
                    aVar.G = obtainStyledAttributes.getDimensionPixelSize(index, aVar.G);
                    break;
                case 3:
                    int resourceId2 = obtainStyledAttributes.getResourceId(index, aVar.f1500o);
                    if (resourceId2 == -1) {
                        resourceId2 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1500o = resourceId2;
                    break;
                case 4:
                    int resourceId3 = obtainStyledAttributes.getResourceId(index, aVar.f1498n);
                    if (resourceId3 == -1) {
                        resourceId3 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1498n = resourceId3;
                    break;
                case 5:
                    aVar.f1516w = obtainStyledAttributes.getString(index);
                    break;
                case 6:
                    aVar.A = obtainStyledAttributes.getDimensionPixelOffset(index, aVar.A);
                    break;
                case 7:
                    aVar.B = obtainStyledAttributes.getDimensionPixelOffset(index, aVar.B);
                    break;
                case 8:
                    aVar.H = obtainStyledAttributes.getDimensionPixelSize(index, aVar.H);
                    break;
                case 9:
                    int resourceId4 = obtainStyledAttributes.getResourceId(index, aVar.f1510t);
                    if (resourceId4 == -1) {
                        resourceId4 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1510t = resourceId4;
                    break;
                case 10:
                    int resourceId5 = obtainStyledAttributes.getResourceId(index, aVar.f1508s);
                    if (resourceId5 == -1) {
                        resourceId5 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1508s = resourceId5;
                    break;
                case 11:
                    aVar.N = obtainStyledAttributes.getDimensionPixelSize(index, aVar.N);
                    break;
                case 12:
                    aVar.O = obtainStyledAttributes.getDimensionPixelSize(index, aVar.O);
                    break;
                case 13:
                    aVar.K = obtainStyledAttributes.getDimensionPixelSize(index, aVar.K);
                    break;
                case 14:
                    aVar.M = obtainStyledAttributes.getDimensionPixelSize(index, aVar.M);
                    break;
                case 15:
                    aVar.P = obtainStyledAttributes.getDimensionPixelSize(index, aVar.P);
                    break;
                case 16:
                    aVar.L = obtainStyledAttributes.getDimensionPixelSize(index, aVar.L);
                    break;
                case 17:
                    aVar.f1481e = obtainStyledAttributes.getDimensionPixelOffset(index, aVar.f1481e);
                    break;
                case 18:
                    aVar.f1483f = obtainStyledAttributes.getDimensionPixelOffset(index, aVar.f1483f);
                    break;
                case 19:
                    aVar.f1485g = obtainStyledAttributes.getFloat(index, aVar.f1485g);
                    break;
                case 20:
                    aVar.f1512u = obtainStyledAttributes.getFloat(index, aVar.f1512u);
                    break;
                case 21:
                    aVar.f1477c = obtainStyledAttributes.getLayoutDimension(index, aVar.f1477c);
                    break;
                case 22:
                    int i12 = obtainStyledAttributes.getInt(index, aVar.J);
                    aVar.J = i12;
                    aVar.J = f1470b[i12];
                    break;
                case 23:
                    aVar.f1475b = obtainStyledAttributes.getLayoutDimension(index, aVar.f1475b);
                    break;
                case 24:
                    aVar.D = obtainStyledAttributes.getDimensionPixelSize(index, aVar.D);
                    break;
                case 25:
                    int resourceId6 = obtainStyledAttributes.getResourceId(index, aVar.h);
                    if (resourceId6 == -1) {
                        resourceId6 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.h = resourceId6;
                    break;
                case 26:
                    int resourceId7 = obtainStyledAttributes.getResourceId(index, aVar.f1488i);
                    if (resourceId7 == -1) {
                        resourceId7 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1488i = resourceId7;
                    break;
                case 27:
                    aVar.C = obtainStyledAttributes.getInt(index, aVar.C);
                    break;
                case 28:
                    aVar.E = obtainStyledAttributes.getDimensionPixelSize(index, aVar.E);
                    break;
                case 29:
                    int resourceId8 = obtainStyledAttributes.getResourceId(index, aVar.f1490j);
                    if (resourceId8 == -1) {
                        resourceId8 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1490j = resourceId8;
                    break;
                case 30:
                    int resourceId9 = obtainStyledAttributes.getResourceId(index, aVar.f1492k);
                    if (resourceId9 == -1) {
                        resourceId9 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1492k = resourceId9;
                    break;
                case 31:
                    aVar.I = obtainStyledAttributes.getDimensionPixelSize(index, aVar.I);
                    break;
                case 32:
                    int resourceId10 = obtainStyledAttributes.getResourceId(index, aVar.f1504q);
                    if (resourceId10 == -1) {
                        resourceId10 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1504q = resourceId10;
                    break;
                case 33:
                    int resourceId11 = obtainStyledAttributes.getResourceId(index, aVar.f1506r);
                    if (resourceId11 == -1) {
                        resourceId11 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1506r = resourceId11;
                    break;
                case 34:
                    aVar.F = obtainStyledAttributes.getDimensionPixelSize(index, aVar.F);
                    break;
                case 35:
                    int resourceId12 = obtainStyledAttributes.getResourceId(index, aVar.f1496m);
                    if (resourceId12 == -1) {
                        resourceId12 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1496m = resourceId12;
                    break;
                case 36:
                    int resourceId13 = obtainStyledAttributes.getResourceId(index, aVar.f1494l);
                    if (resourceId13 == -1) {
                        resourceId13 = obtainStyledAttributes.getInt(index, -1);
                    }
                    aVar.f1494l = resourceId13;
                    break;
                case 37:
                    aVar.f1514v = obtainStyledAttributes.getFloat(index, aVar.f1514v);
                    break;
                case 38:
                    aVar.f1479d = obtainStyledAttributes.getResourceId(index, aVar.f1479d);
                    break;
                case 39:
                    aVar.R = obtainStyledAttributes.getFloat(index, aVar.R);
                    break;
                case 40:
                    aVar.Q = obtainStyledAttributes.getFloat(index, aVar.Q);
                    break;
                case 41:
                    aVar.S = obtainStyledAttributes.getInt(index, aVar.S);
                    break;
                case 42:
                    aVar.T = obtainStyledAttributes.getInt(index, aVar.T);
                    break;
                case 43:
                    aVar.U = obtainStyledAttributes.getFloat(index, aVar.U);
                    break;
                case 44:
                    aVar.V = true;
                    aVar.W = obtainStyledAttributes.getDimension(index, aVar.W);
                    break;
                case 45:
                    aVar.Y = obtainStyledAttributes.getFloat(index, aVar.Y);
                    break;
                case 46:
                    aVar.Z = obtainStyledAttributes.getFloat(index, aVar.Z);
                    break;
                case 47:
                    aVar.f1474a0 = obtainStyledAttributes.getFloat(index, aVar.f1474a0);
                    break;
                case 48:
                    aVar.f1476b0 = obtainStyledAttributes.getFloat(index, aVar.f1476b0);
                    break;
                case 49:
                    aVar.f1478c0 = obtainStyledAttributes.getFloat(index, aVar.f1478c0);
                    break;
                case 50:
                    aVar.f1480d0 = obtainStyledAttributes.getFloat(index, aVar.f1480d0);
                    break;
                case 51:
                    aVar.f1482e0 = obtainStyledAttributes.getDimension(index, aVar.f1482e0);
                    break;
                case 52:
                    aVar.f1484f0 = obtainStyledAttributes.getDimension(index, aVar.f1484f0);
                    break;
                case 53:
                    aVar.f1486g0 = obtainStyledAttributes.getDimension(index, aVar.f1486g0);
                    break;
                default:
                    switch (i11) {
                        case 60:
                            aVar.X = obtainStyledAttributes.getFloat(index, aVar.X);
                            continue;
                        case 61:
                            int resourceId14 = obtainStyledAttributes.getResourceId(index, aVar.f1517x);
                            if (resourceId14 == -1) {
                                resourceId14 = obtainStyledAttributes.getInt(index, -1);
                            }
                            aVar.f1517x = resourceId14;
                            continue;
                        case 62:
                            aVar.f1518y = obtainStyledAttributes.getDimensionPixelSize(index, aVar.f1518y);
                            continue;
                        case 63:
                            aVar.f1519z = obtainStyledAttributes.getFloat(index, aVar.f1519z);
                            continue;
                        default:
                            switch (i11) {
                                case 69:
                                    aVar.f1503p0 = obtainStyledAttributes.getFloat(index, 1.0f);
                                    continue;
                                    continue;
                                case 70:
                                    aVar.f1505q0 = obtainStyledAttributes.getFloat(index, 1.0f);
                                    continue;
                                case 71:
                                    Log.e("ConstraintSet", "CURRENTLY UNSUPPORTED");
                                    continue;
                                case 72:
                                    aVar.f1509s0 = obtainStyledAttributes.getInt(index, aVar.f1509s0);
                                    continue;
                                case 73:
                                    aVar.f1515v0 = obtainStyledAttributes.getString(index);
                                    continue;
                                case 74:
                                    aVar.f1507r0 = obtainStyledAttributes.getBoolean(index, aVar.f1507r0);
                                    continue;
                                case 75:
                                    StringBuilder a10 = f.a("unused attribute 0x");
                                    a10.append(Integer.toHexString(index));
                                    a10.append("   ");
                                    a10.append(f1471c.get(index));
                                    Log.w("ConstraintSet", a10.toString());
                                    continue;
                                default:
                                    StringBuilder a11 = f.a("Unknown attribute 0x");
                                    a11.append(Integer.toHexString(index));
                                    a11.append("   ");
                                    a11.append(f1471c.get(index));
                                    Log.w("ConstraintSet", a11.toString());
                                    continue;
                            }
                    }
            }
        }
        obtainStyledAttributes.recycle();
        return aVar;
    }

    public final C0008a h(int i10) {
        if (!this.f1472a.containsKey(Integer.valueOf(i10))) {
            this.f1472a.put(Integer.valueOf(i10), new C0008a());
        }
        return this.f1472a.get(Integer.valueOf(i10));
    }

    public void i(Context context, int i10) {
        XmlResourceParser xml = context.getResources().getXml(i10);
        try {
            for (int eventType = xml.getEventType(); eventType != 1; eventType = xml.next()) {
                if (eventType == 0) {
                    xml.getName();
                } else if (eventType == 2) {
                    String name = xml.getName();
                    C0008a g10 = g(context, Xml.asAttributeSet(xml));
                    if (name.equalsIgnoreCase("Guideline")) {
                        g10.f1473a = true;
                    }
                    this.f1472a.put(Integer.valueOf(g10.f1479d), g10);
                }
            }
        } catch (XmlPullParserException e10) {
            e10.printStackTrace();
        } catch (IOException e11) {
            e11.printStackTrace();
        }
    }

    public final String j(int i10) {
        switch (i10) {
            case 1:
                return "left";
            case 2:
                return "right";
            case 3:
                return "top";
            case 4:
                return "bottom";
            case 5:
                return "baseline";
            case 6:
                return "start";
            case 7:
                return "end";
            default:
                return "undefined";
        }
    }
}
