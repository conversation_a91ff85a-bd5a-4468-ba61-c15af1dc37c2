package a;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

public interface b extends IInterface {

    public static abstract class a extends Binder implements b {
        private static final String DESCRIPTOR = "com.cleanmaster.sdk.IScanCallback";
        public static final int TRANSACTION_onAllScanFinish = 6;
        public static final int TRANSACTION_onFindScanItem = 4;
        public static final int TRANSACTION_onPreScan = 1;
        public static final int TRANSACTION_onScanFinish = 5;
        public static final int TRANSACTION_onScanItem = 3;
        public static final int TRANSACTION_onStartScan = 2;

        /* renamed from: a.b$a$a  reason: collision with other inner class name */
        public static class C0002a implements b {

            /* renamed from: b  reason: collision with root package name */
            public static b f2b;

            /* renamed from: a  reason: collision with root package name */
            public IBinder f3a;

            public C0002a(IBinder iBinder) {
                this.f3a = iBinder;
            }

            public IBinder asBinder() {
                return this.f3a;
            }
        }

        public a() {
            attachInterface(this, DESCRIPTOR);
        }

        public static b asInterface(IBinder iBinder) {
            if (iBinder == null) {
                return null;
            }
            IInterface queryLocalInterface = iBinder.queryLocalInterface(DESCRIPTOR);
            return (queryLocalInterface == null || !(queryLocalInterface instanceof b)) ? new C0002a(iBinder) : (b) queryLocalInterface;
        }

        public static b getDefaultImpl() {
            return C0002a.f2b;
        }

        public static boolean setDefaultImpl(b bVar) {
            if (C0002a.f2b != null || bVar == null) {
                return false;
            }
            C0002a.f2b = bVar;
            return true;
        }

        public IBinder asBinder() {
            return this;
        }

        @Override // android.os.Binder
        public boolean onTransact(int i10, Parcel parcel, Parcel parcel2, int i11) {
            if (i10 != 1598968902) {
                switch (i10) {
                    case 1:
                        parcel.enforceInterface(DESCRIPTOR);
                        onPreScan();
                        parcel2.writeNoException();
                        return true;
                    case 2:
                        parcel.enforceInterface(DESCRIPTOR);
                        onStartScan(parcel.readInt(), parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 3:
                        parcel.enforceInterface(DESCRIPTOR);
                        boolean onScanItem = onScanItem(parcel.readInt(), parcel.readString(), parcel.readInt());
                        parcel2.writeNoException();
                        parcel2.writeInt(onScanItem ? 1 : 0);
                        return true;
                    case 4:
                        parcel.enforceInterface(DESCRIPTOR);
                        onFindScanItem(parcel.readInt(), parcel.readString(), parcel.readLong());
                        parcel2.writeNoException();
                        return true;
                    case 5:
                        parcel.enforceInterface(DESCRIPTOR);
                        onScanFinish(parcel.readInt());
                        parcel2.writeNoException();
                        return true;
                    case 6:
                        parcel.enforceInterface(DESCRIPTOR);
                        onAllScanFinish(parcel.readLong(), parcel.readLong(), parcel.readLong(), parcel.readLong(), parcel.readLong());
                        parcel2.writeNoException();
                        return true;
                    default:
                        return super.onTransact(i10, parcel, parcel2, i11);
                }
            } else {
                parcel2.writeString(DESCRIPTOR);
                return true;
            }
        }
    }

    void onAllScanFinish(long j10, long j11, long j12, long j13, long j14);

    void onFindScanItem(int i10, String str, long j10);

    void onPreScan();

    void onScanFinish(int i10);

    boolean onScanItem(int i10, String str, int i11);

    void onStartScan(int i10, int i11);
}
