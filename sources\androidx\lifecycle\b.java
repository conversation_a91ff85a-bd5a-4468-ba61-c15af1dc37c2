package androidx.lifecycle;

import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import com.duokan.airkan.server.f;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/* compiled from: ClassesInfoCache */
public final class b {

    /* renamed from: c  reason: collision with root package name */
    public static b f2059c = new b();

    /* renamed from: a  reason: collision with root package name */
    public final Map<Class<?>, a> f2060a = new HashMap();

    /* renamed from: b  reason: collision with root package name */
    public final Map<Class<?>, Boolean> f2061b = new HashMap();

    /* compiled from: ClassesInfoCache */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public final Map<Lifecycle.Event, List<C0014b>> f2062a = new HashMap();

        /* renamed from: b  reason: collision with root package name */
        public final Map<C0014b, Lifecycle.Event> f2063b;

        public a(Map<C0014b, Lifecycle.Event> map) {
            this.f2063b = map;
            for (Map.Entry<C0014b, Lifecycle.Event> entry : map.entrySet()) {
                Lifecycle.Event value = entry.getValue();
                List<C0014b> list = this.f2062a.get(value);
                if (list == null) {
                    list = new ArrayList<>();
                    this.f2062a.put(value, list);
                }
                list.add(entry.getKey());
            }
        }

        public static void a(List<C0014b> list, g gVar, Lifecycle.Event event, Object obj) {
            if (list != null) {
                for (int size = list.size() - 1; size >= 0; size--) {
                    C0014b bVar = list.get(size);
                    Objects.requireNonNull(bVar);
                    try {
                        int i10 = bVar.f2064a;
                        if (i10 == 0) {
                            bVar.f2065b.invoke(obj, new Object[0]);
                        } else if (i10 == 1) {
                            bVar.f2065b.invoke(obj, gVar);
                        } else if (i10 == 2) {
                            bVar.f2065b.invoke(obj, gVar, event);
                        }
                    } catch (InvocationTargetException e10) {
                        throw new RuntimeException("Failed to call observer method", e10.getCause());
                    } catch (IllegalAccessException e11) {
                        throw new RuntimeException(e11);
                    }
                }
            }
        }
    }

    /* renamed from: androidx.lifecycle.b$b  reason: collision with other inner class name */
    /* compiled from: ClassesInfoCache */
    public static final class C0014b {

        /* renamed from: a  reason: collision with root package name */
        public final int f2064a;

        /* renamed from: b  reason: collision with root package name */
        public final Method f2065b;

        public C0014b(int i10, Method method) {
            this.f2064a = i10;
            this.f2065b = method;
            method.setAccessible(true);
        }

        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof C0014b)) {
                return false;
            }
            C0014b bVar = (C0014b) obj;
            return this.f2064a == bVar.f2064a && this.f2065b.getName().equals(bVar.f2065b.getName());
        }

        public int hashCode() {
            return this.f2065b.getName().hashCode() + (this.f2064a * 31);
        }
    }

    public final a a(Class<?> cls, @Nullable Method[] methodArr) {
        int i10;
        a b10;
        Class<?> superclass = cls.getSuperclass();
        HashMap hashMap = new HashMap();
        if (!(superclass == null || (b10 = b(superclass)) == null)) {
            hashMap.putAll(b10.f2063b);
        }
        for (Class<?> cls2 : cls.getInterfaces()) {
            for (Map.Entry<C0014b, Lifecycle.Event> entry : b(cls2).f2063b.entrySet()) {
                c(hashMap, entry.getKey(), entry.getValue(), cls);
            }
        }
        if (methodArr == null) {
            try {
                methodArr = cls.getDeclaredMethods();
            } catch (NoClassDefFoundError e10) {
                throw new IllegalArgumentException("The observer class has some methods that use newer APIs which are not available in the current OS version. Lifecycles cannot access even other methods so you should make sure that your observer classes only access framework classes that are available in your min API level OR use lifecycle:compiler annotation processor.", e10);
            }
        }
        boolean z10 = false;
        for (Method method : methodArr) {
            OnLifecycleEvent onLifecycleEvent = (OnLifecycleEvent) method.getAnnotation(OnLifecycleEvent.class);
            if (onLifecycleEvent != null) {
                Class<?>[] parameterTypes = method.getParameterTypes();
                if (parameterTypes.length <= 0) {
                    i10 = 0;
                } else if (parameterTypes[0].isAssignableFrom(g.class)) {
                    i10 = 1;
                } else {
                    throw new IllegalArgumentException("invalid parameter type. Must be one and instanceof LifecycleOwner");
                }
                Lifecycle.Event value = onLifecycleEvent.value();
                if (parameterTypes.length > 1) {
                    if (!parameterTypes[1].isAssignableFrom(Lifecycle.Event.class)) {
                        throw new IllegalArgumentException("invalid parameter type. second arg must be an event");
                    } else if (value == Lifecycle.Event.ON_ANY) {
                        i10 = 2;
                    } else {
                        throw new IllegalArgumentException("Second arg is supported only for ON_ANY value");
                    }
                }
                if (parameterTypes.length <= 2) {
                    c(hashMap, new C0014b(i10, method), value, cls);
                    z10 = true;
                } else {
                    throw new IllegalArgumentException("cannot have more than 2 params");
                }
            }
        }
        a aVar = new a(hashMap);
        this.f2060a.put(cls, aVar);
        this.f2061b.put(cls, Boolean.valueOf(z10));
        return aVar;
    }

    public a b(Class<?> cls) {
        a aVar = this.f2060a.get(cls);
        if (aVar != null) {
            return aVar;
        }
        return a(cls, null);
    }

    public final void c(Map<C0014b, Lifecycle.Event> map, C0014b bVar, Lifecycle.Event event, Class<?> cls) {
        Lifecycle.Event event2 = map.get(bVar);
        if (event2 != null && event != event2) {
            Method method = bVar.f2065b;
            StringBuilder a10 = f.a("Method ");
            a10.append(method.getName());
            a10.append(" in ");
            a10.append(cls.getName());
            a10.append(" already declared with different @OnLifecycleEvent value: previous value ");
            a10.append(event2);
            a10.append(", new value ");
            a10.append(event);
            throw new IllegalArgumentException(a10.toString());
        } else if (event2 == null) {
            map.put(bVar, event);
        }
    }
}
