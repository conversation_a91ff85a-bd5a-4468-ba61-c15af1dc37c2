package aa;

import java.io.IOException;

/* compiled from: BERApplicationSpecific */
public class z extends a {
    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public z(int r5, aa.f r6) {
        /*
            r4 = this;
            java.io.ByteArrayOutputStream r0 = new java.io.ByteArrayOutputStream
            r0.<init>()
            r1 = 0
        L_0x0006:
            int r2 = r6.b()
            if (r1 == r2) goto L_0x0036
            aa.e r2 = r6.a(r1)     // Catch:{ IOException -> 0x001e }
            aa.l r2 = (aa.l) r2     // Catch:{ IOException -> 0x001e }
            java.lang.String r3 = "BER"
            byte[] r2 = r2.f(r3)     // Catch:{ IOException -> 0x001e }
            r0.write(r2)     // Catch:{ IOException -> 0x001e }
            int r1 = r1 + 1
            goto L_0x0006
        L_0x001e:
            r5 = move-exception
            org.spongycastle.asn1.ASN1ParsingException r6 = new org.spongycastle.asn1.ASN1ParsingException
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r1 = "malformed object: "
            r0.append(r1)
            r0.append(r5)
            java.lang.String r0 = r0.toString()
            r6.<init>(r0, r5)
            throw r6
        L_0x0036:
            byte[] r6 = r0.toByteArray()
            r0 = 1
            r4.<init>(r0, r5, r6)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: aa.z.<init>(int, aa.f):void");
    }

    @Override // aa.a, aa.q
    public void h(p pVar) throws IOException {
        pVar.i(this.f154a ? 96 : 64, this.f155b);
        pVar.c(128);
        pVar.f212a.write(this.f156c);
        pVar.c(0);
        pVar.c(0);
    }
}
