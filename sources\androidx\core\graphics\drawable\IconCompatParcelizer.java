package androidx.core.graphics.drawable;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.os.Parcelable;
import androidx.annotation.RestrictTo;
import androidx.versionedparcelable.VersionedParcel;
import com.xiaomi.mitv.socialtv.common.utils.IOUtil;
import java.nio.charset.Charset;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY})
public class IconCompatParcelizer {
    public static IconCompat read(VersionedParcel versionedParcel) {
        IconCompat iconCompat = new IconCompat();
        iconCompat.f1585a = versionedParcel.k(iconCompat.f1585a, 1);
        byte[] bArr = iconCompat.f1587c;
        if (versionedParcel.i(2)) {
            bArr = versionedParcel.g();
        }
        iconCompat.f1587c = bArr;
        iconCompat.f1588d = versionedParcel.m(iconCompat.f1588d, 3);
        iconCompat.f1589e = versionedParcel.k(iconCompat.f1589e, 4);
        iconCompat.f1590f = versionedParcel.k(iconCompat.f1590f, 5);
        iconCompat.f1591g = (ColorStateList) versionedParcel.m(iconCompat.f1591g, 6);
        String str = iconCompat.f1592i;
        if (versionedParcel.i(7)) {
            str = versionedParcel.n();
        }
        iconCompat.f1592i = str;
        iconCompat.h = PorterDuff.Mode.valueOf(str);
        switch (iconCompat.f1585a) {
            case -1:
                Parcelable parcelable = iconCompat.f1588d;
                if (parcelable != null) {
                    iconCompat.f1586b = parcelable;
                    break;
                } else {
                    throw new IllegalArgumentException("Invalid icon");
                }
            case 1:
            case 5:
                Parcelable parcelable2 = iconCompat.f1588d;
                if (parcelable2 == null) {
                    byte[] bArr2 = iconCompat.f1587c;
                    iconCompat.f1586b = bArr2;
                    iconCompat.f1585a = 3;
                    iconCompat.f1589e = 0;
                    iconCompat.f1590f = bArr2.length;
                    break;
                } else {
                    iconCompat.f1586b = parcelable2;
                    break;
                }
            case 2:
            case 4:
            case 6:
                iconCompat.f1586b = new String(iconCompat.f1587c, Charset.forName(IOUtil.CHARSET_NAME_UTF_16));
                break;
            case 3:
                iconCompat.f1586b = iconCompat.f1587c;
                break;
        }
        return iconCompat;
    }

    public static void write(IconCompat iconCompat, VersionedParcel versionedParcel) {
        Objects.requireNonNull(versionedParcel);
        iconCompat.f1592i = iconCompat.h.name();
        switch (iconCompat.f1585a) {
            case -1:
                iconCompat.f1588d = (Parcelable) iconCompat.f1586b;
                break;
            case 1:
            case 5:
                iconCompat.f1588d = (Parcelable) iconCompat.f1586b;
                break;
            case 2:
                iconCompat.f1587c = ((String) iconCompat.f1586b).getBytes(Charset.forName(IOUtil.CHARSET_NAME_UTF_16));
                break;
            case 3:
                iconCompat.f1587c = (byte[]) iconCompat.f1586b;
                break;
            case 4:
            case 6:
                iconCompat.f1587c = iconCompat.f1586b.toString().getBytes(Charset.forName(IOUtil.CHARSET_NAME_UTF_16));
                break;
        }
        int i10 = iconCompat.f1585a;
        if (-1 != i10) {
            versionedParcel.p(1);
            versionedParcel.t(i10);
        }
        byte[] bArr = iconCompat.f1587c;
        if (bArr != null) {
            versionedParcel.p(2);
            versionedParcel.r(bArr);
        }
        Parcelable parcelable = iconCompat.f1588d;
        if (parcelable != null) {
            versionedParcel.p(3);
            versionedParcel.u(parcelable);
        }
        int i11 = iconCompat.f1589e;
        if (i11 != 0) {
            versionedParcel.p(4);
            versionedParcel.t(i11);
        }
        int i12 = iconCompat.f1590f;
        if (i12 != 0) {
            versionedParcel.p(5);
            versionedParcel.t(i12);
        }
        ColorStateList colorStateList = iconCompat.f1591g;
        if (colorStateList != null) {
            versionedParcel.p(6);
            versionedParcel.u(colorStateList);
        }
        String str = iconCompat.f1592i;
        if (str != null) {
            versionedParcel.p(7);
            versionedParcel.v(str);
        }
    }
}
