package androidx.fragment.app;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.result.c;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.R$styleable;
import com.xiaomi.onetrack.api.g;

/* compiled from: FragmentLayoutInflaterFactory */
public class w implements LayoutInflater.Factory2 {

    /* renamed from: a  reason: collision with root package name */
    public final FragmentManager f2019a;

    public w(FragmentManager fragmentManager) {
        this.f2019a = fragmentManager;
    }

    @Nullable
    public View onCreateView(@NonNull String str, @NonNull Context context, @NonNull AttributeSet attributeSet) {
        return onCreateView(null, str, context, attributeSet);
    }

    @Nullable
    public View onCreateView(@Nullable View view, @NonNull String str, @NonNull Context context, @NonNull AttributeSet attributeSet) {
        boolean z10;
        b0 b0Var;
        if (FragmentContainerView.class.getName().equals(str)) {
            return new FragmentContainerView(context, attributeSet, this.f2019a);
        }
        Fragment fragment = null;
        if (!"fragment".equals(str)) {
            return null;
        }
        String attributeValue = attributeSet.getAttributeValue(null, g.f5192r);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.Fragment);
        if (attributeValue == null) {
            attributeValue = obtainStyledAttributes.getString(R$styleable.Fragment_android_name);
        }
        int resourceId = obtainStyledAttributes.getResourceId(R$styleable.Fragment_android_id, -1);
        String string = obtainStyledAttributes.getString(R$styleable.Fragment_android_tag);
        obtainStyledAttributes.recycle();
        if (attributeValue != null) {
            ClassLoader classLoader = context.getClassLoader();
            u.g<ClassLoader, u.g<String, Class<?>>> gVar = u.f2013a;
            int i10 = 0;
            try {
                z10 = Fragment.class.isAssignableFrom(u.b(classLoader, attributeValue));
            } catch (ClassNotFoundException unused) {
                z10 = false;
            }
            if (z10) {
                if (view != null) {
                    i10 = view.getId();
                }
                if (i10 == -1 && resourceId == -1 && string == null) {
                    throw new IllegalArgumentException(attributeSet.getPositionDescription() + ": Must specify unique android:id, android:tag, or have a parent with an id for " + attributeValue);
                }
                if (resourceId != -1) {
                    fragment = this.f2019a.H(resourceId);
                }
                if (fragment == null && string != null) {
                    fragment = this.f2019a.I(string);
                }
                if (fragment == null && i10 != -1) {
                    fragment = this.f2019a.H(i10);
                }
                if (fragment == null) {
                    fragment = this.f2019a.L().a(context.getClassLoader(), attributeValue);
                    fragment.f1731m = true;
                    fragment.f1752y = resourceId != 0 ? resourceId : i10;
                    fragment.f1732m0 = i10;
                    fragment.f1734n0 = string;
                    fragment.f1733n = true;
                    FragmentManager fragmentManager = this.f2019a;
                    fragment.f1741r = fragmentManager;
                    v<?> vVar = fragmentManager.f1788q;
                    fragment.f1743s = vVar;
                    fragment.C(vVar.f2016b, attributeSet, fragment.f1721b);
                    b0Var = this.f2019a.h(fragment);
                    this.f2019a.a(fragment);
                    if (FragmentManager.O(2)) {
                        Log.v("FragmentManager", "Fragment " + fragment + " has been inflated via the <fragment> tag: id=0x" + Integer.toHexString(resourceId));
                    }
                } else if (!fragment.f1733n) {
                    fragment.f1733n = true;
                    FragmentManager fragmentManager2 = this.f2019a;
                    fragment.f1741r = fragmentManager2;
                    v<?> vVar2 = fragmentManager2.f1788q;
                    fragment.f1743s = vVar2;
                    fragment.C(vVar2.f2016b, attributeSet, fragment.f1721b);
                    b0Var = this.f2019a.h(fragment);
                    if (FragmentManager.O(2)) {
                        Log.v("FragmentManager", "Retained Fragment " + fragment + " has been re-attached via the <fragment> tag: id=0x" + Integer.toHexString(resourceId));
                    }
                } else {
                    throw new IllegalArgumentException(attributeSet.getPositionDescription() + ": Duplicate id 0x" + Integer.toHexString(resourceId) + ", tag " + string + ", or parent id 0x" + Integer.toHexString(i10) + " with another fragment for " + attributeValue);
                }
                fragment.f1746t0 = (ViewGroup) view;
                b0Var.k();
                b0Var.j();
                View view2 = fragment.f1747u0;
                if (view2 != null) {
                    if (resourceId != 0) {
                        view2.setId(resourceId);
                    }
                    if (fragment.f1747u0.getTag() == null) {
                        fragment.f1747u0.setTag(string);
                    }
                    return fragment.f1747u0;
                }
                throw new IllegalStateException(c.a("Fragment ", attributeValue, " did not create a view."));
            }
        }
        return null;
    }
}
