package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$styleable;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.i;
import androidx.appcompat.widget.ActionMenuPresenter;
import androidx.appcompat.widget.ActionMenuView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.x;
import q.f;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ActionMenuItemView extends AppCompatTextView implements i.a, View.OnClickListener, ActionMenuView.a {

    /* renamed from: a  reason: collision with root package name */
    public f f515a;

    /* renamed from: b  reason: collision with root package name */
    public CharSequence f516b;

    /* renamed from: c  reason: collision with root package name */
    public Drawable f517c;

    /* renamed from: d  reason: collision with root package name */
    public d.b f518d;

    /* renamed from: e  reason: collision with root package name */
    public x f519e;

    /* renamed from: f  reason: collision with root package name */
    public b f520f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f521g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public int f522i;

    /* renamed from: j  reason: collision with root package name */
    public int f523j;

    /* renamed from: k  reason: collision with root package name */
    public int f524k;

    public class a extends x {
        public a() {
            super(ActionMenuItemView.this);
        }

        @Override // androidx.appcompat.widget.x
        public f b() {
            ActionMenuPresenter.a aVar;
            b bVar = ActionMenuItemView.this.f520f;
            if (bVar == null || (aVar = ActionMenuPresenter.this.f756x) == null) {
                return null;
            }
            return aVar.a();
        }

        @Override // androidx.appcompat.widget.x
        public boolean c() {
            f b10;
            ActionMenuItemView actionMenuItemView = ActionMenuItemView.this;
            d.b bVar = actionMenuItemView.f518d;
            if (bVar == null || !bVar.a(actionMenuItemView.f515a) || (b10 = b()) == null || !b10.isShowing()) {
                return false;
            }
            return true;
        }
    }

    public static abstract class b {
    }

    public ActionMenuItemView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    @Override // androidx.appcompat.widget.ActionMenuView.a
    public boolean a() {
        return c();
    }

    @Override // androidx.appcompat.widget.ActionMenuView.a
    public boolean b() {
        return c() && this.f515a.getIcon() == null;
    }

    public boolean c() {
        return !TextUtils.isEmpty(getText());
    }

    @Override // androidx.appcompat.view.menu.i.a
    public void d(f fVar, int i10) {
        this.f515a = fVar;
        setIcon(fVar.getIcon());
        setTitle(fVar.getTitleCondensed());
        setId(fVar.f630a);
        setVisibility(fVar.isVisible() ? 0 : 8);
        setEnabled(fVar.isEnabled());
        if (fVar.hasSubMenu() && this.f519e == null) {
            this.f519e = new a();
        }
    }

    public final boolean e() {
        Configuration configuration = getContext().getResources().getConfiguration();
        int i10 = configuration.screenWidthDp;
        return i10 >= 480 || (i10 >= 640 && configuration.screenHeightDp >= 480) || configuration.orientation == 2;
    }

    public final void f() {
        CharSequence charSequence;
        boolean z10 = true;
        boolean z11 = !TextUtils.isEmpty(this.f516b);
        if (this.f517c != null) {
            if (!((this.f515a.f653y & 4) == 4) || (!this.f521g && !this.h)) {
                z10 = false;
            }
        }
        boolean z12 = z11 & z10;
        CharSequence charSequence2 = null;
        setText(z12 ? this.f516b : null);
        CharSequence charSequence3 = this.f515a.f645q;
        if (TextUtils.isEmpty(charSequence3)) {
            if (z12) {
                charSequence = null;
            } else {
                charSequence = this.f515a.f634e;
            }
            setContentDescription(charSequence);
        } else {
            setContentDescription(charSequence3);
        }
        CharSequence charSequence4 = this.f515a.f646r;
        if (TextUtils.isEmpty(charSequence4)) {
            if (!z12) {
                charSequence2 = this.f515a.f634e;
            }
            setTooltipText(charSequence2);
            return;
        }
        setTooltipText(charSequence4);
    }

    @Override // androidx.appcompat.view.menu.i.a
    public f getItemData() {
        return this.f515a;
    }

    public void onClick(View view) {
        d.b bVar = this.f518d;
        if (bVar != null) {
            bVar.a(this.f515a);
        }
    }

    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.f521g = e();
        f();
    }

    @Override // androidx.appcompat.widget.AppCompatTextView
    public void onMeasure(int i10, int i11) {
        int i12;
        int i13;
        boolean c10 = c();
        if (c10 && (i13 = this.f523j) >= 0) {
            super.setPadding(i13, getPaddingTop(), getPaddingRight(), getPaddingBottom());
        }
        super.onMeasure(i10, i11);
        int mode = View.MeasureSpec.getMode(i10);
        int size = View.MeasureSpec.getSize(i10);
        int measuredWidth = getMeasuredWidth();
        if (mode == Integer.MIN_VALUE) {
            i12 = Math.min(size, this.f522i);
        } else {
            i12 = this.f522i;
        }
        if (mode != 1073741824 && this.f522i > 0 && measuredWidth < i12) {
            super.onMeasure(View.MeasureSpec.makeMeasureSpec(i12, 1073741824), i11);
        }
        if (!c10 && this.f517c != null) {
            super.setPadding((getMeasuredWidth() - this.f517c.getBounds().width()) / 2, getPaddingTop(), getPaddingRight(), getPaddingBottom());
        }
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        super.onRestoreInstanceState(null);
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        x xVar;
        if (!this.f515a.hasSubMenu() || (xVar = this.f519e) == null || !xVar.onTouch(this, motionEvent)) {
            return super.onTouchEvent(motionEvent);
        }
        return true;
    }

    public void setCheckable(boolean z10) {
    }

    public void setChecked(boolean z10) {
    }

    public void setExpandedFormat(boolean z10) {
        if (this.h != z10) {
            this.h = z10;
            f fVar = this.f515a;
            if (fVar != null) {
                d dVar = fVar.f642n;
                dVar.f613k = true;
                dVar.p(true);
            }
        }
    }

    public void setIcon(Drawable drawable) {
        this.f517c = drawable;
        if (drawable != null) {
            int intrinsicWidth = drawable.getIntrinsicWidth();
            int intrinsicHeight = drawable.getIntrinsicHeight();
            int i10 = this.f524k;
            if (intrinsicWidth > i10) {
                intrinsicHeight = (int) (((float) intrinsicHeight) * (((float) i10) / ((float) intrinsicWidth)));
                intrinsicWidth = i10;
            }
            if (intrinsicHeight > i10) {
                intrinsicWidth = (int) (((float) intrinsicWidth) * (((float) i10) / ((float) intrinsicHeight)));
            } else {
                i10 = intrinsicHeight;
            }
            drawable.setBounds(0, 0, intrinsicWidth, i10);
        }
        setCompoundDrawables(drawable, null, null, null);
        f();
    }

    public void setItemInvoker(d.b bVar) {
        this.f518d = bVar;
    }

    public void setPadding(int i10, int i11, int i12, int i13) {
        this.f523j = i10;
        super.setPadding(i10, i11, i12, i13);
    }

    public void setPopupCallback(b bVar) {
        this.f520f = bVar;
    }

    public void setTitle(CharSequence charSequence) {
        this.f516b = charSequence;
        f();
    }

    public ActionMenuItemView(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        Resources resources = context.getResources();
        this.f521g = e();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ActionMenuItemView, i10, 0);
        this.f522i = obtainStyledAttributes.getDimensionPixelSize(R$styleable.ActionMenuItemView_android_minWidth, 0);
        obtainStyledAttributes.recycle();
        this.f524k = (int) ((resources.getDisplayMetrics().density * 32.0f) + 0.5f);
        setOnClickListener(this);
        this.f523j = -1;
        setSaveEnabled(false);
    }
}
