package androidx.constraintlayout.solver;

import androidx.constraintlayout.solver.c;
import com.duokan.airkan.common.Constant;
import v.a;

/* compiled from: ArrayRow */
public class b implements c.a {

    /* renamed from: a  reason: collision with root package name */
    public SolverVariable f1258a = null;

    /* renamed from: b  reason: collision with root package name */
    public float f1259b = Constant.VOLUME_FLOAT_MIN;

    /* renamed from: c  reason: collision with root package name */
    public final a f1260c;

    /* renamed from: d  reason: collision with root package name */
    public boolean f1261d = false;

    public b(a aVar) {
        this.f1260c = new a(this, aVar);
    }

    @Override // androidx.constraintlayout.solver.c.a
    public void a(SolverVariable solverVariable) {
        int i10 = solverVariable.f1242c;
        float f10 = 1.0f;
        if (i10 != 1) {
            if (i10 == 2) {
                f10 = 1000.0f;
            } else if (i10 == 3) {
                f10 = 1000000.0f;
            } else if (i10 == 4) {
                f10 = 1.0E9f;
            } else if (i10 == 5) {
                f10 = 1.0E12f;
            }
        }
        this.f1260c.h(solverVariable, f10);
    }

    public b b(c cVar, int i10) {
        this.f1260c.h(cVar.k(i10, "ep"), 1.0f);
        this.f1260c.h(cVar.k(i10, "em"), -1.0f);
        return this;
    }

    public b c(SolverVariable solverVariable, SolverVariable solverVariable2, SolverVariable solverVariable3, SolverVariable solverVariable4, float f10) {
        this.f1260c.h(solverVariable, -1.0f);
        this.f1260c.h(solverVariable2, 1.0f);
        this.f1260c.h(solverVariable3, f10);
        this.f1260c.h(solverVariable4, -f10);
        return this;
    }

    public b d(SolverVariable solverVariable, SolverVariable solverVariable2, SolverVariable solverVariable3, int i10) {
        boolean z10 = false;
        if (i10 != 0) {
            if (i10 < 0) {
                i10 *= -1;
                z10 = true;
            }
            this.f1259b = (float) i10;
        }
        if (!z10) {
            this.f1260c.h(solverVariable, -1.0f);
            this.f1260c.h(solverVariable2, 1.0f);
            this.f1260c.h(solverVariable3, 1.0f);
        } else {
            this.f1260c.h(solverVariable, 1.0f);
            this.f1260c.h(solverVariable2, -1.0f);
            this.f1260c.h(solverVariable3, -1.0f);
        }
        return this;
    }

    public b e(SolverVariable solverVariable, SolverVariable solverVariable2, SolverVariable solverVariable3, int i10) {
        boolean z10 = false;
        if (i10 != 0) {
            if (i10 < 0) {
                i10 *= -1;
                z10 = true;
            }
            this.f1259b = (float) i10;
        }
        if (!z10) {
            this.f1260c.h(solverVariable, -1.0f);
            this.f1260c.h(solverVariable2, 1.0f);
            this.f1260c.h(solverVariable3, -1.0f);
        } else {
            this.f1260c.h(solverVariable, 1.0f);
            this.f1260c.h(solverVariable2, -1.0f);
            this.f1260c.h(solverVariable3, 1.0f);
        }
        return this;
    }

    public b f(SolverVariable solverVariable, SolverVariable solverVariable2, SolverVariable solverVariable3, SolverVariable solverVariable4, float f10) {
        this.f1260c.h(solverVariable3, 0.5f);
        this.f1260c.h(solverVariable4, 0.5f);
        this.f1260c.h(solverVariable, -0.5f);
        this.f1260c.h(solverVariable2, -0.5f);
        this.f1259b = -f10;
        return this;
    }

    public void g(SolverVariable solverVariable) {
        SolverVariable solverVariable2 = this.f1258a;
        if (solverVariable2 != null) {
            this.f1260c.h(solverVariable2, -1.0f);
            this.f1258a = null;
        }
        float i10 = this.f1260c.i(solverVariable, true) * -1.0f;
        this.f1258a = solverVariable;
        if (i10 != 1.0f) {
            this.f1259b /= i10;
            a aVar = this.f1260c;
            int i11 = aVar.h;
            int i12 = 0;
            while (i11 != -1 && i12 < aVar.f1249a) {
                float[] fArr = aVar.f1255g;
                fArr[i11] = fArr[i11] / i10;
                i11 = aVar.f1254f[i11];
                i12++;
            }
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:25:0x0076  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x007b  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public java.lang.String toString() {
        /*
        // Method dump skipped, instructions count: 159
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.b.toString():java.lang.String");
    }
}
