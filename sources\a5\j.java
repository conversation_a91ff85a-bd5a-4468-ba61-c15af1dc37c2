package a5;

import c5.r;
import com.xiaomi.mitv.pie.EventResultPersister;
import io.netty.util.internal.a;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import s0.q;

/* compiled from: Recycler */
public abstract class j<T> {

    /* renamed from: g  reason: collision with root package name */
    public static final e f90g = new a();
    public static final AtomicInteger h;

    /* renamed from: i  reason: collision with root package name */
    public static final int f91i;

    /* renamed from: j  reason: collision with root package name */
    public static final int f92j;

    /* renamed from: k  reason: collision with root package name */
    public static final int f93k;

    /* renamed from: l  reason: collision with root package name */
    public static final int f94l;

    /* renamed from: m  reason: collision with root package name */
    public static final int f95m = Math.max(0, r.d("io.netty.recycler.maxDelayedQueuesPerThread", i.a() * 2));

    /* renamed from: n  reason: collision with root package name */
    public static final int f96n;

    /* renamed from: o  reason: collision with root package name */
    public static final int f97o;

    /* renamed from: p  reason: collision with root package name */
    public static final int f98p;

    /* renamed from: q  reason: collision with root package name */
    public static final b5.b<Map<f<?>, g>> f99q = new c();

    /* renamed from: a  reason: collision with root package name */
    public final int f100a;

    /* renamed from: b  reason: collision with root package name */
    public final int f101b;

    /* renamed from: c  reason: collision with root package name */
    public final int f102c;

    /* renamed from: d  reason: collision with root package name */
    public final int f103d;

    /* renamed from: e  reason: collision with root package name */
    public final int f104e;

    /* renamed from: f  reason: collision with root package name */
    public final b5.b<f<T>> f105f = new b();

    /* compiled from: Recycler */
    public static class a implements e {
        @Override // io.netty.util.internal.a.AbstractC0098a
        public void a(Object obj) {
        }
    }

    /* compiled from: Recycler */
    public class b extends b5.b<f<T>> {
        public b() {
        }

        @Override // b5.b
        public Object b() throws Exception {
            j jVar = j.this;
            Thread currentThread = Thread.currentThread();
            j jVar2 = j.this;
            return new f(jVar, currentThread, jVar2.f100a, jVar2.f101b, jVar2.f102c, jVar2.f103d, jVar2.f104e);
        }
    }

    /* compiled from: Recycler */
    public static class c extends b5.b<Map<f<?>, g>> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // b5.b
        public Map<f<?>, g> b() throws Exception {
            return new WeakHashMap();
        }
    }

    /* compiled from: Recycler */
    public static final class d<T> implements e<T> {

        /* renamed from: a  reason: collision with root package name */
        public int f107a;

        /* renamed from: b  reason: collision with root package name */
        public int f108b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f109c;

        /* renamed from: d  reason: collision with root package name */
        public f<?> f110d;

        /* renamed from: e  reason: collision with root package name */
        public Object f111e;

        public d(f<?> fVar) {
            this.f110d = fVar;
        }

        /* JADX DEBUG: Multi-variable search result rejected for r1v8, resolved type: a5.j$d<?>[] */
        /* JADX DEBUG: Multi-variable search result rejected for r1v13, resolved type: a5.j$d<?>[] */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // io.netty.util.internal.a.AbstractC0098a
        public void a(Object obj) {
            if (obj == this.f111e) {
                f<?> fVar = this.f110d;
                if (this.f107a != this.f108b || fVar == null) {
                    throw new IllegalStateException("recycled already");
                }
                Thread currentThread = Thread.currentThread();
                if (fVar.f112a.get() == currentThread) {
                    if ((this.f108b | this.f107a) == 0) {
                        int i10 = j.f91i;
                        this.f107a = i10;
                        this.f108b = i10;
                        int i11 = fVar.h;
                        if (i11 < fVar.f115d && !fVar.a(this)) {
                            d<?>[] dVarArr = fVar.f118g;
                            if (i11 == dVarArr.length) {
                                fVar.f118g = (d[]) Arrays.copyOf(dVarArr, Math.min(i11 << 1, fVar.f115d));
                            }
                            fVar.f118g[i11] = this;
                            fVar.h = i11 + 1;
                            return;
                        }
                        return;
                    }
                    throw new IllegalStateException("recycled already");
                } else if (fVar.f114c != 0) {
                    Map<f<?>, g> a10 = j.f99q.a();
                    g gVar = a10.get(fVar);
                    if (gVar == null) {
                        if (a10.size() >= fVar.f114c) {
                            a10.put(fVar, g.f123g);
                            return;
                        }
                        g gVar2 = g.f123g;
                        if (!g.a.a(fVar.f113b)) {
                            gVar = null;
                        } else {
                            gVar = new g(fVar, currentThread);
                            synchronized (fVar) {
                                gVar.f126c = fVar.f122l;
                                fVar.f122l = gVar;
                            }
                        }
                        if (gVar != null) {
                            a10.put(fVar, gVar);
                        } else {
                            return;
                        }
                    } else if (gVar == g.f123g) {
                        return;
                    }
                    this.f107a = gVar.f127d;
                    int i12 = gVar.f129f;
                    if (i12 < gVar.f128e) {
                        gVar.f129f = i12 + 1;
                        return;
                    }
                    gVar.f129f = 0;
                    g.b bVar = gVar.f125b;
                    int i13 = bVar.get();
                    if (i13 == j.f96n) {
                        g.b bVar2 = g.a.a(gVar.f124a.f130a) ? new g.b() : null;
                        if (bVar2 != null) {
                            bVar.next = bVar2;
                            gVar.f125b = bVar2;
                            i13 = bVar2.get();
                            bVar = bVar2;
                        } else {
                            return;
                        }
                    }
                    bVar.elements[i13] = this;
                    this.f110d = null;
                    bVar.lazySet(i13 + 1);
                }
            } else {
                throw new IllegalArgumentException("object does not belong to handle");
            }
        }
    }

    /* compiled from: Recycler */
    public interface e<T> extends a.AbstractC0098a<T> {
    }

    /* compiled from: Recycler */
    public static final class f<T> {

        /* renamed from: a  reason: collision with root package name */
        public final WeakReference<Thread> f112a;

        /* renamed from: b  reason: collision with root package name */
        public final AtomicInteger f113b;

        /* renamed from: c  reason: collision with root package name */
        public final int f114c;

        /* renamed from: d  reason: collision with root package name */
        public final int f115d;

        /* renamed from: e  reason: collision with root package name */
        public final int f116e;

        /* renamed from: f  reason: collision with root package name */
        public final int f117f;

        /* renamed from: g  reason: collision with root package name */
        public d<?>[] f118g;
        public int h;

        /* renamed from: i  reason: collision with root package name */
        public int f119i;

        /* renamed from: j  reason: collision with root package name */
        public g f120j;

        /* renamed from: k  reason: collision with root package name */
        public g f121k;

        /* renamed from: l  reason: collision with root package name */
        public volatile g f122l;

        public f(j<T> jVar, Thread thread, int i10, int i11, int i12, int i13, int i14) {
            this.f112a = new WeakReference<>(thread);
            this.f115d = i10;
            this.f113b = new AtomicInteger(Math.max(i10 / i11, j.f96n));
            this.f118g = new d[Math.min(j.f93k, i10)];
            this.f116e = i12;
            this.f117f = i14;
            this.f119i = i12;
            this.f114c = i13;
        }

        public boolean a(d<?> dVar) {
            if (!dVar.f109c) {
                int i10 = this.f119i;
                if (i10 < this.f116e) {
                    this.f119i = i10 + 1;
                    return true;
                }
                this.f119i = 0;
                dVar.f109c = true;
            }
            return false;
        }
    }

    static {
        d5.a b02 = q.b0(j.class.getName());
        AtomicInteger atomicInteger = new AtomicInteger(EventResultPersister.GENERATE_NEW_ID);
        h = atomicInteger;
        f91i = atomicInteger.getAndIncrement();
        int i10 = 4096;
        int d10 = r.d("io.netty.recycler.maxCapacityPerThread", r.d("io.netty.recycler.maxCapacity", 4096));
        if (d10 >= 0) {
            i10 = d10;
        }
        f92j = i10;
        int max = Math.max(2, r.d("io.netty.recycler.maxSharedCapacityFactor", 2));
        f94l = max;
        int q10 = c5.j.q(Math.max(r.d("io.netty.recycler.linkCapacity", 16), 16));
        f96n = q10;
        int max2 = Math.max(0, r.d("io.netty.recycler.ratio", 8));
        f97o = max2;
        int max3 = Math.max(0, r.d("io.netty.recycler.delayedQueue.ratio", max2));
        f98p = max3;
        if (b02.isDebugEnabled()) {
            if (i10 == 0) {
                b02.debug("-Dio.netty.recycler.maxCapacityPerThread: disabled");
                b02.debug("-Dio.netty.recycler.maxSharedCapacityFactor: disabled");
                b02.debug("-Dio.netty.recycler.linkCapacity: disabled");
                b02.debug("-Dio.netty.recycler.ratio: disabled");
                b02.debug("-Dio.netty.recycler.delayedQueue.ratio: disabled");
            } else {
                b02.debug("-Dio.netty.recycler.maxCapacityPerThread: {}", Integer.valueOf(i10));
                b02.debug("-Dio.netty.recycler.maxSharedCapacityFactor: {}", Integer.valueOf(max));
                b02.debug("-Dio.netty.recycler.linkCapacity: {}", Integer.valueOf(q10));
                b02.debug("-Dio.netty.recycler.ratio: {}", Integer.valueOf(max2));
                b02.debug("-Dio.netty.recycler.delayedQueue.ratio: {}", Integer.valueOf(max3));
            }
        }
        f93k = Math.min(i10, 256);
    }

    public j() {
        int i10 = f92j;
        int i11 = f94l;
        int i12 = f97o;
        int i13 = f95m;
        int i14 = f98p;
        this.f102c = Math.max(0, i12);
        this.f104e = Math.max(0, i14);
        if (i10 <= 0) {
            this.f100a = 0;
            this.f101b = 1;
            this.f103d = 0;
            return;
        }
        this.f100a = i10;
        this.f101b = Math.max(1, i11);
        this.f103d = Math.max(0, i13);
    }

    public abstract T a(e<T> eVar);

    /* compiled from: Recycler */
    public static final class g extends WeakReference<Thread> {

        /* renamed from: g  reason: collision with root package name */
        public static final g f123g = new g();

        /* renamed from: a  reason: collision with root package name */
        public final a f124a;

        /* renamed from: b  reason: collision with root package name */
        public b f125b;

        /* renamed from: c  reason: collision with root package name */
        public g f126c;

        /* renamed from: d  reason: collision with root package name */
        public final int f127d;

        /* renamed from: e  reason: collision with root package name */
        public final int f128e;

        /* renamed from: f  reason: collision with root package name */
        public int f129f;

        /* compiled from: Recycler */
        public static final class a {

            /* renamed from: a  reason: collision with root package name */
            public final AtomicInteger f130a;

            /* renamed from: b  reason: collision with root package name */
            public b f131b;

            public a(AtomicInteger atomicInteger) {
                this.f130a = atomicInteger;
            }

            public static boolean a(AtomicInteger atomicInteger) {
                int i10;
                int i11;
                do {
                    i10 = atomicInteger.get();
                    i11 = j.f96n;
                    if (i10 < i11) {
                        return false;
                    }
                } while (!atomicInteger.compareAndSet(i10, i10 - i11));
                return true;
            }
        }

        /* compiled from: Recycler */
        public static final class b extends AtomicInteger {
            public final d<?>[] elements = new d[j.f96n];
            public b next;
            public int readIndex;
        }

        public g() {
            super(null);
            this.f127d = j.h.getAndIncrement();
            this.f124a = new a(null);
            this.f128e = 0;
        }

        public boolean a(f<?> fVar) {
            b bVar;
            a aVar = this.f124a;
            b bVar2 = aVar.f131b;
            if (bVar2 == null) {
                return false;
            }
            int i10 = bVar2.readIndex;
            int i11 = j.f96n;
            if (i10 == i11) {
                bVar2 = bVar2.next;
                if (bVar2 == null) {
                    return false;
                }
                aVar.f130a.addAndGet(i11);
                aVar.f131b = bVar2;
            }
            int i12 = bVar2.readIndex;
            int i13 = bVar2.get();
            int i14 = i13 - i12;
            if (i14 == 0) {
                return false;
            }
            int i15 = fVar.h;
            int i16 = i14 + i15;
            d<?>[] dVarArr = fVar.f118g;
            if (i16 > dVarArr.length) {
                int length = dVarArr.length;
                int i17 = fVar.f115d;
                do {
                    length <<= 1;
                    if (length >= i16) {
                        break;
                    }
                } while (length < i17);
                int min = Math.min(length, i17);
                d<?>[] dVarArr2 = fVar.f118g;
                if (min != dVarArr2.length) {
                    fVar.f118g = (d[]) Arrays.copyOf(dVarArr2, min);
                }
                i13 = Math.min((min + i12) - i15, i13);
            }
            if (i12 == i13) {
                return false;
            }
            d<?>[] dVarArr3 = bVar2.elements;
            d<?>[] dVarArr4 = fVar.f118g;
            while (i12 < i13) {
                d<?> dVar = dVarArr3[i12];
                int i18 = dVar.f108b;
                if (i18 == 0) {
                    dVar.f108b = dVar.f107a;
                } else if (i18 != dVar.f107a) {
                    throw new IllegalStateException("recycled already");
                }
                dVarArr3[i12] = null;
                if (!fVar.a(dVar)) {
                    dVar.f110d = fVar;
                    dVarArr4[i15] = dVar;
                    i15++;
                }
                i12++;
            }
            int i19 = j.f96n;
            if (i13 == i19 && (bVar = bVar2.next) != null) {
                a aVar2 = this.f124a;
                Objects.requireNonNull(aVar2);
                aVar2.f130a.addAndGet(i19);
                aVar2.f131b = bVar;
            }
            bVar2.readIndex = i13;
            if (fVar.h == i15) {
                return false;
            }
            fVar.h = i15;
            return true;
        }

        public g(f<?> fVar, Thread thread) {
            super(thread);
            this.f127d = j.h.getAndIncrement();
            b bVar = new b();
            this.f125b = bVar;
            a aVar = new a(fVar.f113b);
            this.f124a = aVar;
            aVar.f131b = bVar;
            int i10 = fVar.f117f;
            this.f128e = i10;
            this.f129f = i10;
        }
    }
}
