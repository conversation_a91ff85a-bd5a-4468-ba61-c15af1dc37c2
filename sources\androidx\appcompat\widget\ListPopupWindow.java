package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import com.xiaomi.mitv.socialtv.common.net.media.VideoConstants;
import j0.m;
import java.lang.reflect.Method;
import java.util.WeakHashMap;
import q.f;

public class ListPopupWindow implements f {

    /* renamed from: p0  reason: collision with root package name */
    public static Method f881p0;

    /* renamed from: q0  reason: collision with root package name */
    public static Method f882q0;

    /* renamed from: a  reason: collision with root package name */
    public Context f883a;

    /* renamed from: b  reason: collision with root package name */
    public ListAdapter f884b;

    /* renamed from: c  reason: collision with root package name */
    public v f885c;

    /* renamed from: d  reason: collision with root package name */
    public int f886d;

    /* renamed from: e  reason: collision with root package name */
    public int f887e;

    /* renamed from: f  reason: collision with root package name */
    public int f888f;

    /* renamed from: g  reason: collision with root package name */
    public int f889g;
    public int h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f890i;

    /* renamed from: j  reason: collision with root package name */
    public boolean f891j;

    /* renamed from: k  reason: collision with root package name */
    public boolean f892k;

    /* renamed from: l  reason: collision with root package name */
    public int f893l;

    /* renamed from: m  reason: collision with root package name */
    public int f894m;

    /* renamed from: m0  reason: collision with root package name */
    public Rect f895m0;

    /* renamed from: n  reason: collision with root package name */
    public DataSetObserver f896n;

    /* renamed from: n0  reason: collision with root package name */
    public boolean f897n0;

    /* renamed from: o  reason: collision with root package name */
    public View f898o;

    /* renamed from: o0  reason: collision with root package name */
    public PopupWindow f899o0;

    /* renamed from: p  reason: collision with root package name */
    public AdapterView.OnItemClickListener f900p;

    /* renamed from: q  reason: collision with root package name */
    public final e f901q;

    /* renamed from: r  reason: collision with root package name */
    public final d f902r;

    /* renamed from: s  reason: collision with root package name */
    public final c f903s;

    /* renamed from: t  reason: collision with root package name */
    public final a f904t;

    /* renamed from: x  reason: collision with root package name */
    public final Handler f905x;

    /* renamed from: y  reason: collision with root package name */
    public final Rect f906y;

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            v vVar = ListPopupWindow.this.f885c;
            if (vVar != null) {
                vVar.setListSelectionHidden(true);
                vVar.requestLayout();
            }
        }
    }

    public class b extends DataSetObserver {
        public b() {
        }

        public void onChanged() {
            if (ListPopupWindow.this.isShowing()) {
                ListPopupWindow.this.j();
            }
        }

        public void onInvalidated() {
            ListPopupWindow.this.dismiss();
        }
    }

    public class c implements AbsListView.OnScrollListener {
        public c() {
        }

        public void onScroll(AbsListView absListView, int i10, int i11, int i12) {
        }

        public void onScrollStateChanged(AbsListView absListView, int i10) {
            boolean z10 = true;
            if (i10 == 1) {
                if (ListPopupWindow.this.f899o0.getInputMethodMode() != 2) {
                    z10 = false;
                }
                if (!z10 && ListPopupWindow.this.f899o0.getContentView() != null) {
                    ListPopupWindow listPopupWindow = ListPopupWindow.this;
                    listPopupWindow.f905x.removeCallbacks(listPopupWindow.f901q);
                    ListPopupWindow.this.f901q.run();
                }
            }
        }
    }

    public class d implements View.OnTouchListener {
        public d() {
        }

        public boolean onTouch(View view, MotionEvent motionEvent) {
            PopupWindow popupWindow;
            int action = motionEvent.getAction();
            int x8 = (int) motionEvent.getX();
            int y10 = (int) motionEvent.getY();
            if (action == 0 && (popupWindow = ListPopupWindow.this.f899o0) != null && popupWindow.isShowing() && x8 >= 0 && x8 < ListPopupWindow.this.f899o0.getWidth() && y10 >= 0 && y10 < ListPopupWindow.this.f899o0.getHeight()) {
                ListPopupWindow listPopupWindow = ListPopupWindow.this;
                listPopupWindow.f905x.postDelayed(listPopupWindow.f901q, 250);
                return false;
            } else if (action != 1) {
                return false;
            } else {
                ListPopupWindow listPopupWindow2 = ListPopupWindow.this;
                listPopupWindow2.f905x.removeCallbacks(listPopupWindow2.f901q);
                return false;
            }
        }
    }

    public class e implements Runnable {
        public e() {
        }

        public void run() {
            v vVar = ListPopupWindow.this.f885c;
            if (vVar != null) {
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                if (vVar.isAttachedToWindow() && ListPopupWindow.this.f885c.getCount() > ListPopupWindow.this.f885c.getChildCount()) {
                    int childCount = ListPopupWindow.this.f885c.getChildCount();
                    ListPopupWindow listPopupWindow = ListPopupWindow.this;
                    if (childCount <= listPopupWindow.f894m) {
                        listPopupWindow.f899o0.setInputMethodMode(2);
                        ListPopupWindow.this.j();
                    }
                }
            }
        }
    }

    static {
        if (Build.VERSION.SDK_INT <= 28) {
            try {
                f881p0 = PopupWindow.class.getDeclaredMethod("setClipToScreenEnabled", Boolean.TYPE);
            } catch (NoSuchMethodException unused) {
                Log.i("ListPopupWindow", "Could not find method setClipToScreenEnabled() on PopupWindow. Oh well.");
            }
            try {
                f882q0 = PopupWindow.class.getDeclaredMethod("setEpicenterBounds", Rect.class);
            } catch (NoSuchMethodException unused2) {
                Log.i("ListPopupWindow", "Could not find method setEpicenterBounds(Rect) on PopupWindow. Oh well.");
            }
        }
    }

    public ListPopupWindow(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.listPopupWindowStyle);
    }

    public int a() {
        return this.f888f;
    }

    public void c(int i10) {
        this.f889g = i10;
        this.f890i = true;
    }

    @Override // q.f
    public void dismiss() {
        this.f899o0.dismiss();
        this.f899o0.setContentView(null);
        this.f885c = null;
        this.f905x.removeCallbacks(this.f901q);
    }

    public void e(int i10) {
        this.f888f = i10;
    }

    public int f() {
        if (!this.f890i) {
            return 0;
        }
        return this.f889g;
    }

    @Nullable
    public Drawable getBackground() {
        return this.f899o0.getBackground();
    }

    public void h(@Nullable ListAdapter listAdapter) {
        DataSetObserver dataSetObserver = this.f896n;
        if (dataSetObserver == null) {
            this.f896n = new b();
        } else {
            ListAdapter listAdapter2 = this.f884b;
            if (listAdapter2 != null) {
                listAdapter2.unregisterDataSetObserver(dataSetObserver);
            }
        }
        this.f884b = listAdapter;
        if (listAdapter != null) {
            listAdapter.registerDataSetObserver(this.f896n);
        }
        v vVar = this.f885c;
        if (vVar != null) {
            vVar.setAdapter(this.f884b);
        }
    }

    @Override // q.f
    public boolean isShowing() {
        return this.f899o0.isShowing();
    }

    @Override // q.f
    public void j() {
        int i10;
        int i11;
        v vVar;
        int i12;
        if (this.f885c == null) {
            v n10 = n(this.f883a, !this.f897n0);
            this.f885c = n10;
            n10.setAdapter(this.f884b);
            this.f885c.setOnItemClickListener(this.f900p);
            this.f885c.setFocusable(true);
            this.f885c.setFocusableInTouchMode(true);
            this.f885c.setOnItemSelectedListener(new y(this));
            this.f885c.setOnScrollListener(this.f903s);
            this.f899o0.setContentView(this.f885c);
        } else {
            ViewGroup viewGroup = (ViewGroup) this.f899o0.getContentView();
        }
        Drawable background = this.f899o0.getBackground();
        int i13 = 0;
        if (background != null) {
            background.getPadding(this.f906y);
            Rect rect = this.f906y;
            int i14 = rect.top;
            i10 = rect.bottom + i14;
            if (!this.f890i) {
                this.f889g = -i14;
            }
        } else {
            this.f906y.setEmpty();
            i10 = 0;
        }
        int maxAvailableHeight = this.f899o0.getMaxAvailableHeight(this.f898o, this.f889g, this.f899o0.getInputMethodMode() == 2);
        if (this.f886d == -1) {
            i11 = maxAvailableHeight + i10;
        } else {
            int i15 = this.f887e;
            if (i15 == -2) {
                int i16 = this.f883a.getResources().getDisplayMetrics().widthPixels;
                Rect rect2 = this.f906y;
                i12 = View.MeasureSpec.makeMeasureSpec(i16 - (rect2.left + rect2.right), EventResultPersister.GENERATE_NEW_ID);
            } else if (i15 != -1) {
                i12 = View.MeasureSpec.makeMeasureSpec(i15, 1073741824);
            } else {
                int i17 = this.f883a.getResources().getDisplayMetrics().widthPixels;
                Rect rect3 = this.f906y;
                i12 = View.MeasureSpec.makeMeasureSpec(i17 - (rect3.left + rect3.right), 1073741824);
            }
            int a10 = this.f885c.a(i12, maxAvailableHeight - 0, -1);
            i11 = a10 + (a10 > 0 ? this.f885c.getPaddingBottom() + this.f885c.getPaddingTop() + i10 + 0 : 0);
        }
        boolean z10 = this.f899o0.getInputMethodMode() == 2;
        this.f899o0.setWindowLayoutType(this.h);
        if (this.f899o0.isShowing()) {
            View view = this.f898o;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (view.isAttachedToWindow()) {
                int i18 = this.f887e;
                if (i18 == -1) {
                    i18 = -1;
                } else if (i18 == -2) {
                    i18 = this.f898o.getWidth();
                }
                int i19 = this.f886d;
                if (i19 == -1) {
                    if (!z10) {
                        i11 = -1;
                    }
                    if (z10) {
                        this.f899o0.setWidth(this.f887e == -1 ? -1 : 0);
                        this.f899o0.setHeight(0);
                    } else {
                        PopupWindow popupWindow = this.f899o0;
                        if (this.f887e == -1) {
                            i13 = -1;
                        }
                        popupWindow.setWidth(i13);
                        this.f899o0.setHeight(-1);
                    }
                } else if (i19 != -2) {
                    i11 = i19;
                }
                this.f899o0.setOutsideTouchable(true);
                this.f899o0.update(this.f898o, this.f888f, this.f889g, i18 < 0 ? -1 : i18, i11 < 0 ? -1 : i11);
                return;
            }
            return;
        }
        int i20 = this.f887e;
        if (i20 == -1) {
            i20 = -1;
        } else if (i20 == -2) {
            i20 = this.f898o.getWidth();
        }
        int i21 = this.f886d;
        if (i21 == -1) {
            i11 = -1;
        } else if (i21 != -2) {
            i11 = i21;
        }
        this.f899o0.setWidth(i20);
        this.f899o0.setHeight(i11);
        if (Build.VERSION.SDK_INT <= 28) {
            Method method = f881p0;
            if (method != null) {
                try {
                    method.invoke(this.f899o0, Boolean.TRUE);
                } catch (Exception unused) {
                    Log.i("ListPopupWindow", "Could not call setClipToScreenEnabled() on PopupWindow. Oh well.");
                }
            }
        } else {
            this.f899o0.setIsClippedToScreen(true);
        }
        this.f899o0.setOutsideTouchable(true);
        this.f899o0.setTouchInterceptor(this.f902r);
        if (this.f892k) {
            this.f899o0.setOverlapAnchor(this.f891j);
        }
        if (Build.VERSION.SDK_INT <= 28) {
            Method method2 = f882q0;
            if (method2 != null) {
                try {
                    method2.invoke(this.f899o0, this.f895m0);
                } catch (Exception e10) {
                    Log.e("ListPopupWindow", "Could not invoke setEpicenterBounds on PopupWindow", e10);
                }
            }
        } else {
            this.f899o0.setEpicenterBounds(this.f895m0);
        }
        this.f899o0.showAsDropDown(this.f898o, this.f888f, this.f889g, this.f893l);
        this.f885c.setSelection(-1);
        if ((!this.f897n0 || this.f885c.isInTouchMode()) && (vVar = this.f885c) != null) {
            vVar.setListSelectionHidden(true);
            vVar.requestLayout();
        }
        if (!this.f897n0) {
            this.f905x.post(this.f904t);
        }
    }

    @Override // q.f
    @Nullable
    public ListView l() {
        return this.f885c;
    }

    @NonNull
    public v n(Context context, boolean z10) {
        return new v(context, z10);
    }

    public void o(int i10) {
        Drawable background = this.f899o0.getBackground();
        if (background != null) {
            background.getPadding(this.f906y);
            Rect rect = this.f906y;
            this.f887e = rect.left + rect.right + i10;
            return;
        }
        this.f887e = i10;
    }

    public void p(boolean z10) {
        this.f897n0 = z10;
        this.f899o0.setFocusable(z10);
    }

    public void setBackgroundDrawable(@Nullable Drawable drawable) {
        this.f899o0.setBackgroundDrawable(drawable);
    }

    public ListPopupWindow(@NonNull Context context, @Nullable AttributeSet attributeSet, @AttrRes int i10) {
        this(context, attributeSet, i10, 0);
    }

    public ListPopupWindow(@NonNull Context context, @Nullable AttributeSet attributeSet, @AttrRes int i10, @StyleRes int i11) {
        this.f886d = -2;
        this.f887e = -2;
        this.h = VideoConstants.SEARCH_MV_BY_KEYWORD;
        this.f893l = 0;
        this.f894m = Reader.READ_DONE;
        this.f901q = new e();
        this.f902r = new d();
        this.f903s = new c();
        this.f904t = new a();
        this.f906y = new Rect();
        this.f883a = context;
        this.f905x = new Handler(context.getMainLooper());
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ListPopupWindow, i10, i11);
        this.f888f = obtainStyledAttributes.getDimensionPixelOffset(R$styleable.ListPopupWindow_android_dropDownHorizontalOffset, 0);
        int dimensionPixelOffset = obtainStyledAttributes.getDimensionPixelOffset(R$styleable.ListPopupWindow_android_dropDownVerticalOffset, 0);
        this.f889g = dimensionPixelOffset;
        if (dimensionPixelOffset != 0) {
            this.f890i = true;
        }
        obtainStyledAttributes.recycle();
        AppCompatPopupWindow appCompatPopupWindow = new AppCompatPopupWindow(context, attributeSet, i10, i11);
        this.f899o0 = appCompatPopupWindow;
        appCompatPopupWindow.setInputMethodMode(1);
    }
}
