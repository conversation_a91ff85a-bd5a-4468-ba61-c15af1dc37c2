package androidx.constraintlayout.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.duokan.airkan.common.Constant;

public class Group extends ConstraintHelper {
    public Group(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void b(AttributeSet attributeSet) {
        super.b(attributeSet);
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void c(ConstraintLayout constraintLayout) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) getLayoutParams();
        layoutParams.f1437k0.C(0);
        layoutParams.f1437k0.w(0);
    }

    @Override // androidx.constraintlayout.widget.ConstraintHelper
    public void d(ConstraintLayout constraintLayout) {
        int visibility = getVisibility();
        float elevation = getElevation();
        for (int i10 = 0; i10 < this.f1399b; i10++) {
            View view = constraintLayout.f1403a.get(this.f1398a[i10]);
            if (view != null) {
                view.setVisibility(visibility);
                if (elevation > Constant.VOLUME_FLOAT_MIN) {
                    view.setElevation(elevation);
                }
            }
        }
    }

    public Group(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
    }
}
