package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: DLSequence */
public class m1 extends r {

    /* renamed from: b  reason: collision with root package name */
    public int f204b = -1;

    public m1() {
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        p b10 = pVar.b();
        int s10 = s();
        pVar.c(48);
        pVar.g(s10);
        Enumeration q10 = q();
        while (q10.hasMoreElements()) {
            b10.h((e) q10.nextElement());
        }
    }

    @Override // aa.q
    public int i() throws IOException {
        int s10 = s();
        return v1.a(s10) + 1 + s10;
    }

    public final int s() throws IOException {
        if (this.f204b < 0) {
            int i10 = 0;
            Enumeration q10 = q();
            while (q10.hasMoreElements()) {
                i10 += ((e) q10.nextElement()).c().m().i();
            }
            this.f204b = i10;
        }
        return this.f204b;
    }

    public m1(f fVar) {
        super(fVar);
    }
}
