package androidx.fragment.app;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.k0;
import f0.a;

/* compiled from: FragmentTransition */
public class g0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ k0.a f1902a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Fragment f1903b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ a f1904c;

    public g0(k0.a aVar, Fragment fragment, a aVar2) {
        this.f1902a = aVar;
        this.f1903b = fragment;
        this.f1904c = aVar2;
    }

    public void run() {
        ((FragmentManager.d) this.f1902a).a(this.f1903b, this.f1904c);
    }
}
