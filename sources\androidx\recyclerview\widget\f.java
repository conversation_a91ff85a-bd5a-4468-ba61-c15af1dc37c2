package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class f implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ArrayList f2363a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ l f2364b;

    public f(l lVar, ArrayList arrayList) {
        this.f2364b = lVar;
        this.f2363a = arrayList;
    }

    public void run() {
        Iterator it = this.f2363a.iterator();
        while (it.hasNext()) {
            RecyclerView.w wVar = (RecyclerView.w) it.next();
            l lVar = this.f2364b;
            Objects.requireNonNull(lVar);
            View view = wVar.f2267a;
            ViewPropertyAnimator animate = view.animate();
            lVar.f2394o.add(wVar);
            animate.alpha(1.0f).setDuration(lVar.f2182c).setListener(new h(lVar, wVar, view, animate)).start();
        }
        this.f2363a.clear();
        this.f2364b.f2391l.remove(this.f2363a);
    }
}
