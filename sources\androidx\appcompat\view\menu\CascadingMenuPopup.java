package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.os.Handler;
import android.os.Parcelable;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.StyleRes;
import androidx.appcompat.R$dimen;
import androidx.appcompat.R$layout;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.widget.a0;
import androidx.appcompat.widget.z;
import androidx.core.view.ViewCompat;
import j0.m;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;
import java.util.WeakHashMap;

public final class CascadingMenuPopup extends q.d implements View.OnKeyListener, PopupWindow.OnDismissListener {

    /* renamed from: r0  reason: collision with root package name */
    public static final int f526r0 = R$layout.abc_cascading_menu_item_layout;

    /* renamed from: b  reason: collision with root package name */
    public final Context f527b;

    /* renamed from: c  reason: collision with root package name */
    public final int f528c;

    /* renamed from: d  reason: collision with root package name */
    public final int f529d;

    /* renamed from: e  reason: collision with root package name */
    public final int f530e;

    /* renamed from: f  reason: collision with root package name */
    public final boolean f531f;

    /* renamed from: g  reason: collision with root package name */
    public final Handler f532g;
    public final List<d> h = new ArrayList();

    /* renamed from: i  reason: collision with root package name */
    public final List<d> f533i = new ArrayList();

    /* renamed from: j  reason: collision with root package name */
    public final ViewTreeObserver.OnGlobalLayoutListener f534j = new a();

    /* renamed from: k  reason: collision with root package name */
    public final View.OnAttachStateChangeListener f535k = new b();

    /* renamed from: l  reason: collision with root package name */
    public final z f536l = new c();

    /* renamed from: m  reason: collision with root package name */
    public int f537m;

    /* renamed from: m0  reason: collision with root package name */
    public boolean f538m0;

    /* renamed from: n  reason: collision with root package name */
    public int f539n;

    /* renamed from: n0  reason: collision with root package name */
    public h.a f540n0;

    /* renamed from: o  reason: collision with root package name */
    public View f541o;

    /* renamed from: o0  reason: collision with root package name */
    public ViewTreeObserver f542o0;

    /* renamed from: p  reason: collision with root package name */
    public View f543p;

    /* renamed from: p0  reason: collision with root package name */
    public PopupWindow.OnDismissListener f544p0;

    /* renamed from: q  reason: collision with root package name */
    public int f545q;

    /* renamed from: q0  reason: collision with root package name */
    public boolean f546q0;

    /* renamed from: r  reason: collision with root package name */
    public boolean f547r;

    /* renamed from: s  reason: collision with root package name */
    public boolean f548s;

    /* renamed from: t  reason: collision with root package name */
    public int f549t;

    /* renamed from: x  reason: collision with root package name */
    public int f550x;

    /* renamed from: y  reason: collision with root package name */
    public boolean f551y;

    @Retention(RetentionPolicy.SOURCE)
    public @interface HorizPosition {
    }

    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        public void onGlobalLayout() {
            if (CascadingMenuPopup.this.isShowing() && CascadingMenuPopup.this.f533i.size() > 0 && !CascadingMenuPopup.this.f533i.get(0).f559a.f897n0) {
                View view = CascadingMenuPopup.this.f543p;
                if (view == null || !view.isShown()) {
                    CascadingMenuPopup.this.dismiss();
                    return;
                }
                for (d dVar : CascadingMenuPopup.this.f533i) {
                    dVar.f559a.j();
                }
            }
        }
    }

    public class b implements View.OnAttachStateChangeListener {
        public b() {
        }

        public void onViewAttachedToWindow(View view) {
        }

        public void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = CascadingMenuPopup.this.f542o0;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    CascadingMenuPopup.this.f542o0 = view.getViewTreeObserver();
                }
                CascadingMenuPopup cascadingMenuPopup = CascadingMenuPopup.this;
                cascadingMenuPopup.f542o0.removeGlobalOnLayoutListener(cascadingMenuPopup.f534j);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    public class c implements z {

        public class a implements Runnable {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ d f555a;

            /* renamed from: b  reason: collision with root package name */
            public final /* synthetic */ MenuItem f556b;

            /* renamed from: c  reason: collision with root package name */
            public final /* synthetic */ d f557c;

            public a(d dVar, MenuItem menuItem, d dVar2) {
                this.f555a = dVar;
                this.f556b = menuItem;
                this.f557c = dVar2;
            }

            public void run() {
                d dVar = this.f555a;
                if (dVar != null) {
                    CascadingMenuPopup.this.f546q0 = true;
                    dVar.f560b.c(false);
                    CascadingMenuPopup.this.f546q0 = false;
                }
                if (this.f556b.isEnabled() && this.f556b.hasSubMenu()) {
                    this.f557c.q(this.f556b, 4);
                }
            }
        }

        public c() {
        }

        @Override // androidx.appcompat.widget.z
        public void i(@NonNull d dVar, @NonNull MenuItem menuItem) {
            d dVar2 = null;
            CascadingMenuPopup.this.f532g.removeCallbacksAndMessages(null);
            int size = CascadingMenuPopup.this.f533i.size();
            int i10 = 0;
            while (true) {
                if (i10 >= size) {
                    i10 = -1;
                    break;
                } else if (dVar == CascadingMenuPopup.this.f533i.get(i10).f560b) {
                    break;
                } else {
                    i10++;
                }
            }
            if (i10 != -1) {
                int i11 = i10 + 1;
                if (i11 < CascadingMenuPopup.this.f533i.size()) {
                    dVar2 = CascadingMenuPopup.this.f533i.get(i11);
                }
                CascadingMenuPopup.this.f532g.postAtTime(new a(dVar2, menuItem, dVar), dVar, SystemClock.uptimeMillis() + 200);
            }
        }

        @Override // androidx.appcompat.widget.z
        public void k(@NonNull d dVar, @NonNull MenuItem menuItem) {
            CascadingMenuPopup.this.f532g.removeCallbacksAndMessages(dVar);
        }
    }

    public static class d {

        /* renamed from: a  reason: collision with root package name */
        public final a0 f559a;

        /* renamed from: b  reason: collision with root package name */
        public final d f560b;

        /* renamed from: c  reason: collision with root package name */
        public final int f561c;

        public d(@NonNull a0 a0Var, @NonNull d dVar, int i10) {
            this.f559a = a0Var;
            this.f560b = dVar;
            this.f561c = i10;
        }
    }

    public CascadingMenuPopup(@NonNull Context context, @NonNull View view, @AttrRes int i10, @StyleRes int i11, boolean z10) {
        int i12 = 0;
        this.f537m = 0;
        this.f539n = 0;
        this.f527b = context;
        this.f541o = view;
        this.f529d = i10;
        this.f530e = i11;
        this.f531f = z10;
        this.f551y = false;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        this.f545q = view.getLayoutDirection() != 1 ? 1 : i12;
        Resources resources = context.getResources();
        this.f528c = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R$dimen.abc_config_prefDialogWidth));
        this.f532g = new Handler();
    }

    @Override // androidx.appcompat.view.menu.h
    public void a(d dVar, boolean z10) {
        int size = this.f533i.size();
        int i10 = 0;
        while (true) {
            if (i10 >= size) {
                i10 = -1;
                break;
            } else if (dVar == this.f533i.get(i10).f560b) {
                break;
            } else {
                i10++;
            }
        }
        if (i10 >= 0) {
            int i11 = i10 + 1;
            if (i11 < this.f533i.size()) {
                this.f533i.get(i11).f560b.c(false);
            }
            d remove = this.f533i.remove(i10);
            remove.f560b.t(this);
            if (this.f546q0) {
                remove.f559a.f899o0.setExitTransition(null);
                remove.f559a.f899o0.setAnimationStyle(0);
            }
            remove.f559a.dismiss();
            int size2 = this.f533i.size();
            if (size2 > 0) {
                this.f545q = this.f533i.get(size2 - 1).f561c;
            } else {
                View view = this.f541o;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                this.f545q = view.getLayoutDirection() == 1 ? 0 : 1;
            }
            if (size2 == 0) {
                dismiss();
                h.a aVar = this.f540n0;
                if (aVar != null) {
                    aVar.a(dVar, true);
                }
                ViewTreeObserver viewTreeObserver = this.f542o0;
                if (viewTreeObserver != null) {
                    if (viewTreeObserver.isAlive()) {
                        this.f542o0.removeGlobalOnLayoutListener(this.f534j);
                    }
                    this.f542o0 = null;
                }
                this.f543p.removeOnAttachStateChangeListener(this.f535k);
                this.f544p0.onDismiss();
            } else if (z10) {
                this.f533i.get(0).f560b.c(false);
            }
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void b(boolean z10) {
        for (d dVar : this.f533i) {
            ListAdapter adapter = dVar.f559a.f885c.getAdapter();
            if (adapter instanceof HeaderViewListAdapter) {
                adapter = ((HeaderViewListAdapter) adapter).getWrappedAdapter();
            }
            ((c) adapter).notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean c() {
        return false;
    }

    @Override // q.f
    public void dismiss() {
        int size = this.f533i.size();
        if (size > 0) {
            d[] dVarArr = (d[]) this.f533i.toArray(new d[size]);
            for (int i10 = size - 1; i10 >= 0; i10--) {
                d dVar = dVarArr[i10];
                if (dVar.f559a.isShowing()) {
                    dVar.f559a.dismiss();
                }
            }
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void e(Parcelable parcelable) {
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean f(k kVar) {
        for (d dVar : this.f533i) {
            if (kVar == dVar.f560b) {
                dVar.f559a.f885c.requestFocus();
                return true;
            }
        }
        if (!kVar.hasVisibleItems()) {
            return false;
        }
        kVar.b(this, this.f527b);
        if (isShowing()) {
            w(kVar);
        } else {
            this.h.add(kVar);
        }
        h.a aVar = this.f540n0;
        if (aVar != null) {
            aVar.b(kVar);
        }
        return true;
    }

    @Override // androidx.appcompat.view.menu.h
    public Parcelable g() {
        return null;
    }

    @Override // q.f
    public boolean isShowing() {
        return this.f533i.size() > 0 && this.f533i.get(0).f559a.isShowing();
    }

    @Override // q.f
    public void j() {
        if (!isShowing()) {
            for (d dVar : this.h) {
                w(dVar);
            }
            this.h.clear();
            View view = this.f541o;
            this.f543p = view;
            if (view != null) {
                boolean z10 = this.f542o0 == null;
                ViewTreeObserver viewTreeObserver = view.getViewTreeObserver();
                this.f542o0 = viewTreeObserver;
                if (z10) {
                    viewTreeObserver.addOnGlobalLayoutListener(this.f534j);
                }
                this.f543p.addOnAttachStateChangeListener(this.f535k);
            }
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void k(h.a aVar) {
        this.f540n0 = aVar;
    }

    @Override // q.f
    public ListView l() {
        if (this.f533i.isEmpty()) {
            return null;
        }
        List<d> list = this.f533i;
        return list.get(list.size() - 1).f559a.f885c;
    }

    @Override // q.d
    public void m(d dVar) {
        dVar.b(this, this.f527b);
        if (isShowing()) {
            w(dVar);
        } else {
            this.h.add(dVar);
        }
    }

    @Override // q.d
    public void o(@NonNull View view) {
        if (this.f541o != view) {
            this.f541o = view;
            int i10 = this.f537m;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            this.f539n = Gravity.getAbsoluteGravity(i10, view.getLayoutDirection());
        }
    }

    public void onDismiss() {
        d dVar;
        int size = this.f533i.size();
        int i10 = 0;
        while (true) {
            if (i10 >= size) {
                dVar = null;
                break;
            }
            dVar = this.f533i.get(i10);
            if (!dVar.f559a.isShowing()) {
                break;
            }
            i10++;
        }
        if (dVar != null) {
            dVar.f560b.c(false);
        }
    }

    public boolean onKey(View view, int i10, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i10 != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // q.d
    public void p(boolean z10) {
        this.f551y = z10;
    }

    @Override // q.d
    public void q(int i10) {
        if (this.f537m != i10) {
            this.f537m = i10;
            View view = this.f541o;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            this.f539n = Gravity.getAbsoluteGravity(i10, view.getLayoutDirection());
        }
    }

    @Override // q.d
    public void r(int i10) {
        this.f547r = true;
        this.f549t = i10;
    }

    @Override // q.d
    public void s(PopupWindow.OnDismissListener onDismissListener) {
        this.f544p0 = onDismissListener;
    }

    @Override // q.d
    public void t(boolean z10) {
        this.f538m0 = z10;
    }

    @Override // q.d
    public void u(int i10) {
        this.f548s = true;
        this.f550x = i10;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:51:0x0141, code lost:
        if (((r9.getWidth() + r10[0]) + r4) > r11.right) goto L_0x014b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:53:0x0147, code lost:
        if ((r10[0] - r4) < 0) goto L_0x0149;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:55:0x014b, code lost:
        r9 = 0;
     */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x00e9  */
    /* JADX WARNING: Removed duplicated region for block: B:68:0x0177  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void w(@androidx.annotation.NonNull androidx.appcompat.view.menu.d r17) {
        /*
        // Method dump skipped, instructions count: 468
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.CascadingMenuPopup.w(androidx.appcompat.view.menu.d):void");
    }
}
