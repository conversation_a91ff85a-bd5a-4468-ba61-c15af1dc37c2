package androidx.activity;

import android.annotation.SuppressLint;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import androidx.lifecycle.h;
import java.util.ArrayDeque;
import java.util.Iterator;

public final class OnBackPressedDispatcher {
    @Nullable

    /* renamed from: a  reason: collision with root package name */
    public final Runnable f285a;

    /* renamed from: b  reason: collision with root package name */
    public final ArrayDeque<b> f286b = new ArrayDeque<>();

    public class LifecycleOnBackPressedCancellable implements e, a {

        /* renamed from: a  reason: collision with root package name */
        public final Lifecycle f287a;

        /* renamed from: b  reason: collision with root package name */
        public final b f288b;
        @Nullable

        /* renamed from: c  reason: collision with root package name */
        public a f289c;

        public LifecycleOnBackPressedCancellable(@NonNull Lifecycle lifecycle, @NonNull b bVar) {
            this.f287a = lifecycle;
            this.f288b = bVar;
            lifecycle.a(this);
        }

        @Override // androidx.activity.a
        public void cancel() {
            h hVar = (h) this.f287a;
            hVar.d("removeObserver");
            hVar.f2066a.f(this);
            this.f288b.f294b.remove(this);
            a aVar = this.f289c;
            if (aVar != null) {
                aVar.cancel();
                this.f289c = null;
            }
        }

        @Override // androidx.lifecycle.e
        public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
            if (event == Lifecycle.Event.ON_START) {
                OnBackPressedDispatcher onBackPressedDispatcher = OnBackPressedDispatcher.this;
                b bVar = this.f288b;
                onBackPressedDispatcher.f286b.add(bVar);
                a aVar = new a(bVar);
                bVar.f294b.add(aVar);
                this.f289c = aVar;
            } else if (event == Lifecycle.Event.ON_STOP) {
                a aVar2 = this.f289c;
                if (aVar2 != null) {
                    aVar2.cancel();
                }
            } else if (event == Lifecycle.Event.ON_DESTROY) {
                cancel();
            }
        }
    }

    public class a implements a {

        /* renamed from: a  reason: collision with root package name */
        public final b f291a;

        public a(b bVar) {
            this.f291a = bVar;
        }

        @Override // androidx.activity.a
        public void cancel() {
            OnBackPressedDispatcher.this.f286b.remove(this.f291a);
            this.f291a.f294b.remove(this);
        }
    }

    public OnBackPressedDispatcher(@Nullable Runnable runnable) {
        this.f285a = runnable;
    }

    @SuppressLint({"LambdaLast"})
    @MainThread
    public void a(@NonNull g gVar, @NonNull b bVar) {
        Lifecycle lifecycle = gVar.getLifecycle();
        if (((h) lifecycle).f2067b != Lifecycle.State.DESTROYED) {
            bVar.f294b.add(new LifecycleOnBackPressedCancellable(lifecycle, bVar));
        }
    }

    @MainThread
    public void b() {
        Iterator<b> descendingIterator = this.f286b.descendingIterator();
        while (descendingIterator.hasNext()) {
            b next = descendingIterator.next();
            if (next.f293a) {
                next.a();
                return;
            }
        }
        Runnable runnable = this.f285a;
        if (runnable != null) {
            runnable.run();
        }
    }
}
