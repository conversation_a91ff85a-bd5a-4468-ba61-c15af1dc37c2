package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class h extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ RecyclerView.w f2369a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ View f2370b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ ViewPropertyAnimator f2371c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ l f2372d;

    public h(l lVar, RecyclerView.w wVar, View view, ViewPropertyAnimator viewPropertyAnimator) {
        this.f2372d = lVar;
        this.f2369a = wVar;
        this.f2370b = view;
        this.f2371c = viewPropertyAnimator;
    }

    public void onAnimationCancel(Animator animator) {
        this.f2370b.setAlpha(1.0f);
    }

    public void onAnimationEnd(Animator animator) {
        this.f2371c.setListener(null);
        this.f2372d.c(this.f2369a);
        this.f2372d.f2394o.remove(this.f2369a);
        this.f2372d.k();
    }

    public void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2372d);
    }
}
