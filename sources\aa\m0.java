package aa;

import java.io.IOException;

/* compiled from: DERBMPString */
public class m0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final char[] f203a;

    public m0(char[] cArr) {
        this.f203a = cArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof m0)) {
            return false;
        }
        char[] cArr = this.f203a;
        char[] cArr2 = ((m0) qVar).f203a;
        if (cArr != cArr2) {
            if (cArr == null || cArr2 == null || cArr.length != cArr2.length) {
                return false;
            }
            for (int i10 = 0; i10 != cArr.length; i10++) {
                if (cArr[i10] != cArr2[i10]) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override // aa.w
    public String getString() {
        return new String(this.f203a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.c(30);
        pVar.g(this.f203a.length * 2);
        int i10 = 0;
        while (true) {
            char[] cArr = this.f203a;
            if (i10 != cArr.length) {
                char c10 = cArr[i10];
                pVar.c((byte) (c10 >> '\b'));
                pVar.c((byte) c10);
                i10++;
            } else {
                return;
            }
        }
    }

    @Override // aa.l
    public int hashCode() {
        char[] cArr = this.f203a;
        if (cArr == null) {
            return 0;
        }
        int length = cArr.length;
        int i10 = length + 1;
        while (true) {
            length--;
            if (length < 0) {
                return i10;
            }
            i10 = (i10 * 257) ^ cArr[length];
        }
    }

    @Override // aa.q
    public int i() {
        return (this.f203a.length * 2) + v1.a(this.f203a.length * 2) + 1;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
