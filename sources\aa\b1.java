package aa;

import java.io.IOException;

/* compiled from: DERSequenceParser */
public class b1 implements s {

    /* renamed from: a  reason: collision with root package name */
    public v f164a;

    public b1(v vVar) {
        this.f164a = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return new a1(this.f164a.c());
        } catch (IOException e10) {
            throw new IllegalStateException(e10.getMessage());
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new a1(this.f164a.c());
    }
}
