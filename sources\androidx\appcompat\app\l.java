package androidx.appcompat.app;

import android.content.Context;
import android.content.ContextWrapper;
import android.util.AttributeSet;
import android.view.InflateException;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatAutoCompleteTextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.AppCompatTextView;
import com.duokan.airkan.server.f;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import u.g;

/* compiled from: AppCompatViewInflater */
public class l {

    /* renamed from: b  reason: collision with root package name */
    public static final Class<?>[] f450b = {Context.class, AttributeSet.class};

    /* renamed from: c  reason: collision with root package name */
    public static final int[] f451c = {16843375};

    /* renamed from: d  reason: collision with root package name */
    public static final String[] f452d = {"android.widget.", "android.view.", "android.webkit."};

    /* renamed from: e  reason: collision with root package name */
    public static final g<String, Constructor<? extends View>> f453e = new g<>();

    /* renamed from: a  reason: collision with root package name */
    public final Object[] f454a = new Object[2];

    /* compiled from: AppCompatViewInflater */
    public static class a implements View.OnClickListener {

        /* renamed from: a  reason: collision with root package name */
        public final View f455a;

        /* renamed from: b  reason: collision with root package name */
        public final String f456b;

        /* renamed from: c  reason: collision with root package name */
        public Method f457c;

        /* renamed from: d  reason: collision with root package name */
        public Context f458d;

        public a(@NonNull View view, @NonNull String str) {
            this.f455a = view;
            this.f456b = str;
        }

        public void onClick(@NonNull View view) {
            String str;
            Method method;
            if (this.f457c == null) {
                Context context = this.f455a.getContext();
                while (context != null) {
                    try {
                        if (!context.isRestricted() && (method = context.getClass().getMethod(this.f456b, View.class)) != null) {
                            this.f457c = method;
                            this.f458d = context;
                        }
                    } catch (NoSuchMethodException unused) {
                    }
                    context = context instanceof ContextWrapper ? ((ContextWrapper) context).getBaseContext() : null;
                }
                int id = this.f455a.getId();
                if (id == -1) {
                    str = "";
                } else {
                    StringBuilder a10 = f.a(" with id '");
                    a10.append(this.f455a.getContext().getResources().getResourceEntryName(id));
                    a10.append("'");
                    str = a10.toString();
                }
                StringBuilder a11 = f.a("Could not find method ");
                a11.append(this.f456b);
                a11.append("(View) in a parent or ancestor Context for android:onClick attribute defined on view ");
                a11.append(this.f455a.getClass());
                a11.append(str);
                throw new IllegalStateException(a11.toString());
            }
            try {
                this.f457c.invoke(this.f458d, view);
            } catch (IllegalAccessException e10) {
                throw new IllegalStateException("Could not execute non-public method for android:onClick", e10);
            } catch (InvocationTargetException e11) {
                throw new IllegalStateException("Could not execute method for android:onClick", e11);
            }
        }
    }

    @NonNull
    public AppCompatAutoCompleteTextView a(Context context, AttributeSet attributeSet) {
        return new AppCompatAutoCompleteTextView(context, attributeSet);
    }

    @NonNull
    public AppCompatButton b(Context context, AttributeSet attributeSet) {
        return new AppCompatButton(context, attributeSet);
    }

    @NonNull
    public AppCompatCheckBox c(Context context, AttributeSet attributeSet) {
        return new AppCompatCheckBox(context, attributeSet);
    }

    @NonNull
    public AppCompatRadioButton d(Context context, AttributeSet attributeSet) {
        return new AppCompatRadioButton(context, attributeSet);
    }

    @NonNull
    public AppCompatTextView e(Context context, AttributeSet attributeSet) {
        return new AppCompatTextView(context, attributeSet);
    }

    public final View f(Context context, String str, String str2) throws ClassNotFoundException, InflateException {
        String str3;
        g<String, Constructor<? extends View>> gVar = f453e;
        Constructor<? extends View> orDefault = gVar.getOrDefault(str, null);
        if (orDefault == null) {
            if (str2 != null) {
                try {
                    str3 = str2 + str;
                } catch (Exception unused) {
                    return null;
                }
            } else {
                str3 = str;
            }
            orDefault = Class.forName(str3, false, context.getClassLoader()).asSubclass(View.class).getConstructor(f450b);
            gVar.put(str, orDefault);
        }
        orDefault.setAccessible(true);
        return (View) orDefault.newInstance(this.f454a);
    }

    public final void g(View view, String str) {
        if (view == null) {
            throw new IllegalStateException(getClass().getName() + " asked to inflate view for <" + str + ">, but returned null");
        }
    }
}
