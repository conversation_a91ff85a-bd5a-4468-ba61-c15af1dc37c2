<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="a_code_note_1">Press</string>
    <string name="a_code_note_2">to cancel</string>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="actionbar_button_up_description">Back</string>
    <string name="afternoon">Afternoon</string>
    <string name="am">AM</string>
    <string name="cast_ad_name_box">Xiaomi Box</string>
    <string name="cast_ad_name_tv">Xiaomi TV</string>
    <string name="check_auth_accept">Allow</string>
    <string name="check_auth_reject">Reject</string>
    <string name="check_auth_subtitle2">%s is requesting to control this device. Do you want to allow it?</string>
    <string name="check_auth_subtitle_unknown">A new device is requesting to control this device. Do you want to allow it?</string>
    <string name="check_auth_title">Security Alert</string>
    <string name="clearable_edittext_clear_description">Clear</string>
    <string name="close">Close</string>
    <string name="close_in_seconds_last">seconds</string>
    <string name="close_in_seconds_pre">Close in</string>
    <string name="confirm">Confirm</string>
    <string name="date_picker_label_day" />
    <string name="date_picker_label_month" />
    <string name="date_picker_label_year" />
    <string name="date_picker_lunar">Chinese Traditional Calendar</string>
    <string name="date_time_picker_dialog_title">Set date and time</string>
    <string name="default_client_name" />
    <string name="default_device_name">Xiaomi Box</string>
    <string name="default_music_title">Music</string>
    <string name="default_video_title">Video</string>
    <string name="early_morning">Morning</string>
    <string name="empty" />
    <string name="eras_ad">CE</string>
    <string name="eras_bc">BCE</string>
    <string name="evening">Afternoon</string>
    <string name="fmt_chinese_date">N月e</string>
    <string name="fmt_date">D</string>
    <string name="fmt_date_day">d</string>
    <string name="fmt_date_long_month">MMMM</string>
    <string name="fmt_date_long_month_day">MMMM d</string>
    <string name="fmt_date_long_year_month">MMMM yyyy</string>
    <string name="fmt_date_long_year_month_day">MMMM d, yyyy</string>
    <string name="fmt_date_numeric_day">d</string>
    <string name="fmt_date_numeric_month">M</string>
    <string name="fmt_date_numeric_month_day">M-d</string>
    <string name="fmt_date_numeric_year">yyyy</string>
    <string name="fmt_date_numeric_year_month">M-yyyy</string>
    <string name="fmt_date_numeric_year_month_day">M-d-yyyy</string>
    <string name="fmt_date_short_month">MMM</string>
    <string name="fmt_date_short_month_day">MMM d</string>
    <string name="fmt_date_short_year_month">MMM yyyy</string>
    <string name="fmt_date_short_year_month_day">MMM d, yyyy</string>
    <string name="fmt_date_time">D T</string>
    <string name="fmt_date_time_timezone">D T z</string>
    <string name="fmt_date_timezone">D z</string>
    <string name="fmt_date_year">yyyy</string>
    <string name="fmt_time">T</string>
    <string name="fmt_time_12hour">h \'o\'\'clock\'</string>
    <string name="fmt_time_12hour_minute">h:mm</string>
    <string name="fmt_time_12hour_minute_pm">h:mm a</string>
    <string name="fmt_time_12hour_minute_second">h:mm:ms</string>
    <string name="fmt_time_12hour_minute_second_millis">h:mm:ss.S</string>
    <string name="fmt_time_12hour_minute_second_millis_pm">h:mm:ss.S a</string>
    <string name="fmt_time_12hour_minute_second_pm">h:mm:ss a</string>
    <string name="fmt_time_12hour_pm">ha</string>
    <string name="fmt_time_24hour">H \'o\'\'clock\'</string>
    <string name="fmt_time_24hour_minute">H:mm</string>
    <string name="fmt_time_24hour_minute_second">H:mm:ss</string>
    <string name="fmt_time_24hour_minute_second_millis">H:mm:ss.S</string>
    <string name="fmt_time_millis">S\'ms\'</string>
    <string name="fmt_time_minute">m\'min\'</string>
    <string name="fmt_time_minute_second">m\'min\' s\'s\'</string>
    <string name="fmt_time_minute_second_millis">m\'min\' s.S\'s\'</string>
    <string name="fmt_time_second">s\'s\'</string>
    <string name="fmt_time_second_millis">s.S\'s\'</string>
    <string name="fmt_time_timezone">T z</string>
    <string name="fmt_timezone">z</string>
    <string name="fmt_weekday">W</string>
    <string name="fmt_weekday_date">W, D</string>
    <string name="fmt_weekday_date_time">W, D T</string>
    <string name="fmt_weekday_date_time_timezone">W, D T z</string>
    <string name="fmt_weekday_date_timezone">W, D z</string>
    <string name="fmt_weekday_long">EEEE</string>
    <string name="fmt_weekday_short">E</string>
    <string name="fmt_weekday_time">W T</string>
    <string name="fmt_weekday_time_timezone">W T z</string>
    <string name="fmt_weekday_timezone">W z</string>
    <string name="friday">Friday</string>
    <string name="friday_short">Fri</string>
    <string name="friday_shortest">Fri</string>
    <string name="midnight">Night</string>
    <string name="miliao">MiTalk</string>
    <string name="miuix_access_state_desc">Selected</string>
    <string name="miuix_appcompat_action_mode_deselect_all">Deselect</string>
    <string name="miuix_appcompat_action_mode_select_all">Select all</string>
    <string name="miuix_appcompat_action_mode_title_empty">Select items</string>
    <string name="miuix_appcompat_actionbar_immersion_button_more_description">More</string>
    <string name="miuix_appcompat_cancel_description">Cancel</string>
    <string name="miuix_appcompat_confirm_description">Confirm</string>
    <string name="miuix_appcompat_delete_description">Delete</string>
    <string name="miuix_appcompat_deselect_all">Deselect</string>
    <string name="miuix_appcompat_deselect_all_description">Deselect all</string>
    <string name="miuix_appcompat_search_action_mode_cancel">Cancel</string>
    <string name="miuix_appcompat_search_input_description">Search</string>
    <string name="miuix_appcompat_select_all">Select all</string>
    <string name="miuix_appcompat_select_all_description">Select all</string>
    <string name="miuix_appcompat_select_item">Select items</string>
    <string name="miuix_sbl_tracking_progress_labe_pull_to_refresh">Pull down to refresh</string>
    <string name="miuix_sbl_tracking_progress_labe_refreshed">Refreshed successfully</string>
    <string name="miuix_sbl_tracking_progress_labe_refreshing">Refreshing…</string>
    <string name="miuix_sbl_tracking_progress_labe_release_to_refresh">Release to refresh</string>
    <string name="miuix_sbl_tracking_progress_labe_up_nodata">That\'s all for now</string>
    <string name="miuix_sbl_tracking_progress_labe_up_none">That\'s all for now</string>
    <string name="miuix_sbl_tracking_progress_labe_up_refresh">Just a sec…</string>
    <string name="miuix_sbl_tracking_progress_labe_up_refresh_fail">Couldn\'t load items</string>
    <string name="monday">Monday</string>
    <string name="monday_short">Mon</string>
    <string name="monday_shortest">Mon</string>
    <string name="month_april">April</string>
    <string name="month_april_short">Apr</string>
    <string name="month_april_shortest">Apr</string>
    <string name="month_august">August</string>
    <string name="month_august_short">Aug</string>
    <string name="month_august_shortest">Aug</string>
    <string name="month_december">December</string>
    <string name="month_december_short">Dec</string>
    <string name="month_december_shortest">Dec</string>
    <string name="month_february">February</string>
    <string name="month_february_short">Feb</string>
    <string name="month_february_shortest">Feb</string>
    <string name="month_january">January</string>
    <string name="month_january_short">Jan</string>
    <string name="month_january_shortest">Jan</string>
    <string name="month_july">July</string>
    <string name="month_july_short">Jul</string>
    <string name="month_july_shortest">Jul</string>
    <string name="month_june">June</string>
    <string name="month_june_short">Jun</string>
    <string name="month_june_shortest">Jun</string>
    <string name="month_march">March</string>
    <string name="month_march_short">Mar</string>
    <string name="month_march_shortest">Mar</string>
    <string name="month_may">May</string>
    <string name="month_may_short">May</string>
    <string name="month_may_shortest">May</string>
    <string name="month_november">November</string>
    <string name="month_november_short">Nov</string>
    <string name="month_november_shortest">Nov</string>
    <string name="month_october">October</string>
    <string name="month_october_short">Oct</string>
    <string name="month_october_shortest">Oct</string>
    <string name="month_september">September</string>
    <string name="month_september_short">Sep</string>
    <string name="month_september_shortest">Sep</string>
    <string name="more">More</string>
    <string name="morning">Morning</string>
    <string name="night">Evening</string>
    <string name="noon">Midday</string>
    <string name="pc">PC</string>
    <string name="pm">PM</string>
    <string name="saturday">Saturday</string>
    <string name="saturday_short">Sat</string>
    <string name="saturday_shortest">Sat</string>
    <string name="screen_capturing">Capturing screenshot</string>
    <string name="search_menu_title">Search</string>
    <string name="security_confirmation_button_cancel">Reject</string>
    <string name="security_confirmation_button_confirm">Allow</string>
    <string name="security_confirmation_main">Do you want to allow this action?</string>
    <string name="security_confirmation_title_box">A new device is trying to cast to your box</string>
    <string name="security_confirmation_title_tv">A new device is trying to cast to your TV</string>
    <string name="security_password_main">Press the directional buttons on your remote in the order shown to accept the casting</string>
    <string name="security_password_title_box">A new device is trying to control your box</string>
    <string name="security_password_title_tv">A new device is trying to control your TV</string>
    <string name="security_toast_note_1_last">minutes</string>
    <string name="security_toast_note_1_pre">Casting verification is too frequent, please try again in</string>
    <string name="security_toast_note_2">Casting verification is too frequent, please try again later</string>
    <string name="security_toast_note_3">Verification is too frequent, please try again later</string>
    <string name="security_toast_note_3_last">minutes</string>
    <string name="security_toast_note_3_pre">Verification is too frequent, please try again in</string>
    <string name="security_too_many_times_hint_2">Casting verification is too frequent, please try again later</string>
    <string name="security_verifycode_title_tv">Enter the connection code</string>
    <string name="send">Send</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="sunday">Sunday</string>
    <string name="sunday_short">Sun</string>
    <string name="sunday_shortest">Sun</string>
    <string name="the_anniversary_of_lifting_martial_law">Anniversary of Lifting Martial Law</string>
    <string name="the_anti_aggression_day">Anti-Aggression Day</string>
    <string name="the_arbor_day">Arbor Day</string>
    <string name="the_armed_forces_day">Armed Forces Day</string>
    <string name="the_armys_day">PLA Day</string>
    <string name="the_childrens_day">Children\'s Day</string>
    <string name="the_chinese_youth_day">Youth Day</string>
    <string name="the_christmas_day">Christmas</string>
    <string name="the_double_ninth_festival">Double Ninth Festival</string>
    <string name="the_dragon_boat_festival">Dragon Boat Festival</string>
    <string name="the_easter_day">Easter</string>
    <string name="the_eve_of_the_spring_festival">Eve of Chinese New Year</string>
    <string name="the_fifth_day">Fifth day of Chinese New Year</string>
    <string name="the_fools_day">April Fool\'s Day</string>
    <string name="the_forth_day">Fourth day of Chinese New Year</string>
    <string name="the_hksar_establishment_day">Hong Kong SAR Establishment Day</string>
    <string name="the_international_womens_day">Women\'s Day</string>
    <string name="the_laba_festival">Laba Festival</string>
    <string name="the_labour_day">Labor Day</string>
    <string name="the_lantern_festival">Lantern Festival</string>
    <string name="the_mid_autumn_festival">Mid-Autumn Festival</string>
    <string name="the_national_day">National Day</string>
    <string name="the_national_father_day">Sun Yat-sen\'s Birthday</string>
    <string name="the_new_years_day">New Year</string>
    <string name="the_night_of_sevens">Qixi Festival</string>
    <string name="the_partys_day">CPC Day</string>
    <string name="the_peace_day">Remembrance Day</string>
    <string name="the_retrocession_day">Retrocession Day</string>
    <string name="the_second_day">Second day of Chinese New Year</string>
    <string name="the_seventh_day">Seventh day of Chinese New Year</string>
    <string name="the_sixth_day">Sixth day of Chinese New Year</string>
    <string name="the_spirit_festival">Ghost Festival</string>
    <string name="the_spring_festival">Chinese New Year</string>
    <string name="the_teachers_day">Teachers\' Day</string>
    <string name="the_third_day">Third day of Chinese New Year</string>
    <string name="the_tw_childrens_day">Children\'s Day</string>
    <string name="the_tw_youth_day">Youth Day</string>
    <string name="the_united_nations_day">United Nations Day</string>
    <string name="the_valentines_day">Valentine\'s Day</string>
    <string name="the_water_lantern_festival">Water Lantern Festival</string>
    <string name="thursday">Thursday</string>
    <string name="thursday_short">Thu</string>
    <string name="thursday_shortest">Thu</string>
    <string name="time_picker_dialog_title">Set time</string>
    <string name="time_picker_label_hour">H</string>
    <string name="time_picker_label_minute">M</string>
    <string name="today">Today</string>
    <string name="tomorrow">Tomorrow</string>
    <string name="tuesday">Tuesday</string>
    <string name="tuesday_short">Tue</string>
    <string name="tuesday_shortest">Tue</string>
    <string name="wednesday">Wednesday</string>
    <string name="wednesday_short">Wed</string>
    <string name="wednesday_shortest">Wed</string>
    <string name="weixin">WeChat</string>
    <string name="yesterday">Yesterday</string>
</resources>
