package androidx.appcompat.app;

import android.view.View;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import j0.m;
import j0.o;

/* compiled from: AppCompatDelegateImpl */
public class i implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ AppCompatDelegateImpl f444a;

    /* compiled from: AppCompatDelegateImpl */
    public class a extends o {
        public a() {
        }

        @Override // j0.n
        public void d(View view) {
            i.this.f444a.f382o.setAlpha(1.0f);
            i.this.f444a.f388r.d(null);
            i.this.f444a.f388r = null;
        }

        @Override // j0.n, j0.o
        public void e(View view) {
            i.this.f444a.f382o.setVisibility(0);
        }
    }

    public i(AppCompatDelegateImpl appCompatDelegateImpl) {
        this.f444a = appCompatDelegateImpl;
    }

    public void run() {
        AppCompatDelegateImpl appCompatDelegateImpl = this.f444a;
        appCompatDelegateImpl.f384p.showAtLocation(appCompatDelegateImpl.f382o, 55, 0, 0);
        this.f444a.L();
        if (this.f444a.Y()) {
            this.f444a.f382o.setAlpha(Constant.VOLUME_FLOAT_MIN);
            AppCompatDelegateImpl appCompatDelegateImpl2 = this.f444a;
            m a10 = ViewCompat.a(appCompatDelegateImpl2.f382o);
            a10.a(1.0f);
            appCompatDelegateImpl2.f388r = a10;
            m mVar = this.f444a.f388r;
            a aVar = new a();
            View view = mVar.f6912a.get();
            if (view != null) {
                mVar.e(view, aVar);
                return;
            }
            return;
        }
        this.f444a.f382o.setAlpha(1.0f);
        this.f444a.f382o.setVisibility(0);
    }
}
