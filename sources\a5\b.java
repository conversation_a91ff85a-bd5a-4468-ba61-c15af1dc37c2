package a5;

import c5.m;
import c5.n;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import s0.q;

/* compiled from: AbstractReferenceCounted */
public abstract class b implements k {

    /* renamed from: a  reason: collision with root package name */
    public static final long f71a;

    /* renamed from: b  reason: collision with root package name */
    public static final AtomicIntegerFieldUpdater<b> f72b = AtomicIntegerFieldUpdater.newUpdater(b.class, "refCnt");

    /* renamed from: c  reason: collision with root package name */
    public static final q f73c = new a();
    private volatile int refCnt = 2;

    /* compiled from: AbstractReferenceCounted */
    public static class a extends q {
        @Override // s0.q
        public long b2() {
            return b.f71a;
        }

        @Override // s0.q
        public AtomicIntegerFieldUpdater<b> c2() {
            return b.f72b;
        }
    }

    static {
        long j10;
        try {
            if (m.i()) {
                j10 = n.A(b.class.getDeclaredField("refCnt"));
                f71a = j10;
            }
        } catch (Throwable unused) {
        }
        j10 = -1;
        f71a = j10;
    }

    public b() {
        Objects.requireNonNull(f73c);
    }

    public abstract void a();

    @Override // a5.k
    public int refCnt() {
        return f73c.m1(this);
    }

    @Override // a5.k
    public boolean release() {
        boolean n12 = f73c.n1(this);
        if (n12) {
            a();
        }
        return n12;
    }

    @Override // a5.k
    public k retain() {
        f73c.p1(this, 1, 2);
        return this;
    }

    @Override // a5.k
    public k touch() {
        return touch(null);
    }

    @Override // a5.k
    public boolean release(int i10) {
        boolean o12 = f73c.o1(this, i10);
        if (o12) {
            a();
        }
        return o12;
    }

    @Override // a5.k
    public k retain(int i10) {
        q qVar = f73c;
        Objects.requireNonNull(qVar);
        d2.a.d(i10, "increment");
        qVar.p1(this, i10, i10 << 1);
        return this;
    }
}
