package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StringRes;
import androidx.annotation.StyleRes;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$styleable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.f;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.view.menu.k;
import androidx.appcompat.widget.ActionMenuView;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.m;
import java.util.ArrayList;
import java.util.List;
import java.util.WeakHashMap;
import p.g;

public class Toolbar extends ViewGroup {
    public d A0;
    public h.a B0;
    public d.a C0;
    public boolean D0;
    public final Runnable E0;

    /* renamed from: a  reason: collision with root package name */
    public ActionMenuView f993a;

    /* renamed from: b  reason: collision with root package name */
    public TextView f994b;

    /* renamed from: c  reason: collision with root package name */
    public TextView f995c;

    /* renamed from: d  reason: collision with root package name */
    public ImageButton f996d;

    /* renamed from: e  reason: collision with root package name */
    public ImageView f997e;

    /* renamed from: f  reason: collision with root package name */
    public Drawable f998f;

    /* renamed from: g  reason: collision with root package name */
    public CharSequence f999g;
    public ImageButton h;

    /* renamed from: i  reason: collision with root package name */
    public View f1000i;

    /* renamed from: j  reason: collision with root package name */
    public Context f1001j;

    /* renamed from: k  reason: collision with root package name */
    public int f1002k;

    /* renamed from: l  reason: collision with root package name */
    public int f1003l;

    /* renamed from: m  reason: collision with root package name */
    public int f1004m;

    /* renamed from: m0  reason: collision with root package name */
    public int f1005m0;

    /* renamed from: n  reason: collision with root package name */
    public int f1006n;

    /* renamed from: n0  reason: collision with root package name */
    public CharSequence f1007n0;

    /* renamed from: o  reason: collision with root package name */
    public int f1008o;

    /* renamed from: o0  reason: collision with root package name */
    public CharSequence f1009o0;

    /* renamed from: p  reason: collision with root package name */
    public int f1010p;

    /* renamed from: p0  reason: collision with root package name */
    public ColorStateList f1011p0;

    /* renamed from: q  reason: collision with root package name */
    public int f1012q;

    /* renamed from: q0  reason: collision with root package name */
    public ColorStateList f1013q0;

    /* renamed from: r  reason: collision with root package name */
    public int f1014r;

    /* renamed from: r0  reason: collision with root package name */
    public boolean f1015r0;

    /* renamed from: s  reason: collision with root package name */
    public int f1016s;

    /* renamed from: s0  reason: collision with root package name */
    public boolean f1017s0;

    /* renamed from: t  reason: collision with root package name */
    public d0 f1018t;

    /* renamed from: t0  reason: collision with root package name */
    public final ArrayList<View> f1019t0;

    /* renamed from: u0  reason: collision with root package name */
    public final ArrayList<View> f1020u0;

    /* renamed from: v0  reason: collision with root package name */
    public final int[] f1021v0;

    /* renamed from: w0  reason: collision with root package name */
    public e f1022w0;

    /* renamed from: x  reason: collision with root package name */
    public int f1023x;

    /* renamed from: x0  reason: collision with root package name */
    public final ActionMenuView.d f1024x0;

    /* renamed from: y  reason: collision with root package name */
    public int f1025y;

    /* renamed from: y0  reason: collision with root package name */
    public o0 f1026y0;

    /* renamed from: z0  reason: collision with root package name */
    public ActionMenuPresenter f1027z0;

    public class a implements ActionMenuView.d {
        public a() {
        }
    }

    public class b implements Runnable {
        public b() {
        }

        public void run() {
            Toolbar.this.u();
        }
    }

    public class c implements View.OnClickListener {
        public c() {
        }

        public void onClick(View view) {
            f fVar;
            d dVar = Toolbar.this.A0;
            if (dVar == null) {
                fVar = null;
            } else {
                fVar = dVar.f1035b;
            }
            if (fVar != null) {
                fVar.collapseActionView();
            }
        }
    }

    public class d implements h {

        /* renamed from: a  reason: collision with root package name */
        public androidx.appcompat.view.menu.d f1034a;

        /* renamed from: b  reason: collision with root package name */
        public f f1035b;

        public d() {
        }

        @Override // androidx.appcompat.view.menu.h
        public void a(androidx.appcompat.view.menu.d dVar, boolean z10) {
        }

        @Override // androidx.appcompat.view.menu.h
        public void b(boolean z10) {
            if (this.f1035b != null) {
                androidx.appcompat.view.menu.d dVar = this.f1034a;
                boolean z11 = false;
                if (dVar != null) {
                    int size = dVar.size();
                    int i10 = 0;
                    while (true) {
                        if (i10 >= size) {
                            break;
                        } else if (this.f1034a.getItem(i10) == this.f1035b) {
                            z11 = true;
                            break;
                        } else {
                            i10++;
                        }
                    }
                }
                if (!z11) {
                    h(this.f1034a, this.f1035b);
                }
            }
        }

        @Override // androidx.appcompat.view.menu.h
        public boolean c() {
            return false;
        }

        @Override // androidx.appcompat.view.menu.h
        public void d(Context context, androidx.appcompat.view.menu.d dVar) {
            f fVar;
            androidx.appcompat.view.menu.d dVar2 = this.f1034a;
            if (!(dVar2 == null || (fVar = this.f1035b) == null)) {
                dVar2.d(fVar);
            }
            this.f1034a = dVar;
        }

        @Override // androidx.appcompat.view.menu.h
        public void e(Parcelable parcelable) {
        }

        @Override // androidx.appcompat.view.menu.h
        public boolean f(k kVar) {
            return false;
        }

        @Override // androidx.appcompat.view.menu.h
        public Parcelable g() {
            return null;
        }

        @Override // androidx.appcompat.view.menu.h
        public int getId() {
            return 0;
        }

        @Override // androidx.appcompat.view.menu.h
        public boolean h(androidx.appcompat.view.menu.d dVar, f fVar) {
            View view = Toolbar.this.f1000i;
            if (view instanceof p.b) {
                ((p.b) view).e();
            }
            Toolbar toolbar = Toolbar.this;
            toolbar.removeView(toolbar.f1000i);
            Toolbar toolbar2 = Toolbar.this;
            toolbar2.removeView(toolbar2.h);
            Toolbar toolbar3 = Toolbar.this;
            toolbar3.f1000i = null;
            int size = toolbar3.f1020u0.size();
            while (true) {
                size--;
                if (size >= 0) {
                    toolbar3.addView(toolbar3.f1020u0.get(size));
                } else {
                    toolbar3.f1020u0.clear();
                    this.f1035b = null;
                    Toolbar.this.requestLayout();
                    fVar.C = false;
                    fVar.f642n.p(false);
                    return true;
                }
            }
        }

        @Override // androidx.appcompat.view.menu.h
        public boolean i(androidx.appcompat.view.menu.d dVar, f fVar) {
            Toolbar.this.c();
            ViewParent parent = Toolbar.this.h.getParent();
            Toolbar toolbar = Toolbar.this;
            if (parent != toolbar) {
                if (parent instanceof ViewGroup) {
                    ((ViewGroup) parent).removeView(toolbar.h);
                }
                Toolbar toolbar2 = Toolbar.this;
                toolbar2.addView(toolbar2.h);
            }
            Toolbar.this.f1000i = fVar.getActionView();
            this.f1035b = fVar;
            ViewParent parent2 = Toolbar.this.f1000i.getParent();
            Toolbar toolbar3 = Toolbar.this;
            if (parent2 != toolbar3) {
                if (parent2 instanceof ViewGroup) {
                    ((ViewGroup) parent2).removeView(toolbar3.f1000i);
                }
                LayoutParams h = Toolbar.this.generateDefaultLayoutParams();
                Toolbar toolbar4 = Toolbar.this;
                h.f328a = 8388611 | (toolbar4.f1006n & 112);
                h.f1028b = 2;
                toolbar4.f1000i.setLayoutParams(h);
                Toolbar toolbar5 = Toolbar.this;
                toolbar5.addView(toolbar5.f1000i);
            }
            Toolbar toolbar6 = Toolbar.this;
            int childCount = toolbar6.getChildCount();
            while (true) {
                childCount--;
                if (childCount < 0) {
                    break;
                }
                View childAt = toolbar6.getChildAt(childCount);
                if (!(((LayoutParams) childAt.getLayoutParams()).f1028b == 2 || childAt == toolbar6.f993a)) {
                    toolbar6.removeViewAt(childCount);
                    toolbar6.f1020u0.add(childAt);
                }
            }
            Toolbar.this.requestLayout();
            fVar.C = true;
            fVar.f642n.p(false);
            View view = Toolbar.this.f1000i;
            if (view instanceof p.b) {
                ((p.b) view).c();
            }
            return true;
        }
    }

    public interface e {
    }

    public Toolbar(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.toolbarStyle);
    }

    private MenuInflater getMenuInflater() {
        return new g(getContext());
    }

    public final void a(List<View> list, int i10) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        boolean z10 = getLayoutDirection() == 1;
        int childCount = getChildCount();
        int absoluteGravity = Gravity.getAbsoluteGravity(i10, getLayoutDirection());
        list.clear();
        if (z10) {
            for (int i11 = childCount - 1; i11 >= 0; i11--) {
                View childAt = getChildAt(i11);
                LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
                if (layoutParams.f1028b == 0 && t(childAt) && j(layoutParams.f328a) == absoluteGravity) {
                    list.add(childAt);
                }
            }
            return;
        }
        for (int i12 = 0; i12 < childCount; i12++) {
            View childAt2 = getChildAt(i12);
            LayoutParams layoutParams2 = (LayoutParams) childAt2.getLayoutParams();
            if (layoutParams2.f1028b == 0 && t(childAt2) && j(layoutParams2.f328a) == absoluteGravity) {
                list.add(childAt2);
            }
        }
    }

    public final void b(View view, boolean z10) {
        LayoutParams layoutParams;
        ViewGroup.LayoutParams layoutParams2 = view.getLayoutParams();
        if (layoutParams2 == null) {
            layoutParams = generateDefaultLayoutParams();
        } else if (!checkLayoutParams(layoutParams2)) {
            layoutParams = generateLayoutParams(layoutParams2);
        } else {
            layoutParams = (LayoutParams) layoutParams2;
        }
        layoutParams.f1028b = 1;
        if (!z10 || this.f1000i == null) {
            addView(view, layoutParams);
            return;
        }
        view.setLayoutParams(layoutParams);
        this.f1020u0.add(view);
    }

    public void c() {
        if (this.h == null) {
            AppCompatImageButton appCompatImageButton = new AppCompatImageButton(getContext(), null, R$attr.toolbarNavigationButtonStyle);
            this.h = appCompatImageButton;
            appCompatImageButton.setImageDrawable(this.f998f);
            this.h.setContentDescription(this.f999g);
            LayoutParams h6 = generateDefaultLayoutParams();
            h6.f328a = 8388611 | (this.f1006n & 112);
            h6.f1028b = 2;
            this.h.setLayoutParams(h6);
            this.h.setOnClickListener(new c());
        }
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return super.checkLayoutParams(layoutParams) && (layoutParams instanceof LayoutParams);
    }

    public final void d() {
        if (this.f1018t == null) {
            this.f1018t = new d0();
        }
    }

    public final void e() {
        f();
        ActionMenuView actionMenuView = this.f993a;
        if (actionMenuView.f770p == null) {
            androidx.appcompat.view.menu.d dVar = (androidx.appcompat.view.menu.d) actionMenuView.getMenu();
            if (this.A0 == null) {
                this.A0 = new d();
            }
            this.f993a.setExpandedActionViewsExclusive(true);
            dVar.b(this.A0, this.f1001j);
        }
    }

    public final void f() {
        if (this.f993a == null) {
            ActionMenuView actionMenuView = new ActionMenuView(getContext(), null);
            this.f993a = actionMenuView;
            actionMenuView.setPopupTheme(this.f1002k);
            this.f993a.setOnMenuItemClickListener(this.f1024x0);
            ActionMenuView actionMenuView2 = this.f993a;
            h.a aVar = this.B0;
            d.a aVar2 = this.C0;
            actionMenuView2.f777x = aVar;
            actionMenuView2.f778y = aVar2;
            LayoutParams h6 = generateDefaultLayoutParams();
            h6.f328a = 8388613 | (this.f1006n & 112);
            this.f993a.setLayoutParams(h6);
            b(this.f993a, false);
        }
    }

    public final void g() {
        if (this.f996d == null) {
            this.f996d = new AppCompatImageButton(getContext(), null, R$attr.toolbarNavigationButtonStyle);
            LayoutParams h6 = generateDefaultLayoutParams();
            h6.f328a = 8388611 | (this.f1006n & 112);
            this.f996d.setLayoutParams(h6);
        }
    }

    @Nullable
    public CharSequence getCollapseContentDescription() {
        ImageButton imageButton = this.h;
        if (imageButton != null) {
            return imageButton.getContentDescription();
        }
        return null;
    }

    @Nullable
    public Drawable getCollapseIcon() {
        ImageButton imageButton = this.h;
        if (imageButton != null) {
            return imageButton.getDrawable();
        }
        return null;
    }

    public int getContentInsetEnd() {
        d0 d0Var = this.f1018t;
        if (d0Var != null) {
            return d0Var.f1074g ? d0Var.f1068a : d0Var.f1069b;
        }
        return 0;
    }

    public int getContentInsetEndWithActions() {
        int i10 = this.f1025y;
        return i10 != Integer.MIN_VALUE ? i10 : getContentInsetEnd();
    }

    public int getContentInsetLeft() {
        d0 d0Var = this.f1018t;
        if (d0Var != null) {
            return d0Var.f1068a;
        }
        return 0;
    }

    public int getContentInsetRight() {
        d0 d0Var = this.f1018t;
        if (d0Var != null) {
            return d0Var.f1069b;
        }
        return 0;
    }

    public int getContentInsetStart() {
        d0 d0Var = this.f1018t;
        if (d0Var != null) {
            return d0Var.f1074g ? d0Var.f1069b : d0Var.f1068a;
        }
        return 0;
    }

    public int getContentInsetStartWithNavigation() {
        int i10 = this.f1023x;
        return i10 != Integer.MIN_VALUE ? i10 : getContentInsetStart();
    }

    public int getCurrentContentInsetEnd() {
        androidx.appcompat.view.menu.d dVar;
        ActionMenuView actionMenuView = this.f993a;
        if ((actionMenuView == null || (dVar = actionMenuView.f770p) == null || !dVar.hasVisibleItems()) ? false : true) {
            return Math.max(getContentInsetEnd(), Math.max(this.f1025y, 0));
        }
        return getContentInsetEnd();
    }

    public int getCurrentContentInsetLeft() {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        if (getLayoutDirection() == 1) {
            return getCurrentContentInsetEnd();
        }
        return getCurrentContentInsetStart();
    }

    public int getCurrentContentInsetRight() {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        if (getLayoutDirection() == 1) {
            return getCurrentContentInsetStart();
        }
        return getCurrentContentInsetEnd();
    }

    public int getCurrentContentInsetStart() {
        if (getNavigationIcon() != null) {
            return Math.max(getContentInsetStart(), Math.max(this.f1023x, 0));
        }
        return getContentInsetStart();
    }

    public Drawable getLogo() {
        ImageView imageView = this.f997e;
        if (imageView != null) {
            return imageView.getDrawable();
        }
        return null;
    }

    public CharSequence getLogoDescription() {
        ImageView imageView = this.f997e;
        if (imageView != null) {
            return imageView.getContentDescription();
        }
        return null;
    }

    public Menu getMenu() {
        e();
        return this.f993a.getMenu();
    }

    @Nullable
    public CharSequence getNavigationContentDescription() {
        ImageButton imageButton = this.f996d;
        if (imageButton != null) {
            return imageButton.getContentDescription();
        }
        return null;
    }

    @Nullable
    public Drawable getNavigationIcon() {
        ImageButton imageButton = this.f996d;
        if (imageButton != null) {
            return imageButton.getDrawable();
        }
        return null;
    }

    public ActionMenuPresenter getOuterActionMenuPresenter() {
        return this.f1027z0;
    }

    @Nullable
    public Drawable getOverflowIcon() {
        e();
        return this.f993a.getOverflowIcon();
    }

    /* access modifiers changed from: package-private */
    public Context getPopupContext() {
        return this.f1001j;
    }

    public int getPopupTheme() {
        return this.f1002k;
    }

    public CharSequence getSubtitle() {
        return this.f1009o0;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.TESTS})
    public final TextView getSubtitleTextView() {
        return this.f995c;
    }

    public CharSequence getTitle() {
        return this.f1007n0;
    }

    public int getTitleMarginBottom() {
        return this.f1016s;
    }

    public int getTitleMarginEnd() {
        return this.f1012q;
    }

    public int getTitleMarginStart() {
        return this.f1010p;
    }

    public int getTitleMarginTop() {
        return this.f1014r;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.TESTS})
    public final TextView getTitleTextView() {
        return this.f994b;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public t getWrapper() {
        if (this.f1026y0 == null) {
            this.f1026y0 = new o0(this, true);
        }
        return this.f1026y0;
    }

    /* renamed from: h */
    public LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-2, -2);
    }

    /* renamed from: i */
    public LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof LayoutParams) {
            return new LayoutParams((LayoutParams) layoutParams);
        }
        if (layoutParams instanceof ActionBar.LayoutParams) {
            return new LayoutParams((ActionBar.LayoutParams) layoutParams);
        }
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return new LayoutParams((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return new LayoutParams(layoutParams);
    }

    public final int j(int i10) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int layoutDirection = getLayoutDirection();
        int absoluteGravity = Gravity.getAbsoluteGravity(i10, layoutDirection) & 7;
        if (absoluteGravity == 1 || absoluteGravity == 3 || absoluteGravity == 5) {
            return absoluteGravity;
        }
        return layoutDirection == 1 ? 5 : 3;
    }

    public final int k(View view, int i10) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        int measuredHeight = view.getMeasuredHeight();
        int i11 = i10 > 0 ? (measuredHeight - i10) / 2 : 0;
        int i12 = layoutParams.f328a & 112;
        if (!(i12 == 16 || i12 == 48 || i12 == 80)) {
            i12 = this.f1005m0 & 112;
        }
        if (i12 == 48) {
            return getPaddingTop() - i11;
        }
        if (i12 == 80) {
            return (((getHeight() - getPaddingBottom()) - measuredHeight) - ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin) - i11;
        }
        int paddingTop = getPaddingTop();
        int paddingBottom = getPaddingBottom();
        int height = getHeight();
        int i13 = (((height - paddingTop) - paddingBottom) - measuredHeight) / 2;
        int i14 = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin;
        if (i13 < i14) {
            i13 = i14;
        } else {
            int i15 = (((height - paddingBottom) - measuredHeight) - i13) - paddingTop;
            int i16 = ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
            if (i15 < i16) {
                i13 = Math.max(0, i13 - (i16 - i15));
            }
        }
        return paddingTop + i13;
    }

    public final int l(View view) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        return marginLayoutParams.getMarginStart() + marginLayoutParams.getMarginEnd();
    }

    public final int m(View view) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        return marginLayoutParams.topMargin + marginLayoutParams.bottomMargin;
    }

    public final boolean n(View view) {
        return view.getParent() == this || this.f1020u0.contains(view);
    }

    public boolean o() {
        ActionMenuView actionMenuView = this.f993a;
        if (actionMenuView != null) {
            ActionMenuPresenter actionMenuPresenter = actionMenuView.f776t;
            if (actionMenuPresenter != null && actionMenuPresenter.o()) {
                return true;
            }
        }
        return false;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCallbacks(this.E0);
    }

    public boolean onHoverEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 9) {
            this.f1017s0 = false;
        }
        if (!this.f1017s0) {
            boolean onHoverEvent = super.onHoverEvent(motionEvent);
            if (actionMasked == 9 && !onHoverEvent) {
                this.f1017s0 = true;
            }
        }
        if (actionMasked == 10 || actionMasked == 3) {
            this.f1017s0 = false;
        }
        return true;
    }

    /* JADX WARNING: Removed duplicated region for block: B:102:0x02a1 A[LOOP:0: B:101:0x029f->B:102:0x02a1, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:105:0x02c3 A[LOOP:1: B:104:0x02c1->B:105:0x02c3, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:108:0x02e8 A[LOOP:2: B:107:0x02e6->B:108:0x02e8, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:111:0x0329  */
    /* JADX WARNING: Removed duplicated region for block: B:116:0x033d A[LOOP:3: B:115:0x033b->B:116:0x033d, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x0061  */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x0078  */
    /* JADX WARNING: Removed duplicated region for block: B:27:0x00b5  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x00cc  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x00e9  */
    /* JADX WARNING: Removed duplicated region for block: B:38:0x0102  */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x0107  */
    /* JADX WARNING: Removed duplicated region for block: B:41:0x011f  */
    /* JADX WARNING: Removed duplicated region for block: B:46:0x012e  */
    /* JADX WARNING: Removed duplicated region for block: B:47:0x0131  */
    /* JADX WARNING: Removed duplicated region for block: B:49:0x0135  */
    /* JADX WARNING: Removed duplicated region for block: B:50:0x0138  */
    /* JADX WARNING: Removed duplicated region for block: B:62:0x0169  */
    /* JADX WARNING: Removed duplicated region for block: B:72:0x01a7  */
    /* JADX WARNING: Removed duplicated region for block: B:74:0x01b8  */
    /* JADX WARNING: Removed duplicated region for block: B:87:0x0227  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onLayout(boolean r21, int r22, int r23, int r24, int r25) {
        /*
        // Method dump skipped, instructions count: 850
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.Toolbar.onLayout(boolean, int, int, int, int):void");
    }

    public void onMeasure(int i10, int i11) {
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        int i17;
        int i18;
        int[] iArr = this.f1021v0;
        boolean b10 = q0.b(this);
        boolean z10 = true;
        int i19 = 0;
        int i20 = !b10 ? 1 : 0;
        if (t(this.f996d)) {
            s(this.f996d, i10, 0, i11, 0, this.f1008o);
            i14 = l(this.f996d) + this.f996d.getMeasuredWidth();
            i13 = Math.max(0, m(this.f996d) + this.f996d.getMeasuredHeight());
            i12 = View.combineMeasuredStates(0, this.f996d.getMeasuredState());
        } else {
            i14 = 0;
            i13 = 0;
            i12 = 0;
        }
        if (t(this.h)) {
            s(this.h, i10, 0, i11, 0, this.f1008o);
            i14 = l(this.h) + this.h.getMeasuredWidth();
            i13 = Math.max(i13, m(this.h) + this.h.getMeasuredHeight());
            i12 = View.combineMeasuredStates(i12, this.h.getMeasuredState());
        }
        int currentContentInsetStart = getCurrentContentInsetStart();
        int max = Math.max(currentContentInsetStart, i14) + 0;
        iArr[b10 ? 1 : 0] = Math.max(0, currentContentInsetStart - i14);
        if (t(this.f993a)) {
            s(this.f993a, i10, max, i11, 0, this.f1008o);
            i15 = l(this.f993a) + this.f993a.getMeasuredWidth();
            i13 = Math.max(i13, m(this.f993a) + this.f993a.getMeasuredHeight());
            i12 = View.combineMeasuredStates(i12, this.f993a.getMeasuredState());
        } else {
            i15 = 0;
        }
        int currentContentInsetEnd = getCurrentContentInsetEnd();
        int max2 = Math.max(currentContentInsetEnd, i15) + max;
        iArr[i20] = Math.max(0, currentContentInsetEnd - i15);
        if (t(this.f1000i)) {
            max2 += r(this.f1000i, i10, max2, i11, 0, iArr);
            i13 = Math.max(i13, m(this.f1000i) + this.f1000i.getMeasuredHeight());
            i12 = View.combineMeasuredStates(i12, this.f1000i.getMeasuredState());
        }
        if (t(this.f997e)) {
            max2 += r(this.f997e, i10, max2, i11, 0, iArr);
            i13 = Math.max(i13, m(this.f997e) + this.f997e.getMeasuredHeight());
            i12 = View.combineMeasuredStates(i12, this.f997e.getMeasuredState());
        }
        int childCount = getChildCount();
        for (int i21 = 0; i21 < childCount; i21++) {
            View childAt = getChildAt(i21);
            if (((LayoutParams) childAt.getLayoutParams()).f1028b == 0 && t(childAt)) {
                max2 += r(childAt, i10, max2, i11, 0, iArr);
                i13 = Math.max(i13, m(childAt) + childAt.getMeasuredHeight());
                i12 = View.combineMeasuredStates(i12, childAt.getMeasuredState());
            }
        }
        int i22 = this.f1014r + this.f1016s;
        int i23 = this.f1010p + this.f1012q;
        if (t(this.f994b)) {
            r(this.f994b, i10, max2 + i23, i11, i22, iArr);
            int l10 = l(this.f994b) + this.f994b.getMeasuredWidth();
            i16 = m(this.f994b) + this.f994b.getMeasuredHeight();
            i18 = View.combineMeasuredStates(i12, this.f994b.getMeasuredState());
            i17 = l10;
        } else {
            i16 = 0;
            i18 = i12;
            i17 = 0;
        }
        if (t(this.f995c)) {
            i17 = Math.max(i17, r(this.f995c, i10, max2 + i23, i11, i16 + i22, iArr));
            i16 = m(this.f995c) + this.f995c.getMeasuredHeight() + i16;
            i18 = View.combineMeasuredStates(i18, this.f995c.getMeasuredState());
        }
        int max3 = Math.max(i13, i16);
        int paddingRight = getPaddingRight() + getPaddingLeft();
        int paddingBottom = getPaddingBottom() + getPaddingTop() + max3;
        int resolveSizeAndState = View.resolveSizeAndState(Math.max(paddingRight + max2 + i17, getSuggestedMinimumWidth()), i10, -16777216 & i18);
        int resolveSizeAndState2 = View.resolveSizeAndState(Math.max(paddingBottom, getSuggestedMinimumHeight()), i11, i18 << 16);
        if (this.D0) {
            int childCount2 = getChildCount();
            int i24 = 0;
            while (true) {
                if (i24 >= childCount2) {
                    break;
                }
                View childAt2 = getChildAt(i24);
                if (t(childAt2) && childAt2.getMeasuredWidth() > 0 && childAt2.getMeasuredHeight() > 0) {
                    break;
                }
                i24++;
            }
        }
        z10 = false;
        if (!z10) {
            i19 = resolveSizeAndState2;
        }
        setMeasuredDimension(resolveSizeAndState, i19);
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        MenuItem findItem;
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.f1662a);
        ActionMenuView actionMenuView = this.f993a;
        androidx.appcompat.view.menu.d dVar = actionMenuView != null ? actionMenuView.f770p : null;
        int i10 = savedState.f1029c;
        if (!(i10 == 0 || this.A0 == null || dVar == null || (findItem = dVar.findItem(i10)) == null)) {
            findItem.expandActionView();
        }
        if (savedState.f1030d) {
            removeCallbacks(this.E0);
            post(this.E0);
        }
    }

    public void onRtlPropertiesChanged(int i10) {
        super.onRtlPropertiesChanged(i10);
        d();
        d0 d0Var = this.f1018t;
        boolean z10 = true;
        if (i10 != 1) {
            z10 = false;
        }
        if (z10 != d0Var.f1074g) {
            d0Var.f1074g = z10;
            if (!d0Var.h) {
                d0Var.f1068a = d0Var.f1072e;
                d0Var.f1069b = d0Var.f1073f;
            } else if (z10) {
                int i11 = d0Var.f1071d;
                if (i11 == Integer.MIN_VALUE) {
                    i11 = d0Var.f1072e;
                }
                d0Var.f1068a = i11;
                int i12 = d0Var.f1070c;
                if (i12 == Integer.MIN_VALUE) {
                    i12 = d0Var.f1073f;
                }
                d0Var.f1069b = i12;
            } else {
                int i13 = d0Var.f1070c;
                if (i13 == Integer.MIN_VALUE) {
                    i13 = d0Var.f1072e;
                }
                d0Var.f1068a = i13;
                int i14 = d0Var.f1071d;
                if (i14 == Integer.MIN_VALUE) {
                    i14 = d0Var.f1073f;
                }
                d0Var.f1069b = i14;
            }
        }
    }

    public Parcelable onSaveInstanceState() {
        f fVar;
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        d dVar = this.A0;
        if (!(dVar == null || (fVar = dVar.f1035b) == null)) {
            savedState.f1029c = fVar.f630a;
        }
        savedState.f1030d = o();
        return savedState;
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.f1015r0 = false;
        }
        if (!this.f1015r0) {
            boolean onTouchEvent = super.onTouchEvent(motionEvent);
            if (actionMasked == 0 && !onTouchEvent) {
                this.f1015r0 = true;
            }
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.f1015r0 = false;
        }
        return true;
    }

    public final int p(View view, int i10, int[] iArr, int i11) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        int i12 = ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin - iArr[0];
        int max = Math.max(0, i12) + i10;
        iArr[0] = Math.max(0, -i12);
        int k10 = k(view, i11);
        int measuredWidth = view.getMeasuredWidth();
        view.layout(max, k10, max + measuredWidth, view.getMeasuredHeight() + k10);
        return measuredWidth + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin + max;
    }

    public final int q(View view, int i10, int[] iArr, int i11) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        int i12 = ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin - iArr[1];
        int max = i10 - Math.max(0, i12);
        iArr[1] = Math.max(0, -i12);
        int k10 = k(view, i11);
        int measuredWidth = view.getMeasuredWidth();
        view.layout(max - measuredWidth, k10, max, view.getMeasuredHeight() + k10);
        return max - (measuredWidth + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin);
    }

    public final int r(View view, int i10, int i11, int i12, int i13, int[] iArr) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        int i14 = marginLayoutParams.leftMargin - iArr[0];
        int i15 = marginLayoutParams.rightMargin - iArr[1];
        int max = Math.max(0, i15) + Math.max(0, i14);
        iArr[0] = Math.max(0, -i14);
        iArr[1] = Math.max(0, -i15);
        view.measure(ViewGroup.getChildMeasureSpec(i10, getPaddingRight() + getPaddingLeft() + max + i11, marginLayoutParams.width), ViewGroup.getChildMeasureSpec(i12, getPaddingBottom() + getPaddingTop() + marginLayoutParams.topMargin + marginLayoutParams.bottomMargin + i13, marginLayoutParams.height));
        return view.getMeasuredWidth() + max;
    }

    public final void s(View view, int i10, int i11, int i12, int i13, int i14) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        int childMeasureSpec = ViewGroup.getChildMeasureSpec(i10, getPaddingRight() + getPaddingLeft() + marginLayoutParams.leftMargin + marginLayoutParams.rightMargin + i11, marginLayoutParams.width);
        int childMeasureSpec2 = ViewGroup.getChildMeasureSpec(i12, getPaddingBottom() + getPaddingTop() + marginLayoutParams.topMargin + marginLayoutParams.bottomMargin + i13, marginLayoutParams.height);
        int mode = View.MeasureSpec.getMode(childMeasureSpec2);
        if (mode != 1073741824 && i14 >= 0) {
            if (mode != 0) {
                i14 = Math.min(View.MeasureSpec.getSize(childMeasureSpec2), i14);
            }
            childMeasureSpec2 = View.MeasureSpec.makeMeasureSpec(i14, 1073741824);
        }
        view.measure(childMeasureSpec, childMeasureSpec2);
    }

    public void setCollapseContentDescription(@StringRes int i10) {
        setCollapseContentDescription(i10 != 0 ? getContext().getText(i10) : null);
    }

    public void setCollapseIcon(@DrawableRes int i10) {
        setCollapseIcon(m.a.a(getContext(), i10));
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setCollapsible(boolean z10) {
        this.D0 = z10;
        requestLayout();
    }

    public void setContentInsetEndWithActions(int i10) {
        if (i10 < 0) {
            i10 = EventResultPersister.GENERATE_NEW_ID;
        }
        if (i10 != this.f1025y) {
            this.f1025y = i10;
            if (getNavigationIcon() != null) {
                requestLayout();
            }
        }
    }

    public void setContentInsetStartWithNavigation(int i10) {
        if (i10 < 0) {
            i10 = EventResultPersister.GENERATE_NEW_ID;
        }
        if (i10 != this.f1023x) {
            this.f1023x = i10;
            if (getNavigationIcon() != null) {
                requestLayout();
            }
        }
    }

    public void setLogo(@DrawableRes int i10) {
        setLogo(m.a.a(getContext(), i10));
    }

    public void setLogoDescription(@StringRes int i10) {
        setLogoDescription(getContext().getText(i10));
    }

    public void setNavigationContentDescription(@StringRes int i10) {
        setNavigationContentDescription(i10 != 0 ? getContext().getText(i10) : null);
    }

    public void setNavigationIcon(@DrawableRes int i10) {
        setNavigationIcon(m.a.a(getContext(), i10));
    }

    public void setNavigationOnClickListener(View.OnClickListener onClickListener) {
        g();
        this.f996d.setOnClickListener(onClickListener);
    }

    public void setOnMenuItemClickListener(e eVar) {
        this.f1022w0 = eVar;
    }

    public void setOverflowIcon(@Nullable Drawable drawable) {
        e();
        this.f993a.setOverflowIcon(drawable);
    }

    public void setPopupTheme(@StyleRes int i10) {
        if (this.f1002k != i10) {
            this.f1002k = i10;
            if (i10 == 0) {
                this.f1001j = getContext();
            } else {
                this.f1001j = new ContextThemeWrapper(getContext(), i10);
            }
        }
    }

    public void setSubtitle(@StringRes int i10) {
        setSubtitle(getContext().getText(i10));
    }

    public void setSubtitleTextColor(@ColorInt int i10) {
        setSubtitleTextColor(ColorStateList.valueOf(i10));
    }

    public void setTitle(@StringRes int i10) {
        setTitle(getContext().getText(i10));
    }

    public void setTitleMarginBottom(int i10) {
        this.f1016s = i10;
        requestLayout();
    }

    public void setTitleMarginEnd(int i10) {
        this.f1012q = i10;
        requestLayout();
    }

    public void setTitleMarginStart(int i10) {
        this.f1010p = i10;
        requestLayout();
    }

    public void setTitleMarginTop(int i10) {
        this.f1014r = i10;
        requestLayout();
    }

    public void setTitleTextColor(@ColorInt int i10) {
        setTitleTextColor(ColorStateList.valueOf(i10));
    }

    public final boolean t(View view) {
        return (view == null || view.getParent() != this || view.getVisibility() == 8) ? false : true;
    }

    public boolean u() {
        ActionMenuView actionMenuView = this.f993a;
        if (actionMenuView != null) {
            ActionMenuPresenter actionMenuPresenter = actionMenuView.f776t;
            if (actionMenuPresenter != null && actionMenuPresenter.p()) {
                return true;
            }
        }
        return false;
    }

    public static class LayoutParams extends ActionBar.LayoutParams {

        /* renamed from: b  reason: collision with root package name */
        public int f1028b = 0;

        public LayoutParams(@NonNull Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
            this.f328a = 8388627;
        }

        public LayoutParams(LayoutParams layoutParams) {
            super((ActionBar.LayoutParams) layoutParams);
            this.f1028b = layoutParams.f1028b;
        }

        public LayoutParams(ActionBar.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public LayoutParams(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
            ((ViewGroup.MarginLayoutParams) this).leftMargin = marginLayoutParams.leftMargin;
            ((ViewGroup.MarginLayoutParams) this).topMargin = marginLayoutParams.topMargin;
            ((ViewGroup.MarginLayoutParams) this).rightMargin = marginLayoutParams.rightMargin;
            ((ViewGroup.MarginLayoutParams) this).bottomMargin = marginLayoutParams.bottomMargin;
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    public Toolbar(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f1005m0 = 8388627;
        this.f1019t0 = new ArrayList<>();
        this.f1020u0 = new ArrayList<>();
        this.f1021v0 = new int[2];
        this.f1024x0 = new a();
        this.E0 = new b();
        Context context2 = getContext();
        int[] iArr = R$styleable.Toolbar;
        m0 r10 = m0.r(context2, attributeSet, iArr, i10, 0);
        ViewCompat.j(this, context, iArr, attributeSet, r10.f1145b, i10, 0);
        this.f1003l = r10.m(R$styleable.Toolbar_titleTextAppearance, 0);
        this.f1004m = r10.m(R$styleable.Toolbar_subtitleTextAppearance, 0);
        this.f1005m0 = r10.k(R$styleable.Toolbar_android_gravity, this.f1005m0);
        this.f1006n = r10.k(R$styleable.Toolbar_buttonGravity, 48);
        int e10 = r10.e(R$styleable.Toolbar_titleMargin, 0);
        int i11 = R$styleable.Toolbar_titleMargins;
        e10 = r10.p(i11) ? r10.e(i11, e10) : e10;
        this.f1016s = e10;
        this.f1014r = e10;
        this.f1012q = e10;
        this.f1010p = e10;
        int e11 = r10.e(R$styleable.Toolbar_titleMarginStart, -1);
        if (e11 >= 0) {
            this.f1010p = e11;
        }
        int e12 = r10.e(R$styleable.Toolbar_titleMarginEnd, -1);
        if (e12 >= 0) {
            this.f1012q = e12;
        }
        int e13 = r10.e(R$styleable.Toolbar_titleMarginTop, -1);
        if (e13 >= 0) {
            this.f1014r = e13;
        }
        int e14 = r10.e(R$styleable.Toolbar_titleMarginBottom, -1);
        if (e14 >= 0) {
            this.f1016s = e14;
        }
        this.f1008o = r10.f(R$styleable.Toolbar_maxButtonHeight, -1);
        int e15 = r10.e(R$styleable.Toolbar_contentInsetStart, EventResultPersister.GENERATE_NEW_ID);
        int e16 = r10.e(R$styleable.Toolbar_contentInsetEnd, EventResultPersister.GENERATE_NEW_ID);
        int f10 = r10.f(R$styleable.Toolbar_contentInsetLeft, 0);
        int f11 = r10.f(R$styleable.Toolbar_contentInsetRight, 0);
        d();
        d0 d0Var = this.f1018t;
        d0Var.h = false;
        if (f10 != Integer.MIN_VALUE) {
            d0Var.f1072e = f10;
            d0Var.f1068a = f10;
        }
        if (f11 != Integer.MIN_VALUE) {
            d0Var.f1073f = f11;
            d0Var.f1069b = f11;
        }
        if (!(e15 == Integer.MIN_VALUE && e16 == Integer.MIN_VALUE)) {
            d0Var.a(e15, e16);
        }
        this.f1023x = r10.e(R$styleable.Toolbar_contentInsetStartWithNavigation, EventResultPersister.GENERATE_NEW_ID);
        this.f1025y = r10.e(R$styleable.Toolbar_contentInsetEndWithActions, EventResultPersister.GENERATE_NEW_ID);
        this.f998f = r10.g(R$styleable.Toolbar_collapseIcon);
        this.f999g = r10.o(R$styleable.Toolbar_collapseContentDescription);
        CharSequence o3 = r10.o(R$styleable.Toolbar_title);
        if (!TextUtils.isEmpty(o3)) {
            setTitle(o3);
        }
        CharSequence o10 = r10.o(R$styleable.Toolbar_subtitle);
        if (!TextUtils.isEmpty(o10)) {
            setSubtitle(o10);
        }
        this.f1001j = getContext();
        setPopupTheme(r10.m(R$styleable.Toolbar_popupTheme, 0));
        Drawable g10 = r10.g(R$styleable.Toolbar_navigationIcon);
        if (g10 != null) {
            setNavigationIcon(g10);
        }
        CharSequence o11 = r10.o(R$styleable.Toolbar_navigationContentDescription);
        if (!TextUtils.isEmpty(o11)) {
            setNavigationContentDescription(o11);
        }
        Drawable g11 = r10.g(R$styleable.Toolbar_logo);
        if (g11 != null) {
            setLogo(g11);
        }
        CharSequence o12 = r10.o(R$styleable.Toolbar_logoDescription);
        if (!TextUtils.isEmpty(o12)) {
            setLogoDescription(o12);
        }
        int i12 = R$styleable.Toolbar_titleTextColor;
        if (r10.p(i12)) {
            setTitleTextColor(r10.c(i12));
        }
        int i13 = R$styleable.Toolbar_subtitleTextColor;
        if (r10.p(i13)) {
            setSubtitleTextColor(r10.c(i13));
        }
        int i14 = R$styleable.Toolbar_menu;
        if (r10.p(i14)) {
            getMenuInflater().inflate(r10.m(i14, 0), getMenu());
        }
        r10.f1145b.recycle();
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    public void setCollapseContentDescription(@Nullable CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            c();
        }
        ImageButton imageButton = this.h;
        if (imageButton != null) {
            imageButton.setContentDescription(charSequence);
        }
    }

    public void setCollapseIcon(@Nullable Drawable drawable) {
        if (drawable != null) {
            c();
            this.h.setImageDrawable(drawable);
            return;
        }
        ImageButton imageButton = this.h;
        if (imageButton != null) {
            imageButton.setImageDrawable(this.f998f);
        }
    }

    public void setLogo(Drawable drawable) {
        if (drawable != null) {
            if (this.f997e == null) {
                this.f997e = new AppCompatImageView(getContext(), null);
            }
            if (!n(this.f997e)) {
                b(this.f997e, true);
            }
        } else {
            ImageView imageView = this.f997e;
            if (imageView != null && n(imageView)) {
                removeView(this.f997e);
                this.f1020u0.remove(this.f997e);
            }
        }
        ImageView imageView2 = this.f997e;
        if (imageView2 != null) {
            imageView2.setImageDrawable(drawable);
        }
    }

    public void setLogoDescription(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence) && this.f997e == null) {
            this.f997e = new AppCompatImageView(getContext(), null);
        }
        ImageView imageView = this.f997e;
        if (imageView != null) {
            imageView.setContentDescription(charSequence);
        }
    }

    public void setNavigationContentDescription(@Nullable CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            g();
        }
        ImageButton imageButton = this.f996d;
        if (imageButton != null) {
            imageButton.setContentDescription(charSequence);
        }
    }

    public void setNavigationIcon(@Nullable Drawable drawable) {
        if (drawable != null) {
            g();
            if (!n(this.f996d)) {
                b(this.f996d, true);
            }
        } else {
            ImageButton imageButton = this.f996d;
            if (imageButton != null && n(imageButton)) {
                removeView(this.f996d);
                this.f1020u0.remove(this.f996d);
            }
        }
        ImageButton imageButton2 = this.f996d;
        if (imageButton2 != null) {
            imageButton2.setImageDrawable(drawable);
        }
    }

    public void setSubtitle(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            if (this.f995c == null) {
                Context context = getContext();
                AppCompatTextView appCompatTextView = new AppCompatTextView(context);
                this.f995c = appCompatTextView;
                appCompatTextView.setSingleLine();
                this.f995c.setEllipsize(TextUtils.TruncateAt.END);
                int i10 = this.f1004m;
                if (i10 != 0) {
                    this.f995c.setTextAppearance(context, i10);
                }
                ColorStateList colorStateList = this.f1013q0;
                if (colorStateList != null) {
                    this.f995c.setTextColor(colorStateList);
                }
            }
            if (!n(this.f995c)) {
                b(this.f995c, true);
            }
        } else {
            TextView textView = this.f995c;
            if (textView != null && n(textView)) {
                removeView(this.f995c);
                this.f1020u0.remove(this.f995c);
            }
        }
        TextView textView2 = this.f995c;
        if (textView2 != null) {
            textView2.setText(charSequence);
        }
        this.f1009o0 = charSequence;
    }

    public void setSubtitleTextColor(@NonNull ColorStateList colorStateList) {
        this.f1013q0 = colorStateList;
        TextView textView = this.f995c;
        if (textView != null) {
            textView.setTextColor(colorStateList);
        }
    }

    public void setTitle(CharSequence charSequence) {
        if (!TextUtils.isEmpty(charSequence)) {
            if (this.f994b == null) {
                Context context = getContext();
                AppCompatTextView appCompatTextView = new AppCompatTextView(context);
                this.f994b = appCompatTextView;
                appCompatTextView.setSingleLine();
                this.f994b.setEllipsize(TextUtils.TruncateAt.END);
                int i10 = this.f1003l;
                if (i10 != 0) {
                    this.f994b.setTextAppearance(context, i10);
                }
                ColorStateList colorStateList = this.f1011p0;
                if (colorStateList != null) {
                    this.f994b.setTextColor(colorStateList);
                }
            }
            if (!n(this.f994b)) {
                b(this.f994b, true);
            }
        } else {
            TextView textView = this.f994b;
            if (textView != null && n(textView)) {
                removeView(this.f994b);
                this.f1020u0.remove(this.f994b);
            }
        }
        TextView textView2 = this.f994b;
        if (textView2 != null) {
            textView2.setText(charSequence);
        }
        this.f1007n0 = charSequence;
    }

    public void setTitleTextColor(@NonNull ColorStateList colorStateList) {
        this.f1011p0 = colorStateList;
        TextView textView = this.f994b;
        if (textView != null) {
            textView.setTextColor(colorStateList);
        }
    }

    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: c  reason: collision with root package name */
        public int f1029c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f1030d;

        public class a implements Parcelable.ClassLoaderCreator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }
        }

        public SavedState(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f1029c = parcel.readInt();
            this.f1030d = parcel.readInt() != 0;
        }

        @Override // androidx.customview.view.AbsSavedState
        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeParcelable(this.f1662a, i10);
            parcel.writeInt(this.f1029c);
            parcel.writeInt(this.f1030d ? 1 : 0);
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }
    }
}
