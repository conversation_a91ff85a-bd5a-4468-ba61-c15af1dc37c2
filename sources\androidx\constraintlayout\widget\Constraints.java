package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.a;
import com.duokan.airkan.common.Constant;
import java.util.Objects;

public class Constraints extends ViewGroup {

    /* renamed from: a  reason: collision with root package name */
    public a f1454a;

    public Constraints(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        Log.v("Constraints", " ################# init");
        super.setVisibility(8);
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-2, -2);
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    public a getConstraintSet() {
        if (this.f1454a == null) {
            this.f1454a = new a();
        }
        a aVar = this.f1454a;
        Objects.requireNonNull(aVar);
        int childCount = getChildCount();
        aVar.f1472a.clear();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
            int id = childAt.getId();
            if (id != -1) {
                if (!aVar.f1472a.containsKey(Integer.valueOf(id))) {
                    aVar.f1472a.put(Integer.valueOf(id), new a.C0008a());
                }
                a.C0008a aVar2 = aVar.f1472a.get(Integer.valueOf(id));
                if (childAt instanceof ConstraintHelper) {
                    ConstraintHelper constraintHelper = (ConstraintHelper) childAt;
                    aVar2.c(id, layoutParams);
                    if (constraintHelper instanceof Barrier) {
                        aVar2.f1511t0 = 1;
                        Barrier barrier = (Barrier) constraintHelper;
                        aVar2.f1509s0 = barrier.getType();
                        aVar2.f1513u0 = barrier.getReferencedIds();
                    }
                }
                aVar2.c(id, layoutParams);
            } else {
                throw new RuntimeException("All children of ConstraintLayout must have ids to use ConstraintSet");
            }
        }
        return this.f1454a;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return new ConstraintLayout.LayoutParams(layoutParams);
    }

    public Constraints(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        Log.v("Constraints", " ################# init");
        super.setVisibility(8);
    }

    public static class LayoutParams extends ConstraintLayout.LayoutParams {

        /* renamed from: l0  reason: collision with root package name */
        public float f1455l0;

        /* renamed from: m0  reason: collision with root package name */
        public boolean f1456m0;

        /* renamed from: n0  reason: collision with root package name */
        public float f1457n0;

        /* renamed from: o0  reason: collision with root package name */
        public float f1458o0;

        /* renamed from: p0  reason: collision with root package name */
        public float f1459p0;

        /* renamed from: q0  reason: collision with root package name */
        public float f1460q0;

        /* renamed from: r0  reason: collision with root package name */
        public float f1461r0;

        /* renamed from: s0  reason: collision with root package name */
        public float f1462s0;

        /* renamed from: t0  reason: collision with root package name */
        public float f1463t0;

        /* renamed from: u0  reason: collision with root package name */
        public float f1464u0;

        /* renamed from: v0  reason: collision with root package name */
        public float f1465v0;

        /* renamed from: w0  reason: collision with root package name */
        public float f1466w0;

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
            this.f1455l0 = 1.0f;
            this.f1456m0 = false;
            this.f1457n0 = Constant.VOLUME_FLOAT_MIN;
            this.f1458o0 = Constant.VOLUME_FLOAT_MIN;
            this.f1459p0 = Constant.VOLUME_FLOAT_MIN;
            this.f1460q0 = Constant.VOLUME_FLOAT_MIN;
            this.f1461r0 = 1.0f;
            this.f1462s0 = 1.0f;
            this.f1463t0 = Constant.VOLUME_FLOAT_MIN;
            this.f1464u0 = Constant.VOLUME_FLOAT_MIN;
            this.f1465v0 = Constant.VOLUME_FLOAT_MIN;
            this.f1466w0 = Constant.VOLUME_FLOAT_MIN;
        }

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            this.f1455l0 = 1.0f;
            this.f1456m0 = false;
            this.f1457n0 = Constant.VOLUME_FLOAT_MIN;
            this.f1458o0 = Constant.VOLUME_FLOAT_MIN;
            this.f1459p0 = Constant.VOLUME_FLOAT_MIN;
            this.f1460q0 = Constant.VOLUME_FLOAT_MIN;
            this.f1461r0 = 1.0f;
            this.f1462s0 = 1.0f;
            this.f1463t0 = Constant.VOLUME_FLOAT_MIN;
            this.f1464u0 = Constant.VOLUME_FLOAT_MIN;
            this.f1465v0 = Constant.VOLUME_FLOAT_MIN;
            this.f1466w0 = Constant.VOLUME_FLOAT_MIN;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ConstraintSet);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i10 = 0; i10 < indexCount; i10++) {
                int index = obtainStyledAttributes.getIndex(i10);
                if (index == R$styleable.ConstraintSet_android_alpha) {
                    this.f1455l0 = obtainStyledAttributes.getFloat(index, this.f1455l0);
                } else if (index == R$styleable.ConstraintSet_android_elevation) {
                    this.f1457n0 = obtainStyledAttributes.getFloat(index, this.f1457n0);
                    this.f1456m0 = true;
                } else if (index == R$styleable.ConstraintSet_android_rotationX) {
                    this.f1459p0 = obtainStyledAttributes.getFloat(index, this.f1459p0);
                } else if (index == R$styleable.ConstraintSet_android_rotationY) {
                    this.f1460q0 = obtainStyledAttributes.getFloat(index, this.f1460q0);
                } else if (index == R$styleable.ConstraintSet_android_rotation) {
                    this.f1458o0 = obtainStyledAttributes.getFloat(index, this.f1458o0);
                } else if (index == R$styleable.ConstraintSet_android_scaleX) {
                    this.f1461r0 = obtainStyledAttributes.getFloat(index, this.f1461r0);
                } else if (index == R$styleable.ConstraintSet_android_scaleY) {
                    this.f1462s0 = obtainStyledAttributes.getFloat(index, this.f1462s0);
                } else if (index == R$styleable.ConstraintSet_android_transformPivotX) {
                    this.f1463t0 = obtainStyledAttributes.getFloat(index, this.f1463t0);
                } else if (index == R$styleable.ConstraintSet_android_transformPivotY) {
                    this.f1464u0 = obtainStyledAttributes.getFloat(index, this.f1464u0);
                } else if (index == R$styleable.ConstraintSet_android_translationX) {
                    this.f1465v0 = obtainStyledAttributes.getFloat(index, this.f1465v0);
                } else if (index == R$styleable.ConstraintSet_android_translationY) {
                    this.f1466w0 = obtainStyledAttributes.getFloat(index, this.f1466w0);
                } else if (index == R$styleable.ConstraintSet_android_translationZ) {
                    this.f1465v0 = obtainStyledAttributes.getFloat(index, Constant.VOLUME_FLOAT_MIN);
                }
            }
        }
    }
}
