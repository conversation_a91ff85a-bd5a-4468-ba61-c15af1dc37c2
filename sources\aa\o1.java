package aa;

import java.io.IOException;

/* compiled from: DLTaggedObject */
public class o1 extends x {
    public o1(boolean z10, int i10, e eVar) {
        super(z10, i10, eVar);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        q m10 = this.f244c.c().m();
        int i10 = 160;
        if (this.f243b) {
            pVar.i(160, this.f242a);
            pVar.g(m10.i());
            pVar.h(m10);
            return;
        }
        if (!m10.k()) {
            i10 = 128;
        }
        pVar.i(i10, this.f242a);
        pVar.f(m10);
    }

    @Override // aa.q
    public int i() throws IOException {
        int i10 = this.f244c.c().m().i();
        if (this.f243b) {
            return v1.a(i10) + v1.b(this.f242a) + i10;
        }
        return v1.b(this.f242a) + (i10 - 1);
    }

    @Override // aa.q
    public boolean k() {
        if (this.f243b) {
            return true;
        }
        return this.f244c.c().m().k();
    }
}
