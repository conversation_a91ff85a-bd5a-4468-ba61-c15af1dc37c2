package androidx.core.widget;

import android.content.res.Resources;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import j0.m;
import java.util.Objects;
import java.util.WeakHashMap;

/* compiled from: AutoScrollHelper */
public abstract class a implements View.OnTouchListener {

    /* renamed from: q  reason: collision with root package name */
    public static final int f1633q = ViewConfiguration.getTapTimeout();

    /* renamed from: a  reason: collision with root package name */
    public final C0012a f1634a;

    /* renamed from: b  reason: collision with root package name */
    public final Interpolator f1635b = new AccelerateInterpolator();

    /* renamed from: c  reason: collision with root package name */
    public final View f1636c;

    /* renamed from: d  reason: collision with root package name */
    public Runnable f1637d;

    /* renamed from: e  reason: collision with root package name */
    public float[] f1638e = {Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN};

    /* renamed from: f  reason: collision with root package name */
    public float[] f1639f = {Float.MAX_VALUE, Float.MAX_VALUE};

    /* renamed from: g  reason: collision with root package name */
    public int f1640g;
    public int h;

    /* renamed from: i  reason: collision with root package name */
    public float[] f1641i = {Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN};

    /* renamed from: j  reason: collision with root package name */
    public float[] f1642j = {Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN};

    /* renamed from: k  reason: collision with root package name */
    public float[] f1643k = {Float.MAX_VALUE, Float.MAX_VALUE};

    /* renamed from: l  reason: collision with root package name */
    public boolean f1644l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1645m;

    /* renamed from: n  reason: collision with root package name */
    public boolean f1646n;

    /* renamed from: o  reason: collision with root package name */
    public boolean f1647o;

    /* renamed from: p  reason: collision with root package name */
    public boolean f1648p;

    /* renamed from: androidx.core.widget.a$a  reason: collision with other inner class name */
    /* compiled from: AutoScrollHelper */
    public static class C0012a {

        /* renamed from: a  reason: collision with root package name */
        public int f1649a;

        /* renamed from: b  reason: collision with root package name */
        public int f1650b;

        /* renamed from: c  reason: collision with root package name */
        public float f1651c;

        /* renamed from: d  reason: collision with root package name */
        public float f1652d;

        /* renamed from: e  reason: collision with root package name */
        public long f1653e = Long.MIN_VALUE;

        /* renamed from: f  reason: collision with root package name */
        public long f1654f = 0;

        /* renamed from: g  reason: collision with root package name */
        public int f1655g = 0;
        public int h = 0;

        /* renamed from: i  reason: collision with root package name */
        public long f1656i = -1;

        /* renamed from: j  reason: collision with root package name */
        public float f1657j;

        /* renamed from: k  reason: collision with root package name */
        public int f1658k;

        public final float a(long j10) {
            long j11 = this.f1653e;
            if (j10 < j11) {
                return Constant.VOLUME_FLOAT_MIN;
            }
            long j12 = this.f1656i;
            if (j12 < 0 || j10 < j12) {
                return a.b(((float) (j10 - j11)) / ((float) this.f1649a), Constant.VOLUME_FLOAT_MIN, 1.0f) * 0.5f;
            }
            float f10 = this.f1657j;
            return (a.b(((float) (j10 - j12)) / ((float) this.f1658k), Constant.VOLUME_FLOAT_MIN, 1.0f) * f10) + (1.0f - f10);
        }
    }

    /* compiled from: AutoScrollHelper */
    public class b implements Runnable {
        public b() {
        }

        public void run() {
            a aVar = a.this;
            if (aVar.f1647o) {
                if (aVar.f1645m) {
                    aVar.f1645m = false;
                    C0012a aVar2 = aVar.f1634a;
                    Objects.requireNonNull(aVar2);
                    long currentAnimationTimeMillis = AnimationUtils.currentAnimationTimeMillis();
                    aVar2.f1653e = currentAnimationTimeMillis;
                    aVar2.f1656i = -1;
                    aVar2.f1654f = currentAnimationTimeMillis;
                    aVar2.f1657j = 0.5f;
                    aVar2.f1655g = 0;
                    aVar2.h = 0;
                }
                C0012a aVar3 = a.this.f1634a;
                if ((aVar3.f1656i > 0 && AnimationUtils.currentAnimationTimeMillis() > aVar3.f1656i + ((long) aVar3.f1658k)) || !a.this.e()) {
                    a.this.f1647o = false;
                    return;
                }
                a aVar4 = a.this;
                if (aVar4.f1646n) {
                    aVar4.f1646n = false;
                    Objects.requireNonNull(aVar4);
                    long uptimeMillis = SystemClock.uptimeMillis();
                    MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0);
                    aVar4.f1636c.onTouchEvent(obtain);
                    obtain.recycle();
                }
                if (aVar3.f1654f != 0) {
                    long currentAnimationTimeMillis2 = AnimationUtils.currentAnimationTimeMillis();
                    float a10 = aVar3.a(currentAnimationTimeMillis2);
                    aVar3.f1654f = currentAnimationTimeMillis2;
                    float f10 = ((float) (currentAnimationTimeMillis2 - aVar3.f1654f)) * ((a10 * 4.0f) + (-4.0f * a10 * a10));
                    aVar3.f1655g = (int) (aVar3.f1651c * f10);
                    int i10 = (int) (f10 * aVar3.f1652d);
                    aVar3.h = i10;
                    ((b) a.this).f1660r.scrollListBy(i10);
                    View view = a.this.f1636c;
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    view.postOnAnimation(this);
                    return;
                }
                throw new RuntimeException("Cannot compute scroll delta before calling start()");
            }
        }
    }

    public a(@NonNull View view) {
        C0012a aVar = new C0012a();
        this.f1634a = aVar;
        this.f1636c = view;
        float f10 = Resources.getSystem().getDisplayMetrics().density;
        float[] fArr = this.f1643k;
        float f11 = ((float) ((int) ((1575.0f * f10) + 0.5f))) / 1000.0f;
        fArr[0] = f11;
        fArr[1] = f11;
        float[] fArr2 = this.f1642j;
        float f12 = ((float) ((int) ((f10 * 315.0f) + 0.5f))) / 1000.0f;
        fArr2[0] = f12;
        fArr2[1] = f12;
        this.f1640g = 1;
        float[] fArr3 = this.f1639f;
        fArr3[0] = Float.MAX_VALUE;
        fArr3[1] = Float.MAX_VALUE;
        float[] fArr4 = this.f1638e;
        fArr4[0] = 0.2f;
        fArr4[1] = 0.2f;
        float[] fArr5 = this.f1641i;
        fArr5[0] = 0.001f;
        fArr5[1] = 0.001f;
        this.h = f1633q;
        aVar.f1649a = 500;
        aVar.f1650b = 500;
    }

    public static float b(float f10, float f11, float f12) {
        return f10 > f12 ? f12 : f10 < f11 ? f11 : f10;
    }

    /* JADX WARNING: Removed duplicated region for block: B:10:0x003d A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:11:0x003e  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final float a(int r4, float r5, float r6, float r7) {
        /*
            r3 = this;
            float[] r0 = r3.f1638e
            r0 = r0[r4]
            float[] r1 = r3.f1639f
            r1 = r1[r4]
            float r0 = r0 * r6
            r2 = 0
            float r0 = b(r0, r2, r1)
            float r1 = r3.c(r5, r0)
            float r6 = r6 - r5
            float r5 = r3.c(r6, r0)
            float r5 = r5 - r1
            int r6 = (r5 > r2 ? 1 : (r5 == r2 ? 0 : -1))
            if (r6 >= 0) goto L_0x0025
            android.view.animation.Interpolator r6 = r3.f1635b
            float r5 = -r5
            float r5 = r6.getInterpolation(r5)
            float r5 = -r5
            goto L_0x002f
        L_0x0025:
            int r6 = (r5 > r2 ? 1 : (r5 == r2 ? 0 : -1))
            if (r6 <= 0) goto L_0x0038
            android.view.animation.Interpolator r6 = r3.f1635b
            float r5 = r6.getInterpolation(r5)
        L_0x002f:
            r6 = -1082130432(0xffffffffbf800000, float:-1.0)
            r0 = 1065353216(0x3f800000, float:1.0)
            float r5 = b(r5, r6, r0)
            goto L_0x0039
        L_0x0038:
            r5 = r2
        L_0x0039:
            int r6 = (r5 > r2 ? 1 : (r5 == r2 ? 0 : -1))
            if (r6 != 0) goto L_0x003e
            return r2
        L_0x003e:
            float[] r0 = r3.f1641i
            r0 = r0[r4]
            float[] r1 = r3.f1642j
            r1 = r1[r4]
            float[] r2 = r3.f1643k
            r4 = r2[r4]
            float r0 = r0 * r7
            if (r6 <= 0) goto L_0x0053
            float r5 = r5 * r0
            float r4 = b(r5, r1, r4)
            return r4
        L_0x0053:
            float r5 = -r5
            float r5 = r5 * r0
            float r4 = b(r5, r1, r4)
            float r4 = -r4
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.a.a(int, float, float, float):float");
    }

    public final float c(float f10, float f11) {
        if (f11 == Constant.VOLUME_FLOAT_MIN) {
            return Constant.VOLUME_FLOAT_MIN;
        }
        int i10 = this.f1640g;
        if (i10 == 0 || i10 == 1) {
            if (f10 < f11) {
                if (f10 >= Constant.VOLUME_FLOAT_MIN) {
                    return 1.0f - (f10 / f11);
                }
                if (!this.f1647o || i10 != 1) {
                    return Constant.VOLUME_FLOAT_MIN;
                }
                return 1.0f;
            }
        } else if (i10 == 2 && f10 < Constant.VOLUME_FLOAT_MIN) {
            return f10 / (-f11);
        }
        return Constant.VOLUME_FLOAT_MIN;
    }

    public final void d() {
        int i10 = 0;
        if (this.f1645m) {
            this.f1647o = false;
            return;
        }
        C0012a aVar = this.f1634a;
        Objects.requireNonNull(aVar);
        long currentAnimationTimeMillis = AnimationUtils.currentAnimationTimeMillis();
        int i11 = (int) (currentAnimationTimeMillis - aVar.f1653e);
        int i12 = aVar.f1650b;
        if (i11 > i12) {
            i10 = i12;
        } else if (i11 >= 0) {
            i10 = i11;
        }
        aVar.f1658k = i10;
        aVar.f1657j = aVar.a(currentAnimationTimeMillis);
        aVar.f1656i = currentAnimationTimeMillis;
    }

    /* JADX WARNING: Removed duplicated region for block: B:18:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean e() {
        /*
            r9 = this;
            androidx.core.widget.a$a r0 = r9.f1634a
            float r1 = r0.f1652d
            float r2 = java.lang.Math.abs(r1)
            float r1 = r1 / r2
            int r1 = (int) r1
            float r0 = r0.f1651c
            float r2 = java.lang.Math.abs(r0)
            float r0 = r0 / r2
            int r0 = (int) r0
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L_0x0053
            r4 = r9
            androidx.core.widget.b r4 = (androidx.core.widget.b) r4
            android.widget.ListView r4 = r4.f1660r
            int r5 = r4.getCount()
            if (r5 != 0) goto L_0x0023
        L_0x0021:
            r1 = r3
            goto L_0x0051
        L_0x0023:
            int r6 = r4.getChildCount()
            int r7 = r4.getFirstVisiblePosition()
            int r8 = r7 + r6
            if (r1 <= 0) goto L_0x0041
            if (r8 < r5) goto L_0x0050
            int r6 = r6 - r2
            android.view.View r1 = r4.getChildAt(r6)
            int r1 = r1.getBottom()
            int r4 = r4.getHeight()
            if (r1 > r4) goto L_0x0050
            goto L_0x0021
        L_0x0041:
            if (r1 >= 0) goto L_0x0021
            if (r7 > 0) goto L_0x0050
            android.view.View r1 = r4.getChildAt(r3)
            int r1 = r1.getTop()
            if (r1 < 0) goto L_0x0050
            goto L_0x0021
        L_0x0050:
            r1 = r2
        L_0x0051:
            if (r1 != 0) goto L_0x0054
        L_0x0053:
            r2 = r3
        L_0x0054:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.a.e():boolean");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:9:0x0013, code lost:
        if (r0 != 3) goto L_0x007f;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouch(android.view.View r6, android.view.MotionEvent r7) {
        /*
        // Method dump skipped, instructions count: 128
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.a.onTouch(android.view.View, android.view.MotionEvent):boolean");
    }
}
