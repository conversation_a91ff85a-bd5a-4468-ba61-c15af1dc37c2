package androidx.fragment.app;

import android.view.View;
import java.util.ArrayList;

/* compiled from: FragmentTransition */
public class h0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ Object f1908a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ m0 f1909b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ View f1910c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ Fragment f1911d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1912e;

    /* renamed from: f  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1913f;

    /* renamed from: g  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1914g;
    public final /* synthetic */ Object h;

    public h0(Object obj, m0 m0Var, View view, Fragment fragment, ArrayList arrayList, ArrayList arrayList2, ArrayList arrayList3, Object obj2) {
        this.f1908a = obj;
        this.f1909b = m0Var;
        this.f1910c = view;
        this.f1911d = fragment;
        this.f1912e = arrayList;
        this.f1913f = arrayList2;
        this.f1914g = arrayList3;
        this.h = obj2;
    }

    public void run() {
        Object obj = this.f1908a;
        if (obj != null) {
            this.f1909b.o(obj, this.f1910c);
            this.f1913f.addAll(k0.h(this.f1909b, this.f1908a, this.f1911d, this.f1912e, this.f1910c));
        }
        if (this.f1914g != null) {
            if (this.h != null) {
                ArrayList<View> arrayList = new ArrayList<>();
                arrayList.add(this.f1910c);
                this.f1909b.p(this.h, this.f1914g, arrayList);
            }
            this.f1914g.clear();
            this.f1914g.add(this.f1910c);
        }
    }
}
