package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERPrintableString */
public class z0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f247a;

    public z0(byte[] bArr) {
        this.f247a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof z0)) {
            return false;
        }
        return a.a(this.f247a, ((z0) qVar).f247a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f247a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(19, this.f247a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f247a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f247a.length) + 1 + this.f247a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
