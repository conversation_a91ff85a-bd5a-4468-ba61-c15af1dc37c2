package androidx.activity.result;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.duokan.airkan.server.f;

@SuppressLint({"BanParcelableUsage"})
public final class ActivityResult implements Parcelable {
    @NonNull
    public static final Parcelable.Creator<ActivityResult> CREATOR = new a();

    /* renamed from: a  reason: collision with root package name */
    public final int f295a;
    @Nullable

    /* renamed from: b  reason: collision with root package name */
    public final Intent f296b;

    public class a implements Parcelable.Creator<ActivityResult> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // android.os.Parcelable.Creator
        public ActivityResult createFromParcel(@NonNull Parcel parcel) {
            return new ActivityResult(parcel);
        }

        /* Return type fixed from 'java.lang.Object[]' to match base method */
        @Override // android.os.Parcelable.Creator
        public ActivityResult[] newArray(int i10) {
            return new ActivityResult[i10];
        }
    }

    public ActivityResult(int i10, @Nullable Intent intent) {
        this.f295a = i10;
        this.f296b = intent;
    }

    public int describeContents() {
        return 0;
    }

    public String toString() {
        StringBuilder a10 = f.a("ActivityResult{resultCode=");
        int i10 = this.f295a;
        a10.append(i10 != -1 ? i10 != 0 ? String.valueOf(i10) : "RESULT_CANCELED" : "RESULT_OK");
        a10.append(", data=");
        a10.append(this.f296b);
        a10.append('}');
        return a10.toString();
    }

    public void writeToParcel(@NonNull Parcel parcel, int i10) {
        parcel.writeInt(this.f295a);
        parcel.writeInt(this.f296b == null ? 0 : 1);
        Intent intent = this.f296b;
        if (intent != null) {
            intent.writeToParcel(parcel, i10);
        }
    }

    public ActivityResult(Parcel parcel) {
        this.f295a = parcel.readInt();
        this.f296b = parcel.readInt() == 0 ? null : (Intent) Intent.CREATOR.createFromParcel(parcel);
    }
}
