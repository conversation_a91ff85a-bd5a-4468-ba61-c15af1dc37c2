package androidx.fragment.app;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.f0;
import androidx.core.view.ViewCompat;
import androidx.fragment.R$id;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.t0;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.w;
import androidx.lifecycle.x;
import bb.h;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import j0.m;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;
import java.util.UUID;
import java.util.WeakHashMap;

/* compiled from: FragmentStateManager */
public class b0 {

    /* renamed from: a  reason: collision with root package name */
    public final x f1850a;

    /* renamed from: b  reason: collision with root package name */
    public final c0 f1851b;
    @NonNull

    /* renamed from: c  reason: collision with root package name */
    public final Fragment f1852c;

    /* renamed from: d  reason: collision with root package name */
    public boolean f1853d = false;

    /* renamed from: e  reason: collision with root package name */
    public int f1854e = -1;

    /* compiled from: FragmentStateManager */
    public class a implements View.OnAttachStateChangeListener {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ View f1855a;

        public a(b0 b0Var, View view) {
            this.f1855a = view;
        }

        public void onViewAttachedToWindow(View view) {
            this.f1855a.removeOnAttachStateChangeListener(this);
            View view2 = this.f1855a;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            view2.requestApplyInsets();
        }

        public void onViewDetachedFromWindow(View view) {
        }
    }

    /* compiled from: FragmentStateManager */
    public static /* synthetic */ class b {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int[] f1856a;

        /* JADX WARNING: Can't wrap try/catch for region: R(8:0|1|2|3|4|5|6|(3:7|8|10)) */
        /* JADX WARNING: Failed to process nested try/catch */
        /* JADX WARNING: Missing exception handler attribute for start block: B:3:0x0012 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:5:0x001d */
        /* JADX WARNING: Missing exception handler attribute for start block: B:7:0x0028 */
        static {
            /*
                androidx.lifecycle.Lifecycle$State[] r0 = androidx.lifecycle.Lifecycle.State.values()
                int r0 = r0.length
                int[] r0 = new int[r0]
                androidx.fragment.app.b0.b.f1856a = r0
                androidx.lifecycle.Lifecycle$State r1 = androidx.lifecycle.Lifecycle.State.RESUMED     // Catch:{ NoSuchFieldError -> 0x0012 }
                int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x0012 }
                r2 = 1
                r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x0012 }
            L_0x0012:
                int[] r0 = androidx.fragment.app.b0.b.f1856a     // Catch:{ NoSuchFieldError -> 0x001d }
                androidx.lifecycle.Lifecycle$State r1 = androidx.lifecycle.Lifecycle.State.STARTED     // Catch:{ NoSuchFieldError -> 0x001d }
                int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x001d }
                r2 = 2
                r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x001d }
            L_0x001d:
                int[] r0 = androidx.fragment.app.b0.b.f1856a     // Catch:{ NoSuchFieldError -> 0x0028 }
                androidx.lifecycle.Lifecycle$State r1 = androidx.lifecycle.Lifecycle.State.CREATED     // Catch:{ NoSuchFieldError -> 0x0028 }
                int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x0028 }
                r2 = 3
                r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x0028 }
            L_0x0028:
                int[] r0 = androidx.fragment.app.b0.b.f1856a     // Catch:{ NoSuchFieldError -> 0x0033 }
                androidx.lifecycle.Lifecycle$State r1 = androidx.lifecycle.Lifecycle.State.INITIALIZED     // Catch:{ NoSuchFieldError -> 0x0033 }
                int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x0033 }
                r2 = 4
                r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x0033 }
            L_0x0033:
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.b0.b.<clinit>():void");
        }
    }

    public b0(@NonNull x xVar, @NonNull c0 c0Var, @NonNull Fragment fragment) {
        this.f1850a = xVar;
        this.f1851b = c0Var;
        this.f1852c = fragment;
    }

    public void a() {
        if (FragmentManager.O(3)) {
            StringBuilder a10 = f.a("moveto ACTIVITY_CREATED: ");
            a10.append(this.f1852c);
            Log.d("FragmentManager", a10.toString());
        }
        Fragment fragment = this.f1852c;
        Bundle bundle = fragment.f1721b;
        fragment.f1745t.V();
        fragment.f1720a = 3;
        fragment.f1744s0 = false;
        fragment.f1744s0 = true;
        if (FragmentManager.O(3)) {
            Log.d("FragmentManager", "moveto RESTORE_VIEW_STATE: " + fragment);
        }
        View view = fragment.f1747u0;
        if (view != null) {
            Bundle bundle2 = fragment.f1721b;
            SparseArray<Parcelable> sparseArray = fragment.f1722c;
            if (sparseArray != null) {
                view.restoreHierarchyState(sparseArray);
                fragment.f1722c = null;
            }
            if (fragment.f1747u0 != null) {
                p0 p0Var = fragment.D0;
                p0Var.f1981b.a(fragment.f1723d);
                fragment.f1723d = null;
            }
            fragment.f1744s0 = false;
            fragment.G(bundle2);
            if (!fragment.f1744s0) {
                throw new z0(m.b("Fragment ", fragment, " did not call through to super.onViewStateRestored()"));
            } else if (fragment.f1747u0 != null) {
                fragment.D0.a(Lifecycle.Event.ON_CREATE);
            }
        }
        fragment.f1721b = null;
        FragmentManager fragmentManager = fragment.f1745t;
        fragmentManager.B = false;
        fragmentManager.C = false;
        fragmentManager.J.h = false;
        fragmentManager.w(4);
        x xVar = this.f1850a;
        Fragment fragment2 = this.f1852c;
        xVar.a(fragment2, fragment2.f1721b, false);
    }

    public void b() {
        View view;
        View view2;
        c0 c0Var = this.f1851b;
        Fragment fragment = this.f1852c;
        Objects.requireNonNull(c0Var);
        ViewGroup viewGroup = fragment.f1746t0;
        int i10 = -1;
        if (viewGroup != null) {
            int indexOf = c0Var.f1862a.indexOf(fragment);
            int i11 = indexOf - 1;
            while (true) {
                if (i11 < 0) {
                    while (true) {
                        indexOf++;
                        if (indexOf >= c0Var.f1862a.size()) {
                            break;
                        }
                        Fragment fragment2 = c0Var.f1862a.get(indexOf);
                        if (fragment2.f1746t0 == viewGroup && (view = fragment2.f1747u0) != null) {
                            i10 = viewGroup.indexOfChild(view);
                            break;
                        }
                    }
                } else {
                    Fragment fragment3 = c0Var.f1862a.get(i11);
                    if (fragment3.f1746t0 == viewGroup && (view2 = fragment3.f1747u0) != null) {
                        i10 = viewGroup.indexOfChild(view2) + 1;
                        break;
                    }
                    i11--;
                }
            }
        }
        Fragment fragment4 = this.f1852c;
        fragment4.f1746t0.addView(fragment4.f1747u0, i10);
    }

    public void c() {
        if (FragmentManager.O(3)) {
            StringBuilder a10 = f.a("moveto ATTACHED: ");
            a10.append(this.f1852c);
            Log.d("FragmentManager", a10.toString());
        }
        Fragment fragment = this.f1852c;
        Fragment fragment2 = fragment.f1726g;
        b0 b0Var = null;
        if (fragment2 != null) {
            b0 h = this.f1851b.h(fragment2.f1724e);
            if (h != null) {
                Fragment fragment3 = this.f1852c;
                fragment3.h = fragment3.f1726g.f1724e;
                fragment3.f1726g = null;
                b0Var = h;
            } else {
                StringBuilder a11 = f.a("Fragment ");
                a11.append(this.f1852c);
                a11.append(" declared target fragment ");
                a11.append(this.f1852c.f1726g);
                a11.append(" that does not belong to this FragmentManager!");
                throw new IllegalStateException(a11.toString());
            }
        } else {
            String str = fragment.h;
            if (str != null && (b0Var = this.f1851b.h(str)) == null) {
                StringBuilder a12 = f.a("Fragment ");
                a12.append(this.f1852c);
                a12.append(" declared target fragment ");
                throw new IllegalStateException(h.b(a12, this.f1852c.h, " that does not belong to this FragmentManager!"));
            }
        }
        if (b0Var != null) {
            b0Var.k();
        }
        Fragment fragment4 = this.f1852c;
        FragmentManager fragmentManager = fragment4.f1741r;
        fragment4.f1743s = fragmentManager.f1788q;
        fragment4.f1750x = fragmentManager.f1790s;
        this.f1850a.g(fragment4, false);
        Fragment fragment5 = this.f1852c;
        Iterator<Fragment.c> it = fragment5.G0.iterator();
        while (it.hasNext()) {
            it.next().a();
        }
        fragment5.G0.clear();
        fragment5.f1745t.b(fragment5.f1743s, fragment5.a(), fragment5);
        fragment5.f1720a = 0;
        fragment5.f1744s0 = false;
        fragment5.w(fragment5.f1743s.f2016b);
        if (fragment5.f1744s0) {
            FragmentManager fragmentManager2 = fragment5.f1741r;
            Iterator<a0> it2 = fragmentManager2.f1786o.iterator();
            while (it2.hasNext()) {
                it2.next().a(fragmentManager2, fragment5);
            }
            FragmentManager fragmentManager3 = fragment5.f1745t;
            fragmentManager3.B = false;
            fragmentManager3.C = false;
            fragmentManager3.J.h = false;
            fragmentManager3.w(0);
            this.f1850a.b(this.f1852c, false);
            return;
        }
        throw new z0(m.b("Fragment ", fragment5, " did not call through to super.onAttach()"));
    }

    public int d() {
        int i10;
        Fragment fragment = this.f1852c;
        if (fragment.f1741r == null) {
            return fragment.f1720a;
        }
        int i11 = this.f1854e;
        int i12 = b.f1856a[fragment.B0.ordinal()];
        int i13 = 0;
        if (i12 != 1) {
            if (i12 == 2) {
                i11 = Math.min(i11, 5);
            } else if (i12 == 3) {
                i11 = Math.min(i11, 1);
            } else if (i12 != 4) {
                i11 = Math.min(i11, -1);
            } else {
                i11 = Math.min(i11, 0);
            }
        }
        Fragment fragment2 = this.f1852c;
        if (fragment2.f1731m) {
            if (fragment2.f1733n) {
                i11 = Math.max(this.f1854e, 2);
                View view = this.f1852c.f1747u0;
                if (view != null && view.getParent() == null) {
                    i11 = Math.min(i11, 2);
                }
            } else {
                i11 = this.f1854e < 4 ? Math.min(i11, fragment2.f1720a) : Math.min(i11, 1);
            }
        }
        if (!this.f1852c.f1729k) {
            i11 = Math.min(i11, 1);
        }
        Fragment fragment3 = this.f1852c;
        ViewGroup viewGroup = fragment3.f1746t0;
        t0.b bVar = null;
        if (viewGroup != null) {
            t0 f10 = t0.f(viewGroup, fragment3.m().M());
            Objects.requireNonNull(f10);
            t0.b d10 = f10.d(this.f1852c);
            if (d10 != null) {
                i10 = d10.f2007b;
            } else {
                Fragment fragment4 = this.f1852c;
                Iterator<t0.b> it = f10.f2003c.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    t0.b next = it.next();
                    if (next.f2008c.equals(fragment4) && !next.f2011f) {
                        bVar = next;
                        break;
                    }
                }
                if (bVar != null) {
                    i10 = bVar.f2007b;
                }
            }
            i13 = i10;
        }
        if (i13 == 2) {
            i11 = Math.min(i11, 6);
        } else if (i13 == 3) {
            i11 = Math.max(i11, 3);
        } else {
            Fragment fragment5 = this.f1852c;
            if (fragment5.f1730l) {
                if (fragment5.t()) {
                    i11 = Math.min(i11, 1);
                } else {
                    i11 = Math.min(i11, -1);
                }
            }
        }
        Fragment fragment6 = this.f1852c;
        if (fragment6.f1748v0 && fragment6.f1720a < 5) {
            i11 = Math.min(i11, 4);
        }
        if (FragmentManager.O(2)) {
            StringBuilder b10 = f0.b("computeExpectedState() of ", i11, " for ");
            b10.append(this.f1852c);
            Log.v("FragmentManager", b10.toString());
        }
        return i11;
    }

    public void e() {
        Parcelable parcelable;
        if (FragmentManager.O(3)) {
            StringBuilder a10 = f.a("moveto CREATED: ");
            a10.append(this.f1852c);
            Log.d("FragmentManager", a10.toString());
        }
        Fragment fragment = this.f1852c;
        if (!fragment.A0) {
            this.f1850a.h(fragment, fragment.f1721b, false);
            Fragment fragment2 = this.f1852c;
            Bundle bundle = fragment2.f1721b;
            fragment2.f1745t.V();
            fragment2.f1720a = 1;
            fragment2.f1744s0 = false;
            fragment2.C0.a(
            /*  JADX ERROR: Method code generation error
                jadx.core.utils.exceptions.CodegenException: Error generate insn: 0x003e: INVOKE  
                  (wrap: androidx.lifecycle.h : 0x0037: IGET  (r3v4 androidx.lifecycle.h) = (r0v5 'fragment2' androidx.fragment.app.Fragment) androidx.fragment.app.Fragment.C0 androidx.lifecycle.h)
                  (wrap: androidx.fragment.app.Fragment$5 : 0x003b: CONSTRUCTOR  (r5v0 androidx.fragment.app.Fragment$5) = (r0v5 'fragment2' androidx.fragment.app.Fragment) call: androidx.fragment.app.Fragment.5.<init>(androidx.fragment.app.Fragment):void type: CONSTRUCTOR)
                 type: VIRTUAL call: androidx.lifecycle.h.a(androidx.lifecycle.f):void in method: androidx.fragment.app.b0.e():void, file: classes.dex
                	at jadx.core.codegen.InsnGen.makeInsn(InsnGen.java:255)
                	at jadx.core.codegen.InsnGen.makeInsn(InsnGen.java:217)
                	at jadx.core.codegen.RegionGen.makeSimpleBlock(RegionGen.java:110)
                	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:56)
                	at jadx.core.codegen.RegionGen.makeSimpleRegion(RegionGen.java:93)
                	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:59)
                	at jadx.core.codegen.RegionGen.makeRegionIndent(RegionGen.java:99)
                	at jadx.core.codegen.RegionGen.makeIf(RegionGen.java:143)
                	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:63)
                	at jadx.core.codegen.RegionGen.makeSimpleRegion(RegionGen.java:93)
                	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:59)
                	at jadx.core.codegen.RegionGen.makeSimpleRegion(RegionGen.java:93)
                	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:59)
                	at jadx.core.codegen.MethodGen.addRegionInsns(MethodGen.java:244)
                	at jadx.core.codegen.MethodGen.addInstructions(MethodGen.java:237)
                	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:342)
                	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:295)
                	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$2(ClassGen.java:264)
                	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
                	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
                	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
                	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
                Caused by: jadx.core.utils.exceptions.CodegenException: Error generate insn: 0x003b: CONSTRUCTOR  (r5v0 androidx.fragment.app.Fragment$5) = (r0v5 'fragment2' androidx.fragment.app.Fragment) call: androidx.fragment.app.Fragment.5.<init>(androidx.fragment.app.Fragment):void type: CONSTRUCTOR in method: androidx.fragment.app.b0.e():void, file: classes.dex
                	at jadx.core.codegen.InsnGen.makeInsn(InsnGen.java:255)
                	at jadx.core.codegen.InsnGen.addWrappedArg(InsnGen.java:119)
                	at jadx.core.codegen.InsnGen.addArg(InsnGen.java:103)
                	at jadx.core.codegen.InsnGen.generateMethodArguments(InsnGen.java:806)
                	at jadx.core.codegen.InsnGen.makeInvoke(InsnGen.java:746)
                	at jadx.core.codegen.InsnGen.makeInsnBody(InsnGen.java:367)
                	at jadx.core.codegen.InsnGen.makeInsn(InsnGen.java:249)
                	... 21 more
                Caused by: jadx.core.utils.exceptions.JadxRuntimeException: Expected class to be processed at this point, class: androidx.fragment.app.Fragment, state: GENERATED_AND_UNLOADED
                	at jadx.core.dex.nodes.ClassNode.ensureProcessed(ClassNode.java:215)
                	at jadx.core.codegen.InsnGen.makeConstructor(InsnGen.java:630)
                	at jadx.core.codegen.InsnGen.makeInsnBody(InsnGen.java:363)
                	at jadx.core.codegen.InsnGen.makeInsn(InsnGen.java:230)
                	... 27 more
                */
            /*
            // Method dump skipped, instructions count: 137
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.b0.e():void");
        }

        public void f() {
            String str;
            if (!this.f1852c.f1731m) {
                if (FragmentManager.O(3)) {
                    StringBuilder a10 = f.a("moveto CREATE_VIEW: ");
                    a10.append(this.f1852c);
                    Log.d("FragmentManager", a10.toString());
                }
                Fragment fragment = this.f1852c;
                LayoutInflater B = fragment.B(fragment.f1721b);
                ViewGroup viewGroup = null;
                Fragment fragment2 = this.f1852c;
                ViewGroup viewGroup2 = fragment2.f1746t0;
                if (viewGroup2 != null) {
                    viewGroup = viewGroup2;
                } else {
                    int i10 = fragment2.f1732m0;
                    if (i10 != 0) {
                        if (i10 != -1) {
                            viewGroup = (ViewGroup) fragment2.f1741r.f1789r.b(i10);
                            if (viewGroup == null) {
                                Fragment fragment3 = this.f1852c;
                                if (!fragment3.f1735o) {
                                    try {
                                        str = fragment3.o().getResourceName(this.f1852c.f1732m0);
                                    } catch (Resources.NotFoundException unused) {
                                        str = "unknown";
                                    }
                                    StringBuilder a11 = f.a("No view found for id 0x");
                                    a11.append(Integer.toHexString(this.f1852c.f1732m0));
                                    a11.append(" (");
                                    a11.append(str);
                                    a11.append(") for fragment ");
                                    a11.append(this.f1852c);
                                    throw new IllegalArgumentException(a11.toString());
                                }
                            }
                        } else {
                            StringBuilder a12 = f.a("Cannot create fragment ");
                            a12.append(this.f1852c);
                            a12.append(" for a container view with no id");
                            throw new IllegalArgumentException(a12.toString());
                        }
                    }
                }
                Fragment fragment4 = this.f1852c;
                fragment4.f1746t0 = viewGroup;
                fragment4.I(B, viewGroup, fragment4.f1721b);
                View view = this.f1852c.f1747u0;
                if (view != null) {
                    view.setSaveFromParentEnabled(false);
                    Fragment fragment5 = this.f1852c;
                    fragment5.f1747u0.setTag(R$id.fragment_container_view_tag, fragment5);
                    if (viewGroup != null) {
                        b();
                    }
                    Fragment fragment6 = this.f1852c;
                    if (fragment6.f1736o0) {
                        fragment6.f1747u0.setVisibility(8);
                    }
                    View view2 = this.f1852c.f1747u0;
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    if (view2.isAttachedToWindow()) {
                        this.f1852c.f1747u0.requestApplyInsets();
                    } else {
                        View view3 = this.f1852c.f1747u0;
                        view3.addOnAttachStateChangeListener(new a(this, view3));
                    }
                    this.f1852c.f1745t.w(2);
                    x xVar = this.f1850a;
                    Fragment fragment7 = this.f1852c;
                    xVar.m(fragment7, fragment7.f1747u0, fragment7.f1721b, false);
                    int visibility = this.f1852c.f1747u0.getVisibility();
                    this.f1852c.c().f1766j = this.f1852c.f1747u0.getAlpha();
                    Fragment fragment8 = this.f1852c;
                    if (fragment8.f1746t0 != null && visibility == 0) {
                        View findFocus = fragment8.f1747u0.findFocus();
                        if (findFocus != null) {
                            this.f1852c.c().f1767k = findFocus;
                            if (FragmentManager.O(2)) {
                                Log.v("FragmentManager", "requestFocus: Saved focused view " + findFocus + " for Fragment " + this.f1852c);
                            }
                        }
                        this.f1852c.f1747u0.setAlpha(Constant.VOLUME_FLOAT_MIN);
                    }
                }
                this.f1852c.f1720a = 2;
            }
        }

        public void g() {
            Fragment d10;
            boolean z10;
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("movefrom CREATED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            boolean z11 = fragment.f1730l && !fragment.t();
            if (z11 || this.f1851b.f1864c.d(this.f1852c)) {
                v<?> vVar = this.f1852c.f1743s;
                if (vVar instanceof x) {
                    z10 = this.f1851b.f1864c.f2028g;
                } else {
                    Context context = vVar.f2016b;
                    z10 = context instanceof Activity ? !((Activity) context).isChangingConfigurations() : true;
                }
                if (z11 || z10) {
                    z zVar = this.f1851b.f1864c;
                    Fragment fragment2 = this.f1852c;
                    Objects.requireNonNull(zVar);
                    if (FragmentManager.O(3)) {
                        Log.d("FragmentManager", "Clearing non-config state for " + fragment2);
                    }
                    z zVar2 = zVar.f2025d.get(fragment2.f1724e);
                    if (zVar2 != null) {
                        zVar2.a();
                        zVar.f2025d.remove(fragment2.f1724e);
                    }
                    w wVar = zVar.f2026e.get(fragment2.f1724e);
                    if (wVar != null) {
                        wVar.a();
                        zVar.f2026e.remove(fragment2.f1724e);
                    }
                }
                Fragment fragment3 = this.f1852c;
                fragment3.f1745t.o();
                fragment3.C0.e(Lifecycle.Event.ON_DESTROY);
                fragment3.f1720a = 0;
                fragment3.f1744s0 = false;
                fragment3.A0 = false;
                fragment3.f1744s0 = true;
                this.f1850a.d(this.f1852c, false);
                Iterator it = ((ArrayList) this.f1851b.f()).iterator();
                while (it.hasNext()) {
                    b0 b0Var = (b0) it.next();
                    if (b0Var != null) {
                        Fragment fragment4 = b0Var.f1852c;
                        if (this.f1852c.f1724e.equals(fragment4.h)) {
                            fragment4.f1726g = this.f1852c;
                            fragment4.h = null;
                        }
                    }
                }
                Fragment fragment5 = this.f1852c;
                String str = fragment5.h;
                if (str != null) {
                    fragment5.f1726g = this.f1851b.d(str);
                }
                this.f1851b.k(this);
                return;
            }
            String str2 = this.f1852c.h;
            if (!(str2 == null || (d10 = this.f1851b.d(str2)) == null || !d10.f1740q0)) {
                this.f1852c.f1726g = d10;
            }
            this.f1852c.f1720a = 0;
        }

        public void h() {
            View view;
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("movefrom CREATE_VIEW: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            ViewGroup viewGroup = fragment.f1746t0;
            if (!(viewGroup == null || (view = fragment.f1747u0) == null)) {
                viewGroup.removeView(view);
            }
            this.f1852c.J();
            this.f1850a.n(this.f1852c, false);
            Fragment fragment2 = this.f1852c;
            fragment2.f1746t0 = null;
            fragment2.f1747u0 = null;
            fragment2.D0 = null;
            fragment2.E0.h(null);
            this.f1852c.f1733n = false;
        }

        public void i() {
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("movefrom ATTACHED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            fragment.f1720a = -1;
            fragment.f1744s0 = false;
            fragment.A();
            if (fragment.f1744s0) {
                FragmentManager fragmentManager = fragment.f1745t;
                if (!fragmentManager.D) {
                    fragmentManager.o();
                    fragment.f1745t = new y();
                }
                this.f1850a.e(this.f1852c, false);
                Fragment fragment2 = this.f1852c;
                fragment2.f1720a = -1;
                fragment2.f1743s = null;
                fragment2.f1750x = null;
                fragment2.f1741r = null;
                if ((fragment2.f1730l && !fragment2.t()) || this.f1851b.f1864c.d(this.f1852c)) {
                    if (FragmentManager.O(3)) {
                        StringBuilder a11 = f.a("initState called for fragment: ");
                        a11.append(this.f1852c);
                        Log.d("FragmentManager", a11.toString());
                    }
                    Fragment fragment3 = this.f1852c;
                    Objects.requireNonNull(fragment3);
                    fragment3.C0 = new androidx.lifecycle.h(fragment3);
                    fragment3.F0 = new androidx.savedstate.b(fragment3);
                    fragment3.f1724e = UUID.randomUUID().toString();
                    fragment3.f1729k = false;
                    fragment3.f1730l = false;
                    fragment3.f1731m = false;
                    fragment3.f1733n = false;
                    fragment3.f1735o = false;
                    fragment3.f1739q = 0;
                    fragment3.f1741r = null;
                    fragment3.f1745t = new y();
                    fragment3.f1743s = null;
                    fragment3.f1752y = 0;
                    fragment3.f1732m0 = 0;
                    fragment3.f1734n0 = null;
                    fragment3.f1736o0 = false;
                    fragment3.f1738p0 = false;
                    return;
                }
                return;
            }
            throw new z0(m.b("Fragment ", fragment, " did not call through to super.onDetach()"));
        }

        public void j() {
            Fragment fragment = this.f1852c;
            if (fragment.f1731m && fragment.f1733n && !fragment.f1737p) {
                if (FragmentManager.O(3)) {
                    StringBuilder a10 = f.a("moveto CREATE_VIEW: ");
                    a10.append(this.f1852c);
                    Log.d("FragmentManager", a10.toString());
                }
                Fragment fragment2 = this.f1852c;
                fragment2.I(fragment2.B(fragment2.f1721b), null, this.f1852c.f1721b);
                View view = this.f1852c.f1747u0;
                if (view != null) {
                    view.setSaveFromParentEnabled(false);
                    Fragment fragment3 = this.f1852c;
                    fragment3.f1747u0.setTag(R$id.fragment_container_view_tag, fragment3);
                    Fragment fragment4 = this.f1852c;
                    if (fragment4.f1736o0) {
                        fragment4.f1747u0.setVisibility(8);
                    }
                    this.f1852c.f1745t.w(2);
                    x xVar = this.f1850a;
                    Fragment fragment5 = this.f1852c;
                    xVar.m(fragment5, fragment5.f1747u0, fragment5.f1721b, false);
                    this.f1852c.f1720a = 2;
                }
            }
        }

        /* JADX DEBUG: Multi-variable search result rejected for r4v4, resolved type: androidx.fragment.app.t0 */
        /* JADX DEBUG: Multi-variable search result rejected for r4v12, resolved type: androidx.fragment.app.t0 */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r3v0, types: [boolean, int] */
        /* JADX WARNING: Unknown variable types count: 1 */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public void k() {
            /*
            // Method dump skipped, instructions count: 458
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.b0.k():void");
        }

        public void l() {
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("movefrom RESUMED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            fragment.f1745t.w(5);
            if (fragment.f1747u0 != null) {
                fragment.D0.a(Lifecycle.Event.ON_PAUSE);
            }
            fragment.C0.e(Lifecycle.Event.ON_PAUSE);
            fragment.f1720a = 6;
            fragment.f1744s0 = false;
            fragment.f1744s0 = true;
            this.f1850a.f(this.f1852c, false);
        }

        public void m(@NonNull ClassLoader classLoader) {
            Bundle bundle = this.f1852c.f1721b;
            if (bundle != null) {
                bundle.setClassLoader(classLoader);
                Fragment fragment = this.f1852c;
                fragment.f1722c = fragment.f1721b.getSparseParcelableArray("android:view_state");
                Fragment fragment2 = this.f1852c;
                fragment2.f1723d = fragment2.f1721b.getBundle("android:view_registry_state");
                Fragment fragment3 = this.f1852c;
                fragment3.h = fragment3.f1721b.getString("android:target_state");
                Fragment fragment4 = this.f1852c;
                if (fragment4.h != null) {
                    fragment4.f1727i = fragment4.f1721b.getInt("android:target_req_state", 0);
                }
                Fragment fragment5 = this.f1852c;
                Objects.requireNonNull(fragment5);
                fragment5.f1749w0 = fragment5.f1721b.getBoolean("android:user_visible_hint", true);
                Fragment fragment6 = this.f1852c;
                if (!fragment6.f1749w0) {
                    fragment6.f1748v0 = true;
                }
            }
        }

        public void n() {
            View view;
            boolean z10;
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("moveto RESUMED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            Fragment.b bVar = fragment.f1751x0;
            if (bVar == null) {
                view = null;
            } else {
                view = bVar.f1767k;
            }
            if (view != null) {
                if (view != fragment.f1747u0) {
                    ViewParent parent = view.getParent();
                    while (true) {
                        if (parent == null) {
                            z10 = false;
                            break;
                        } else if (parent == this.f1852c.f1747u0) {
                            break;
                        } else {
                            parent = parent.getParent();
                        }
                    }
                }
                z10 = true;
                if (z10) {
                    boolean requestFocus = view.requestFocus();
                    if (FragmentManager.O(2)) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("requestFocus: Restoring focused view ");
                        sb.append(view);
                        sb.append(" ");
                        sb.append(requestFocus ? "succeeded" : "failed");
                        sb.append(" on Fragment ");
                        sb.append(this.f1852c);
                        sb.append(" resulting in focused view ");
                        sb.append(this.f1852c.f1747u0.findFocus());
                        Log.v("FragmentManager", sb.toString());
                    }
                }
            }
            this.f1852c.S(null);
            Fragment fragment2 = this.f1852c;
            fragment2.f1745t.V();
            fragment2.f1745t.C(true);
            fragment2.f1720a = 7;
            fragment2.f1744s0 = false;
            fragment2.f1744s0 = true;
            androidx.lifecycle.h hVar = fragment2.C0;
            Lifecycle.Event event = Lifecycle.Event.ON_RESUME;
            hVar.e(event);
            if (fragment2.f1747u0 != null) {
                fragment2.D0.a(event);
            }
            FragmentManager fragmentManager = fragment2.f1745t;
            fragmentManager.B = false;
            fragmentManager.C = false;
            fragmentManager.J.h = false;
            fragmentManager.w(7);
            this.f1850a.i(this.f1852c, false);
            Fragment fragment3 = this.f1852c;
            fragment3.f1721b = null;
            fragment3.f1722c = null;
            fragment3.f1723d = null;
        }

        public void o() {
            if (this.f1852c.f1747u0 != null) {
                SparseArray<Parcelable> sparseArray = new SparseArray<>();
                this.f1852c.f1747u0.saveHierarchyState(sparseArray);
                if (sparseArray.size() > 0) {
                    this.f1852c.f1722c = sparseArray;
                }
                Bundle bundle = new Bundle();
                this.f1852c.D0.f1981b.b(bundle);
                if (!bundle.isEmpty()) {
                    this.f1852c.f1723d = bundle;
                }
            }
        }

        public void p() {
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("moveto STARTED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            fragment.f1745t.V();
            fragment.f1745t.C(true);
            fragment.f1720a = 5;
            fragment.f1744s0 = false;
            fragment.E();
            if (fragment.f1744s0) {
                androidx.lifecycle.h hVar = fragment.C0;
                Lifecycle.Event event = Lifecycle.Event.ON_START;
                hVar.e(event);
                if (fragment.f1747u0 != null) {
                    fragment.D0.a(event);
                }
                FragmentManager fragmentManager = fragment.f1745t;
                fragmentManager.B = false;
                fragmentManager.C = false;
                fragmentManager.J.h = false;
                fragmentManager.w(5);
                this.f1850a.k(this.f1852c, false);
                return;
            }
            throw new z0(m.b("Fragment ", fragment, " did not call through to super.onStart()"));
        }

        public void q() {
            if (FragmentManager.O(3)) {
                StringBuilder a10 = f.a("movefrom STARTED: ");
                a10.append(this.f1852c);
                Log.d("FragmentManager", a10.toString());
            }
            Fragment fragment = this.f1852c;
            FragmentManager fragmentManager = fragment.f1745t;
            fragmentManager.C = true;
            fragmentManager.J.h = true;
            fragmentManager.w(4);
            if (fragment.f1747u0 != null) {
                fragment.D0.a(Lifecycle.Event.ON_STOP);
            }
            fragment.C0.e(Lifecycle.Event.ON_STOP);
            fragment.f1720a = 4;
            fragment.f1744s0 = false;
            fragment.F();
            if (fragment.f1744s0) {
                this.f1850a.l(this.f1852c, false);
                return;
            }
            throw new z0(m.b("Fragment ", fragment, " did not call through to super.onStop()"));
        }

        public b0(@NonNull x xVar, @NonNull c0 c0Var, @NonNull ClassLoader classLoader, @NonNull u uVar, @NonNull FragmentState fragmentState) {
            this.f1850a = xVar;
            this.f1851b = c0Var;
            Fragment a10 = uVar.a(classLoader, fragmentState.f1821a);
            this.f1852c = a10;
            Bundle bundle = fragmentState.f1829j;
            if (bundle != null) {
                bundle.setClassLoader(classLoader);
            }
            a10.R(fragmentState.f1829j);
            a10.f1724e = fragmentState.f1822b;
            a10.f1731m = fragmentState.f1823c;
            a10.f1735o = true;
            a10.f1752y = fragmentState.f1824d;
            a10.f1732m0 = fragmentState.f1825e;
            a10.f1734n0 = fragmentState.f1826f;
            a10.f1740q0 = fragmentState.f1827g;
            a10.f1730l = fragmentState.h;
            a10.f1738p0 = fragmentState.f1828i;
            a10.f1736o0 = fragmentState.f1830k;
            a10.B0 = Lifecycle.State.values()[fragmentState.f1831l];
            Bundle bundle2 = fragmentState.f1832m;
            if (bundle2 != null) {
                a10.f1721b = bundle2;
            } else {
                a10.f1721b = new Bundle();
            }
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "Instantiated fragment " + a10);
            }
        }

        public b0(@NonNull x xVar, @NonNull c0 c0Var, @NonNull Fragment fragment, @NonNull FragmentState fragmentState) {
            this.f1850a = xVar;
            this.f1851b = c0Var;
            this.f1852c = fragment;
            fragment.f1722c = null;
            fragment.f1723d = null;
            fragment.f1739q = 0;
            fragment.f1733n = false;
            fragment.f1729k = false;
            Fragment fragment2 = fragment.f1726g;
            fragment.h = fragment2 != null ? fragment2.f1724e : null;
            fragment.f1726g = null;
            Bundle bundle = fragmentState.f1832m;
            if (bundle != null) {
                fragment.f1721b = bundle;
            } else {
                fragment.f1721b = new Bundle();
            }
        }
    }
