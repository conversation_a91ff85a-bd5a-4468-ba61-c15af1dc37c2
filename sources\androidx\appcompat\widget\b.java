package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$styleable;
import androidx.appcompat.view.menu.d;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.m;
import j0.n;

/* compiled from: AbsActionBarView */
public abstract class b extends ViewGroup {

    /* renamed from: a  reason: collision with root package name */
    public final a f1048a;

    /* renamed from: b  reason: collision with root package name */
    public final Context f1049b;

    /* renamed from: c  reason: collision with root package name */
    public ActionMenuView f1050c;

    /* renamed from: d  reason: collision with root package name */
    public ActionMenuPresenter f1051d;

    /* renamed from: e  reason: collision with root package name */
    public int f1052e;

    /* renamed from: f  reason: collision with root package name */
    public m f1053f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f1054g;
    public boolean h;

    /* compiled from: AbsActionBarView */
    public class a implements n {

        /* renamed from: a  reason: collision with root package name */
        public boolean f1055a = false;

        /* renamed from: b  reason: collision with root package name */
        public int f1056b;

        public a() {
        }

        @Override // j0.n
        public void b(View view) {
            this.f1055a = true;
        }

        @Override // j0.n
        public void d(View view) {
            if (!this.f1055a) {
                b bVar = b.this;
                bVar.f1053f = null;
                b.super.setVisibility(this.f1056b);
            }
        }

        @Override // j0.n
        public void e(View view) {
            b.super.setVisibility(0);
            this.f1055a = false;
        }
    }

    public b(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public int c(View view, int i10, int i11, int i12) {
        view.measure(View.MeasureSpec.makeMeasureSpec(i10, EventResultPersister.GENERATE_NEW_ID), i11);
        return Math.max(0, (i10 - view.getMeasuredWidth()) - i12);
    }

    public int d(View view, int i10, int i11, int i12, boolean z10) {
        int measuredWidth = view.getMeasuredWidth();
        int measuredHeight = view.getMeasuredHeight();
        int a10 = a.a(i12, measuredHeight, 2, i11);
        if (z10) {
            view.layout(i10 - measuredWidth, a10, i10, measuredHeight + a10);
        } else {
            view.layout(i10, a10, i10 + measuredWidth, measuredHeight + a10);
        }
        return z10 ? -measuredWidth : measuredWidth;
    }

    public m e(int i10, long j10) {
        m mVar = this.f1053f;
        if (mVar != null) {
            mVar.b();
        }
        if (i10 == 0) {
            if (getVisibility() != 0) {
                setAlpha(Constant.VOLUME_FLOAT_MIN);
            }
            m a10 = ViewCompat.a(this);
            a10.a(1.0f);
            a10.c(j10);
            a aVar = this.f1048a;
            b.this.f1053f = a10;
            aVar.f1056b = i10;
            View view = a10.f6912a.get();
            if (view != null) {
                a10.e(view, aVar);
            }
            return a10;
        }
        m a11 = ViewCompat.a(this);
        a11.a(Constant.VOLUME_FLOAT_MIN);
        a11.c(j10);
        a aVar2 = this.f1048a;
        b.this.f1053f = a11;
        aVar2.f1056b = i10;
        View view2 = a11.f6912a.get();
        if (view2 != null) {
            a11.e(view2, aVar2);
        }
        return a11;
    }

    public int getAnimatedVisibility() {
        if (this.f1053f != null) {
            return this.f1048a.f1056b;
        }
        return getVisibility();
    }

    public int getContentHeight() {
        return this.f1052e;
    }

    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(null, R$styleable.ActionBar, R$attr.actionBarStyle, 0);
        setContentHeight(obtainStyledAttributes.getLayoutDimension(R$styleable.ActionBar_height, 0));
        obtainStyledAttributes.recycle();
        ActionMenuPresenter actionMenuPresenter = this.f1051d;
        if (actionMenuPresenter != null) {
            Configuration configuration2 = actionMenuPresenter.f581b.getResources().getConfiguration();
            int i10 = configuration2.screenWidthDp;
            int i11 = configuration2.screenHeightDp;
            actionMenuPresenter.f752q = (configuration2.smallestScreenWidthDp > 600 || i10 > 600 || (i10 > 960 && i11 > 720) || (i10 > 720 && i11 > 960)) ? 5 : (i10 >= 500 || (i10 > 640 && i11 > 480) || (i10 > 480 && i11 > 640)) ? 4 : i10 >= 360 ? 3 : 2;
            d dVar = actionMenuPresenter.f582c;
            if (dVar != null) {
                dVar.p(true);
            }
        }
    }

    public boolean onHoverEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 9) {
            this.h = false;
        }
        if (!this.h) {
            boolean onHoverEvent = super.onHoverEvent(motionEvent);
            if (actionMasked == 9 && !onHoverEvent) {
                this.h = true;
            }
        }
        if (actionMasked == 10 || actionMasked == 3) {
            this.h = false;
        }
        return true;
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.f1054g = false;
        }
        if (!this.f1054g) {
            boolean onTouchEvent = super.onTouchEvent(motionEvent);
            if (actionMasked == 0 && !onTouchEvent) {
                this.f1054g = true;
            }
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.f1054g = false;
        }
        return true;
    }

    public void setContentHeight(int i10) {
        this.f1052e = i10;
        requestLayout();
    }

    public void setVisibility(int i10) {
        if (i10 != getVisibility()) {
            m mVar = this.f1053f;
            if (mVar != null) {
                mVar.b();
            }
            super.setVisibility(i10);
        }
    }

    public b(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f1048a = new a();
        TypedValue typedValue = new TypedValue();
        if (!context.getTheme().resolveAttribute(R$attr.actionBarPopupTheme, typedValue, true) || typedValue.resourceId == 0) {
            this.f1049b = context;
        } else {
            this.f1049b = new ContextThemeWrapper(context, typedValue.resourceId);
        }
    }
}
