package androidx.constraintlayout.solver;

import com.duokan.airkan.common.Constant;
import java.util.Arrays;

public class SolverVariable {

    /* renamed from: j  reason: collision with root package name */
    public static int f1239j = 1;

    /* renamed from: a  reason: collision with root package name */
    public int f1240a = -1;

    /* renamed from: b  reason: collision with root package name */
    public int f1241b = -1;

    /* renamed from: c  reason: collision with root package name */
    public int f1242c = 0;

    /* renamed from: d  reason: collision with root package name */
    public float f1243d;

    /* renamed from: e  reason: collision with root package name */
    public float[] f1244e = new float[7];

    /* renamed from: f  reason: collision with root package name */
    public Type f1245f;

    /* renamed from: g  reason: collision with root package name */
    public b[] f1246g = new b[8];
    public int h = 0;

    /* renamed from: i  reason: collision with root package name */
    public int f1247i = 0;

    public enum Type {
        UNRESTRICTED,
        CONSTANT,
        SLACK,
        ERROR,
        UNKNOWN
    }

    public SolverVariable(Type type) {
        this.f1245f = type;
    }

    public final void a(b bVar) {
        int i10 = 0;
        while (true) {
            int i11 = this.h;
            if (i10 >= i11) {
                b[] bVarArr = this.f1246g;
                if (i11 >= bVarArr.length) {
                    this.f1246g = (b[]) Arrays.copyOf(bVarArr, bVarArr.length * 2);
                }
                b[] bVarArr2 = this.f1246g;
                int i12 = this.h;
                bVarArr2[i12] = bVar;
                this.h = i12 + 1;
                return;
            } else if (this.f1246g[i10] != bVar) {
                i10++;
            } else {
                return;
            }
        }
    }

    public final void b(b bVar) {
        int i10 = this.h;
        for (int i11 = 0; i11 < i10; i11++) {
            if (this.f1246g[i11] == bVar) {
                for (int i12 = 0; i12 < (i10 - i11) - 1; i12++) {
                    b[] bVarArr = this.f1246g;
                    int i13 = i11 + i12;
                    bVarArr[i13] = bVarArr[i13 + 1];
                }
                this.h--;
                return;
            }
        }
    }

    public void c() {
        this.f1245f = Type.UNKNOWN;
        this.f1242c = 0;
        this.f1240a = -1;
        this.f1241b = -1;
        this.f1243d = Constant.VOLUME_FLOAT_MIN;
        this.h = 0;
        this.f1247i = 0;
    }

    public final void d(b bVar) {
        int i10 = this.h;
        for (int i11 = 0; i11 < i10; i11++) {
            b[] bVarArr = this.f1246g;
            a aVar = bVarArr[i11].f1260c;
            b bVar2 = bVarArr[i11];
            int i12 = aVar.h;
            while (true) {
                int i13 = 0;
                while (i12 != -1 && i13 < aVar.f1249a) {
                    int i14 = aVar.f1253e[i12];
                    SolverVariable solverVariable = bVar.f1258a;
                    if (i14 == solverVariable.f1240a) {
                        float f10 = aVar.f1255g[i12];
                        aVar.i(solverVariable, false);
                        a aVar2 = bVar.f1260c;
                        int i15 = aVar2.h;
                        int i16 = 0;
                        while (i15 != -1 && i16 < aVar2.f1249a) {
                            aVar.a(aVar.f1251c.f10542c[aVar2.f1253e[i15]], aVar2.f1255g[i15] * f10, false);
                            i15 = aVar2.f1254f[i15];
                            i16++;
                        }
                        bVar2.f1259b = (bVar.f1259b * f10) + bVar2.f1259b;
                        i12 = aVar.h;
                    } else {
                        i12 = aVar.f1254f[i12];
                        i13++;
                    }
                }
            }
        }
        this.h = 0;
    }

    public String toString() {
        return "null";
    }
}
