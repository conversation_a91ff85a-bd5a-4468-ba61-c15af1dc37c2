syntax = "proto3";

package com.xiaomi.idm.uwb.proto;

option java_package = "com.xiaomi.idm.uwb.proto";
option java_outer_classname = "UwbCommand";

// Commands
message SetServerSecurityKey {
  string memoryKey = 1;
  string strangeKey = 2;
}

message StartScan {
  int32 appId = 1;
  int32 deviceRole = 2;
  int32 projectId = 3;
  string uwbAddress = 4;
  int32 timeout = 5;
}

message StartADV {
  string uwbAddress = 1;
  int32 timeout = 2;
  int32 projectId = 3;
}

message Connect {
  string uwbAddress = 1;
}

message Disconnect {
  string uwbAddress = 2;
}

message SendPayload {
  string uwbAddress = 1;
  bytes payload = 2;
  int32 flag = 3;
  int32 target = 4;
  int32 taskId = 5;
}

message EstablishSecurityLine {
  string uwbAddress = 1;
  string key = 2;
  bytes uidHash = 3;
  int32 memberType = 4;
  int32 appId = 5;
}

message StopUwb {
  string uwbAddress = 1;
}

message SwitchCommunicationType {
  int32 appId = 1;
  string uwbAddress = 2;
}

// onCommands
message OnSendPayload {
  int32 taskId = 1;
}

message OnTagOTA {
  int32 taskId = 1;
}