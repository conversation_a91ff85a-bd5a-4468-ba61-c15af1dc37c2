package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.n;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.List;
import java.util.Objects;
import k0.b;

public class StaggeredGridLayoutManager extends RecyclerView.j implements RecyclerView.s.b {
    public int A;
    public LazySpanLookup B;
    public int C;
    public boolean D;
    public boolean E;
    public SavedState F;
    public final Rect G;
    public final b H;
    public boolean I;
    public int[] J;
    public final Runnable K;

    /* renamed from: p  reason: collision with root package name */
    public int f2284p = -1;

    /* renamed from: q  reason: collision with root package name */
    public c[] f2285q;
    @NonNull

    /* renamed from: r  reason: collision with root package name */
    public v f2286r;
    @NonNull

    /* renamed from: s  reason: collision with root package name */
    public v f2287s;

    /* renamed from: t  reason: collision with root package name */
    public int f2288t;

    /* renamed from: u  reason: collision with root package name */
    public int f2289u;
    @NonNull

    /* renamed from: v  reason: collision with root package name */
    public final p f2290v;

    /* renamed from: w  reason: collision with root package name */
    public boolean f2291w;

    /* renamed from: x  reason: collision with root package name */
    public boolean f2292x;

    /* renamed from: y  reason: collision with root package name */
    public BitSet f2293y;

    /* renamed from: z  reason: collision with root package name */
    public int f2294z;

    public static class LayoutParams extends RecyclerView.LayoutParams {

        /* renamed from: e  reason: collision with root package name */
        public c f2295e;

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public static class SavedState implements Parcelable {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public int f2302a;

        /* renamed from: b  reason: collision with root package name */
        public int f2303b;

        /* renamed from: c  reason: collision with root package name */
        public int f2304c;

        /* renamed from: d  reason: collision with root package name */
        public int[] f2305d;

        /* renamed from: e  reason: collision with root package name */
        public int f2306e;

        /* renamed from: f  reason: collision with root package name */
        public int[] f2307f;

        /* renamed from: g  reason: collision with root package name */
        public List<LazySpanLookup.FullSpanItem> f2308g;
        public boolean h;

        /* renamed from: i  reason: collision with root package name */
        public boolean f2309i;

        /* renamed from: j  reason: collision with root package name */
        public boolean f2310j;

        public static class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState() {
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeInt(this.f2302a);
            parcel.writeInt(this.f2303b);
            parcel.writeInt(this.f2304c);
            if (this.f2304c > 0) {
                parcel.writeIntArray(this.f2305d);
            }
            parcel.writeInt(this.f2306e);
            if (this.f2306e > 0) {
                parcel.writeIntArray(this.f2307f);
            }
            parcel.writeInt(this.h ? 1 : 0);
            parcel.writeInt(this.f2309i ? 1 : 0);
            parcel.writeInt(this.f2310j ? 1 : 0);
            parcel.writeList(this.f2308g);
        }

        public SavedState(Parcel parcel) {
            this.f2302a = parcel.readInt();
            this.f2303b = parcel.readInt();
            int readInt = parcel.readInt();
            this.f2304c = readInt;
            if (readInt > 0) {
                int[] iArr = new int[readInt];
                this.f2305d = iArr;
                parcel.readIntArray(iArr);
            }
            int readInt2 = parcel.readInt();
            this.f2306e = readInt2;
            if (readInt2 > 0) {
                int[] iArr2 = new int[readInt2];
                this.f2307f = iArr2;
                parcel.readIntArray(iArr2);
            }
            boolean z10 = false;
            this.h = parcel.readInt() == 1;
            this.f2309i = parcel.readInt() == 1;
            this.f2310j = parcel.readInt() == 1 ? true : z10;
            this.f2308g = parcel.readArrayList(LazySpanLookup.FullSpanItem.class.getClassLoader());
        }

        public SavedState(SavedState savedState) {
            this.f2304c = savedState.f2304c;
            this.f2302a = savedState.f2302a;
            this.f2303b = savedState.f2303b;
            this.f2305d = savedState.f2305d;
            this.f2306e = savedState.f2306e;
            this.f2307f = savedState.f2307f;
            this.h = savedState.h;
            this.f2309i = savedState.f2309i;
            this.f2310j = savedState.f2310j;
            this.f2308g = savedState.f2308g;
        }
    }

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            StaggeredGridLayoutManager.this.M0();
        }
    }

    public class b {

        /* renamed from: a  reason: collision with root package name */
        public int f2312a;

        /* renamed from: b  reason: collision with root package name */
        public int f2313b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f2314c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f2315d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f2316e;

        /* renamed from: f  reason: collision with root package name */
        public int[] f2317f;

        public b() {
            b();
        }

        public void a() {
            int i10;
            if (this.f2314c) {
                i10 = StaggeredGridLayoutManager.this.f2286r.g();
            } else {
                i10 = StaggeredGridLayoutManager.this.f2286r.k();
            }
            this.f2313b = i10;
        }

        public void b() {
            this.f2312a = -1;
            this.f2313b = EventResultPersister.GENERATE_NEW_ID;
            this.f2314c = false;
            this.f2315d = false;
            this.f2316e = false;
            int[] iArr = this.f2317f;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
        }
    }

    public class c {

        /* renamed from: a  reason: collision with root package name */
        public ArrayList<View> f2319a = new ArrayList<>();

        /* renamed from: b  reason: collision with root package name */
        public int f2320b = EventResultPersister.GENERATE_NEW_ID;

        /* renamed from: c  reason: collision with root package name */
        public int f2321c = EventResultPersister.GENERATE_NEW_ID;

        /* renamed from: d  reason: collision with root package name */
        public int f2322d = 0;

        /* renamed from: e  reason: collision with root package name */
        public final int f2323e;

        public c(int i10) {
            this.f2323e = i10;
        }

        public void a(View view) {
            LayoutParams j10 = j(view);
            j10.f2295e = this;
            this.f2319a.add(view);
            this.f2321c = EventResultPersister.GENERATE_NEW_ID;
            if (this.f2319a.size() == 1) {
                this.f2320b = EventResultPersister.GENERATE_NEW_ID;
            }
            if (j10.c() || j10.b()) {
                this.f2322d = StaggeredGridLayoutManager.this.f2286r.c(view) + this.f2322d;
            }
        }

        public void b() {
            ArrayList<View> arrayList = this.f2319a;
            View view = arrayList.get(arrayList.size() - 1);
            LayoutParams j10 = j(view);
            this.f2321c = StaggeredGridLayoutManager.this.f2286r.b(view);
            Objects.requireNonNull(j10);
        }

        public void c() {
            View view = this.f2319a.get(0);
            LayoutParams j10 = j(view);
            this.f2320b = StaggeredGridLayoutManager.this.f2286r.e(view);
            Objects.requireNonNull(j10);
        }

        public void d() {
            this.f2319a.clear();
            this.f2320b = EventResultPersister.GENERATE_NEW_ID;
            this.f2321c = EventResultPersister.GENERATE_NEW_ID;
            this.f2322d = 0;
        }

        public int e() {
            if (StaggeredGridLayoutManager.this.f2291w) {
                return g(this.f2319a.size() - 1, -1, true);
            }
            return g(0, this.f2319a.size(), true);
        }

        public int f() {
            if (StaggeredGridLayoutManager.this.f2291w) {
                return g(0, this.f2319a.size(), true);
            }
            return g(this.f2319a.size() - 1, -1, true);
        }

        public int g(int i10, int i11, boolean z10) {
            int k10 = StaggeredGridLayoutManager.this.f2286r.k();
            int g10 = StaggeredGridLayoutManager.this.f2286r.g();
            int i12 = i11 > i10 ? 1 : -1;
            while (i10 != i11) {
                View view = this.f2319a.get(i10);
                int e10 = StaggeredGridLayoutManager.this.f2286r.e(view);
                int b10 = StaggeredGridLayoutManager.this.f2286r.b(view);
                boolean z11 = false;
                boolean z12 = !z10 ? e10 < g10 : e10 <= g10;
                if (!z10 ? b10 > k10 : b10 >= k10) {
                    z11 = true;
                }
                if (z12 && z11 && (e10 < k10 || b10 > g10)) {
                    return StaggeredGridLayoutManager.this.Q(view);
                }
                i10 += i12;
            }
            return -1;
        }

        public int h(int i10) {
            int i11 = this.f2321c;
            if (i11 != Integer.MIN_VALUE) {
                return i11;
            }
            if (this.f2319a.size() == 0) {
                return i10;
            }
            b();
            return this.f2321c;
        }

        public View i(int i10, int i11) {
            View view = null;
            if (i11 != -1) {
                int size = this.f2319a.size() - 1;
                while (size >= 0) {
                    View view2 = this.f2319a.get(size);
                    StaggeredGridLayoutManager staggeredGridLayoutManager = StaggeredGridLayoutManager.this;
                    if (staggeredGridLayoutManager.f2291w && staggeredGridLayoutManager.Q(view2) >= i10) {
                        break;
                    }
                    StaggeredGridLayoutManager staggeredGridLayoutManager2 = StaggeredGridLayoutManager.this;
                    if ((!staggeredGridLayoutManager2.f2291w && staggeredGridLayoutManager2.Q(view2) <= i10) || !view2.hasFocusable()) {
                        break;
                    }
                    size--;
                    view = view2;
                }
            } else {
                int size2 = this.f2319a.size();
                int i12 = 0;
                while (i12 < size2) {
                    View view3 = this.f2319a.get(i12);
                    StaggeredGridLayoutManager staggeredGridLayoutManager3 = StaggeredGridLayoutManager.this;
                    if (staggeredGridLayoutManager3.f2291w && staggeredGridLayoutManager3.Q(view3) <= i10) {
                        break;
                    }
                    StaggeredGridLayoutManager staggeredGridLayoutManager4 = StaggeredGridLayoutManager.this;
                    if ((!staggeredGridLayoutManager4.f2291w && staggeredGridLayoutManager4.Q(view3) >= i10) || !view3.hasFocusable()) {
                        break;
                    }
                    i12++;
                    view = view3;
                }
            }
            return view;
        }

        public LayoutParams j(View view) {
            return (LayoutParams) view.getLayoutParams();
        }

        public int k(int i10) {
            int i11 = this.f2320b;
            if (i11 != Integer.MIN_VALUE) {
                return i11;
            }
            if (this.f2319a.size() == 0) {
                return i10;
            }
            c();
            return this.f2320b;
        }

        public void l() {
            int size = this.f2319a.size();
            View remove = this.f2319a.remove(size - 1);
            LayoutParams j10 = j(remove);
            j10.f2295e = null;
            if (j10.c() || j10.b()) {
                this.f2322d -= StaggeredGridLayoutManager.this.f2286r.c(remove);
            }
            if (size == 1) {
                this.f2320b = EventResultPersister.GENERATE_NEW_ID;
            }
            this.f2321c = EventResultPersister.GENERATE_NEW_ID;
        }

        public void m() {
            View remove = this.f2319a.remove(0);
            LayoutParams j10 = j(remove);
            j10.f2295e = null;
            if (this.f2319a.size() == 0) {
                this.f2321c = EventResultPersister.GENERATE_NEW_ID;
            }
            if (j10.c() || j10.b()) {
                this.f2322d -= StaggeredGridLayoutManager.this.f2286r.c(remove);
            }
            this.f2320b = EventResultPersister.GENERATE_NEW_ID;
        }

        public void n(View view) {
            LayoutParams j10 = j(view);
            j10.f2295e = this;
            this.f2319a.add(0, view);
            this.f2320b = EventResultPersister.GENERATE_NEW_ID;
            if (this.f2319a.size() == 1) {
                this.f2321c = EventResultPersister.GENERATE_NEW_ID;
            }
            if (j10.c() || j10.b()) {
                this.f2322d = StaggeredGridLayoutManager.this.f2286r.c(view) + this.f2322d;
            }
        }
    }

    public StaggeredGridLayoutManager(Context context, AttributeSet attributeSet, int i10, int i11) {
        this.f2291w = false;
        this.f2292x = false;
        this.f2294z = -1;
        this.A = EventResultPersister.GENERATE_NEW_ID;
        this.B = new LazySpanLookup();
        this.C = 2;
        this.G = new Rect();
        this.H = new b();
        this.I = true;
        this.K = new a();
        RecyclerView.j.d R = RecyclerView.j.R(context, attributeSet, i10, i11);
        int i12 = R.f2214a;
        if (i12 == 0 || i12 == 1) {
            d(null);
            if (i12 != this.f2288t) {
                this.f2288t = i12;
                v vVar = this.f2286r;
                this.f2286r = this.f2287s;
                this.f2287s = vVar;
                w0();
            }
            int i13 = R.f2215b;
            d(null);
            if (i13 != this.f2284p) {
                this.B.a();
                w0();
                this.f2284p = i13;
                this.f2293y = new BitSet(this.f2284p);
                this.f2285q = new c[this.f2284p];
                for (int i14 = 0; i14 < this.f2284p; i14++) {
                    this.f2285q[i14] = new c(i14);
                }
                w0();
            }
            boolean z10 = R.f2216c;
            d(null);
            SavedState savedState = this.F;
            if (!(savedState == null || savedState.h == z10)) {
                savedState.h = z10;
            }
            this.f2291w = z10;
            w0();
            this.f2290v = new p();
            this.f2286r = v.a(this, this.f2288t);
            this.f2287s = v.a(this, 1 - this.f2288t);
            return;
        }
        throw new IllegalArgumentException("invalid orientation.");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void C0(Rect rect, int i10, int i11) {
        int i12;
        int i13;
        int O = O() + N();
        int M = M() + P();
        if (this.f2288t == 1) {
            i13 = RecyclerView.j.h(i11, rect.height() + M, K());
            i12 = RecyclerView.j.h(i10, (this.f2289u * this.f2284p) + O, L());
        } else {
            i12 = RecyclerView.j.h(i10, rect.width() + O, L());
            i13 = RecyclerView.j.h(i11, (this.f2289u * this.f2284p) + M, K());
        }
        this.f2199b.setMeasuredDimension(i12, i13);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void I0(RecyclerView recyclerView, RecyclerView.t tVar, int i10) {
        q qVar = new q(recyclerView.getContext());
        qVar.f2232a = i10;
        J0(qVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean K0() {
        return this.F == null;
    }

    public final int L0(int i10) {
        if (x() == 0) {
            return this.f2292x ? 1 : -1;
        }
        return (i10 < V0()) != this.f2292x ? -1 : 1;
    }

    public boolean M0() {
        int i10;
        if (!(x() == 0 || this.C == 0 || !this.f2204g)) {
            if (this.f2292x) {
                i10 = W0();
                V0();
            } else {
                i10 = V0();
                W0();
            }
            if (i10 == 0 && a1() != null) {
                this.B.a();
                this.f2203f = true;
                w0();
                return true;
            }
        }
        return false;
    }

    public final int N0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        return a0.a(tVar, this.f2286r, S0(!this.I), R0(!this.I), this, this.I);
    }

    public final int O0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        return a0.b(tVar, this.f2286r, S0(!this.I), R0(!this.I), this, this.I, this.f2292x);
    }

    public final int P0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        return a0.c(tVar, this.f2286r, S0(!this.I), R0(!this.I), this, this.I);
    }

    public final int Q0(RecyclerView.p pVar, p pVar2, RecyclerView.t tVar) {
        int i10;
        int i11;
        int i12;
        c cVar;
        boolean z10;
        int i13;
        int i14;
        int i15;
        int i16;
        boolean z11;
        int i17;
        int i18;
        int i19;
        int i20;
        boolean z12 = false;
        this.f2293y.set(0, this.f2284p, true);
        if (this.f2290v.f2461i) {
            i10 = pVar2.f2458e == 1 ? Reader.READ_DONE : EventResultPersister.GENERATE_NEW_ID;
        } else {
            if (pVar2.f2458e == 1) {
                i20 = pVar2.f2460g + pVar2.f2455b;
            } else {
                i20 = pVar2.f2459f - pVar2.f2455b;
            }
            i10 = i20;
        }
        m1(pVar2.f2458e, i10);
        if (this.f2292x) {
            i11 = this.f2286r.g();
        } else {
            i11 = this.f2286r.k();
        }
        Object[] objArr = null;
        while (true) {
            int i21 = pVar2.f2456c;
            if (((i21 < 0 || i21 >= tVar.b()) ? z12 : true) && (this.f2290v.f2461i || !this.f2293y.isEmpty())) {
                View view = pVar.j(pVar2.f2456c, z12, Long.MAX_VALUE).f2267a;
                pVar2.f2456c += pVar2.f2457d;
                LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
                int a10 = layoutParams.a();
                int[] iArr = this.B.f2296a;
                int i22 = (iArr == null || a10 >= iArr.length) ? -1 : iArr[a10];
                if (i22 == -1 ? true : z12) {
                    if (e1(pVar2.f2458e)) {
                        i19 = this.f2284p - 1;
                        i18 = -1;
                        i17 = -1;
                    } else {
                        i18 = this.f2284p;
                        int i23 = z12 ? 1 : 0;
                        Object[] objArr2 = z12 ? 1 : 0;
                        Object[] objArr3 = z12 ? 1 : 0;
                        i19 = i23;
                        i17 = 1;
                    }
                    c cVar2 = null;
                    if (pVar2.f2458e == 1) {
                        int k10 = this.f2286r.k();
                        int i24 = Reader.READ_DONE;
                        while (i19 != i18) {
                            c cVar3 = this.f2285q[i19];
                            int h = cVar3.h(k10);
                            if (h < i24) {
                                cVar2 = cVar3;
                                i24 = h;
                            }
                            i19 += i17;
                        }
                    } else {
                        int g10 = this.f2286r.g();
                        int i25 = EventResultPersister.GENERATE_NEW_ID;
                        while (i19 != i18) {
                            c cVar4 = this.f2285q[i19];
                            int k11 = cVar4.k(g10);
                            if (k11 > i25) {
                                cVar2 = cVar4;
                                i25 = k11;
                            }
                            i19 += i17;
                        }
                    }
                    cVar = cVar2;
                    LazySpanLookup lazySpanLookup = this.B;
                    lazySpanLookup.b(a10);
                    lazySpanLookup.f2296a[a10] = cVar.f2323e;
                } else {
                    cVar = this.f2285q[i22];
                }
                layoutParams.f2295e = cVar;
                if (pVar2.f2458e == 1) {
                    z10 = false;
                    c(view, -1, false);
                } else {
                    z10 = false;
                    c(view, 0, false);
                }
                if (this.f2288t == 1) {
                    int i26 = this.f2289u;
                    int i27 = this.f2208l;
                    int i28 = ((ViewGroup.MarginLayoutParams) layoutParams).width;
                    int i29 = z10 ? 1 : 0;
                    int i30 = z10 ? 1 : 0;
                    int i31 = z10 ? 1 : 0;
                    c1(view, RecyclerView.j.y(i26, i27, i29, i28, z10), RecyclerView.j.y(this.f2211o, this.f2209m, M() + P(), ((ViewGroup.MarginLayoutParams) layoutParams).height, true), z10);
                } else {
                    c1(view, RecyclerView.j.y(this.f2210n, this.f2208l, O() + N(), ((ViewGroup.MarginLayoutParams) layoutParams).width, true), RecyclerView.j.y(this.f2289u, this.f2209m, 0, ((ViewGroup.MarginLayoutParams) layoutParams).height, false), false);
                }
                if (pVar2.f2458e == 1) {
                    int h6 = cVar.h(i11);
                    i14 = h6;
                    i13 = this.f2286r.c(view) + h6;
                } else {
                    int k12 = cVar.k(i11);
                    i13 = k12;
                    i14 = k12 - this.f2286r.c(view);
                }
                if (pVar2.f2458e == 1) {
                    layoutParams.f2295e.a(view);
                } else {
                    layoutParams.f2295e.n(view);
                }
                if (!b1() || this.f2288t != 1) {
                    i15 = this.f2287s.k() + (cVar.f2323e * this.f2289u);
                    i16 = this.f2287s.c(view) + i15;
                } else {
                    i16 = this.f2287s.g() - (((this.f2284p - 1) - cVar.f2323e) * this.f2289u);
                    i15 = i16 - this.f2287s.c(view);
                }
                if (this.f2288t == 1) {
                    W(view, i15, i14, i16, i13);
                } else {
                    W(view, i14, i15, i13, i16);
                }
                o1(cVar, this.f2290v.f2458e, i10);
                g1(pVar, this.f2290v);
                if (!this.f2290v.h || !view.hasFocusable()) {
                    z11 = false;
                } else {
                    BitSet bitSet = this.f2293y;
                    int i32 = cVar.f2323e;
                    z11 = false;
                    bitSet.set(i32, false);
                }
                z12 = z11;
                objArr = 1;
            }
        }
        if (objArr == null) {
            g1(pVar, this.f2290v);
        }
        if (this.f2290v.f2458e == -1) {
            i12 = this.f2286r.k() - Y0(this.f2286r.k());
        } else {
            i12 = X0(this.f2286r.g()) - this.f2286r.g();
        }
        if (i12 > 0) {
            return Math.min(pVar2.f2455b, i12);
        }
        int i33 = z12 ? 1 : 0;
        int i34 = z12 ? 1 : 0;
        int i35 = z12 ? 1 : 0;
        return i33;
    }

    public View R0(boolean z10) {
        int k10 = this.f2286r.k();
        int g10 = this.f2286r.g();
        View view = null;
        for (int x8 = x() - 1; x8 >= 0; x8--) {
            View w10 = w(x8);
            int e10 = this.f2286r.e(w10);
            int b10 = this.f2286r.b(w10);
            if (b10 > k10 && e10 < g10) {
                if (b10 <= g10 || !z10) {
                    return w10;
                }
                if (view == null) {
                    view = w10;
                }
            }
        }
        return view;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int S(RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2288t == 0) {
            return this.f2284p;
        }
        return super.S(pVar, tVar);
    }

    public View S0(boolean z10) {
        int k10 = this.f2286r.k();
        int g10 = this.f2286r.g();
        int x8 = x();
        View view = null;
        for (int i10 = 0; i10 < x8; i10++) {
            View w10 = w(i10);
            int e10 = this.f2286r.e(w10);
            if (this.f2286r.b(w10) > k10 && e10 < g10) {
                if (e10 >= k10 || !z10) {
                    return w10;
                }
                if (view == null) {
                    view = w10;
                }
            }
        }
        return view;
    }

    public final void T0(RecyclerView.p pVar, RecyclerView.t tVar, boolean z10) {
        int g10;
        int X0 = X0(EventResultPersister.GENERATE_NEW_ID);
        if (X0 != Integer.MIN_VALUE && (g10 = this.f2286r.g() - X0) > 0) {
            int i10 = g10 - (-k1(-g10, pVar, tVar));
            if (z10 && i10 > 0) {
                this.f2286r.p(i10);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean U() {
        return this.C != 0;
    }

    public final void U0(RecyclerView.p pVar, RecyclerView.t tVar, boolean z10) {
        int k10;
        int Y0 = Y0(Reader.READ_DONE);
        if (Y0 != Integer.MAX_VALUE && (k10 = Y0 - this.f2286r.k()) > 0) {
            int k12 = k10 - k1(k10, pVar, tVar);
            if (z10 && k12 > 0) {
                this.f2286r.p(-k12);
            }
        }
    }

    public int V0() {
        if (x() == 0) {
            return 0;
        }
        return Q(w(0));
    }

    public int W0() {
        int x8 = x();
        if (x8 == 0) {
            return 0;
        }
        return Q(w(x8 - 1));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void X(int i10) {
        super.X(i10);
        for (int i11 = 0; i11 < this.f2284p; i11++) {
            c cVar = this.f2285q[i11];
            int i12 = cVar.f2320b;
            if (i12 != Integer.MIN_VALUE) {
                cVar.f2320b = i12 + i10;
            }
            int i13 = cVar.f2321c;
            if (i13 != Integer.MIN_VALUE) {
                cVar.f2321c = i13 + i10;
            }
        }
    }

    public final int X0(int i10) {
        int h = this.f2285q[0].h(i10);
        for (int i11 = 1; i11 < this.f2284p; i11++) {
            int h6 = this.f2285q[i11].h(i10);
            if (h6 > h) {
                h = h6;
            }
        }
        return h;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void Y(int i10) {
        super.Y(i10);
        for (int i11 = 0; i11 < this.f2284p; i11++) {
            c cVar = this.f2285q[i11];
            int i12 = cVar.f2320b;
            if (i12 != Integer.MIN_VALUE) {
                cVar.f2320b = i12 + i10;
            }
            int i13 = cVar.f2321c;
            if (i13 != Integer.MIN_VALUE) {
                cVar.f2321c = i13 + i10;
            }
        }
    }

    public final int Y0(int i10) {
        int k10 = this.f2285q[0].k(i10);
        for (int i11 = 1; i11 < this.f2284p; i11++) {
            int k11 = this.f2285q[i11].k(i10);
            if (k11 < k10) {
                k10 = k11;
            }
        }
        return k10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void Z(RecyclerView recyclerView, RecyclerView.p pVar) {
        Runnable runnable = this.K;
        RecyclerView recyclerView2 = this.f2199b;
        if (recyclerView2 != null) {
            recyclerView2.removeCallbacks(runnable);
        }
        for (int i10 = 0; i10 < this.f2284p; i10++) {
            this.f2285q[i10].d();
        }
        recyclerView.requestLayout();
    }

    /* JADX WARNING: Removed duplicated region for block: B:13:0x0025  */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x003c  */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x0043 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0044  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void Z0(int r7, int r8, int r9) {
        /*
            r6 = this;
            boolean r0 = r6.f2292x
            if (r0 == 0) goto L_0x0009
            int r0 = r6.W0()
            goto L_0x000d
        L_0x0009:
            int r0 = r6.V0()
        L_0x000d:
            r1 = 8
            if (r9 != r1) goto L_0x001a
            if (r7 >= r8) goto L_0x0016
            int r2 = r8 + 1
            goto L_0x001c
        L_0x0016:
            int r2 = r7 + 1
            r3 = r8
            goto L_0x001d
        L_0x001a:
            int r2 = r7 + r8
        L_0x001c:
            r3 = r7
        L_0x001d:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup r4 = r6.B
            r4.d(r3)
            r4 = 1
            if (r9 == r4) goto L_0x003c
            r5 = 2
            if (r9 == r5) goto L_0x0036
            if (r9 == r1) goto L_0x002b
            goto L_0x0041
        L_0x002b:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup r9 = r6.B
            r9.f(r7, r4)
            androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup r7 = r6.B
            r7.e(r8, r4)
            goto L_0x0041
        L_0x0036:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup r9 = r6.B
            r9.f(r7, r8)
            goto L_0x0041
        L_0x003c:
            androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup r9 = r6.B
            r9.e(r7, r8)
        L_0x0041:
            if (r2 > r0) goto L_0x0044
            return
        L_0x0044:
            boolean r7 = r6.f2292x
            if (r7 == 0) goto L_0x004d
            int r7 = r6.V0()
            goto L_0x0051
        L_0x004d:
            int r7 = r6.W0()
        L_0x0051:
            if (r3 > r7) goto L_0x0056
            r6.w0()
        L_0x0056:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.Z0(int, int, int):void");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.s.b
    public PointF a(int i10) {
        int L0 = L0(i10);
        PointF pointF = new PointF();
        if (L0 == 0) {
            return null;
        }
        if (this.f2288t == 0) {
            pointF.x = (float) L0;
            pointF.y = Constant.VOLUME_FLOAT_MIN;
        } else {
            pointF.x = Constant.VOLUME_FLOAT_MIN;
            pointF.y = (float) L0;
        }
        return pointF;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:23:0x0038, code lost:
        if (r8.f2288t == 1) goto L_0x005b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x003d, code lost:
        if (r8.f2288t == 0) goto L_0x005b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:30:0x004b, code lost:
        if (b1() == false) goto L_0x0059;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:34:0x0057, code lost:
        if (b1() == false) goto L_0x005b;
     */
    /* JADX WARNING: Removed duplicated region for block: B:38:0x005e A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x005f  */
    @Override // androidx.recyclerview.widget.RecyclerView.j
    @androidx.annotation.Nullable
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.view.View a0(android.view.View r9, int r10, androidx.recyclerview.widget.RecyclerView.p r11, androidx.recyclerview.widget.RecyclerView.t r12) {
        /*
        // Method dump skipped, instructions count: 331
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.a0(android.view.View, int, androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t):android.view.View");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:41:0x00bc, code lost:
        if (r10 == r11) goto L_0x00d0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:45:0x00ce, code lost:
        if (r10 == r11) goto L_0x00d0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:47:0x00d2, code lost:
        r10 = false;
     */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x009a  */
    /* JADX WARNING: Removed duplicated region for block: B:61:0x0099 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.view.View a1() {
        /*
        // Method dump skipped, instructions count: 246
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.a1():android.view.View");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void b0(AccessibilityEvent accessibilityEvent) {
        super.b0(accessibilityEvent);
        if (x() > 0) {
            View S0 = S0(false);
            View R0 = R0(false);
            if (S0 != null && R0 != null) {
                int Q = Q(S0);
                int Q2 = Q(R0);
                if (Q < Q2) {
                    accessibilityEvent.setFromIndex(Q);
                    accessibilityEvent.setToIndex(Q2);
                    return;
                }
                accessibilityEvent.setFromIndex(Q2);
                accessibilityEvent.setToIndex(Q);
            }
        }
    }

    public boolean b1() {
        return J() == 1;
    }

    public final void c1(View view, int i10, int i11, boolean z10) {
        boolean z11;
        Rect rect = this.G;
        RecyclerView recyclerView = this.f2199b;
        if (recyclerView == null) {
            rect.set(0, 0, 0, 0);
        } else {
            rect.set(recyclerView.L(view));
        }
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        int i12 = ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin;
        Rect rect2 = this.G;
        int p12 = p1(i10, i12 + rect2.left, ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin + rect2.right);
        int i13 = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin;
        Rect rect3 = this.G;
        int p13 = p1(i11, i13 + rect3.top, ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin + rect3.bottom);
        if (z10) {
            z11 = H0(view, p12, p13, layoutParams);
        } else {
            z11 = F0(view, p12, p13, layoutParams);
        }
        if (z11) {
            view.measure(p12, p13);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void d(String str) {
        RecyclerView recyclerView;
        if (this.F == null && (recyclerView = this.f2199b) != null) {
            recyclerView.i(str);
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:232:0x040c, code lost:
        if (M0() != false) goto L_0x0410;
     */
    /* JADX WARNING: Removed duplicated region for block: B:105:0x01b9  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void d1(androidx.recyclerview.widget.RecyclerView.p r12, androidx.recyclerview.widget.RecyclerView.t r13, boolean r14) {
        /*
        // Method dump skipped, instructions count: 1070
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.d1(androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t, boolean):void");
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean e() {
        return this.f2288t == 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void e0(RecyclerView.p pVar, RecyclerView.t tVar, View view, k0.b bVar) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (!(layoutParams instanceof LayoutParams)) {
            d0(view, bVar);
            return;
        }
        LayoutParams layoutParams2 = (LayoutParams) layoutParams;
        int i10 = -1;
        if (this.f2288t == 0) {
            c cVar = layoutParams2.f2295e;
            if (cVar != null) {
                i10 = cVar.f2323e;
            }
            bVar.f7267a.setCollectionItemInfo((AccessibilityNodeInfo.CollectionItemInfo) b.c.a(i10, 1, -1, -1, false, false).f7285a);
            return;
        }
        c cVar2 = layoutParams2.f2295e;
        if (cVar2 != null) {
            i10 = cVar2.f2323e;
        }
        bVar.f7267a.setCollectionItemInfo((AccessibilityNodeInfo.CollectionItemInfo) b.c.a(-1, -1, i10, 1, false, false).f7285a);
    }

    public final boolean e1(int i10) {
        if (this.f2288t == 0) {
            return (i10 == -1) != this.f2292x;
        }
        return ((i10 == -1) == this.f2292x) == b1();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean f() {
        return this.f2288t == 1;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void f0(RecyclerView recyclerView, int i10, int i11) {
        Z0(i10, i11, 1);
    }

    public void f1(int i10, RecyclerView.t tVar) {
        int i11;
        int i12;
        if (i10 > 0) {
            i12 = W0();
            i11 = 1;
        } else {
            i11 = -1;
            i12 = V0();
        }
        this.f2290v.f2454a = true;
        n1(i12, tVar);
        l1(i11);
        p pVar = this.f2290v;
        pVar.f2456c = i12 + pVar.f2457d;
        pVar.f2455b = Math.abs(i10);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean g(RecyclerView.LayoutParams layoutParams) {
        return layoutParams instanceof LayoutParams;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void g0(RecyclerView recyclerView) {
        this.B.a();
        w0();
    }

    public final void g1(RecyclerView.p pVar, p pVar2) {
        int i10;
        int i11;
        if (pVar2.f2454a && !pVar2.f2461i) {
            if (pVar2.f2455b != 0) {
                int i12 = 1;
                if (pVar2.f2458e == -1) {
                    int i13 = pVar2.f2459f;
                    int k10 = this.f2285q[0].k(i13);
                    while (i12 < this.f2284p) {
                        int k11 = this.f2285q[i12].k(i13);
                        if (k11 > k10) {
                            k10 = k11;
                        }
                        i12++;
                    }
                    int i14 = i13 - k10;
                    if (i14 < 0) {
                        i11 = pVar2.f2460g;
                    } else {
                        i11 = pVar2.f2460g - Math.min(i14, pVar2.f2455b);
                    }
                    h1(pVar, i11);
                    return;
                }
                int i15 = pVar2.f2460g;
                int h = this.f2285q[0].h(i15);
                while (i12 < this.f2284p) {
                    int h6 = this.f2285q[i12].h(i15);
                    if (h6 < h) {
                        h = h6;
                    }
                    i12++;
                }
                int i16 = h - pVar2.f2460g;
                if (i16 < 0) {
                    i10 = pVar2.f2459f;
                } else {
                    i10 = Math.min(i16, pVar2.f2455b) + pVar2.f2459f;
                }
                i1(pVar, i10);
            } else if (pVar2.f2458e == -1) {
                h1(pVar, pVar2.f2460g);
            } else {
                i1(pVar, pVar2.f2459f);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void h0(RecyclerView recyclerView, int i10, int i11, int i12) {
        Z0(i10, i11, 8);
    }

    public final void h1(RecyclerView.p pVar, int i10) {
        for (int x8 = x() - 1; x8 >= 0; x8--) {
            View w10 = w(x8);
            if (this.f2286r.e(w10) >= i10 && this.f2286r.o(w10) >= i10) {
                LayoutParams layoutParams = (LayoutParams) w10.getLayoutParams();
                Objects.requireNonNull(layoutParams);
                if (layoutParams.f2295e.f2319a.size() != 1) {
                    layoutParams.f2295e.l();
                    s0(w10, pVar);
                } else {
                    return;
                }
            } else {
                return;
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public void i(int i10, int i11, RecyclerView.t tVar, RecyclerView.j.c cVar) {
        int i12;
        int i13;
        if (this.f2288t != 0) {
            i10 = i11;
        }
        if (!(x() == 0 || i10 == 0)) {
            f1(i10, tVar);
            int[] iArr = this.J;
            if (iArr == null || iArr.length < this.f2284p) {
                this.J = new int[this.f2284p];
            }
            int i14 = 0;
            for (int i15 = 0; i15 < this.f2284p; i15++) {
                p pVar = this.f2290v;
                if (pVar.f2457d == -1) {
                    i13 = pVar.f2459f;
                    i12 = this.f2285q[i15].k(i13);
                } else {
                    i13 = this.f2285q[i15].h(pVar.f2460g);
                    i12 = this.f2290v.f2460g;
                }
                int i16 = i13 - i12;
                if (i16 >= 0) {
                    this.J[i14] = i16;
                    i14++;
                }
            }
            Arrays.sort(this.J, 0, i14);
            for (int i17 = 0; i17 < i14; i17++) {
                int i18 = this.f2290v.f2456c;
                if (i18 >= 0 && i18 < tVar.b()) {
                    ((n.b) cVar).a(this.f2290v.f2456c, this.J[i17]);
                    p pVar2 = this.f2290v;
                    pVar2.f2456c += pVar2.f2457d;
                } else {
                    return;
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void i0(RecyclerView recyclerView, int i10, int i11) {
        Z0(i10, i11, 2);
    }

    public final void i1(RecyclerView.p pVar, int i10) {
        while (x() > 0) {
            View w10 = w(0);
            if (this.f2286r.b(w10) <= i10 && this.f2286r.n(w10) <= i10) {
                LayoutParams layoutParams = (LayoutParams) w10.getLayoutParams();
                Objects.requireNonNull(layoutParams);
                if (layoutParams.f2295e.f2319a.size() != 1) {
                    layoutParams.f2295e.m();
                    s0(w10, pVar);
                } else {
                    return;
                }
            } else {
                return;
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void j0(RecyclerView recyclerView, int i10, int i11, Object obj) {
        Z0(i10, i11, 4);
    }

    public final void j1() {
        if (this.f2288t == 1 || !b1()) {
            this.f2292x = this.f2291w;
        } else {
            this.f2292x = !this.f2291w;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int k(RecyclerView.t tVar) {
        return N0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void k0(RecyclerView.p pVar, RecyclerView.t tVar) {
        d1(pVar, tVar, true);
    }

    public int k1(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        if (x() == 0 || i10 == 0) {
            return 0;
        }
        f1(i10, tVar);
        int Q0 = Q0(pVar, this.f2290v, tVar);
        if (this.f2290v.f2455b >= Q0) {
            i10 = i10 < 0 ? -Q0 : Q0;
        }
        this.f2286r.p(-i10);
        this.D = this.f2292x;
        p pVar2 = this.f2290v;
        pVar2.f2455b = 0;
        g1(pVar, pVar2);
        return i10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int l(RecyclerView.t tVar) {
        return O0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void l0(RecyclerView.t tVar) {
        this.f2294z = -1;
        this.A = EventResultPersister.GENERATE_NEW_ID;
        this.F = null;
        this.H.b();
    }

    public final void l1(int i10) {
        p pVar = this.f2290v;
        pVar.f2458e = i10;
        int i11 = 1;
        if (this.f2292x != (i10 == -1)) {
            i11 = -1;
        }
        pVar.f2457d = i11;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int m(RecyclerView.t tVar) {
        return P0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void m0(Parcelable parcelable) {
        if (parcelable instanceof SavedState) {
            this.F = (SavedState) parcelable;
            w0();
        }
    }

    public final void m1(int i10, int i11) {
        for (int i12 = 0; i12 < this.f2284p; i12++) {
            if (!this.f2285q[i12].f2319a.isEmpty()) {
                o1(this.f2285q[i12], i10, i11);
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int n(RecyclerView.t tVar) {
        return N0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public Parcelable n0() {
        int i10;
        View view;
        int i11;
        int i12;
        int[] iArr;
        SavedState savedState = this.F;
        if (savedState != null) {
            return new SavedState(savedState);
        }
        SavedState savedState2 = new SavedState();
        savedState2.h = this.f2291w;
        savedState2.f2309i = this.D;
        savedState2.f2310j = this.E;
        LazySpanLookup lazySpanLookup = this.B;
        if (lazySpanLookup == null || (iArr = lazySpanLookup.f2296a) == null) {
            savedState2.f2306e = 0;
        } else {
            savedState2.f2307f = iArr;
            savedState2.f2306e = iArr.length;
            savedState2.f2308g = lazySpanLookup.f2297b;
        }
        int i13 = -1;
        if (x() > 0) {
            if (this.D) {
                i10 = W0();
            } else {
                i10 = V0();
            }
            savedState2.f2302a = i10;
            if (this.f2292x) {
                view = R0(true);
            } else {
                view = S0(true);
            }
            if (view != null) {
                i13 = Q(view);
            }
            savedState2.f2303b = i13;
            int i14 = this.f2284p;
            savedState2.f2304c = i14;
            savedState2.f2305d = new int[i14];
            for (int i15 = 0; i15 < this.f2284p; i15++) {
                if (this.D) {
                    i11 = this.f2285q[i15].h(EventResultPersister.GENERATE_NEW_ID);
                    if (i11 != Integer.MIN_VALUE) {
                        i12 = this.f2286r.g();
                    } else {
                        savedState2.f2305d[i15] = i11;
                    }
                } else {
                    i11 = this.f2285q[i15].k(EventResultPersister.GENERATE_NEW_ID);
                    if (i11 != Integer.MIN_VALUE) {
                        i12 = this.f2286r.k();
                    } else {
                        savedState2.f2305d[i15] = i11;
                    }
                }
                i11 -= i12;
                savedState2.f2305d[i15] = i11;
            }
        } else {
            savedState2.f2302a = -1;
            savedState2.f2303b = -1;
            savedState2.f2304c = 0;
        }
        return savedState2;
    }

    public final void n1(int i10, RecyclerView.t tVar) {
        int i11;
        int i12;
        int i13;
        p pVar = this.f2290v;
        boolean z10 = false;
        pVar.f2455b = 0;
        pVar.f2456c = i10;
        RecyclerView.s sVar = this.f2202e;
        if (!(sVar != null && sVar.f2236e) || (i13 = tVar.f2246a) == -1) {
            i12 = 0;
            i11 = 0;
        } else {
            if (this.f2292x == (i13 < i10)) {
                i12 = this.f2286r.l();
                i11 = 0;
            } else {
                i11 = this.f2286r.l();
                i12 = 0;
            }
        }
        RecyclerView recyclerView = this.f2199b;
        if (recyclerView != null && recyclerView.f2149g) {
            this.f2290v.f2459f = this.f2286r.k() - i11;
            this.f2290v.f2460g = this.f2286r.g() + i12;
        } else {
            this.f2290v.f2460g = this.f2286r.f() + i12;
            this.f2290v.f2459f = -i11;
        }
        p pVar2 = this.f2290v;
        pVar2.h = false;
        pVar2.f2454a = true;
        if (this.f2286r.i() == 0 && this.f2286r.f() == 0) {
            z10 = true;
        }
        pVar2.f2461i = z10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int o(RecyclerView.t tVar) {
        return O0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void o0(int i10) {
        if (i10 == 0) {
            M0();
        }
    }

    public final void o1(c cVar, int i10, int i11) {
        int i12 = cVar.f2322d;
        if (i10 == -1) {
            int i13 = cVar.f2320b;
            if (i13 == Integer.MIN_VALUE) {
                cVar.c();
                i13 = cVar.f2320b;
            }
            if (i13 + i12 <= i11) {
                this.f2293y.set(cVar.f2323e, false);
                return;
            }
            return;
        }
        int i14 = cVar.f2321c;
        if (i14 == Integer.MIN_VALUE) {
            cVar.b();
            i14 = cVar.f2321c;
        }
        if (i14 - i12 >= i11) {
            this.f2293y.set(cVar.f2323e, false);
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int p(RecyclerView.t tVar) {
        return P0(tVar);
    }

    public final int p1(int i10, int i11, int i12) {
        if (i11 == 0 && i12 == 0) {
            return i10;
        }
        int mode = View.MeasureSpec.getMode(i10);
        if (mode == Integer.MIN_VALUE || mode == 1073741824) {
            return View.MeasureSpec.makeMeasureSpec(Math.max(0, (View.MeasureSpec.getSize(i10) - i11) - i12), mode);
        }
        return i10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams t() {
        if (this.f2288t == 0) {
            return new LayoutParams(-2, -1);
        }
        return new LayoutParams(-1, -2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams u(Context context, AttributeSet attributeSet) {
        return new LayoutParams(context, attributeSet);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams v(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return new LayoutParams((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return new LayoutParams(layoutParams);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int x0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        return k1(i10, pVar, tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void y0(int i10) {
        SavedState savedState = this.F;
        if (!(savedState == null || savedState.f2302a == i10)) {
            savedState.f2305d = null;
            savedState.f2304c = 0;
            savedState.f2302a = -1;
            savedState.f2303b = -1;
        }
        this.f2294z = i10;
        this.A = EventResultPersister.GENERATE_NEW_ID;
        w0();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int z(RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2288t == 1) {
            return this.f2284p;
        }
        return super.z(pVar, tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int z0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        return k1(i10, pVar, tVar);
    }

    public static class LazySpanLookup {

        /* renamed from: a  reason: collision with root package name */
        public int[] f2296a;

        /* renamed from: b  reason: collision with root package name */
        public List<FullSpanItem> f2297b;

        public void a() {
            int[] iArr = this.f2296a;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
            this.f2297b = null;
        }

        public void b(int i10) {
            int[] iArr = this.f2296a;
            if (iArr == null) {
                int[] iArr2 = new int[(Math.max(i10, 10) + 1)];
                this.f2296a = iArr2;
                Arrays.fill(iArr2, -1);
            } else if (i10 >= iArr.length) {
                int length = iArr.length;
                while (length <= i10) {
                    length *= 2;
                }
                int[] iArr3 = new int[length];
                this.f2296a = iArr3;
                System.arraycopy(iArr, 0, iArr3, 0, iArr.length);
                int[] iArr4 = this.f2296a;
                Arrays.fill(iArr4, iArr.length, iArr4.length, -1);
            }
        }

        public FullSpanItem c(int i10) {
            List<FullSpanItem> list = this.f2297b;
            if (list == null) {
                return null;
            }
            for (int size = list.size() - 1; size >= 0; size--) {
                FullSpanItem fullSpanItem = this.f2297b.get(size);
                if (fullSpanItem.f2298a == i10) {
                    return fullSpanItem;
                }
            }
            return null;
        }

        /* JADX WARNING: Removed duplicated region for block: B:21:0x0048  */
        /* JADX WARNING: Removed duplicated region for block: B:23:0x0052  */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public int d(int r5) {
            /*
                r4 = this;
                int[] r0 = r4.f2296a
                r1 = -1
                if (r0 != 0) goto L_0x0006
                return r1
            L_0x0006:
                int r0 = r0.length
                if (r5 < r0) goto L_0x000a
                return r1
            L_0x000a:
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r0 = r4.f2297b
                if (r0 != 0) goto L_0x0010
            L_0x000e:
                r0 = r1
                goto L_0x0046
            L_0x0010:
                androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem r0 = r4.c(r5)
                if (r0 == 0) goto L_0x001b
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r2 = r4.f2297b
                r2.remove(r0)
            L_0x001b:
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r0 = r4.f2297b
                int r0 = r0.size()
                r2 = 0
            L_0x0022:
                if (r2 >= r0) goto L_0x0034
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r3 = r4.f2297b
                java.lang.Object r3 = r3.get(r2)
                androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem r3 = (androidx.recyclerview.widget.StaggeredGridLayoutManager.LazySpanLookup.FullSpanItem) r3
                int r3 = r3.f2298a
                if (r3 < r5) goto L_0x0031
                goto L_0x0035
            L_0x0031:
                int r2 = r2 + 1
                goto L_0x0022
            L_0x0034:
                r2 = r1
            L_0x0035:
                if (r2 == r1) goto L_0x000e
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r0 = r4.f2297b
                java.lang.Object r0 = r0.get(r2)
                androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem r0 = (androidx.recyclerview.widget.StaggeredGridLayoutManager.LazySpanLookup.FullSpanItem) r0
                java.util.List<androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem> r3 = r4.f2297b
                r3.remove(r2)
                int r0 = r0.f2298a
            L_0x0046:
                if (r0 != r1) goto L_0x0052
                int[] r0 = r4.f2296a
                int r2 = r0.length
                java.util.Arrays.fill(r0, r5, r2, r1)
                int[] r5 = r4.f2296a
                int r5 = r5.length
                return r5
            L_0x0052:
                int[] r2 = r4.f2296a
                int r0 = r0 + 1
                java.util.Arrays.fill(r2, r5, r0, r1)
                return r0
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.StaggeredGridLayoutManager.LazySpanLookup.d(int):int");
        }

        public void e(int i10, int i11) {
            int[] iArr = this.f2296a;
            if (iArr != null && i10 < iArr.length) {
                int i12 = i10 + i11;
                b(i12);
                int[] iArr2 = this.f2296a;
                System.arraycopy(iArr2, i10, iArr2, i12, (iArr2.length - i10) - i11);
                Arrays.fill(this.f2296a, i10, i12, -1);
                List<FullSpanItem> list = this.f2297b;
                if (list != null) {
                    for (int size = list.size() - 1; size >= 0; size--) {
                        FullSpanItem fullSpanItem = this.f2297b.get(size);
                        int i13 = fullSpanItem.f2298a;
                        if (i13 >= i10) {
                            fullSpanItem.f2298a = i13 + i11;
                        }
                    }
                }
            }
        }

        public void f(int i10, int i11) {
            int[] iArr = this.f2296a;
            if (iArr != null && i10 < iArr.length) {
                int i12 = i10 + i11;
                b(i12);
                int[] iArr2 = this.f2296a;
                System.arraycopy(iArr2, i12, iArr2, i10, (iArr2.length - i10) - i11);
                int[] iArr3 = this.f2296a;
                Arrays.fill(iArr3, iArr3.length - i11, iArr3.length, -1);
                List<FullSpanItem> list = this.f2297b;
                if (list != null) {
                    for (int size = list.size() - 1; size >= 0; size--) {
                        FullSpanItem fullSpanItem = this.f2297b.get(size);
                        int i13 = fullSpanItem.f2298a;
                        if (i13 >= i10) {
                            if (i13 < i12) {
                                this.f2297b.remove(size);
                            } else {
                                fullSpanItem.f2298a = i13 - i11;
                            }
                        }
                    }
                }
            }
        }

        @SuppressLint({"BanParcelableUsage"})
        public static class FullSpanItem implements Parcelable {
            public static final Parcelable.Creator<FullSpanItem> CREATOR = new a();

            /* renamed from: a  reason: collision with root package name */
            public int f2298a;

            /* renamed from: b  reason: collision with root package name */
            public int f2299b;

            /* renamed from: c  reason: collision with root package name */
            public int[] f2300c;

            /* renamed from: d  reason: collision with root package name */
            public boolean f2301d;

            public static class a implements Parcelable.Creator<FullSpanItem> {
                /* Return type fixed from 'java.lang.Object' to match base method */
                @Override // android.os.Parcelable.Creator
                public FullSpanItem createFromParcel(Parcel parcel) {
                    return new FullSpanItem(parcel);
                }

                /* Return type fixed from 'java.lang.Object[]' to match base method */
                @Override // android.os.Parcelable.Creator
                public FullSpanItem[] newArray(int i10) {
                    return new FullSpanItem[i10];
                }
            }

            public FullSpanItem(Parcel parcel) {
                this.f2298a = parcel.readInt();
                this.f2299b = parcel.readInt();
                this.f2301d = parcel.readInt() != 1 ? false : true;
                int readInt = parcel.readInt();
                if (readInt > 0) {
                    int[] iArr = new int[readInt];
                    this.f2300c = iArr;
                    parcel.readIntArray(iArr);
                }
            }

            public int describeContents() {
                return 0;
            }

            public String toString() {
                StringBuilder a10 = f.a("FullSpanItem{mPosition=");
                a10.append(this.f2298a);
                a10.append(", mGapDir=");
                a10.append(this.f2299b);
                a10.append(", mHasUnwantedGapAfter=");
                a10.append(this.f2301d);
                a10.append(", mGapPerSpan=");
                a10.append(Arrays.toString(this.f2300c));
                a10.append('}');
                return a10.toString();
            }

            public void writeToParcel(Parcel parcel, int i10) {
                parcel.writeInt(this.f2298a);
                parcel.writeInt(this.f2299b);
                parcel.writeInt(this.f2301d ? 1 : 0);
                int[] iArr = this.f2300c;
                if (iArr == null || iArr.length <= 0) {
                    parcel.writeInt(0);
                    return;
                }
                parcel.writeInt(iArr.length);
                parcel.writeIntArray(this.f2300c);
            }

            public FullSpanItem() {
            }
        }
    }
}
