package androidx.appcompat.app;

import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.StyleRes;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$id;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.view.ViewCompat;
import androidx.core.widget.NestedScrollView;
import com.duokan.airkan.common.Constant;
import j0.m;
import java.util.Objects;
import java.util.WeakHashMap;

/* compiled from: AlertDialog */
public class d extends k {

    /* renamed from: c  reason: collision with root package name */
    public final AlertController f439c = new AlertController(getContext(), this, getWindow());

    /* compiled from: AlertDialog */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public final AlertController.b f440a;

        /* renamed from: b  reason: collision with root package name */
        public final int f441b;

        public a(@NonNull Context context) {
            int c10 = d.c(context, 0);
            this.f440a = new AlertController.b(new ContextThemeWrapper(context, d.c(context, c10)));
            this.f441b = c10;
        }

        @NonNull
        public d a() {
            int i10;
            d dVar = new d(this.f440a.f357a, this.f441b);
            AlertController.b bVar = this.f440a;
            AlertController alertController = dVar.f439c;
            View view = bVar.f361e;
            if (view != null) {
                alertController.G = view;
            } else {
                CharSequence charSequence = bVar.f360d;
                if (charSequence != null) {
                    alertController.f333e = charSequence;
                    TextView textView = alertController.E;
                    if (textView != null) {
                        textView.setText(charSequence);
                    }
                }
                Drawable drawable = bVar.f359c;
                if (drawable != null) {
                    alertController.C = drawable;
                    alertController.B = 0;
                    ImageView imageView = alertController.D;
                    if (imageView != null) {
                        imageView.setVisibility(0);
                        alertController.D.setImageDrawable(drawable);
                    }
                }
            }
            if (bVar.f363g != null) {
                AlertController.RecycleListView recycleListView = (AlertController.RecycleListView) bVar.f358b.inflate(alertController.L, (ViewGroup) null);
                if (bVar.f364i) {
                    i10 = alertController.N;
                } else {
                    i10 = alertController.O;
                }
                ListAdapter listAdapter = bVar.f363g;
                if (listAdapter == null) {
                    listAdapter = new AlertController.d(bVar.f357a, i10, 16908308, null);
                }
                alertController.H = listAdapter;
                alertController.I = bVar.f365j;
                if (bVar.h != null) {
                    recycleListView.setOnItemClickListener(new c(bVar, alertController));
                }
                if (bVar.f364i) {
                    recycleListView.setChoiceMode(1);
                }
                alertController.f335g = recycleListView;
            }
            Objects.requireNonNull(this.f440a);
            dVar.setCancelable(true);
            Objects.requireNonNull(this.f440a);
            dVar.setCanceledOnTouchOutside(true);
            Objects.requireNonNull(this.f440a);
            dVar.setOnCancelListener(null);
            Objects.requireNonNull(this.f440a);
            dVar.setOnDismissListener(null);
            DialogInterface.OnKeyListener onKeyListener = this.f440a.f362f;
            if (onKeyListener != null) {
                dVar.setOnKeyListener(onKeyListener);
            }
            return dVar;
        }
    }

    public d(@NonNull Context context, @StyleRes int i10) {
        super(context, c(context, i10));
    }

    public static int c(@NonNull Context context, @StyleRes int i10) {
        if (((i10 >>> 24) & 255) >= 1) {
            return i10;
        }
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(R$attr.alertDialogTheme, typedValue, true);
        return typedValue.resourceId;
    }

    @Override // androidx.appcompat.app.k
    public void onCreate(Bundle bundle) {
        int i10;
        boolean z10;
        View view;
        ListAdapter listAdapter;
        View view2;
        View findViewById;
        super.onCreate(bundle);
        AlertController alertController = this.f439c;
        if (alertController.K == 0) {
            i10 = alertController.J;
        } else {
            i10 = alertController.J;
        }
        alertController.f330b.setContentView(i10);
        View findViewById2 = alertController.f331c.findViewById(R$id.parentPanel);
        int i11 = R$id.topPanel;
        View findViewById3 = findViewById2.findViewById(i11);
        int i12 = R$id.contentPanel;
        View findViewById4 = findViewById2.findViewById(i12);
        int i13 = R$id.buttonPanel;
        View findViewById5 = findViewById2.findViewById(i13);
        ViewGroup viewGroup = (ViewGroup) findViewById2.findViewById(R$id.customPanel);
        View view3 = alertController.h;
        int i14 = 0;
        if (view3 == null) {
            view3 = alertController.f336i != 0 ? LayoutInflater.from(alertController.f329a).inflate(alertController.f336i, viewGroup, false) : null;
        }
        boolean z11 = view3 != null;
        if (!z11 || !AlertController.a(view3)) {
            alertController.f331c.setFlags(131072, 131072);
        }
        if (z11) {
            FrameLayout frameLayout = (FrameLayout) alertController.f331c.findViewById(R$id.custom);
            frameLayout.addView(view3, new ViewGroup.LayoutParams(-1, -1));
            if (alertController.f341n) {
                frameLayout.setPadding(alertController.f337j, alertController.f338k, alertController.f339l, alertController.f340m);
            }
            if (alertController.f335g != null) {
                ((LinearLayoutCompat.LayoutParams) viewGroup.getLayoutParams()).f879a = Constant.VOLUME_FLOAT_MIN;
            }
        } else {
            viewGroup.setVisibility(8);
        }
        View findViewById6 = viewGroup.findViewById(i11);
        View findViewById7 = viewGroup.findViewById(i12);
        View findViewById8 = viewGroup.findViewById(i13);
        ViewGroup d10 = alertController.d(findViewById6, findViewById3);
        ViewGroup d11 = alertController.d(findViewById7, findViewById4);
        ViewGroup d12 = alertController.d(findViewById8, findViewById5);
        NestedScrollView nestedScrollView = (NestedScrollView) alertController.f331c.findViewById(R$id.scrollView);
        alertController.A = nestedScrollView;
        nestedScrollView.setFocusable(false);
        alertController.A.setNestedScrollingEnabled(false);
        TextView textView = (TextView) d11.findViewById(16908299);
        alertController.F = textView;
        if (textView != null) {
            CharSequence charSequence = alertController.f334f;
            if (charSequence != null) {
                textView.setText(charSequence);
            } else {
                textView.setVisibility(8);
                alertController.A.removeView(alertController.F);
                if (alertController.f335g != null) {
                    ViewGroup viewGroup2 = (ViewGroup) alertController.A.getParent();
                    int indexOfChild = viewGroup2.indexOfChild(alertController.A);
                    viewGroup2.removeViewAt(indexOfChild);
                    viewGroup2.addView(alertController.f335g, indexOfChild, new ViewGroup.LayoutParams(-1, -1));
                } else {
                    d11.setVisibility(8);
                }
            }
        }
        Button button = (Button) d12.findViewById(16908313);
        alertController.f342o = button;
        button.setOnClickListener(alertController.R);
        if (!TextUtils.isEmpty(alertController.f343p) || alertController.f345r != null) {
            alertController.f342o.setText(alertController.f343p);
            Drawable drawable = alertController.f345r;
            if (drawable != null) {
                int i15 = alertController.f332d;
                drawable.setBounds(0, 0, i15, i15);
                alertController.f342o.setCompoundDrawables(alertController.f345r, null, null, null);
            }
            alertController.f342o.setVisibility(0);
            z10 = true;
        } else {
            alertController.f342o.setVisibility(8);
            z10 = false;
        }
        Button button2 = (Button) d12.findViewById(16908314);
        alertController.f346s = button2;
        button2.setOnClickListener(alertController.R);
        if (!TextUtils.isEmpty(alertController.f347t) || alertController.f349v != null) {
            alertController.f346s.setText(alertController.f347t);
            Drawable drawable2 = alertController.f349v;
            if (drawable2 != null) {
                int i16 = alertController.f332d;
                drawable2.setBounds(0, 0, i16, i16);
                alertController.f346s.setCompoundDrawables(alertController.f349v, null, null, null);
            }
            alertController.f346s.setVisibility(0);
            z10 |= true;
        } else {
            alertController.f346s.setVisibility(8);
        }
        Button button3 = (Button) d12.findViewById(16908315);
        alertController.f350w = button3;
        button3.setOnClickListener(alertController.R);
        if (!TextUtils.isEmpty(alertController.f351x) || alertController.f353z != null) {
            alertController.f350w.setText(alertController.f351x);
            Drawable drawable3 = alertController.f353z;
            if (drawable3 != null) {
                int i17 = alertController.f332d;
                drawable3.setBounds(0, 0, i17, i17);
                view = null;
                alertController.f350w.setCompoundDrawables(alertController.f353z, null, null, null);
            } else {
                view = null;
            }
            alertController.f350w.setVisibility(0);
            z10 |= true;
        } else {
            alertController.f350w.setVisibility(8);
            view = null;
        }
        Context context = alertController.f329a;
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(R$attr.alertDialogCenterButtons, typedValue, true);
        if (typedValue.data != 0) {
            if (z10) {
                alertController.b(alertController.f342o);
            } else if (z10) {
                alertController.b(alertController.f346s);
            } else if (z10) {
                alertController.b(alertController.f350w);
            }
        }
        if (!(z10)) {
            d12.setVisibility(8);
        }
        if (alertController.G != null) {
            d10.addView(alertController.G, 0, new ViewGroup.LayoutParams(-1, -2));
            alertController.f331c.findViewById(R$id.title_template).setVisibility(8);
        } else {
            alertController.D = (ImageView) alertController.f331c.findViewById(16908294);
            if (!(!TextUtils.isEmpty(alertController.f333e)) || !alertController.P) {
                alertController.f331c.findViewById(R$id.title_template).setVisibility(8);
                alertController.D.setVisibility(8);
                d10.setVisibility(8);
            } else {
                TextView textView2 = (TextView) alertController.f331c.findViewById(R$id.alertTitle);
                alertController.E = textView2;
                textView2.setText(alertController.f333e);
                int i18 = alertController.B;
                if (i18 != 0) {
                    alertController.D.setImageResource(i18);
                } else {
                    Drawable drawable4 = alertController.C;
                    if (drawable4 != null) {
                        alertController.D.setImageDrawable(drawable4);
                    } else {
                        alertController.E.setPadding(alertController.D.getPaddingLeft(), alertController.D.getPaddingTop(), alertController.D.getPaddingRight(), alertController.D.getPaddingBottom());
                        alertController.D.setVisibility(8);
                    }
                }
            }
        }
        boolean z12 = viewGroup.getVisibility() != 8;
        int i19 = (d10 == null || d10.getVisibility() == 8) ? 0 : 1;
        boolean z13 = d12.getVisibility() != 8;
        if (!z13 && (findViewById = d11.findViewById(R$id.textSpacerNoButtons)) != null) {
            findViewById.setVisibility(0);
        }
        if (i19 != 0) {
            NestedScrollView nestedScrollView2 = alertController.A;
            if (nestedScrollView2 != null) {
                nestedScrollView2.setClipToPadding(true);
            }
            if (alertController.f334f == null && alertController.f335g == null) {
                view2 = view;
            } else {
                view2 = d10.findViewById(R$id.titleDividerNoCustom);
            }
            if (view2 != null) {
                view2.setVisibility(0);
            }
        } else {
            View findViewById9 = d11.findViewById(R$id.textSpacerNoTitle);
            if (findViewById9 != null) {
                findViewById9.setVisibility(0);
            }
        }
        ListView listView = alertController.f335g;
        if (listView instanceof AlertController.RecycleListView) {
            AlertController.RecycleListView recycleListView = (AlertController.RecycleListView) listView;
            Objects.requireNonNull(recycleListView);
            if (!z13 || i19 == 0) {
                recycleListView.setPadding(recycleListView.getPaddingLeft(), i19 != 0 ? recycleListView.getPaddingTop() : recycleListView.f354a, recycleListView.getPaddingRight(), z13 ? recycleListView.getPaddingBottom() : recycleListView.f355b);
            }
        }
        if (!z12) {
            View view4 = alertController.f335g;
            if (view4 == null) {
                view4 = alertController.A;
            }
            if (view4 != null) {
                if (z13) {
                    i14 = 2;
                }
                View findViewById10 = alertController.f331c.findViewById(R$id.scrollIndicatorUp);
                View findViewById11 = alertController.f331c.findViewById(R$id.scrollIndicatorDown);
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                view4.setScrollIndicators(i19 | i14, 3);
                if (findViewById10 != null) {
                    d11.removeView(findViewById10);
                }
                if (findViewById11 != null) {
                    d11.removeView(findViewById11);
                }
            }
        }
        ListView listView2 = alertController.f335g;
        if (listView2 != null && (listAdapter = alertController.H) != null) {
            listView2.setAdapter(listAdapter);
            int i20 = alertController.I;
            if (i20 > -1) {
                listView2.setItemChecked(i20, true);
                listView2.setSelection(i20);
            }
        }
    }

    public boolean onKeyDown(int i10, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f439c.A;
        if (nestedScrollView != null && nestedScrollView.l(keyEvent)) {
            return true;
        }
        return super.onKeyDown(i10, keyEvent);
    }

    public boolean onKeyUp(int i10, KeyEvent keyEvent) {
        NestedScrollView nestedScrollView = this.f439c.A;
        if (nestedScrollView != null && nestedScrollView.l(keyEvent)) {
            return true;
        }
        return super.onKeyUp(i10, keyEvent);
    }

    @Override // android.app.Dialog, androidx.appcompat.app.k
    public void setTitle(CharSequence charSequence) {
        super.setTitle(charSequence);
        AlertController alertController = this.f439c;
        alertController.f333e = charSequence;
        TextView textView = alertController.E;
        if (textView != null) {
            textView.setText(charSequence);
        }
    }
}
