package aa;

import aa.p;
import java.io.IOException;

/* compiled from: DERTaggedObject */
public class f1 extends x {
    public f1(boolean z10, int i10, e eVar) {
        super(z10, i10, eVar);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        q l10 = this.f244c.c().l();
        int i10 = 160;
        if (this.f243b) {
            pVar.i(160, this.f242a);
            pVar.g(l10.i());
            pVar.h(l10);
            return;
        }
        if (!l10.k()) {
            i10 = 128;
        }
        pVar.i(i10, this.f242a);
        l10.h(new p.a(pVar, pVar.f212a));
    }

    @Override // aa.q
    public int i() throws IOException {
        int i10 = this.f244c.c().l().i();
        if (this.f243b) {
            return v1.a(i10) + v1.b(this.f242a) + i10;
        }
        return v1.b(this.f242a) + (i10 - 1);
    }

    @Override // aa.q
    public boolean k() {
        if (this.f243b) {
            return true;
        }
        return this.f244c.c().l().k();
    }
}
