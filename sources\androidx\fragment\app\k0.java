package androidx.fragment.app;

import android.view.View;
import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.WeakHashMap;
import u.f;

/* compiled from: FragmentTransition */
public class k0 {

    /* renamed from: a  reason: collision with root package name */
    public static final int[] f1935a = {0, 3, 0, 1, 5, 4, 7, 6, 9, 8, 10};

    /* renamed from: b  reason: collision with root package name */
    public static final m0 f1936b = new l0();

    /* renamed from: c  reason: collision with root package name */
    public static final m0 f1937c;

    /* compiled from: FragmentTransition */
    public interface a {
    }

    /* compiled from: FragmentTransition */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public Fragment f1938a;

        /* renamed from: b  reason: collision with root package name */
        public boolean f1939b;

        /* renamed from: c  reason: collision with root package name */
        public a f1940c;

        /* renamed from: d  reason: collision with root package name */
        public Fragment f1941d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f1942e;

        /* renamed from: f  reason: collision with root package name */
        public a f1943f;
    }

    static {
        m0 m0Var;
        try {
            m0Var = (m0) androidx.transition.b.class.getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
        } catch (Exception unused) {
            m0Var = null;
        }
        f1937c = m0Var;
    }

    public static void a(ArrayList<View> arrayList, u.a<String, View> aVar, Collection<String> collection) {
        for (int i10 = aVar.f10432c - 1; i10 >= 0; i10--) {
            View k10 = aVar.k(i10);
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (collection.contains(k10.getTransitionName())) {
                arrayList.add(k10);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:28:0x0039, code lost:
        if (r0.f1729k != false) goto L_0x008b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:54:0x0077, code lost:
        r9 = true;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:62:0x0089, code lost:
        if (r0.f1736o0 != false) goto L_0x0081;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:63:0x008b, code lost:
        r9 = true;
     */
    /* JADX WARNING: Removed duplicated region for block: B:67:0x0095  */
    /* JADX WARNING: Removed duplicated region for block: B:79:0x00b7  */
    /* JADX WARNING: Removed duplicated region for block: B:85:0x00d1  */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x00e2 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:94:? A[ADDED_TO_REGION, RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void b(androidx.fragment.app.a r8, androidx.fragment.app.d0.a r9, android.util.SparseArray<androidx.fragment.app.k0.b> r10, boolean r11, boolean r12) {
        /*
        // Method dump skipped, instructions count: 237
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.k0.b(androidx.fragment.app.a, androidx.fragment.app.d0$a, android.util.SparseArray, boolean, boolean):void");
    }

    public static void c(Fragment fragment, Fragment fragment2, boolean z10, u.a<String, View> aVar, boolean z11) {
        if (z10) {
            fragment2.h();
        } else {
            fragment.h();
        }
    }

    public static boolean d(m0 m0Var, List<Object> list) {
        int size = list.size();
        for (int i10 = 0; i10 < size; i10++) {
            if (!m0Var.e(list.get(i10))) {
                return false;
            }
        }
        return true;
    }

    public static u.a<String, View> e(m0 m0Var, u.a<String, String> aVar, Object obj, b bVar) {
        ArrayList<String> arrayList;
        Fragment fragment = bVar.f1938a;
        View view = fragment.f1747u0;
        if (aVar.isEmpty() || obj == null || view == null) {
            aVar.clear();
            return null;
        }
        u.a<String, View> aVar2 = new u.a<>();
        m0Var.i(aVar2, view);
        a aVar3 = bVar.f1940c;
        if (bVar.f1939b) {
            fragment.j();
            arrayList = aVar3.f1877m;
        } else {
            fragment.h();
            arrayList = aVar3.f1878n;
        }
        if (arrayList != null) {
            f.k(aVar2, arrayList);
            f.k(aVar2, aVar.values());
        }
        m(aVar, aVar2);
        return aVar2;
    }

    public static u.a<String, View> f(m0 m0Var, u.a<String, String> aVar, Object obj, b bVar) {
        ArrayList<String> arrayList;
        if (aVar.isEmpty() || obj == null) {
            aVar.clear();
            return null;
        }
        Fragment fragment = bVar.f1941d;
        u.a<String, View> aVar2 = new u.a<>();
        m0Var.i(aVar2, fragment.O());
        a aVar3 = bVar.f1943f;
        if (bVar.f1942e) {
            fragment.h();
            arrayList = aVar3.f1878n;
        } else {
            fragment.j();
            arrayList = aVar3.f1877m;
        }
        if (arrayList != null) {
            f.k(aVar2, arrayList);
        }
        f.k(aVar, aVar2.keySet());
        return aVar2;
    }

    public static m0 g(Fragment fragment, Fragment fragment2) {
        ArrayList arrayList = new ArrayList();
        if (fragment != null) {
            fragment.i();
            Object p10 = fragment.p();
            if (p10 != null) {
                arrayList.add(p10);
            }
            Object r10 = fragment.r();
            if (r10 != null) {
                arrayList.add(r10);
            }
        }
        if (fragment2 != null) {
            fragment2.g();
            Object n10 = fragment2.n();
            if (n10 != null) {
                arrayList.add(n10);
            }
            fragment2.q();
        }
        if (arrayList.isEmpty()) {
            return null;
        }
        m0 m0Var = f1936b;
        if (d(m0Var, arrayList)) {
            return m0Var;
        }
        m0 m0Var2 = f1937c;
        if (m0Var2 != null && d(m0Var2, arrayList)) {
            return m0Var2;
        }
        throw new IllegalArgumentException("Invalid Transition types");
    }

    public static ArrayList<View> h(m0 m0Var, Object obj, Fragment fragment, ArrayList<View> arrayList, View view) {
        if (obj == null) {
            return null;
        }
        ArrayList<View> arrayList2 = new ArrayList<>();
        View view2 = fragment.f1747u0;
        if (view2 != null) {
            m0Var.f(arrayList2, view2);
        }
        if (arrayList != null) {
            arrayList2.removeAll(arrayList);
        }
        if (arrayList2.isEmpty()) {
            return arrayList2;
        }
        arrayList2.add(view);
        m0Var.b(obj, arrayList2);
        return arrayList2;
    }

    public static Object i(m0 m0Var, Fragment fragment, boolean z10) {
        Object obj = null;
        if (fragment == null) {
            return null;
        }
        if (z10) {
            obj = fragment.n();
        } else {
            fragment.g();
        }
        return m0Var.g(obj);
    }

    public static Object j(m0 m0Var, Fragment fragment, boolean z10) {
        Object obj = null;
        if (fragment == null) {
            return null;
        }
        if (z10) {
            obj = fragment.p();
        } else {
            fragment.i();
        }
        return m0Var.g(obj);
    }

    public static View k(u.a<String, View> aVar, b bVar, Object obj, boolean z10) {
        ArrayList<String> arrayList;
        String str;
        a aVar2 = bVar.f1940c;
        if (obj == null || aVar == null || (arrayList = aVar2.f1877m) == null || arrayList.isEmpty()) {
            return null;
        }
        if (z10) {
            str = aVar2.f1877m.get(0);
        } else {
            str = aVar2.f1878n.get(0);
        }
        return aVar.get(str);
    }

    public static Object l(m0 m0Var, Fragment fragment, Fragment fragment2, boolean z10) {
        Object obj;
        if (z10) {
            obj = fragment2.r();
        } else {
            fragment.q();
            obj = null;
        }
        return m0Var.y(m0Var.g(obj));
    }

    public static void m(@NonNull u.a<String, String> aVar, @NonNull u.a<String, View> aVar2) {
        int i10 = aVar.f10432c;
        while (true) {
            i10--;
            if (i10 < 0) {
                return;
            }
            if (!aVar2.containsKey(aVar.k(i10))) {
                aVar.i(i10);
            }
        }
    }

    public static void n(m0 m0Var, Object obj, Object obj2, u.a<String, View> aVar, boolean z10, a aVar2) {
        String str;
        ArrayList<String> arrayList = aVar2.f1877m;
        if (arrayList != null && !arrayList.isEmpty()) {
            if (z10) {
                str = aVar2.f1878n.get(0);
            } else {
                str = aVar2.f1877m.get(0);
            }
            View view = aVar.get(str);
            m0Var.t(obj, view);
            if (obj2 != null) {
                m0Var.t(obj2, view);
            }
        }
    }

    public static void o(ArrayList<View> arrayList, int i10) {
        if (arrayList != null) {
            for (int size = arrayList.size() - 1; size >= 0; size--) {
                arrayList.get(size).setVisibility(i10);
            }
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:143:0x03b1  */
    /* JADX WARNING: Removed duplicated region for block: B:153:0x03f5 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x0230 A[ADDED_TO_REGION] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void p(@androidx.annotation.NonNull android.content.Context r37, @androidx.annotation.NonNull androidx.fragment.app.s r38, java.util.ArrayList<androidx.fragment.app.a> r39, java.util.ArrayList<java.lang.Boolean> r40, int r41, int r42, boolean r43, androidx.fragment.app.k0.a r44) {
        /*
        // Method dump skipped, instructions count: 1032
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.k0.p(android.content.Context, androidx.fragment.app.s, java.util.ArrayList, java.util.ArrayList, int, int, boolean, androidx.fragment.app.k0$a):void");
    }
}
