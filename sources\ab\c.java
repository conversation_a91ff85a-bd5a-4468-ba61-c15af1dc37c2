package ab;

import java.math.BigInteger;
import java.security.spec.ECParameterSpec;
import java.security.spec.ECPoint;
import java.security.spec.EllipticCurve;

/* compiled from: ECNamedCurveSpec */
public class c extends ECParameterSpec {

    /* renamed from: a  reason: collision with root package name */
    public String f249a;

    public c(String str, EllipticCurve ellipticCurve, ECPoint eCPoint, BigInteger bigInteger, BigInteger bigInteger2) {
        super(ellipticCurve, eCPoint, bigInteger, bigInteger2.intValue());
        this.f249a = str;
    }
}
