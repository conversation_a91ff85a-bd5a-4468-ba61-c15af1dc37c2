package aa;

import java.io.IOException;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: BERApplicationSpecificParser */
public class a0 implements e, q1 {

    /* renamed from: a  reason: collision with root package name */
    public final int f157a;

    /* renamed from: b  reason: collision with root package name */
    public final v f158b;

    public a0(int i10, v vVar) {
        this.f157a = i10;
        this.f158b = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            throw new ASN1ParsingException(e10.getMessage(), e10);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new z(this.f157a, this.f158b.c());
    }
}
