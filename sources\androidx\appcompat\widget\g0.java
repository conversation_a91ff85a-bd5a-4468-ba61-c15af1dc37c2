package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.app.SearchableInfo;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.R$id;
import com.xiaomi.mitv.socialtv.common.model.PushMessage;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.WeakHashMap;
import l0.c;

@SuppressLint({"RestrictedAPI"})
/* compiled from: SuggestionsAdapter */
public class g0 extends c implements View.OnClickListener {

    /* renamed from: o0  reason: collision with root package name */
    public static final /* synthetic */ int f1100o0 = 0;

    /* renamed from: l  reason: collision with root package name */
    public final SearchView f1101l;

    /* renamed from: m  reason: collision with root package name */
    public final SearchableInfo f1102m;

    /* renamed from: m0  reason: collision with root package name */
    public int f1103m0 = -1;

    /* renamed from: n  reason: collision with root package name */
    public final Context f1104n;

    /* renamed from: n0  reason: collision with root package name */
    public int f1105n0 = -1;

    /* renamed from: o  reason: collision with root package name */
    public final WeakHashMap<String, Drawable.ConstantState> f1106o;

    /* renamed from: p  reason: collision with root package name */
    public final int f1107p;

    /* renamed from: q  reason: collision with root package name */
    public int f1108q = 1;

    /* renamed from: r  reason: collision with root package name */
    public ColorStateList f1109r;

    /* renamed from: s  reason: collision with root package name */
    public int f1110s = -1;

    /* renamed from: t  reason: collision with root package name */
    public int f1111t = -1;

    /* renamed from: x  reason: collision with root package name */
    public int f1112x = -1;

    /* renamed from: y  reason: collision with root package name */
    public int f1113y = -1;

    /* compiled from: SuggestionsAdapter */
    public static final class a {

        /* renamed from: a  reason: collision with root package name */
        public final TextView f1114a;

        /* renamed from: b  reason: collision with root package name */
        public final TextView f1115b;

        /* renamed from: c  reason: collision with root package name */
        public final ImageView f1116c;

        /* renamed from: d  reason: collision with root package name */
        public final ImageView f1117d;

        /* renamed from: e  reason: collision with root package name */
        public final ImageView f1118e;

        public a(View view) {
            this.f1114a = (TextView) view.findViewById(16908308);
            this.f1115b = (TextView) view.findViewById(16908309);
            this.f1116c = (ImageView) view.findViewById(16908295);
            this.f1117d = (ImageView) view.findViewById(16908296);
            this.f1118e = (ImageView) view.findViewById(R$id.edit_query);
        }
    }

    public g0(Context context, SearchView searchView, SearchableInfo searchableInfo, WeakHashMap<String, Drawable.ConstantState> weakHashMap) {
        super(context, searchView.getSuggestionRowLayout(), null, true);
        this.f1101l = searchView;
        this.f1102m = searchableInfo;
        this.f1107p = searchView.getSuggestionCommitIconResId();
        this.f1104n = context;
        this.f1106o = weakHashMap;
    }

    public static String h(Cursor cursor, int i10) {
        if (i10 == -1) {
            return null;
        }
        try {
            return cursor.getString(i10);
        } catch (Exception e10) {
            Log.e("SuggestionsAdapter", "unexpected error retrieving valid column from cursor, did the remote process die?", e10);
            return null;
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for r9v9, resolved type: android.text.SpannableString */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:56:0x0143  */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x0145  */
    @Override // l0.a
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void a(android.view.View r18, android.content.Context r19, android.database.Cursor r20) {
        /*
        // Method dump skipped, instructions count: 447
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.g0.a(android.view.View, android.content.Context, android.database.Cursor):void");
    }

    @Override // l0.a
    public void b(Cursor cursor) {
        try {
            super.b(cursor);
            if (cursor != null) {
                this.f1110s = cursor.getColumnIndex("suggest_text_1");
                this.f1111t = cursor.getColumnIndex("suggest_text_2");
                this.f1112x = cursor.getColumnIndex("suggest_text_2_url");
                this.f1113y = cursor.getColumnIndex("suggest_icon_1");
                this.f1103m0 = cursor.getColumnIndex("suggest_icon_2");
                this.f1105n0 = cursor.getColumnIndex("suggest_flags");
            }
        } catch (Exception e10) {
            Log.e("SuggestionsAdapter", "error changing cursor and caching columns", e10);
        }
    }

    @Override // l0.a
    public CharSequence c(Cursor cursor) {
        String h;
        String h6;
        if (cursor == null) {
            return null;
        }
        String h10 = h(cursor, cursor.getColumnIndex("suggest_intent_query"));
        if (h10 != null) {
            return h10;
        }
        if (this.f1102m.shouldRewriteQueryFromData() && (h6 = h(cursor, cursor.getColumnIndex("suggest_intent_data"))) != null) {
            return h6;
        }
        if (!this.f1102m.shouldRewriteQueryFromText() || (h = h(cursor, cursor.getColumnIndex("suggest_text_1"))) == null) {
            return null;
        }
        return h;
    }

    @Override // l0.a
    public View d(Context context, Cursor cursor, ViewGroup viewGroup) {
        View inflate = this.f7428k.inflate(this.f7426i, viewGroup, false);
        inflate.setTag(new a(inflate));
        ((ImageView) inflate.findViewById(R$id.edit_query)).setImageResource(this.f1107p);
        return inflate;
    }

    public Drawable e(Uri uri) throws FileNotFoundException {
        int i10;
        String authority = uri.getAuthority();
        if (!TextUtils.isEmpty(authority)) {
            try {
                Resources resourcesForApplication = this.f7419d.getPackageManager().getResourcesForApplication(authority);
                List<String> pathSegments = uri.getPathSegments();
                if (pathSegments != null) {
                    int size = pathSegments.size();
                    if (size == 1) {
                        try {
                            i10 = Integer.parseInt(pathSegments.get(0));
                        } catch (NumberFormatException unused) {
                            throw new FileNotFoundException("Single path segment is not a resource ID: " + uri);
                        }
                    } else if (size == 2) {
                        i10 = resourcesForApplication.getIdentifier(pathSegments.get(1), pathSegments.get(0), authority);
                    } else {
                        throw new FileNotFoundException("More than two path segments: " + uri);
                    }
                    if (i10 != 0) {
                        return resourcesForApplication.getDrawable(i10);
                    }
                    throw new FileNotFoundException("No resource found for: " + uri);
                }
                throw new FileNotFoundException("No path: " + uri);
            } catch (PackageManager.NameNotFoundException unused2) {
                throw new FileNotFoundException("No package found for authority: " + uri);
            }
        } else {
            throw new FileNotFoundException("No authority: " + uri);
        }
    }

    public final Drawable f(String str) {
        Drawable drawable;
        Uri parse;
        InputStream openInputStream;
        Drawable drawable2;
        Drawable drawable3 = null;
        if (str != null && !str.isEmpty() && !"0".equals(str)) {
            try {
                int parseInt = Integer.parseInt(str);
                String str2 = "android.resource://" + this.f1104n.getPackageName() + "/" + parseInt;
                Drawable.ConstantState constantState = this.f1106o.get(str2);
                if (constantState == null) {
                    drawable2 = null;
                } else {
                    drawable2 = constantState.newDrawable();
                }
                if (drawable2 != null) {
                    return drawable2;
                }
                Context context = this.f1104n;
                Object obj = z.a.f11008a;
                Drawable drawable4 = context.getDrawable(parseInt);
                if (drawable4 != null) {
                    this.f1106o.put(str2, drawable4.getConstantState());
                }
                return drawable4;
            } catch (NumberFormatException unused) {
                Drawable.ConstantState constantState2 = this.f1106o.get(str);
                if (constantState2 == null) {
                    drawable = null;
                } else {
                    drawable = constantState2.newDrawable();
                }
                if (drawable != null) {
                    return drawable;
                }
                parse = Uri.parse(str);
                try {
                    if ("android.resource".equals(parse.getScheme())) {
                        try {
                            drawable3 = e(parse);
                        } catch (Resources.NotFoundException unused2) {
                            throw new FileNotFoundException("Resource does not exist: " + parse);
                        }
                    } else {
                        openInputStream = this.f1104n.getContentResolver().openInputStream(parse);
                        if (openInputStream != null) {
                            Drawable createFromStream = Drawable.createFromStream(openInputStream, null);
                            try {
                                openInputStream.close();
                            } catch (IOException e10) {
                                Log.e("SuggestionsAdapter", "Error closing icon stream for " + parse, e10);
                            }
                            drawable3 = createFromStream;
                        } else {
                            throw new FileNotFoundException("Failed to open " + parse);
                        }
                    }
                } catch (FileNotFoundException e11) {
                    Log.w("SuggestionsAdapter", "Icon not found: " + parse + ", " + e11.getMessage());
                }
                if (drawable3 != null) {
                    this.f1106o.put(str, drawable3.getConstantState());
                }
            } catch (Resources.NotFoundException unused3) {
                Log.w("SuggestionsAdapter", "Icon resource not found: " + str);
                return null;
            } catch (Throwable th) {
                try {
                    openInputStream.close();
                } catch (IOException e12) {
                    Log.e("SuggestionsAdapter", "Error closing icon stream for " + parse, e12);
                }
                throw th;
            }
        }
        return drawable3;
    }

    public Cursor g(SearchableInfo searchableInfo, String str, int i10) {
        String suggestAuthority;
        String[] strArr = null;
        if (searchableInfo == null || (suggestAuthority = searchableInfo.getSuggestAuthority()) == null) {
            return null;
        }
        Uri.Builder fragment = new Uri.Builder().scheme(PushMessage.JSON_KEY_CONTENT).authority(suggestAuthority).query("").fragment("");
        String suggestPath = searchableInfo.getSuggestPath();
        if (suggestPath != null) {
            fragment.appendEncodedPath(suggestPath);
        }
        fragment.appendPath("search_suggest_query");
        String suggestSelection = searchableInfo.getSuggestSelection();
        if (suggestSelection != null) {
            strArr = new String[]{str};
        } else {
            fragment.appendPath(str);
        }
        if (i10 > 0) {
            fragment.appendQueryParameter("limit", String.valueOf(i10));
        }
        return this.f7419d.getContentResolver().query(fragment.build(), null, suggestSelection, strArr, null);
    }

    @Override // l0.a
    public View getDropDownView(int i10, View view, ViewGroup viewGroup) {
        try {
            return super.getDropDownView(i10, view, viewGroup);
        } catch (RuntimeException e10) {
            Log.w("SuggestionsAdapter", "Search suggestions cursor threw exception.", e10);
            View inflate = this.f7428k.inflate(this.f7427j, viewGroup, false);
            if (inflate != null) {
                ((a) inflate.getTag()).f1114a.setText(e10.toString());
            }
            return inflate;
        }
    }

    @Override // l0.a
    public View getView(int i10, View view, ViewGroup viewGroup) {
        try {
            return super.getView(i10, view, viewGroup);
        } catch (RuntimeException e10) {
            Log.w("SuggestionsAdapter", "Search suggestions cursor threw exception.", e10);
            View d10 = d(this.f7419d, this.f7418c, viewGroup);
            ((a) d10.getTag()).f1114a.setText(e10.toString());
            return d10;
        }
    }

    public boolean hasStableIds() {
        return false;
    }

    public void notifyDataSetChanged() {
        super.notifyDataSetChanged();
        Cursor cursor = this.f7418c;
        Bundle extras = cursor != null ? cursor.getExtras() : null;
        if (extras != null) {
            extras.getBoolean("in_progress");
        }
    }

    public void notifyDataSetInvalidated() {
        super.notifyDataSetInvalidated();
        Cursor cursor = this.f7418c;
        Bundle extras = cursor != null ? cursor.getExtras() : null;
        if (extras != null) {
            extras.getBoolean("in_progress");
        }
    }

    public void onClick(View view) {
        Object tag = view.getTag();
        if (tag instanceof CharSequence) {
            this.f1101l.s((CharSequence) tag);
        }
    }
}
