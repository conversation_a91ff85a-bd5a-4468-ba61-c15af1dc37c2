package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.ActionMode;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$id;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.m;
import java.util.WeakHashMap;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ActionBarContainer extends FrameLayout {

    /* renamed from: a  reason: collision with root package name */
    public boolean f691a;

    /* renamed from: b  reason: collision with root package name */
    public View f692b;

    /* renamed from: c  reason: collision with root package name */
    public View f693c;

    /* renamed from: d  reason: collision with root package name */
    public View f694d;

    /* renamed from: e  reason: collision with root package name */
    public Drawable f695e;

    /* renamed from: f  reason: collision with root package name */
    public Drawable f696f;

    /* renamed from: g  reason: collision with root package name */
    public Drawable f697g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f698i;

    /* renamed from: j  reason: collision with root package name */
    public int f699j;

    public ActionBarContainer(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        c cVar = new c(this);
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        setBackground(cVar);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.ActionBar);
        this.f695e = obtainStyledAttributes.getDrawable(R$styleable.ActionBar_background);
        this.f696f = obtainStyledAttributes.getDrawable(R$styleable.ActionBar_backgroundStacked);
        this.f699j = obtainStyledAttributes.getDimensionPixelSize(R$styleable.ActionBar_height, -1);
        boolean z10 = true;
        if (getId() == R$id.split_action_bar) {
            this.h = true;
            this.f697g = obtainStyledAttributes.getDrawable(R$styleable.ActionBar_backgroundSplit);
        }
        obtainStyledAttributes.recycle();
        if (!this.h ? !(this.f695e == null && this.f696f == null) : this.f697g != null) {
            z10 = false;
        }
        setWillNotDraw(z10);
    }

    public final int a(View view) {
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) view.getLayoutParams();
        return view.getMeasuredHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
    }

    public final boolean b(View view) {
        return view == null || view.getVisibility() == 8 || view.getMeasuredHeight() == 0;
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        Drawable drawable = this.f695e;
        if (drawable != null && drawable.isStateful()) {
            this.f695e.setState(getDrawableState());
        }
        Drawable drawable2 = this.f696f;
        if (drawable2 != null && drawable2.isStateful()) {
            this.f696f.setState(getDrawableState());
        }
        Drawable drawable3 = this.f697g;
        if (drawable3 != null && drawable3.isStateful()) {
            this.f697g.setState(getDrawableState());
        }
    }

    public View getTabContainer() {
        return this.f692b;
    }

    public void jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState();
        Drawable drawable = this.f695e;
        if (drawable != null) {
            drawable.jumpToCurrentState();
        }
        Drawable drawable2 = this.f696f;
        if (drawable2 != null) {
            drawable2.jumpToCurrentState();
        }
        Drawable drawable3 = this.f697g;
        if (drawable3 != null) {
            drawable3.jumpToCurrentState();
        }
    }

    public void onFinishInflate() {
        super.onFinishInflate();
        this.f693c = findViewById(R$id.action_bar);
        this.f694d = findViewById(R$id.action_context_bar);
    }

    public boolean onHoverEvent(MotionEvent motionEvent) {
        super.onHoverEvent(motionEvent);
        return true;
    }

    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        return this.f691a || super.onInterceptTouchEvent(motionEvent);
    }

    /* JADX WARNING: Removed duplicated region for block: B:34:0x00c0  */
    /* JADX WARNING: Removed duplicated region for block: B:36:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onLayout(boolean r5, int r6, int r7, int r8, int r9) {
        /*
        // Method dump skipped, instructions count: 196
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.ActionBarContainer.onLayout(boolean, int, int, int, int):void");
    }

    public void onMeasure(int i10, int i11) {
        int i12;
        int i13;
        if (this.f693c == null && View.MeasureSpec.getMode(i11) == Integer.MIN_VALUE && (i13 = this.f699j) >= 0) {
            i11 = View.MeasureSpec.makeMeasureSpec(Math.min(i13, View.MeasureSpec.getSize(i11)), EventResultPersister.GENERATE_NEW_ID);
        }
        super.onMeasure(i10, i11);
        if (this.f693c != null) {
            int mode = View.MeasureSpec.getMode(i11);
            View view = this.f692b;
            if (view != null && view.getVisibility() != 8 && mode != 1073741824) {
                if (!b(this.f693c)) {
                    i12 = a(this.f693c);
                } else {
                    i12 = !b(this.f694d) ? a(this.f694d) : 0;
                }
                setMeasuredDimension(getMeasuredWidth(), Math.min(a(this.f692b) + i12, mode == Integer.MIN_VALUE ? View.MeasureSpec.getSize(i11) : Reader.READ_DONE));
            }
        }
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        super.onTouchEvent(motionEvent);
        return true;
    }

    public void setPrimaryBackground(Drawable drawable) {
        Drawable drawable2 = this.f695e;
        if (drawable2 != null) {
            drawable2.setCallback(null);
            unscheduleDrawable(this.f695e);
        }
        this.f695e = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
            View view = this.f693c;
            if (view != null) {
                this.f695e.setBounds(view.getLeft(), this.f693c.getTop(), this.f693c.getRight(), this.f693c.getBottom());
            }
        }
        boolean z10 = true;
        if (!this.h ? !(this.f695e == null && this.f696f == null) : this.f697g != null) {
            z10 = false;
        }
        setWillNotDraw(z10);
        invalidate();
        invalidateOutline();
    }

    public void setSplitBackground(Drawable drawable) {
        Drawable drawable2;
        Drawable drawable3 = this.f697g;
        if (drawable3 != null) {
            drawable3.setCallback(null);
            unscheduleDrawable(this.f697g);
        }
        this.f697g = drawable;
        boolean z10 = false;
        if (drawable != null) {
            drawable.setCallback(this);
            if (this.h && (drawable2 = this.f697g) != null) {
                drawable2.setBounds(0, 0, getMeasuredWidth(), getMeasuredHeight());
            }
        }
        if (!this.h ? this.f695e == null && this.f696f == null : this.f697g == null) {
            z10 = true;
        }
        setWillNotDraw(z10);
        invalidate();
        invalidateOutline();
    }

    public void setStackedBackground(Drawable drawable) {
        Drawable drawable2;
        Drawable drawable3 = this.f696f;
        if (drawable3 != null) {
            drawable3.setCallback(null);
            unscheduleDrawable(this.f696f);
        }
        this.f696f = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
            if (this.f698i && (drawable2 = this.f696f) != null) {
                drawable2.setBounds(this.f692b.getLeft(), this.f692b.getTop(), this.f692b.getRight(), this.f692b.getBottom());
            }
        }
        boolean z10 = true;
        if (!this.h ? !(this.f695e == null && this.f696f == null) : this.f697g != null) {
            z10 = false;
        }
        setWillNotDraw(z10);
        invalidate();
        invalidateOutline();
    }

    public void setTabContainer(e0 e0Var) {
        View view = this.f692b;
        if (view != null) {
            removeView(view);
        }
        this.f692b = e0Var;
        if (e0Var != null) {
            addView(e0Var);
            ViewGroup.LayoutParams layoutParams = e0Var.getLayoutParams();
            layoutParams.width = -1;
            layoutParams.height = -2;
            e0Var.setAllowCollapse(false);
        }
    }

    public void setTransitioning(boolean z10) {
        this.f691a = z10;
        setDescendantFocusability(z10 ? 393216 : 262144);
    }

    public void setVisibility(int i10) {
        super.setVisibility(i10);
        boolean z10 = i10 == 0;
        Drawable drawable = this.f695e;
        if (drawable != null) {
            drawable.setVisible(z10, false);
        }
        Drawable drawable2 = this.f696f;
        if (drawable2 != null) {
            drawable2.setVisible(z10, false);
        }
        Drawable drawable3 = this.f697g;
        if (drawable3 != null) {
            drawable3.setVisible(z10, false);
        }
    }

    public ActionMode startActionModeForChild(View view, ActionMode.Callback callback) {
        return null;
    }

    public ActionMode startActionModeForChild(View view, ActionMode.Callback callback, int i10) {
        if (i10 != 0) {
            return super.startActionModeForChild(view, callback, i10);
        }
        return null;
    }

    public boolean verifyDrawable(Drawable drawable) {
        return (drawable == this.f695e && !this.h) || (drawable == this.f696f && this.f698i) || ((drawable == this.f697g && this.h) || super.verifyDrawable(drawable));
    }
}
