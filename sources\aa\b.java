package aa;

import com.duokan.airkan.server.f;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Objects;
import mb.a;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: ASN1BitString */
public abstract class b extends q implements w {

    /* renamed from: c  reason: collision with root package name */
    public static final char[] f160c = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f161a;

    /* renamed from: b  reason: collision with root package name */
    public final int f162b;

    public b(byte[] bArr, int i10) {
        Objects.requireNonNull(bArr, "data cannot be null");
        if (bArr.length == 0 && i10 != 0) {
            throw new IllegalArgumentException("zero length data with non-zero pad bits");
        } else if (i10 > 7 || i10 < 0) {
            throw new IllegalArgumentException("pad bits cannot be greater than 7 or less than 0");
        } else {
            this.f161a = a.c(bArr);
            this.f162b = i10;
        }
    }

    public static byte[] n(byte[] bArr, int i10) {
        byte[] c10 = a.c(bArr);
        if (i10 > 0) {
            int length = bArr.length - 1;
            c10[length] = (byte) ((255 << i10) & c10[length]);
        }
        return c10;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof b)) {
            return false;
        }
        b bVar = (b) qVar;
        if (this.f162b != bVar.f162b || !a.a(o(), bVar.o())) {
            return false;
        }
        return true;
    }

    @Override // aa.w
    public String getString() {
        StringBuffer stringBuffer = new StringBuffer("#");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            new p(byteArrayOutputStream).h(this);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            for (int i10 = 0; i10 != byteArray.length; i10++) {
                char[] cArr = f160c;
                stringBuffer.append(cArr[(byteArray[i10] >>> 4) & 15]);
                stringBuffer.append(cArr[byteArray[i10] & 15]);
            }
            return stringBuffer.toString();
        } catch (IOException e10) {
            StringBuilder a10 = f.a("Internal error encoding BitString: ");
            a10.append(e10.getMessage());
            throw new ASN1ParsingException(a10.toString(), e10);
        }
    }

    @Override // aa.l
    public int hashCode() {
        return this.f162b ^ a.d(o());
    }

    @Override // aa.q
    public q l() {
        return new n0(this.f161a, this.f162b);
    }

    @Override // aa.q
    public q m() {
        return new k1(this.f161a, this.f162b);
    }

    public byte[] o() {
        return n(this.f161a, this.f162b);
    }

    public String toString() {
        return getString();
    }
}
