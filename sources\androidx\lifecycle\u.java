package androidx.lifecycle;

import androidx.annotation.NonNull;

/* compiled from: ViewModelProvider */
public class u implements s {

    /* renamed from: a  reason: collision with root package name */
    public static u f2094a;

    @Override // androidx.lifecycle.s
    @NonNull
    public <T extends q> T a(@NonNull Class<T> cls) {
        try {
            return cls.newInstance();
        } catch (InstantiationException e10) {
            throw new RuntimeException("Cannot create an instance of " + cls, e10);
        } catch (IllegalAccessException e11) {
            throw new RuntimeException("Cannot create an instance of " + cls, e11);
        }
    }
}
