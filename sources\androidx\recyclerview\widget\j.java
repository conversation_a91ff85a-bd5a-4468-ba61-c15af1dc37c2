package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.l;
import com.duokan.airkan.common.Constant;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class j extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ l.a f2379a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ ViewPropertyAnimator f2380b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ View f2381c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ l f2382d;

    public j(l lVar, l.a aVar, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2382d = lVar;
        this.f2379a = aVar;
        this.f2380b = viewPropertyAnimator;
        this.f2381c = view;
    }

    public void onAnimationEnd(Animator animator) {
        this.f2380b.setListener(null);
        this.f2381c.setAlpha(1.0f);
        this.f2381c.setTranslationX(Constant.VOLUME_FLOAT_MIN);
        this.f2381c.setTranslationY(Constant.VOLUME_FLOAT_MIN);
        this.f2382d.c(this.f2379a.f2398a);
        this.f2382d.f2397r.remove(this.f2379a.f2398a);
        this.f2382d.k();
    }

    public void onAnimationStart(Animator animator) {
        l lVar = this.f2382d;
        RecyclerView.w wVar = this.f2379a.f2398a;
        Objects.requireNonNull(lVar);
    }
}
