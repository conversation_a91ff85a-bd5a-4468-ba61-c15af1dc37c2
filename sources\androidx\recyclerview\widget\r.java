package androidx.recyclerview.widget;

import android.graphics.PointF;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;

/* compiled from: LinearSnapHelper */
public class r extends c0 {
    @Nullable

    /* renamed from: d  reason: collision with root package name */
    public v f2470d;
    @Nullable

    /* renamed from: e  reason: collision with root package name */
    public v f2471e;

    @Override // androidx.recyclerview.widget.c0
    public int[] b(@NonNull RecyclerView.j jVar, @NonNull View view) {
        int[] iArr = new int[2];
        if (jVar.e()) {
            iArr[0] = g(view, j(jVar));
        } else {
            iArr[0] = 0;
        }
        if (jVar.f()) {
            iArr[1] = g(view, k(jVar));
        } else {
            iArr[1] = 0;
        }
        return iArr;
    }

    @Override // androidx.recyclerview.widget.c0
    public View d(RecyclerView.j jVar) {
        if (jVar.f()) {
            return i(jVar, k(jVar));
        }
        if (jVar.e()) {
            return i(jVar, j(jVar));
        }
        return null;
    }

    @Override // androidx.recyclerview.widget.c0
    public int e(RecyclerView.j jVar, int i10, int i11) {
        int I;
        View d10;
        int Q;
        int i12;
        PointF a10;
        int i13;
        int i14;
        if (!(jVar instanceof RecyclerView.s.b) || (I = jVar.I()) == 0 || (d10 = d(jVar)) == null || (Q = jVar.Q(d10)) == -1 || (a10 = ((RecyclerView.s.b) jVar).a(I - 1)) == null) {
            return -1;
        }
        int i15 = 0;
        if (jVar.e()) {
            i13 = h(jVar, j(jVar), i10, 0);
            if (a10.x < Constant.VOLUME_FLOAT_MIN) {
                i13 = -i13;
            }
        } else {
            i13 = 0;
        }
        if (jVar.f()) {
            i14 = h(jVar, k(jVar), 0, i11);
            if (a10.y < Constant.VOLUME_FLOAT_MIN) {
                i14 = -i14;
            }
        } else {
            i14 = 0;
        }
        if (jVar.f()) {
            i13 = i14;
        }
        if (i13 == 0) {
            return -1;
        }
        int i16 = Q + i13;
        if (i16 >= 0) {
            i15 = i16;
        }
        return i15 >= I ? i12 : i15;
    }

    public final int g(@NonNull View view, v vVar) {
        return ((vVar.c(view) / 2) + vVar.e(view)) - ((vVar.l() / 2) + vVar.k());
    }

    public final int h(RecyclerView.j jVar, v vVar, int i10, int i11) {
        int max;
        this.f2348b.fling(0, 0, i10, i11, EventResultPersister.GENERATE_NEW_ID, Reader.READ_DONE, EventResultPersister.GENERATE_NEW_ID, Reader.READ_DONE);
        int[] iArr = {this.f2348b.getFinalX(), this.f2348b.getFinalY()};
        int x8 = jVar.x();
        float f10 = 1.0f;
        if (x8 != 0) {
            View view = null;
            int i12 = Integer.MIN_VALUE;
            int i13 = Integer.MAX_VALUE;
            View view2 = null;
            for (int i14 = 0; i14 < x8; i14++) {
                View w10 = jVar.w(i14);
                int Q = jVar.Q(w10);
                if (Q != -1) {
                    if (Q < i13) {
                        view = w10;
                        i13 = Q;
                    }
                    if (Q > i12) {
                        view2 = w10;
                        i12 = Q;
                    }
                }
            }
            if (!(view == null || view2 == null || (max = Math.max(vVar.b(view), vVar.b(view2)) - Math.min(vVar.e(view), vVar.e(view2))) == 0)) {
                f10 = (((float) max) * 1.0f) / ((float) ((i12 - i13) + 1));
            }
        }
        if (f10 <= Constant.VOLUME_FLOAT_MIN) {
            return 0;
        }
        return Math.round(((float) (Math.abs(iArr[0]) > Math.abs(iArr[1]) ? iArr[0] : iArr[1])) / f10);
    }

    @Nullable
    public final View i(RecyclerView.j jVar, v vVar) {
        int x8 = jVar.x();
        View view = null;
        if (x8 == 0) {
            return null;
        }
        int l10 = (vVar.l() / 2) + vVar.k();
        int i10 = Reader.READ_DONE;
        for (int i11 = 0; i11 < x8; i11++) {
            View w10 = jVar.w(i11);
            int abs = Math.abs(((vVar.c(w10) / 2) + vVar.e(w10)) - l10);
            if (abs < i10) {
                view = w10;
                i10 = abs;
            }
        }
        return view;
    }

    @NonNull
    public final v j(@NonNull RecyclerView.j jVar) {
        v vVar = this.f2471e;
        if (vVar == null || vVar.f2473a != jVar) {
            this.f2471e = new t(jVar);
        }
        return this.f2471e;
    }

    @NonNull
    public final v k(@NonNull RecyclerView.j jVar) {
        v vVar = this.f2470d;
        if (vVar == null || vVar.f2473a != jVar) {
            this.f2470d = new u(jVar);
        }
        return this.f2470d;
    }
}
