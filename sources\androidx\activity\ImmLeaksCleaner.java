package androidx.activity;

import android.app.Activity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.e;
import androidx.lifecycle.g;
import java.lang.reflect.Field;

@RequiresApi(19)
final class ImmLeaksCleaner implements e {

    /* renamed from: b  reason: collision with root package name */
    public static int f280b;

    /* renamed from: c  reason: collision with root package name */
    public static Field f281c;

    /* renamed from: d  reason: collision with root package name */
    public static Field f282d;

    /* renamed from: e  reason: collision with root package name */
    public static Field f283e;

    /* renamed from: a  reason: collision with root package name */
    public Activity f284a;

    @Override // androidx.lifecycle.e
    public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            if (f280b == 0) {
                try {
                    f280b = 2;
                    Field declaredField = InputMethodManager.class.getDeclaredField("mServedView");
                    f282d = declaredField;
                    declaredField.setAccessible(true);
                    Field declaredField2 = InputMethodManager.class.getDeclaredField("mNextServedView");
                    f283e = declaredField2;
                    declaredField2.setAccessible(true);
                    Field declaredField3 = InputMethodManager.class.getDeclaredField("mH");
                    f281c = declaredField3;
                    declaredField3.setAccessible(true);
                    f280b = 1;
                } catch (NoSuchFieldException unused) {
                }
            }
            if (f280b == 1) {
                InputMethodManager inputMethodManager = (InputMethodManager) this.f284a.getSystemService("input_method");
                try {
                    Object obj = f281c.get(inputMethodManager);
                    if (obj != null) {
                        synchronized (obj) {
                            try {
                                View view = (View) f282d.get(inputMethodManager);
                                if (view != null) {
                                    if (!view.isAttachedToWindow()) {
                                        try {
                                            f283e.set(inputMethodManager, null);
                                            inputMethodManager.isActive();
                                        } catch (IllegalAccessException unused2) {
                                        }
                                    }
                                }
                            } catch (IllegalAccessException unused3) {
                            } catch (ClassCastException unused4) {
                            }
                        }
                    }
                } catch (IllegalAccessException unused5) {
                }
            }
        }
    }
}
