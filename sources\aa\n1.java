package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: DLSet */
public class n1 extends t {

    /* renamed from: c  reason: collision with root package name */
    public int f206c = -1;

    public n1() {
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        p b10 = pVar.b();
        int s10 = s();
        pVar.c(49);
        pVar.g(s10);
        Enumeration q10 = q();
        while (q10.hasMoreElements()) {
            b10.h((e) q10.nextElement());
        }
    }

    @Override // aa.q
    public int i() throws IOException {
        int s10 = s();
        return v1.a(s10) + 1 + s10;
    }

    public final int s() throws IOException {
        if (this.f206c < 0) {
            int i10 = 0;
            Enumeration q10 = q();
            while (q10.hasMoreElements()) {
                i10 += ((e) q10.nextElement()).c().m().i();
            }
            this.f206c = i10;
        }
        return this.f206c;
    }

    public n1(e eVar) {
        super(eVar);
    }

    public n1(f fVar) {
        super(fVar, false);
    }

    public n1(e[] eVarArr) {
        super(eVarArr, false);
    }
}
