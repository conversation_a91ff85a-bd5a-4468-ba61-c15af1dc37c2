package androidx.fragment.app;

import androidx.activity.result.c;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import u.g;

/* compiled from: FragmentFactory */
public class u {

    /* renamed from: a  reason: collision with root package name */
    public static final g<ClassLoader, g<String, Class<?>>> f2013a = new g<>();

    @NonNull
    public static Class<?> b(@NonNull ClassLoader classLoader, @NonNull String str) throws ClassNotFoundException {
        g<ClassLoader, g<String, Class<?>>> gVar = f2013a;
        g<String, Class<?>> orDefault = gVar.getOrDefault(classLoader, null);
        if (orDefault == null) {
            orDefault = new g<>();
            gVar.put(classLoader, orDefault);
        }
        Class<?> orDefault2 = orDefault.getOrDefault(str, null);
        if (orDefault2 != null) {
            return orDefault2;
        }
        Class<?> cls = Class.forName(str, false, classLoader);
        orDefault.put(str, cls);
        return cls;
    }

    /* JADX DEBUG: Type inference failed for r3v3. Raw type applied. Possible types: java.lang.Class<?>, java.lang.Class<? extends androidx.fragment.app.Fragment> */
    @NonNull
    public static Class<? extends Fragment> c(@NonNull ClassLoader classLoader, @NonNull String str) {
        try {
            return b(classLoader, str);
        } catch (ClassNotFoundException e10) {
            throw new Fragment.InstantiationException(c.a("Unable to instantiate fragment ", str, ": make sure class name exists"), e10);
        } catch (ClassCastException e11) {
            throw new Fragment.InstantiationException(c.a("Unable to instantiate fragment ", str, ": make sure class is a valid subclass of Fragment"), e11);
        }
    }

    @NonNull
    public Fragment a(@NonNull ClassLoader classLoader, @NonNull String str) {
        throw null;
    }
}
