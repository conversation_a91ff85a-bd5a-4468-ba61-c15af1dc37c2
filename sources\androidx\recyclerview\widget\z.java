package androidx.recyclerview.widget;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import java.util.Map;
import java.util.WeakHashMap;
import k0.b;
import k0.c;

/* compiled from: RecyclerViewAccessibilityDelegate */
public class z extends androidx.core.view.a {

    /* renamed from: d  reason: collision with root package name */
    public final RecyclerView f2481d;

    /* renamed from: e  reason: collision with root package name */
    public final a f2482e;

    /* compiled from: RecyclerViewAccessibilityDelegate */
    public static class a extends androidx.core.view.a {

        /* renamed from: d  reason: collision with root package name */
        public final z f2483d;

        /* renamed from: e  reason: collision with root package name */
        public Map<View, androidx.core.view.a> f2484e = new WeakHashMap();

        public a(@NonNull z zVar) {
            this.f2483d = zVar;
        }

        @Override // androidx.core.view.a
        public boolean a(@NonNull View view, @NonNull AccessibilityEvent accessibilityEvent) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                return aVar.a(view, accessibilityEvent);
            }
            return this.f1598a.dispatchPopulateAccessibilityEvent(view, accessibilityEvent);
        }

        @Override // androidx.core.view.a
        @Nullable
        public c b(@NonNull View view) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                return aVar.b(view);
            }
            return super.b(view);
        }

        @Override // androidx.core.view.a
        public void c(@NonNull View view, @NonNull AccessibilityEvent accessibilityEvent) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                aVar.c(view, accessibilityEvent);
            } else {
                this.f1598a.onInitializeAccessibilityEvent(view, accessibilityEvent);
            }
        }

        @Override // androidx.core.view.a
        public void d(View view, b bVar) {
            if (this.f2483d.j() || this.f2483d.f2481d.getLayoutManager() == null) {
                this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
                return;
            }
            this.f2483d.f2481d.getLayoutManager().d0(view, bVar);
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                aVar.d(view, bVar);
            } else {
                this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
            }
        }

        @Override // androidx.core.view.a
        public void e(@NonNull View view, @NonNull AccessibilityEvent accessibilityEvent) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                aVar.e(view, accessibilityEvent);
            } else {
                this.f1598a.onPopulateAccessibilityEvent(view, accessibilityEvent);
            }
        }

        @Override // androidx.core.view.a
        public boolean f(@NonNull ViewGroup viewGroup, @NonNull View view, @NonNull AccessibilityEvent accessibilityEvent) {
            androidx.core.view.a aVar = this.f2484e.get(viewGroup);
            if (aVar != null) {
                return aVar.f(viewGroup, view, accessibilityEvent);
            }
            return this.f1598a.onRequestSendAccessibilityEvent(viewGroup, view, accessibilityEvent);
        }

        @Override // androidx.core.view.a
        public boolean g(View view, int i10, Bundle bundle) {
            if (this.f2483d.j() || this.f2483d.f2481d.getLayoutManager() == null) {
                return super.g(view, i10, bundle);
            }
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                if (aVar.g(view, i10, bundle)) {
                    return true;
                }
            } else if (super.g(view, i10, bundle)) {
                return true;
            }
            RecyclerView.p pVar = this.f2483d.f2481d.getLayoutManager().f2199b.f2140b;
            return false;
        }

        @Override // androidx.core.view.a
        public void h(@NonNull View view, int i10) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                aVar.h(view, i10);
            } else {
                this.f1598a.sendAccessibilityEvent(view, i10);
            }
        }

        @Override // androidx.core.view.a
        public void i(@NonNull View view, @NonNull AccessibilityEvent accessibilityEvent) {
            androidx.core.view.a aVar = this.f2484e.get(view);
            if (aVar != null) {
                aVar.i(view, accessibilityEvent);
            } else {
                this.f1598a.sendAccessibilityEventUnchecked(view, accessibilityEvent);
            }
        }
    }

    public z(@NonNull RecyclerView recyclerView) {
        this.f2481d = recyclerView;
        a aVar = this.f2482e;
        if (aVar != null) {
            this.f2482e = aVar;
        } else {
            this.f2482e = new a(this);
        }
    }

    @Override // androidx.core.view.a
    public void c(View view, AccessibilityEvent accessibilityEvent) {
        this.f1598a.onInitializeAccessibilityEvent(view, accessibilityEvent);
        if ((view instanceof RecyclerView) && !j()) {
            RecyclerView recyclerView = (RecyclerView) view;
            if (recyclerView.getLayoutManager() != null) {
                recyclerView.getLayoutManager().b0(accessibilityEvent);
            }
        }
    }

    @Override // androidx.core.view.a
    public void d(View view, b bVar) {
        this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
        if (!j() && this.f2481d.getLayoutManager() != null) {
            RecyclerView.j layoutManager = this.f2481d.getLayoutManager();
            RecyclerView recyclerView = layoutManager.f2199b;
            layoutManager.c0(recyclerView.f2140b, recyclerView.R0, bVar);
        }
    }

    @Override // androidx.core.view.a
    public boolean g(View view, int i10, Bundle bundle) {
        if (super.g(view, i10, bundle)) {
            return true;
        }
        if (j() || this.f2481d.getLayoutManager() == null) {
            return false;
        }
        RecyclerView.j layoutManager = this.f2481d.getLayoutManager();
        RecyclerView recyclerView = layoutManager.f2199b;
        return layoutManager.p0(recyclerView.f2140b, recyclerView.R0, i10, bundle);
    }

    public boolean j() {
        return this.f2481d.M();
    }
}
