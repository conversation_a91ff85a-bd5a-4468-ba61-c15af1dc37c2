<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="a_code_note_1">Press</string>
    <string name="a_code_note_2">button to cancle</string>
    <string name="a_code_title">Verification Code</string>
    <string name="airkan_app_name">Mi-Link Airkan Services</string>
    <string name="airkan_daemon_handle_progress">Airkan Handle Progress</string>
    <string name="airkan_daemon_name">Airkan TV Daemon</string>
    <string name="airkan_daemon_start">RemoteControllerService Started.</string>
    <string name="app_name">RemoteControllerService</string>
    <string name="cast_ad_name_box">MiBox</string>
    <string name="cast_ad_name_tv">MiTV</string>
    <string name="check_auth_accept">允许</string>
    <string name="check_auth_cast_subtitle">%s的%s正在请求投屏该设备，请确认是否允许？</string>
    <string name="check_auth_cast_subtitle_unknown">一台新设备正在请求投屏该设备，请确认是否允许？</string>
    <string name="check_auth_reject">拒绝</string>
    <string name="check_auth_subtitle">%s的%s正在请求操作该设备，请确认是否允许？</string>
    <string name="check_auth_subtitle2">%s正在请求控制该设备，请确认是否允许？</string>
    <string name="check_auth_subtitle_unknown">一台新设备正在请求操作该设备，请确认是否允许？</string>
    <string name="check_auth_title">安全提醒</string>
    <string name="close_in_seconds_last">seconds</string>
    <string name="close_in_seconds_pre">Close in</string>
    <string name="confirm">Confirm</string>
    <string name="default_client_name" />
    <string name="default_device_name">MiBox</string>
    <string name="default_music_title">Music</string>
    <string name="default_video_title">Video</string>
    <string name="mihome_remote_logout_main">Account loged out forcedly by remote command</string>
    <string name="mihome_remote_logout_title">Logout</string>
    <string name="miliao">MiChat</string>
    <string name="milink_httpservice_name">MiLinkHttpServer</string>
    <string name="modify_device_name">New name:</string>
    <string name="notification_des">Too many tries on APK installation, it maybe malicious attack</string>
    <string name="notification_title">Attention</string>
    <string name="notify_rc_content">The strongest partner for MITV</string>
    <string name="notify_rc_title">Mi Remote</string>
    <string name="pc">PC</string>
    <string name="playerservice_name">Airkan TV Player Service</string>
    <string name="rc_daemon_name">Remote Controller TV Daemon</string>
    <string name="rc_daemon_start">Remote Controller TV Daemon Started.</string>
    <string name="recv_service_start">to receive service start broadcast</string>
    <string name="retry_too_many_times_tip">For security, please try again 24 hours later</string>
    <string name="security_check_title">Cast from %1$s?</string>
    <string name="security_confirmation_button_cancel">Reject</string>
    <string name="security_confirmation_button_confirm">Accept</string>
    <string name="security_confirmation_main">Will you allow this operation?</string>
    <string name="security_confirmation_title_box">A device want to control your Box</string>
    <string name="security_confirmation_title_tv">A device want to control your TV</string>
    <string name="security_error_too_many_times">校验失败次数过多，请%1$d小时后重试</string>
    <string name="security_password_main">Please press the following keys with your remote controller</string>
    <string name="security_password_title_box">A Device want to control your Xiaomi Box</string>
    <string name="security_password_title_tv">A Device want to control your Xiaomi TV</string>
    <string name="security_toast_note_1_last">minutes</string>
    <string name="security_toast_note_1_pre">Too many times to cancel certification, please try it after</string>
    <string name="security_toast_note_2">Too many times to cancel certification, please try it later.</string>
    <string name="security_toast_note_3">Too many times to cancel certification, please try it later.</string>
    <string name="security_toast_note_3_last">minutes</string>
    <string name="security_toast_note_3_pre">Too many times to cancel certification, please try it after</string>
    <string name="security_too_many_times_hint_2">投屏认证太频繁，请稍后重试</string>
    <string name="security_verifycode_title_tv">Please enter the connection code</string>
    <string name="send_service_start">to send service start broadcast</string>
    <string name="share_input_default_message">New share for you, check it now ~</string>
    <string name="weixin">WeChat</string>
</resources>
