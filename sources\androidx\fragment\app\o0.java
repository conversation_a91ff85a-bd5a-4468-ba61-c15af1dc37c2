package androidx.fragment.app;

import android.view.View;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.ArrayList;
import java.util.Map;
import java.util.WeakHashMap;

/* compiled from: FragmentTransitionImpl */
public class o0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ArrayList f1973a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Map f1974b;

    public o0(m0 m0Var, ArrayList arrayList, Map map) {
        this.f1973a = arrayList;
        this.f1974b = map;
    }

    public void run() {
        int size = this.f1973a.size();
        for (int i10 = 0; i10 < size; i10++) {
            View view = (View) this.f1973a.get(i10);
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            view.setTransitionName((String) this.f1974b.get(view.getTransitionName()));
        }
    }
}
