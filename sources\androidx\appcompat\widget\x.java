package androidx.appcompat.widget;

import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import androidx.annotation.RestrictTo;
import com.duokan.airkan.common.Constant;
import q.f;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: ForwardingListener */
public abstract class x implements View.OnTouchListener, View.OnAttachStateChangeListener {

    /* renamed from: a  reason: collision with root package name */
    public final float f1218a;

    /* renamed from: b  reason: collision with root package name */
    public final int f1219b;

    /* renamed from: c  reason: collision with root package name */
    public final int f1220c;

    /* renamed from: d  reason: collision with root package name */
    public final View f1221d;

    /* renamed from: e  reason: collision with root package name */
    public Runnable f1222e;

    /* renamed from: f  reason: collision with root package name */
    public Runnable f1223f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f1224g;
    public int h;

    /* renamed from: i  reason: collision with root package name */
    public final int[] f1225i = new int[2];

    /* compiled from: ForwardingListener */
    public class a implements Runnable {
        public a() {
        }

        public void run() {
            ViewParent parent = x.this.f1221d.getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(true);
            }
        }
    }

    /* compiled from: ForwardingListener */
    public class b implements Runnable {
        public b() {
        }

        public void run() {
            x xVar = x.this;
            xVar.a();
            View view = xVar.f1221d;
            if (view.isEnabled() && !view.isLongClickable() && xVar.c()) {
                view.getParent().requestDisallowInterceptTouchEvent(true);
                long uptimeMillis = SystemClock.uptimeMillis();
                MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0);
                view.onTouchEvent(obtain);
                obtain.recycle();
                xVar.f1224g = true;
            }
        }
    }

    public x(View view) {
        this.f1221d = view;
        view.setLongClickable(true);
        view.addOnAttachStateChangeListener(this);
        this.f1218a = (float) ViewConfiguration.get(view.getContext()).getScaledTouchSlop();
        int tapTimeout = ViewConfiguration.getTapTimeout();
        this.f1219b = tapTimeout;
        this.f1220c = (ViewConfiguration.getLongPressTimeout() + tapTimeout) / 2;
    }

    public final void a() {
        Runnable runnable = this.f1223f;
        if (runnable != null) {
            this.f1221d.removeCallbacks(runnable);
        }
        Runnable runnable2 = this.f1222e;
        if (runnable2 != null) {
            this.f1221d.removeCallbacks(runnable2);
        }
    }

    public abstract f b();

    public abstract boolean c();

    public boolean d() {
        f b10 = b();
        if (b10 == null || !b10.isShowing()) {
            return true;
        }
        b10.dismiss();
        return true;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:32:0x0087, code lost:
        if (r4 != 3) goto L_0x0079;
     */
    /* JADX WARNING: Removed duplicated region for block: B:61:0x0113  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouch(android.view.View r12, android.view.MotionEvent r13) {
        /*
        // Method dump skipped, instructions count: 305
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.x.onTouch(android.view.View, android.view.MotionEvent):boolean");
    }

    public void onViewAttachedToWindow(View view) {
    }

    public void onViewDetachedFromWindow(View view) {
        this.f1224g = false;
        this.h = -1;
        Runnable runnable = this.f1222e;
        if (runnable != null) {
            this.f1221d.removeCallbacks(runnable);
        }
    }
}
