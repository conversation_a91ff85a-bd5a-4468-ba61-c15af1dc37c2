package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;

/* access modifiers changed from: package-private */
@SuppressLint({"BanParcelableUsage"})
public final class FragmentState implements Parcelable {
    public static final Parcelable.Creator<FragmentState> CREATOR = new a();

    /* renamed from: a  reason: collision with root package name */
    public final String f1821a;

    /* renamed from: b  reason: collision with root package name */
    public final String f1822b;

    /* renamed from: c  reason: collision with root package name */
    public final boolean f1823c;

    /* renamed from: d  reason: collision with root package name */
    public final int f1824d;

    /* renamed from: e  reason: collision with root package name */
    public final int f1825e;

    /* renamed from: f  reason: collision with root package name */
    public final String f1826f;

    /* renamed from: g  reason: collision with root package name */
    public final boolean f1827g;
    public final boolean h;

    /* renamed from: i  reason: collision with root package name */
    public final boolean f1828i;

    /* renamed from: j  reason: collision with root package name */
    public final Bundle f1829j;

    /* renamed from: k  reason: collision with root package name */
    public final boolean f1830k;

    /* renamed from: l  reason: collision with root package name */
    public final int f1831l;

    /* renamed from: m  reason: collision with root package name */
    public Bundle f1832m;

    public class a implements Parcelable.Creator<FragmentState> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // android.os.Parcelable.Creator
        public FragmentState createFromParcel(Parcel parcel) {
            return new FragmentState(parcel);
        }

        /* Return type fixed from 'java.lang.Object[]' to match base method */
        @Override // android.os.Parcelable.Creator
        public FragmentState[] newArray(int i10) {
            return new FragmentState[i10];
        }
    }

    public FragmentState(Fragment fragment) {
        this.f1821a = fragment.getClass().getName();
        this.f1822b = fragment.f1724e;
        this.f1823c = fragment.f1731m;
        this.f1824d = fragment.f1752y;
        this.f1825e = fragment.f1732m0;
        this.f1826f = fragment.f1734n0;
        this.f1827g = fragment.f1740q0;
        this.h = fragment.f1730l;
        this.f1828i = fragment.f1738p0;
        this.f1829j = fragment.f1725f;
        this.f1830k = fragment.f1736o0;
        this.f1831l = fragment.B0.ordinal();
    }

    public int describeContents() {
        return 0;
    }

    @NonNull
    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("FragmentState{");
        sb.append(this.f1821a);
        sb.append(" (");
        sb.append(this.f1822b);
        sb.append(")}:");
        if (this.f1823c) {
            sb.append(" fromLayout");
        }
        if (this.f1825e != 0) {
            sb.append(" id=0x");
            sb.append(Integer.toHexString(this.f1825e));
        }
        String str = this.f1826f;
        if (str != null && !str.isEmpty()) {
            sb.append(" tag=");
            sb.append(this.f1826f);
        }
        if (this.f1827g) {
            sb.append(" retainInstance");
        }
        if (this.h) {
            sb.append(" removing");
        }
        if (this.f1828i) {
            sb.append(" detached");
        }
        if (this.f1830k) {
            sb.append(" hidden");
        }
        return sb.toString();
    }

    public void writeToParcel(Parcel parcel, int i10) {
        parcel.writeString(this.f1821a);
        parcel.writeString(this.f1822b);
        parcel.writeInt(this.f1823c ? 1 : 0);
        parcel.writeInt(this.f1824d);
        parcel.writeInt(this.f1825e);
        parcel.writeString(this.f1826f);
        parcel.writeInt(this.f1827g ? 1 : 0);
        parcel.writeInt(this.h ? 1 : 0);
        parcel.writeInt(this.f1828i ? 1 : 0);
        parcel.writeBundle(this.f1829j);
        parcel.writeInt(this.f1830k ? 1 : 0);
        parcel.writeBundle(this.f1832m);
        parcel.writeInt(this.f1831l);
    }

    public FragmentState(Parcel parcel) {
        this.f1821a = parcel.readString();
        this.f1822b = parcel.readString();
        boolean z10 = true;
        this.f1823c = parcel.readInt() != 0;
        this.f1824d = parcel.readInt();
        this.f1825e = parcel.readInt();
        this.f1826f = parcel.readString();
        this.f1827g = parcel.readInt() != 0;
        this.h = parcel.readInt() != 0;
        this.f1828i = parcel.readInt() != 0;
        this.f1829j = parcel.readBundle();
        this.f1830k = parcel.readInt() == 0 ? false : z10;
        this.f1832m = parcel.readBundle();
        this.f1831l = parcel.readInt();
    }
}
