package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.SolverVariable;
import androidx.constraintlayout.solver.c;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;
import com.duokan.airkan.common.Constant;
import java.util.ArrayList;
import w.a;

/* compiled from: Barrier */
public class b extends a {

    /* renamed from: k0  reason: collision with root package name */
    public int f1326k0 = 0;

    /* renamed from: l0  reason: collision with root package name */
    public ArrayList<i> f1327l0 = new ArrayList<>(4);

    /* renamed from: m0  reason: collision with root package name */
    public boolean f1328m0 = true;

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void a(c cVar) {
        Object[] objArr;
        boolean z10;
        int i10;
        int i11;
        ConstraintAnchor[] constraintAnchorArr = this.A;
        constraintAnchorArr[0] = this.f1314s;
        constraintAnchorArr[2] = this.f1315t;
        constraintAnchorArr[1] = this.f1316u;
        constraintAnchorArr[3] = this.f1317v;
        int i12 = 0;
        while (true) {
            objArr = this.A;
            if (i12 >= objArr.length) {
                break;
            }
            objArr[i12].f1283i = cVar.l(objArr[i12]);
            i12++;
        }
        int i13 = this.f1326k0;
        if (i13 >= 0 && i13 < 4) {
            ConstraintAnchor constraintAnchor = objArr[i13];
            int i14 = 0;
            while (true) {
                if (i14 >= this.f10632j0) {
                    z10 = false;
                    break;
                }
                ConstraintWidget constraintWidget = this.f10631i0[i14];
                if ((this.f1328m0 || constraintWidget.b()) && ((((i10 = this.f1326k0) == 0 || i10 == 1) && constraintWidget.i() == ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT) || (((i11 = this.f1326k0) == 2 || i11 == 3) && constraintWidget.m() == ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT))) {
                    z10 = true;
                } else {
                    i14++;
                }
            }
            int i15 = this.f1326k0;
            if (i15 == 0 || i15 == 1 ? this.D.i() == ConstraintWidget.DimensionBehaviour.WRAP_CONTENT : this.D.m() == ConstraintWidget.DimensionBehaviour.WRAP_CONTENT) {
                z10 = false;
            }
            for (int i16 = 0; i16 < this.f10632j0; i16++) {
                ConstraintWidget constraintWidget2 = this.f10631i0[i16];
                if (this.f1328m0 || constraintWidget2.b()) {
                    SolverVariable l10 = cVar.l(constraintWidget2.A[this.f1326k0]);
                    ConstraintAnchor[] constraintAnchorArr2 = constraintWidget2.A;
                    int i17 = this.f1326k0;
                    constraintAnchorArr2[i17].f1283i = l10;
                    if (i17 == 0 || i17 == 2) {
                        SolverVariable solverVariable = constraintAnchor.f1283i;
                        androidx.constraintlayout.solver.b m10 = cVar.m();
                        SolverVariable n10 = cVar.n();
                        n10.f1242c = 0;
                        m10.e(solverVariable, l10, n10, 0);
                        if (z10) {
                            m10.f1260c.h(cVar.k(1, null), (float) ((int) (m10.f1260c.c(n10) * -1.0f)));
                        }
                        cVar.c(m10);
                    } else {
                        SolverVariable solverVariable2 = constraintAnchor.f1283i;
                        androidx.constraintlayout.solver.b m11 = cVar.m();
                        SolverVariable n11 = cVar.n();
                        n11.f1242c = 0;
                        m11.d(solverVariable2, l10, n11, 0);
                        if (z10) {
                            m11.f1260c.h(cVar.k(1, null), (float) ((int) (m11.f1260c.c(n11) * -1.0f)));
                        }
                        cVar.c(m11);
                    }
                }
            }
            int i18 = this.f1326k0;
            if (i18 == 0) {
                cVar.d(this.f1316u.f1283i, this.f1314s.f1283i, 0, 6);
                if (!z10) {
                    cVar.d(this.f1314s.f1283i, this.D.f1316u.f1283i, 0, 5);
                }
            } else if (i18 == 1) {
                cVar.d(this.f1314s.f1283i, this.f1316u.f1283i, 0, 6);
                if (!z10) {
                    cVar.d(this.f1314s.f1283i, this.D.f1314s.f1283i, 0, 5);
                }
            } else if (i18 == 2) {
                cVar.d(this.f1317v.f1283i, this.f1315t.f1283i, 0, 6);
                if (!z10) {
                    cVar.d(this.f1315t.f1283i, this.D.f1317v.f1283i, 0, 5);
                }
            } else if (i18 == 3) {
                cVar.d(this.f1315t.f1283i, this.f1317v.f1283i, 0, 6);
                if (!z10) {
                    cVar.d(this.f1315t.f1283i, this.D.f1315t.f1283i, 0, 5);
                }
            }
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public boolean b() {
        return true;
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void c(int i10) {
        i iVar;
        i iVar2;
        ConstraintWidget constraintWidget = this.D;
        if (constraintWidget != null && ((e) constraintWidget).I(2)) {
            int i11 = this.f1326k0;
            if (i11 == 0) {
                iVar = this.f1314s.f1276a;
            } else if (i11 == 1) {
                iVar = this.f1316u.f1276a;
            } else if (i11 == 2) {
                iVar = this.f1315t.f1276a;
            } else if (i11 == 3) {
                iVar = this.f1317v.f1276a;
            } else {
                return;
            }
            iVar.h = 5;
            if (i11 == 0 || i11 == 1) {
                this.f1315t.f1276a.j(null, Constant.VOLUME_FLOAT_MIN);
                this.f1317v.f1276a.j(null, Constant.VOLUME_FLOAT_MIN);
            } else {
                this.f1314s.f1276a.j(null, Constant.VOLUME_FLOAT_MIN);
                this.f1316u.f1276a.j(null, Constant.VOLUME_FLOAT_MIN);
            }
            this.f1327l0.clear();
            for (int i12 = 0; i12 < this.f10632j0; i12++) {
                ConstraintWidget constraintWidget2 = this.f10631i0[i12];
                if (this.f1328m0 || constraintWidget2.b()) {
                    int i13 = this.f1326k0;
                    if (i13 == 0) {
                        iVar2 = constraintWidget2.f1314s.f1276a;
                    } else if (i13 == 1) {
                        iVar2 = constraintWidget2.f1316u.f1276a;
                    } else if (i13 == 2) {
                        iVar2 = constraintWidget2.f1315t.f1276a;
                    } else if (i13 != 3) {
                        iVar2 = null;
                    } else {
                        iVar2 = constraintWidget2.f1317v.f1276a;
                    }
                    if (iVar2 != null) {
                        this.f1327l0.add(iVar2);
                        iVar2.f10634a.add(iVar);
                    }
                }
            }
        }
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void s() {
        super.s();
        this.f1327l0.clear();
    }

    @Override // androidx.constraintlayout.solver.widgets.ConstraintWidget
    public void u() {
        i iVar;
        float f10;
        i iVar2;
        int i10 = this.f1326k0;
        float f11 = Float.MAX_VALUE;
        if (i10 != 0) {
            if (i10 == 1) {
                iVar = this.f1316u.f1276a;
            } else if (i10 == 2) {
                iVar = this.f1315t.f1276a;
            } else if (i10 == 3) {
                iVar = this.f1317v.f1276a;
            } else {
                return;
            }
            f11 = 0.0f;
        } else {
            iVar = this.f1314s.f1276a;
        }
        int size = this.f1327l0.size();
        i iVar3 = null;
        for (int i11 = 0; i11 < size; i11++) {
            i iVar4 = this.f1327l0.get(i11);
            if (iVar4.f10635b == 1) {
                int i12 = this.f1326k0;
                if (i12 == 0 || i12 == 2) {
                    f10 = iVar4.f1381g;
                    if (f10 < f11) {
                        iVar2 = iVar4.f1380f;
                    }
                } else {
                    f10 = iVar4.f1381g;
                    if (f10 > f11) {
                        iVar2 = iVar4.f1380f;
                    }
                }
                iVar3 = iVar2;
                f11 = f10;
            } else {
                return;
            }
        }
        iVar.f1380f = iVar3;
        iVar.f1381g = f11;
        iVar.a();
        int i13 = this.f1326k0;
        if (i13 == 0) {
            this.f1316u.f1276a.j(iVar3, f11);
        } else if (i13 == 1) {
            this.f1314s.f1276a.j(iVar3, f11);
        } else if (i13 == 2) {
            this.f1317v.f1276a.j(iVar3, f11);
        } else if (i13 == 3) {
            this.f1315t.f1276a.j(iVar3, f11);
        }
    }
}
