package ab;

import bb.c;
import bb.g;
import java.math.BigInteger;
import java.security.spec.AlgorithmParameterSpec;

/* compiled from: ECParameterSpec */
public class d implements AlgorithmParameterSpec {

    /* renamed from: a  reason: collision with root package name */
    public c f250a;

    /* renamed from: b  reason: collision with root package name */
    public byte[] f251b;

    /* renamed from: c  reason: collision with root package name */
    public g f252c;

    /* renamed from: d  reason: collision with root package name */
    public BigInteger f253d;

    /* renamed from: e  reason: collision with root package name */
    public BigInteger f254e;

    public d(c cVar, g gVar, BigInteger bigInteger) {
        this.f250a = cVar;
        this.f252c = gVar.o();
        this.f253d = bigInteger;
        this.f254e = BigInteger.valueOf(1);
        this.f251b = null;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof d)) {
            return false;
        }
        d dVar = (d) obj;
        if (!this.f250a.g(dVar.f250a) || !this.f252c.d(dVar.f252c)) {
            return false;
        }
        return true;
    }

    public int hashCode() {
        return this.f250a.hashCode() ^ this.f252c.hashCode();
    }

    public d(c cVar, g gVar, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr) {
        this.f250a = cVar;
        this.f252c = gVar.o();
        this.f253d = bigInteger;
        this.f254e = bigInteger2;
        this.f251b = bArr;
    }
}
