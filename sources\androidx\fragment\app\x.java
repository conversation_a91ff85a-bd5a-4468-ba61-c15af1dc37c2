package androidx.fragment.app;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.Iterator;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/* compiled from: FragmentLifecycleCallbacksDispatcher */
public class x {
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final CopyOnWriteArrayList<a> f2021a = new CopyOnWriteArrayList<>();
    @NonNull

    /* renamed from: b  reason: collision with root package name */
    public final FragmentManager f2022b;

    /* compiled from: FragmentLifecycleCallbacksDispatcher */
    public static final class a {
    }

    public x(@NonNull FragmentManager fragmentManager) {
        this.f2022b = fragmentManager;
    }

    public void a(@NonNull Fragment fragment, @Nullable Bundle bundle, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.a(fragment, bundle, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void b(@NonNull Fragment fragment, boolean z10) {
        FragmentManager fragmentManager = this.f2022b;
        Context context = fragmentManager.f1788q.f2016b;
        Fragment fragment2 = fragmentManager.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.b(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void c(@NonNull Fragment fragment, @Nullable Bundle bundle, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.c(fragment, bundle, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void d(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.d(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void e(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.e(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void f(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.f(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void g(@NonNull Fragment fragment, boolean z10) {
        FragmentManager fragmentManager = this.f2022b;
        Context context = fragmentManager.f1788q.f2016b;
        Fragment fragment2 = fragmentManager.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.g(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void h(@NonNull Fragment fragment, @Nullable Bundle bundle, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.h(fragment, bundle, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void i(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.i(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void j(@NonNull Fragment fragment, @NonNull Bundle bundle, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.j(fragment, bundle, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void k(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.k(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void l(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.l(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void m(@NonNull Fragment fragment, @NonNull View view, @Nullable Bundle bundle, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.m(fragment, view, bundle, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }

    public void n(@NonNull Fragment fragment, boolean z10) {
        Fragment fragment2 = this.f2022b.f1790s;
        if (fragment2 != null) {
            fragment2.m().f1785n.n(fragment, true);
        }
        Iterator<a> it = this.f2021a.iterator();
        while (it.hasNext()) {
            a next = it.next();
            if (z10) {
                Objects.requireNonNull(next);
            } else {
                Objects.requireNonNull(next);
                throw null;
            }
        }
    }
}
