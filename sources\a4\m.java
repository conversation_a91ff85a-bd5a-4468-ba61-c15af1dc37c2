package a4;

import com.duokan.airkan.common.Log;
import com.duokan.airkan.common.ServiceData;
import com.duokan.airkan.common.aidl.ParcelDeviceData;
import com.duokan.airkan.server.f;
import javax.jmdns.ServiceInfo;
import javax.jmdns.impl.JmDNSImpl;
import javax.jmdns.impl.h;

public class m implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ServiceData f60a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ l f61b;

    public m(l lVar, ServiceData serviceData) {
        this.f61b = lVar;
        this.f60a = serviceData;
    }

    public void run() {
        if (this.f61b.f49i == null) {
            Log.i("AKTS-UDTJmDNSThread", "JmDNS not available");
            return;
        }
        ServiceData serviceData = new ServiceData();
        ServiceData serviceData2 = this.f60a;
        String str = serviceData2.type;
        serviceData.type = str;
        String str2 = serviceData2.name;
        serviceData.name = str2;
        ServiceInfo B = ((JmDNSImpl) this.f61b.f49i).B(str, str2);
        if (B != null) {
            serviceData.port = ((h) B).h;
            serviceData.ip = B.f();
            serviceData.extraText = new String(B.o());
            StringBuilder a10 = f.a("ADD SERVICE: name[");
            a10.append(serviceData.name);
            a10.append("] type[");
            a10.append(serviceData.type);
            a10.append("] ip[");
            a10.append(serviceData.getIP());
            a10.append("] port[");
            a10.append(serviceData.port);
            a10.append("] text[");
            a10.append(serviceData.extraText);
            a10.append("]");
            Log.i("AKTS-UDTJmDNSThread", a10.toString());
            f fVar = this.f61b.f44c;
            o oVar = fVar.f25e.get(serviceData.type);
            if (oVar != null) {
                synchronized (oVar) {
                    if (oVar.f66c.find(serviceData) != null) {
                        Log.d("UDTListenManager", "Old service: name[" + serviceData.name + "] type[" + serviceData.type + "] ip[" + serviceData.getIP() + "] port[" + serviceData.port + "]");
                    } else {
                        Log.i("UDTListenManager", "New service: name[" + serviceData.name + "] type[" + serviceData.type + "] ip[" + serviceData.getIP() + "] port[" + serviceData.port + "] text[" + serviceData.extraText + "]");
                        ServiceData findIgnoreIP = oVar.f66c.findIgnoreIP(serviceData);
                        if (findIgnoreIP != null) {
                            Log.w("UDTListenManager", "Similar service: name[" + findIgnoreIP.name + "] type[" + findIgnoreIP.type + "] ip[" + findIgnoreIP.getIP() + "] port[" + findIgnoreIP.port + "]");
                            oVar.b(new ParcelDeviceData(findIgnoreIP.name, findIgnoreIP.type, findIgnoreIP.getIP(), findIgnoreIP.extraText, findIgnoreIP.getMac()));
                            oVar.f66c.remove(findIgnoreIP);
                        }
                        oVar.f66c.add(serviceData);
                        ParcelDeviceData parcelDeviceData = new ParcelDeviceData(serviceData.name, serviceData.type, serviceData.getIP(), serviceData.extraText, serviceData.getMac());
                        Log.d("UDTListenManager", "ADD DEVICE: name:" + serviceData.name + " type:" + serviceData.type);
                        oVar.a(parcelDeviceData);
                    }
                }
                return;
            }
            Log.e("UDTDiscoverManager", serviceData.type + " not in listen list, try to remove!");
            fVar.f25e.remove(serviceData.type);
            l lVar = fVar.f27g;
            if (lVar != null) {
                lVar.i(serviceData.type);
            } else {
                Log.e("UDTDiscoverManager", "UDTJmDNSThread not available, no need post regist subscibed service");
            }
        } else {
            Log.w("AKTS-UDTJmDNSThread", "Service information is null");
        }
    }
}
