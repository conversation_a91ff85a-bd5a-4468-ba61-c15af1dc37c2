package androidx.recyclerview.widget;

import b0.b;
import com.duokan.airkan.server.f;

/* compiled from: LayoutState */
public class p {

    /* renamed from: a  reason: collision with root package name */
    public boolean f2454a = true;

    /* renamed from: b  reason: collision with root package name */
    public int f2455b;

    /* renamed from: c  reason: collision with root package name */
    public int f2456c;

    /* renamed from: d  reason: collision with root package name */
    public int f2457d;

    /* renamed from: e  reason: collision with root package name */
    public int f2458e;

    /* renamed from: f  reason: collision with root package name */
    public int f2459f = 0;

    /* renamed from: g  reason: collision with root package name */
    public int f2460g = 0;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f2461i;

    public String toString() {
        StringBuilder a10 = f.a("LayoutState{mAvailable=");
        a10.append(this.f2455b);
        a10.append(", mCurrentPosition=");
        a10.append(this.f2456c);
        a10.append(", mItemDirection=");
        a10.append(this.f2457d);
        a10.append(", mLayoutDirection=");
        a10.append(this.f2458e);
        a10.append(", mStartLine=");
        a10.append(this.f2459f);
        a10.append(", mEndLine=");
        return b.a(a10, this.f2460g, '}');
    }
}
