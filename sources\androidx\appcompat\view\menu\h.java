package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuPresenter */
public interface h {

    /* compiled from: MenuPresenter */
    public interface a {
        void a(@NonNull d dVar, boolean z10);

        boolean b(@NonNull d dVar);
    }

    void a(d dVar, boolean z10);

    void b(boolean z10);

    boolean c();

    void d(Context context, d dVar);

    void e(Parcelable parcelable);

    boolean f(k kVar);

    Parcelable g();

    int getId();

    boolean h(d dVar, f fVar);

    boolean i(d dVar, f fVar);

    void k(a aVar);
}
