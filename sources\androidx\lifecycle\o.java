package androidx.lifecycle;

import android.os.Binder;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.Size;
import android.util.SizeF;
import android.util.SparseArray;
import androidx.annotation.NonNull;
import androidx.savedstate.a;
import com.duokan.airkan.server.f;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/* compiled from: SavedStateHandle */
public final class o {

    /* renamed from: e  reason: collision with root package name */
    public static final Class[] f2077e = {Boolean.TYPE, boolean[].class, Double.TYPE, double[].class, Integer.TYPE, int[].class, Long.TYPE, long[].class, String.class, String[].class, Binder.class, Bundle.class, Byte.TYPE, byte[].class, Character.TYPE, char[].class, CharSequence.class, CharSequence[].class, ArrayList.class, Float.TYPE, float[].class, Parcelable.class, Parcelable[].class, Serializable.class, Short.TYPE, short[].class, SparseArray.class, Size.class, SizeF.class};

    /* renamed from: a  reason: collision with root package name */
    public final Map<String, Object> f2078a;

    /* renamed from: b  reason: collision with root package name */
    public final Map<String, a.b> f2079b;

    /* renamed from: c  reason: collision with root package name */
    public final Map<String, Object> f2080c;

    /* renamed from: d  reason: collision with root package name */
    public final a.b f2081d;

    /* compiled from: SavedStateHandle */
    public class a implements a.b {
        public a() {
        }

        @Override // androidx.savedstate.a.b
        @NonNull
        public Bundle a() {
            for (Map.Entry entry : new HashMap(o.this.f2079b).entrySet()) {
                Bundle a10 = ((a.b) entry.getValue()).a();
                o oVar = o.this;
                String str = (String) entry.getKey();
                Objects.requireNonNull(oVar);
                if (a10 != null) {
                    for (Class cls : o.f2077e) {
                        if (!cls.isInstance(a10)) {
                        }
                    }
                    StringBuilder a11 = f.a("Can't put value with type ");
                    a11.append(a10.getClass());
                    a11.append(" into saved state");
                    throw new IllegalArgumentException(a11.toString());
                }
                l lVar = (l) oVar.f2080c.get(str);
                if (lVar != null) {
                    lVar.h(a10);
                } else {
                    oVar.f2078a.put(str, a10);
                }
            }
            Set<String> keySet = o.this.f2078a.keySet();
            ArrayList<? extends Parcelable> arrayList = new ArrayList<>(keySet.size());
            ArrayList<? extends Parcelable> arrayList2 = new ArrayList<>(arrayList.size());
            for (String str2 : keySet) {
                arrayList.add(str2);
                arrayList2.add(o.this.f2078a.get(str2));
            }
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("keys", arrayList);
            bundle.putParcelableArrayList("values", arrayList2);
            return bundle;
        }
    }

    public o(@NonNull Map<String, Object> map) {
        this.f2079b = new HashMap();
        this.f2080c = new HashMap();
        this.f2081d = new a();
        this.f2078a = new HashMap(map);
    }

    public o() {
        this.f2079b = new HashMap();
        this.f2080c = new HashMap();
        this.f2081d = new a();
        this.f2078a = new HashMap();
    }
}
