package a5;

import com.xiaomi.mitv.socialtv.common.utils.IOUtil;
import java.nio.charset.Charset;

/* compiled from: CharsetUtil */
public final class d {

    /* renamed from: a  reason: collision with root package name */
    public static final Charset f81a = Charset.forName("UTF-8");

    /* renamed from: b  reason: collision with root package name */
    public static final Charset f82b = Charset.forName(IOUtil.CHARSET_NAME_US_ASCII);

    static {
        Charset.forName(IOUtil.CHARSET_NAME_UTF_16);
        Charset.forName(IOUtil.CHARSET_NAME_UTF_16BE);
        Charset.forName(IOUtil.CHARSET_NAME_UTF_16LE);
        Charset.forName(IOUtil.CHARSET_NAME_ISO_8859_1);
    }
}
