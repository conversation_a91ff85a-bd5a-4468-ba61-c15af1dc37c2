package androidx.constraintlayout.solver.widgets;

/* compiled from: Optimizer */
public class h {

    /* renamed from: a  reason: collision with root package name */
    public static boolean[] f1376a = new boolean[3];

    /* JADX WARNING: Removed duplicated region for block: B:25:0x0037 A[RETURN] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static boolean a(androidx.constraintlayout.solver.widgets.ConstraintWidget r4, int r5) {
        /*
            androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour[] r0 = r4.C
            r1 = r0[r5]
            androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour r2 = androidx.constraintlayout.solver.widgets.ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT
            r3 = 0
            if (r1 == r2) goto L_0x000a
            return r3
        L_0x000a:
            float r1 = r4.G
            r2 = 0
            int r1 = (r1 > r2 ? 1 : (r1 == r2 ? 0 : -1))
            r2 = 1
            if (r1 == 0) goto L_0x0019
            if (r5 != 0) goto L_0x0015
            goto L_0x0016
        L_0x0015:
            r2 = r3
        L_0x0016:
            r4 = r0[r2]
            return r3
        L_0x0019:
            if (r5 != 0) goto L_0x0029
            int r5 = r4.f1297e
            if (r5 == 0) goto L_0x0020
            return r3
        L_0x0020:
            int r5 = r4.h
            if (r5 != 0) goto L_0x0028
            int r4 = r4.f1304i
            if (r4 == 0) goto L_0x0037
        L_0x0028:
            return r3
        L_0x0029:
            int r5 = r4.f1299f
            if (r5 == 0) goto L_0x002e
            return r3
        L_0x002e:
            int r5 = r4.f1306k
            if (r5 != 0) goto L_0x0038
            int r4 = r4.f1307l
            if (r4 == 0) goto L_0x0037
            goto L_0x0038
        L_0x0037:
            return r2
        L_0x0038:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.h.a(androidx.constraintlayout.solver.widgets.ConstraintWidget, int):boolean");
    }

    public static void b(ConstraintWidget constraintWidget, int i10, int i11) {
        int i12 = i10 * 2;
        int i13 = i12 + 1;
        ConstraintAnchor[] constraintAnchorArr = constraintWidget.A;
        constraintAnchorArr[i12].f1276a.f1380f = constraintWidget.D.f1314s.f1276a;
        constraintAnchorArr[i12].f1276a.f1381g = (float) i11;
        constraintAnchorArr[i12].f1276a.f10635b = 1;
        constraintAnchorArr[i13].f1276a.f1380f = constraintAnchorArr[i12].f1276a;
        constraintAnchorArr[i13].f1276a.f1381g = (float) constraintWidget.j(i10);
        constraintWidget.A[i13].f1276a.f10635b = 1;
    }
}
