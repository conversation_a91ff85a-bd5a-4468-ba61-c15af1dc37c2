package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: SubMenuBuilder */
public class k extends d implements SubMenu {
    public f A;

    /* renamed from: z  reason: collision with root package name */
    public d f690z;

    public k(Context context, d dVar, f fVar) {
        super(context);
        this.f690z = dVar;
        this.A = fVar;
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean d(f fVar) {
        return this.f690z.d(fVar);
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean e(@NonNull d dVar, @NonNull MenuItem menuItem) {
        return super.e(dVar, menuItem) || this.f690z.e(dVar, menuItem);
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean f(f fVar) {
        return this.f690z.f(fVar);
    }

    public MenuItem getItem() {
        return this.A;
    }

    @Override // androidx.appcompat.view.menu.d
    public String j() {
        f fVar = this.A;
        int i10 = fVar != null ? fVar.f630a : 0;
        if (i10 == 0) {
            return null;
        }
        return "android:menu:actionviewstates" + ":" + i10;
    }

    @Override // androidx.appcompat.view.menu.d
    public d k() {
        return this.f690z.k();
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean m() {
        return this.f690z.m();
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean n() {
        return this.f690z.n();
    }

    @Override // androidx.appcompat.view.menu.d
    public boolean o() {
        return this.f690z.o();
    }

    @Override // androidx.appcompat.view.menu.d
    public void setGroupDividerEnabled(boolean z10) {
        this.f690z.setGroupDividerEnabled(z10);
    }

    @Override // android.view.SubMenu
    public SubMenu setHeaderIcon(Drawable drawable) {
        y(0, null, 0, drawable, null);
        return this;
    }

    @Override // android.view.SubMenu
    public SubMenu setHeaderTitle(CharSequence charSequence) {
        y(0, charSequence, 0, null, null);
        return this;
    }

    public SubMenu setHeaderView(View view) {
        y(0, null, 0, null, view);
        return this;
    }

    @Override // android.view.SubMenu
    public SubMenu setIcon(Drawable drawable) {
        this.A.setIcon(drawable);
        return this;
    }

    @Override // androidx.appcompat.view.menu.d
    public void setQwertyMode(boolean z10) {
        this.f690z.setQwertyMode(z10);
    }

    @Override // android.view.SubMenu
    public SubMenu setHeaderIcon(int i10) {
        y(0, null, i10, null, null);
        return this;
    }

    @Override // android.view.SubMenu
    public SubMenu setHeaderTitle(int i10) {
        y(i10, null, 0, null, null);
        return this;
    }

    @Override // android.view.SubMenu
    public SubMenu setIcon(int i10) {
        this.A.setIcon(i10);
        return this;
    }
}
