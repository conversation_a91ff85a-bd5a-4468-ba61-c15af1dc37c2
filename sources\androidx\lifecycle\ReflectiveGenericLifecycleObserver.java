package androidx.lifecycle;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.b;

/* access modifiers changed from: package-private */
public class ReflectiveGenericLifecycleObserver implements e {

    /* renamed from: a  reason: collision with root package name */
    public final Object f2051a;

    /* renamed from: b  reason: collision with root package name */
    public final b.a f2052b;

    public ReflectiveGenericLifecycleObserver(Object obj) {
        this.f2051a = obj;
        this.f2052b = b.f2059c.b(obj.getClass());
    }

    @Override // androidx.lifecycle.e
    public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
        b.a aVar = this.f2052b;
        Object obj = this.f2051a;
        b.a.a(aVar.f2062a.get(event), gVar, event, obj);
        b.a.a(aVar.f2062a.get(Lifecycle.Event.ON_ANY), gVar, event, obj);
    }
}
