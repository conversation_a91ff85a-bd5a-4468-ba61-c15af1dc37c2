package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;

/* compiled from: ScrollbarHelper */
public class a0 {
    public static int a(RecyclerView.t tVar, v vVar, View view, View view2, RecyclerView.j jVar, boolean z10) {
        if (jVar.x() == 0 || tVar.b() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z10) {
            return Math.abs(jVar.Q(view) - jVar.Q(view2)) + 1;
        }
        return Math.min(vVar.l(), vVar.b(view2) - vVar.e(view));
    }

    public static int b(RecyclerView.t tVar, v vVar, View view, View view2, RecyclerView.j jVar, boolean z10, boolean z11) {
        int i10;
        if (jVar.x() == 0 || tVar.b() == 0 || view == null || view2 == null) {
            return 0;
        }
        int min = Math.min(jVar.Q(view), jVar.Q(view2));
        int max = Math.max(jVar.Q(view), jVar.Q(view2));
        if (z11) {
            i10 = Math.max(0, (tVar.b() - max) - 1);
        } else {
            i10 = Math.max(0, min);
        }
        if (!z10) {
            return i10;
        }
        return Math.round((((float) i10) * (((float) Math.abs(vVar.b(view2) - vVar.e(view))) / ((float) (Math.abs(jVar.Q(view) - jVar.Q(view2)) + 1)))) + ((float) (vVar.k() - vVar.e(view))));
    }

    public static int c(RecyclerView.t tVar, v vVar, View view, View view2, RecyclerView.j jVar, boolean z10) {
        if (jVar.x() == 0 || tVar.b() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z10) {
            return tVar.b();
        }
        return (int) ((((float) (vVar.b(view2) - vVar.e(view))) / ((float) (Math.abs(jVar.Q(view) - jVar.Q(view2)) + 1))) * ((float) tVar.b()));
    }
}
