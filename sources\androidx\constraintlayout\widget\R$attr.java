package androidx.constraintlayout.widget;

public final class R$attr {
    public static final int barrierAllowsGoneWidgets = 2130968713;
    public static final int barrierDirection = 2130968714;
    public static final int chainUseRtl = 2130968778;
    public static final int constraintSet = 2130968877;
    public static final int constraint_referenced_ids = 2130968878;
    public static final int content = 2130968879;
    public static final int emptyVisibility = 2130968977;
    public static final int layout_constrainedHeight = 2130969136;
    public static final int layout_constrainedWidth = 2130969137;
    public static final int layout_constraintBaseline_creator = 2130969138;
    public static final int layout_constraintBaseline_toBaselineOf = 2130969139;
    public static final int layout_constraintBottom_creator = 2130969140;
    public static final int layout_constraintBottom_toBottomOf = 2130969141;
    public static final int layout_constraintBottom_toTopOf = 2130969142;
    public static final int layout_constraintCircle = 2130969143;
    public static final int layout_constraintCircleAngle = 2130969144;
    public static final int layout_constraintCircleRadius = 2130969145;
    public static final int layout_constraintDimensionRatio = 2130969146;
    public static final int layout_constraintEnd_toEndOf = 2130969147;
    public static final int layout_constraintEnd_toStartOf = 2130969148;
    public static final int layout_constraintGuide_begin = 2130969149;
    public static final int layout_constraintGuide_end = 2130969150;
    public static final int layout_constraintGuide_percent = 2130969151;
    public static final int layout_constraintHeight_default = 2130969152;
    public static final int layout_constraintHeight_max = 2130969153;
    public static final int layout_constraintHeight_min = 2130969154;
    public static final int layout_constraintHeight_percent = 2130969155;
    public static final int layout_constraintHorizontal_bias = 2130969156;
    public static final int layout_constraintHorizontal_chainStyle = 2130969157;
    public static final int layout_constraintHorizontal_weight = 2130969158;
    public static final int layout_constraintLeft_creator = 2130969159;
    public static final int layout_constraintLeft_toLeftOf = 2130969160;
    public static final int layout_constraintLeft_toRightOf = 2130969161;
    public static final int layout_constraintRight_creator = 2130969162;
    public static final int layout_constraintRight_toLeftOf = 2130969163;
    public static final int layout_constraintRight_toRightOf = 2130969164;
    public static final int layout_constraintStart_toEndOf = 2130969165;
    public static final int layout_constraintStart_toStartOf = 2130969166;
    public static final int layout_constraintTop_creator = 2130969167;
    public static final int layout_constraintTop_toBottomOf = 2130969168;
    public static final int layout_constraintTop_toTopOf = 2130969169;
    public static final int layout_constraintVertical_bias = 2130969170;
    public static final int layout_constraintVertical_chainStyle = 2130969171;
    public static final int layout_constraintVertical_weight = 2130969172;
    public static final int layout_constraintWidth_default = 2130969173;
    public static final int layout_constraintWidth_max = 2130969174;
    public static final int layout_constraintWidth_min = 2130969175;
    public static final int layout_constraintWidth_percent = 2130969176;
    public static final int layout_editor_absoluteX = 2130969178;
    public static final int layout_editor_absoluteY = 2130969179;
    public static final int layout_goneMarginBottom = 2130969180;
    public static final int layout_goneMarginEnd = 2130969181;
    public static final int layout_goneMarginLeft = 2130969182;
    public static final int layout_goneMarginRight = 2130969183;
    public static final int layout_goneMarginStart = 2130969184;
    public static final int layout_goneMarginTop = 2130969185;
    public static final int layout_optimizationLevel = 2130969188;

    private R$attr() {
    }
}
