package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.os.Trace;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;

/* compiled from: GapWorker */
public final class n implements Runnable {

    /* renamed from: e  reason: collision with root package name */
    public static final ThreadLocal<n> f2439e = new ThreadLocal<>();

    /* renamed from: f  reason: collision with root package name */
    public static Comparator<c> f2440f = new a();

    /* renamed from: a  reason: collision with root package name */
    public ArrayList<RecyclerView> f2441a = new ArrayList<>();

    /* renamed from: b  reason: collision with root package name */
    public long f2442b;

    /* renamed from: c  reason: collision with root package name */
    public long f2443c;

    /* renamed from: d  reason: collision with root package name */
    public ArrayList<c> f2444d = new ArrayList<>();

    /* compiled from: GapWorker */
    public static class a implements Comparator<c> {
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object, java.lang.Object] */
        /* JADX WARNING: Code restructure failed: missing block: B:10:0x0017, code lost:
            if (r0 == null) goto L_0x0019;
         */
        /* JADX WARNING: Code restructure failed: missing block: B:15:0x0023, code lost:
            if (r0 != false) goto L_0x001b;
         */
        /* JADX WARNING: Code restructure failed: missing block: B:24:?, code lost:
            return -1;
         */
        @Override // java.util.Comparator
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public int compare(androidx.recyclerview.widget.n.c r7, androidx.recyclerview.widget.n.c r8) {
            /*
                r6 = this;
                androidx.recyclerview.widget.n$c r7 = (androidx.recyclerview.widget.n.c) r7
                androidx.recyclerview.widget.n$c r8 = (androidx.recyclerview.widget.n.c) r8
                androidx.recyclerview.widget.RecyclerView r0 = r7.f2452d
                r1 = 0
                r2 = 1
                if (r0 != 0) goto L_0x000c
                r3 = r2
                goto L_0x000d
            L_0x000c:
                r3 = r1
            L_0x000d:
                androidx.recyclerview.widget.RecyclerView r4 = r8.f2452d
                if (r4 != 0) goto L_0x0013
                r4 = r2
                goto L_0x0014
            L_0x0013:
                r4 = r1
            L_0x0014:
                r5 = -1
                if (r3 == r4) goto L_0x001d
                if (r0 != 0) goto L_0x001b
            L_0x0019:
                r1 = r2
                goto L_0x0037
            L_0x001b:
                r1 = r5
                goto L_0x0037
            L_0x001d:
                boolean r0 = r7.f2449a
                boolean r3 = r8.f2449a
                if (r0 == r3) goto L_0x0026
                if (r0 == 0) goto L_0x0019
                goto L_0x001b
            L_0x0026:
                int r0 = r8.f2450b
                int r2 = r7.f2450b
                int r0 = r0 - r2
                if (r0 == 0) goto L_0x002f
                r1 = r0
                goto L_0x0037
            L_0x002f:
                int r7 = r7.f2451c
                int r8 = r8.f2451c
                int r7 = r7 - r8
                if (r7 == 0) goto L_0x0037
                r1 = r7
            L_0x0037:
                return r1
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.n.a.compare(java.lang.Object, java.lang.Object):int");
        }
    }

    @SuppressLint({"VisibleForTests"})
    /* compiled from: GapWorker */
    public static class b implements RecyclerView.j.c {

        /* renamed from: a  reason: collision with root package name */
        public int f2445a;

        /* renamed from: b  reason: collision with root package name */
        public int f2446b;

        /* renamed from: c  reason: collision with root package name */
        public int[] f2447c;

        /* renamed from: d  reason: collision with root package name */
        public int f2448d;

        public void a(int i10, int i11) {
            if (i10 < 0) {
                throw new IllegalArgumentException("Layout positions must be non-negative");
            } else if (i11 >= 0) {
                int i12 = this.f2448d * 2;
                int[] iArr = this.f2447c;
                if (iArr == null) {
                    int[] iArr2 = new int[4];
                    this.f2447c = iArr2;
                    Arrays.fill(iArr2, -1);
                } else if (i12 >= iArr.length) {
                    int[] iArr3 = new int[(i12 * 2)];
                    this.f2447c = iArr3;
                    System.arraycopy(iArr, 0, iArr3, 0, iArr.length);
                }
                int[] iArr4 = this.f2447c;
                iArr4[i12] = i10;
                iArr4[i12 + 1] = i11;
                this.f2448d++;
            } else {
                throw new IllegalArgumentException("Pixel distance must be non-negative");
            }
        }

        public void b(RecyclerView recyclerView, boolean z10) {
            this.f2448d = 0;
            int[] iArr = this.f2447c;
            if (iArr != null) {
                Arrays.fill(iArr, -1);
            }
            RecyclerView.j jVar = recyclerView.f2155l;
            if (recyclerView.f2154k != null && jVar != null && jVar.f2205i) {
                if (z10) {
                    if (!recyclerView.f2144d.g()) {
                        jVar.j(recyclerView.f2154k.a(), this);
                    }
                } else if (!recyclerView.M()) {
                    jVar.i(this.f2445a, this.f2446b, recyclerView.R0, this);
                }
                int i10 = this.f2448d;
                if (i10 > jVar.f2206j) {
                    jVar.f2206j = i10;
                    jVar.f2207k = z10;
                    recyclerView.f2140b.l();
                }
            }
        }

        public boolean c(int i10) {
            if (this.f2447c != null) {
                int i11 = this.f2448d * 2;
                for (int i12 = 0; i12 < i11; i12 += 2) {
                    if (this.f2447c[i12] == i10) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    /* compiled from: GapWorker */
    public static class c {

        /* renamed from: a  reason: collision with root package name */
        public boolean f2449a;

        /* renamed from: b  reason: collision with root package name */
        public int f2450b;

        /* renamed from: c  reason: collision with root package name */
        public int f2451c;

        /* renamed from: d  reason: collision with root package name */
        public RecyclerView f2452d;

        /* renamed from: e  reason: collision with root package name */
        public int f2453e;
    }

    public void a(RecyclerView recyclerView, int i10, int i11) {
        if (recyclerView.isAttachedToWindow() && this.f2442b == 0) {
            this.f2442b = recyclerView.getNanoTime();
            recyclerView.post(this);
        }
        b bVar = recyclerView.Q0;
        bVar.f2445a = i10;
        bVar.f2446b = i11;
    }

    public void b(long j10) {
        c cVar;
        RecyclerView recyclerView;
        RecyclerView recyclerView2;
        c cVar2;
        int size = this.f2441a.size();
        int i10 = 0;
        for (int i11 = 0; i11 < size; i11++) {
            RecyclerView recyclerView3 = this.f2441a.get(i11);
            if (recyclerView3.getWindowVisibility() == 0) {
                recyclerView3.Q0.b(recyclerView3, false);
                i10 += recyclerView3.Q0.f2448d;
            }
        }
        this.f2444d.ensureCapacity(i10);
        int i12 = 0;
        for (int i13 = 0; i13 < size; i13++) {
            RecyclerView recyclerView4 = this.f2441a.get(i13);
            if (recyclerView4.getWindowVisibility() == 0) {
                b bVar = recyclerView4.Q0;
                int abs = Math.abs(bVar.f2446b) + Math.abs(bVar.f2445a);
                for (int i14 = 0; i14 < bVar.f2448d * 2; i14 += 2) {
                    if (i12 >= this.f2444d.size()) {
                        cVar2 = new c();
                        this.f2444d.add(cVar2);
                    } else {
                        cVar2 = this.f2444d.get(i12);
                    }
                    int[] iArr = bVar.f2447c;
                    int i15 = iArr[i14 + 1];
                    cVar2.f2449a = i15 <= abs;
                    cVar2.f2450b = abs;
                    cVar2.f2451c = i15;
                    cVar2.f2452d = recyclerView4;
                    cVar2.f2453e = iArr[i14];
                    i12++;
                }
            }
        }
        Collections.sort(this.f2444d, f2440f);
        int i16 = 0;
        while (i16 < this.f2444d.size() && (recyclerView = (cVar = this.f2444d.get(i16)).f2452d) != null) {
            RecyclerView.w c10 = c(recyclerView, cVar.f2453e, cVar.f2449a ? Long.MAX_VALUE : j10);
            if (!(c10 == null || c10.f2268b == null || !c10.i() || c10.j() || (recyclerView2 = c10.f2268b.get()) == null)) {
                if (recyclerView2.f2165q0 && recyclerView2.f2145e.h() != 0) {
                    recyclerView2.Z();
                }
                b bVar2 = recyclerView2.Q0;
                bVar2.b(recyclerView2, true);
                if (bVar2.f2448d != 0) {
                    try {
                        int i17 = f0.b.f6259a;
                        Trace.beginSection("RV Nested Prefetch");
                        RecyclerView.t tVar = recyclerView2.R0;
                        RecyclerView.d dVar = recyclerView2.f2154k;
                        tVar.f2249d = 1;
                        tVar.f2250e = dVar.a();
                        tVar.f2252g = false;
                        tVar.h = false;
                        tVar.f2253i = false;
                        for (int i18 = 0; i18 < bVar2.f2448d * 2; i18 += 2) {
                            c(recyclerView2, bVar2.f2447c[i18], j10);
                        }
                        Trace.endSection();
                    } catch (Throwable th) {
                        int i19 = f0.b.f6259a;
                        Trace.endSection();
                        throw th;
                    }
                }
            }
            cVar.f2449a = false;
            cVar.f2450b = 0;
            cVar.f2451c = 0;
            cVar.f2452d = null;
            cVar.f2453e = 0;
            i16++;
        }
    }

    public final RecyclerView.w c(RecyclerView recyclerView, int i10, long j10) {
        boolean z10;
        int h = recyclerView.f2145e.h();
        int i11 = 0;
        while (true) {
            if (i11 >= h) {
                z10 = false;
                break;
            }
            RecyclerView.w K = RecyclerView.K(recyclerView.f2145e.g(i11));
            if (K.f2269c == i10 && !K.j()) {
                z10 = true;
                break;
            }
            i11++;
        }
        if (z10) {
            return null;
        }
        RecyclerView.p pVar = recyclerView.f2140b;
        try {
            recyclerView.S();
            RecyclerView.w j11 = pVar.j(i10, false, j10);
            if (j11 != null) {
                if (!j11.i() || j11.j()) {
                    pVar.a(j11, false);
                } else {
                    pVar.g(j11.f2267a);
                }
            }
            return j11;
        } finally {
            recyclerView.T(false);
        }
    }

    public void run() {
        try {
            int i10 = f0.b.f6259a;
            Trace.beginSection("RV Prefetch");
            if (this.f2441a.isEmpty()) {
                this.f2442b = 0;
                Trace.endSection();
                return;
            }
            int size = this.f2441a.size();
            long j10 = 0;
            for (int i11 = 0; i11 < size; i11++) {
                RecyclerView recyclerView = this.f2441a.get(i11);
                if (recyclerView.getWindowVisibility() == 0) {
                    j10 = Math.max(recyclerView.getDrawingTime(), j10);
                }
            }
            if (j10 == 0) {
                this.f2442b = 0;
                Trace.endSection();
                return;
            }
            b(TimeUnit.MILLISECONDS.toNanos(j10) + this.f2443c);
            this.f2442b = 0;
            Trace.endSection();
        } catch (Throwable th) {
            this.f2442b = 0;
            int i12 = f0.b.f6259a;
            Trace.endSection();
            throw th;
        }
    }
}
