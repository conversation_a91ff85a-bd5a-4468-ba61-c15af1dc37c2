package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.appcompat.widget.p;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.n;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import java.util.List;

public class LinearLayoutManager extends RecyclerView.j implements RecyclerView.s.b {
    public final a A;
    public final b B;
    public int C;
    public int[] D;

    /* renamed from: p  reason: collision with root package name */
    public int f2101p;

    /* renamed from: q  reason: collision with root package name */
    public c f2102q;

    /* renamed from: r  reason: collision with root package name */
    public v f2103r;

    /* renamed from: s  reason: collision with root package name */
    public boolean f2104s;

    /* renamed from: t  reason: collision with root package name */
    public boolean f2105t;

    /* renamed from: u  reason: collision with root package name */
    public boolean f2106u;

    /* renamed from: v  reason: collision with root package name */
    public boolean f2107v;

    /* renamed from: w  reason: collision with root package name */
    public boolean f2108w;

    /* renamed from: x  reason: collision with root package name */
    public int f2109x;

    /* renamed from: y  reason: collision with root package name */
    public int f2110y;

    /* renamed from: z  reason: collision with root package name */
    public SavedState f2111z;

    @SuppressLint({"BanParcelableUsage"})
    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public static class SavedState implements Parcelable {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public int f2112a;

        /* renamed from: b  reason: collision with root package name */
        public int f2113b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f2114c;

        public static class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState() {
        }

        public boolean d() {
            return this.f2112a >= 0;
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeInt(this.f2112a);
            parcel.writeInt(this.f2113b);
            parcel.writeInt(this.f2114c ? 1 : 0);
        }

        public SavedState(Parcel parcel) {
            this.f2112a = parcel.readInt();
            this.f2113b = parcel.readInt();
            this.f2114c = parcel.readInt() != 1 ? false : true;
        }

        public SavedState(SavedState savedState) {
            this.f2112a = savedState.f2112a;
            this.f2113b = savedState.f2113b;
            this.f2114c = savedState.f2114c;
        }
    }

    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public v f2115a;

        /* renamed from: b  reason: collision with root package name */
        public int f2116b;

        /* renamed from: c  reason: collision with root package name */
        public int f2117c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f2118d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f2119e;

        public a() {
            d();
        }

        public void a() {
            int i10;
            if (this.f2118d) {
                i10 = this.f2115a.g();
            } else {
                i10 = this.f2115a.k();
            }
            this.f2117c = i10;
        }

        public void b(View view, int i10) {
            if (this.f2118d) {
                this.f2117c = this.f2115a.m() + this.f2115a.b(view);
            } else {
                this.f2117c = this.f2115a.e(view);
            }
            this.f2116b = i10;
        }

        public void c(View view, int i10) {
            int m10 = this.f2115a.m();
            if (m10 >= 0) {
                b(view, i10);
                return;
            }
            this.f2116b = i10;
            if (this.f2118d) {
                int g10 = (this.f2115a.g() - m10) - this.f2115a.b(view);
                this.f2117c = this.f2115a.g() - g10;
                if (g10 > 0) {
                    int c10 = this.f2117c - this.f2115a.c(view);
                    int k10 = this.f2115a.k();
                    int min = c10 - (Math.min(this.f2115a.e(view) - k10, 0) + k10);
                    if (min < 0) {
                        this.f2117c = Math.min(g10, -min) + this.f2117c;
                        return;
                    }
                    return;
                }
                return;
            }
            int e10 = this.f2115a.e(view);
            int k11 = e10 - this.f2115a.k();
            this.f2117c = e10;
            if (k11 > 0) {
                int g11 = (this.f2115a.g() - Math.min(0, (this.f2115a.g() - m10) - this.f2115a.b(view))) - (this.f2115a.c(view) + e10);
                if (g11 < 0) {
                    this.f2117c -= Math.min(k11, -g11);
                }
            }
        }

        public void d() {
            this.f2116b = -1;
            this.f2117c = EventResultPersister.GENERATE_NEW_ID;
            this.f2118d = false;
            this.f2119e = false;
        }

        public String toString() {
            StringBuilder a10 = f.a("AnchorInfo{mPosition=");
            a10.append(this.f2116b);
            a10.append(", mCoordinate=");
            a10.append(this.f2117c);
            a10.append(", mLayoutFromEnd=");
            a10.append(this.f2118d);
            a10.append(", mValid=");
            a10.append(this.f2119e);
            a10.append('}');
            return a10.toString();
        }
    }

    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public int f2120a;

        /* renamed from: b  reason: collision with root package name */
        public boolean f2121b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f2122c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f2123d;
    }

    public static class c {

        /* renamed from: a  reason: collision with root package name */
        public boolean f2124a = true;

        /* renamed from: b  reason: collision with root package name */
        public int f2125b;

        /* renamed from: c  reason: collision with root package name */
        public int f2126c;

        /* renamed from: d  reason: collision with root package name */
        public int f2127d;

        /* renamed from: e  reason: collision with root package name */
        public int f2128e;

        /* renamed from: f  reason: collision with root package name */
        public int f2129f;

        /* renamed from: g  reason: collision with root package name */
        public int f2130g;
        public int h = 0;

        /* renamed from: i  reason: collision with root package name */
        public int f2131i = 0;

        /* renamed from: j  reason: collision with root package name */
        public int f2132j;

        /* renamed from: k  reason: collision with root package name */
        public List<RecyclerView.w> f2133k = null;

        /* renamed from: l  reason: collision with root package name */
        public boolean f2134l;

        public void a(View view) {
            int a10;
            int size = this.f2133k.size();
            View view2 = null;
            int i10 = Reader.READ_DONE;
            for (int i11 = 0; i11 < size; i11++) {
                View view3 = this.f2133k.get(i11).f2267a;
                RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view3.getLayoutParams();
                if (view3 != view && !layoutParams.c() && (a10 = (layoutParams.a() - this.f2127d) * this.f2128e) >= 0 && a10 < i10) {
                    view2 = view3;
                    if (a10 == 0) {
                        break;
                    }
                    i10 = a10;
                }
            }
            if (view2 == null) {
                this.f2127d = -1;
            } else {
                this.f2127d = ((RecyclerView.LayoutParams) view2.getLayoutParams()).a();
            }
        }

        public boolean b(RecyclerView.t tVar) {
            int i10 = this.f2127d;
            return i10 >= 0 && i10 < tVar.b();
        }

        public View c(RecyclerView.p pVar) {
            List<RecyclerView.w> list = this.f2133k;
            if (list != null) {
                int size = list.size();
                for (int i10 = 0; i10 < size; i10++) {
                    View view = this.f2133k.get(i10).f2267a;
                    RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) view.getLayoutParams();
                    if (!layoutParams.c() && this.f2127d == layoutParams.a()) {
                        a(view);
                        return view;
                    }
                }
                return null;
            }
            View view2 = pVar.j(this.f2127d, false, Long.MAX_VALUE).f2267a;
            this.f2127d += this.f2128e;
            return view2;
        }
    }

    public LinearLayoutManager(int i10, boolean z10) {
        this.f2101p = 1;
        this.f2105t = false;
        this.f2106u = false;
        this.f2107v = false;
        this.f2108w = true;
        this.f2109x = -1;
        this.f2110y = EventResultPersister.GENERATE_NEW_ID;
        this.f2111z = null;
        this.A = new a();
        this.B = new b();
        this.C = 2;
        this.D = new int[2];
        o1(i10);
        d(null);
        if (z10 != this.f2105t) {
            this.f2105t = z10;
            w0();
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean G0() {
        boolean z10;
        if (this.f2209m == 1073741824 || this.f2208l == 1073741824) {
            return false;
        }
        int x8 = x();
        int i10 = 0;
        while (true) {
            if (i10 >= x8) {
                z10 = false;
                break;
            }
            ViewGroup.LayoutParams layoutParams = w(i10).getLayoutParams();
            if (layoutParams.width < 0 && layoutParams.height < 0) {
                z10 = true;
                break;
            }
            i10++;
        }
        return z10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void I0(RecyclerView recyclerView, RecyclerView.t tVar, int i10) {
        q qVar = new q(recyclerView.getContext());
        qVar.f2232a = i10;
        J0(qVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean K0() {
        return this.f2111z == null && this.f2104s == this.f2107v;
    }

    public void L0(@NonNull RecyclerView.t tVar, @NonNull int[] iArr) {
        int i10;
        int l10 = tVar.f2246a != -1 ? this.f2103r.l() : 0;
        if (this.f2102q.f2129f == -1) {
            i10 = 0;
        } else {
            i10 = l10;
            l10 = 0;
        }
        iArr[0] = l10;
        iArr[1] = i10;
    }

    public void M0(RecyclerView.t tVar, c cVar, RecyclerView.j.c cVar2) {
        int i10 = cVar.f2127d;
        if (i10 >= 0 && i10 < tVar.b()) {
            ((n.b) cVar2).a(i10, Math.max(0, cVar.f2130g));
        }
    }

    public final int N0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        R0();
        return a0.a(tVar, this.f2103r, V0(!this.f2108w, true), U0(!this.f2108w, true), this, this.f2108w);
    }

    public final int O0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        R0();
        return a0.b(tVar, this.f2103r, V0(!this.f2108w, true), U0(!this.f2108w, true), this, this.f2108w, this.f2106u);
    }

    public final int P0(RecyclerView.t tVar) {
        if (x() == 0) {
            return 0;
        }
        R0();
        return a0.c(tVar, this.f2103r, V0(!this.f2108w, true), U0(!this.f2108w, true), this, this.f2108w);
    }

    public int Q0(int i10) {
        if (i10 == 1) {
            return (this.f2101p != 1 && g1()) ? 1 : -1;
        }
        if (i10 == 2) {
            return (this.f2101p != 1 && g1()) ? -1 : 1;
        }
        if (i10 != 17) {
            if (i10 != 33) {
                if (i10 != 66) {
                    if (i10 != 130) {
                        return EventResultPersister.GENERATE_NEW_ID;
                    }
                    if (this.f2101p == 1) {
                        return 1;
                    }
                    return EventResultPersister.GENERATE_NEW_ID;
                } else if (this.f2101p == 0) {
                    return 1;
                } else {
                    return EventResultPersister.GENERATE_NEW_ID;
                }
            } else if (this.f2101p == 1) {
                return -1;
            } else {
                return EventResultPersister.GENERATE_NEW_ID;
            }
        } else if (this.f2101p == 0) {
            return -1;
        } else {
            return EventResultPersister.GENERATE_NEW_ID;
        }
    }

    public void R0() {
        if (this.f2102q == null) {
            this.f2102q = new c();
        }
    }

    public int S0(RecyclerView.p pVar, c cVar, RecyclerView.t tVar, boolean z10) {
        int i10 = cVar.f2126c;
        int i11 = cVar.f2130g;
        if (i11 != Integer.MIN_VALUE) {
            if (i10 < 0) {
                cVar.f2130g = i11 + i10;
            }
            j1(pVar, cVar);
        }
        int i12 = cVar.f2126c + cVar.h;
        b bVar = this.B;
        while (true) {
            if ((!cVar.f2134l && i12 <= 0) || !cVar.b(tVar)) {
                break;
            }
            bVar.f2120a = 0;
            bVar.f2121b = false;
            bVar.f2122c = false;
            bVar.f2123d = false;
            h1(pVar, tVar, cVar, bVar);
            if (!bVar.f2121b) {
                int i13 = cVar.f2125b;
                int i14 = bVar.f2120a;
                cVar.f2125b = (cVar.f2129f * i14) + i13;
                if (!bVar.f2122c || cVar.f2133k != null || !tVar.f2252g) {
                    cVar.f2126c -= i14;
                    i12 -= i14;
                }
                int i15 = cVar.f2130g;
                if (i15 != Integer.MIN_VALUE) {
                    int i16 = i15 + i14;
                    cVar.f2130g = i16;
                    int i17 = cVar.f2126c;
                    if (i17 < 0) {
                        cVar.f2130g = i16 + i17;
                    }
                    j1(pVar, cVar);
                }
                if (z10 && bVar.f2123d) {
                    break;
                }
            } else {
                break;
            }
        }
        return i10 - cVar.f2126c;
    }

    public final View T0(RecyclerView.p pVar, RecyclerView.t tVar) {
        return b1(pVar, tVar, 0, x(), tVar.b());
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean U() {
        return true;
    }

    public View U0(boolean z10, boolean z11) {
        if (this.f2106u) {
            return a1(0, x(), z10, z11);
        }
        return a1(x() - 1, -1, z10, z11);
    }

    public View V0(boolean z10, boolean z11) {
        if (this.f2106u) {
            return a1(x() - 1, -1, z10, z11);
        }
        return a1(0, x(), z10, z11);
    }

    public int W0() {
        View a12 = a1(0, x(), false, true);
        if (a12 == null) {
            return -1;
        }
        return Q(a12);
    }

    public final View X0(RecyclerView.p pVar, RecyclerView.t tVar) {
        return b1(pVar, tVar, x() - 1, -1, tVar.b());
    }

    public int Y0() {
        View a12 = a1(x() - 1, -1, false, true);
        if (a12 == null) {
            return -1;
        }
        return Q(a12);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void Z(RecyclerView recyclerView, RecyclerView.p pVar) {
    }

    public View Z0(int i10, int i11) {
        int i12;
        int i13;
        R0();
        if ((i11 > i10 ? 1 : i11 < i10 ? (char) 65535 : 0) == 0) {
            return w(i10);
        }
        if (this.f2103r.e(w(i10)) < this.f2103r.k()) {
            i13 = 16644;
            i12 = 16388;
        } else {
            i13 = 4161;
            i12 = 4097;
        }
        if (this.f2101p == 0) {
            return this.f2200c.a(i10, i11, i13, i12);
        }
        return this.f2201d.a(i10, i11, i13, i12);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.s.b
    public PointF a(int i10) {
        if (x() == 0) {
            return null;
        }
        boolean z10 = false;
        int i11 = 1;
        if (i10 < Q(w(0))) {
            z10 = true;
        }
        if (z10 != this.f2106u) {
            i11 = -1;
        }
        if (this.f2101p == 0) {
            return new PointF((float) i11, Constant.VOLUME_FLOAT_MIN);
        }
        return new PointF(Constant.VOLUME_FLOAT_MIN, (float) i11);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public View a0(View view, int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        int Q0;
        View view2;
        View view3;
        m1();
        if (x() == 0 || (Q0 = Q0(i10)) == Integer.MIN_VALUE) {
            return null;
        }
        R0();
        q1(Q0, (int) (((float) this.f2103r.l()) * 0.33333334f), false, tVar);
        c cVar = this.f2102q;
        cVar.f2130g = EventResultPersister.GENERATE_NEW_ID;
        cVar.f2124a = false;
        S0(pVar, cVar, tVar, true);
        if (Q0 == -1) {
            if (this.f2106u) {
                view2 = Z0(x() - 1, -1);
            } else {
                view2 = Z0(0, x());
            }
        } else if (this.f2106u) {
            view2 = Z0(0, x());
        } else {
            view2 = Z0(x() - 1, -1);
        }
        if (Q0 == -1) {
            view3 = f1();
        } else {
            view3 = e1();
        }
        if (!view3.hasFocusable()) {
            return view2;
        }
        if (view2 == null) {
            return null;
        }
        return view3;
    }

    public View a1(int i10, int i11, boolean z10, boolean z11) {
        R0();
        int i12 = 320;
        int i13 = z10 ? 24579 : 320;
        if (!z11) {
            i12 = 0;
        }
        if (this.f2101p == 0) {
            return this.f2200c.a(i10, i11, i13, i12);
        }
        return this.f2201d.a(i10, i11, i13, i12);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void b0(AccessibilityEvent accessibilityEvent) {
        super.b0(accessibilityEvent);
        if (x() > 0) {
            accessibilityEvent.setFromIndex(W0());
            accessibilityEvent.setToIndex(Y0());
        }
    }

    public View b1(RecyclerView.p pVar, RecyclerView.t tVar, int i10, int i11, int i12) {
        R0();
        int k10 = this.f2103r.k();
        int g10 = this.f2103r.g();
        int i13 = i11 > i10 ? 1 : -1;
        View view = null;
        View view2 = null;
        while (i10 != i11) {
            View w10 = w(i10);
            int Q = Q(w10);
            if (Q >= 0 && Q < i12) {
                if (((RecyclerView.LayoutParams) w10.getLayoutParams()).c()) {
                    if (view2 == null) {
                        view2 = w10;
                    }
                } else if (this.f2103r.e(w10) < g10 && this.f2103r.b(w10) >= k10) {
                    return w10;
                } else {
                    if (view == null) {
                        view = w10;
                    }
                }
            }
            i10 += i13;
        }
        return view != null ? view : view2;
    }

    public final int c1(int i10, RecyclerView.p pVar, RecyclerView.t tVar, boolean z10) {
        int g10;
        int g11 = this.f2103r.g() - i10;
        if (g11 <= 0) {
            return 0;
        }
        int i11 = -n1(-g11, pVar, tVar);
        int i12 = i10 + i11;
        if (!z10 || (g10 = this.f2103r.g() - i12) <= 0) {
            return i11;
        }
        this.f2103r.p(g10);
        return g10 + i11;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void d(String str) {
        RecyclerView recyclerView;
        if (this.f2111z == null && (recyclerView = this.f2199b) != null) {
            recyclerView.i(str);
        }
    }

    public final int d1(int i10, RecyclerView.p pVar, RecyclerView.t tVar, boolean z10) {
        int k10;
        int k11 = i10 - this.f2103r.k();
        if (k11 <= 0) {
            return 0;
        }
        int i11 = -n1(k11, pVar, tVar);
        int i12 = i10 + i11;
        if (!z10 || (k10 = i12 - this.f2103r.k()) <= 0) {
            return i11;
        }
        this.f2103r.p(-k10);
        return i11 - k10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean e() {
        return this.f2101p == 0;
    }

    public final View e1() {
        return w(this.f2106u ? 0 : x() - 1);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public boolean f() {
        return this.f2101p == 1;
    }

    public final View f1() {
        return w(this.f2106u ? x() - 1 : 0);
    }

    public boolean g1() {
        return J() == 1;
    }

    public void h1(RecyclerView.p pVar, RecyclerView.t tVar, c cVar, b bVar) {
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        View c10 = cVar.c(pVar);
        if (c10 == null) {
            bVar.f2121b = true;
            return;
        }
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) c10.getLayoutParams();
        if (cVar.f2133k == null) {
            if (this.f2106u == (cVar.f2129f == -1)) {
                c(c10, -1, false);
            } else {
                c(c10, 0, false);
            }
        } else {
            if (this.f2106u == (cVar.f2129f == -1)) {
                c(c10, -1, true);
            } else {
                c(c10, 0, true);
            }
        }
        RecyclerView.LayoutParams layoutParams2 = (RecyclerView.LayoutParams) c10.getLayoutParams();
        Rect L = this.f2199b.L(c10);
        int y10 = RecyclerView.j.y(this.f2210n, this.f2208l, O() + N() + ((ViewGroup.MarginLayoutParams) layoutParams2).leftMargin + ((ViewGroup.MarginLayoutParams) layoutParams2).rightMargin + L.left + L.right + 0, ((ViewGroup.MarginLayoutParams) layoutParams2).width, e());
        int y11 = RecyclerView.j.y(this.f2211o, this.f2209m, M() + P() + ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin + ((ViewGroup.MarginLayoutParams) layoutParams2).bottomMargin + L.top + L.bottom + 0, ((ViewGroup.MarginLayoutParams) layoutParams2).height, f());
        if (F0(c10, y10, y11, layoutParams2)) {
            c10.measure(y10, y11);
        }
        bVar.f2120a = this.f2103r.c(c10);
        if (this.f2101p == 1) {
            if (g1()) {
                i14 = this.f2210n - O();
                i13 = i14 - this.f2103r.d(c10);
            } else {
                i13 = N();
                i14 = this.f2103r.d(c10) + i13;
            }
            if (cVar.f2129f == -1) {
                int i15 = cVar.f2125b;
                i10 = i15;
                i11 = i14;
                i12 = i15 - bVar.f2120a;
            } else {
                int i16 = cVar.f2125b;
                i12 = i16;
                i11 = i14;
                i10 = bVar.f2120a + i16;
            }
        } else {
            int P = P();
            int d10 = this.f2103r.d(c10) + P;
            if (cVar.f2129f == -1) {
                int i17 = cVar.f2125b;
                i11 = i17;
                i12 = P;
                i10 = d10;
                i13 = i17 - bVar.f2120a;
            } else {
                int i18 = cVar.f2125b;
                i12 = P;
                i11 = bVar.f2120a + i18;
                i10 = d10;
                i13 = i18;
            }
        }
        W(c10, i13, i12, i11, i10);
        if (layoutParams.c() || layoutParams.b()) {
            bVar.f2122c = true;
        }
        bVar.f2123d = c10.hasFocusable();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void i(int i10, int i11, RecyclerView.t tVar, RecyclerView.j.c cVar) {
        if (this.f2101p != 0) {
            i10 = i11;
        }
        if (x() != 0 && i10 != 0) {
            R0();
            q1(i10 > 0 ? 1 : -1, Math.abs(i10), true, tVar);
            M0(tVar, this.f2102q, cVar);
        }
    }

    public void i1(RecyclerView.p pVar, RecyclerView.t tVar, a aVar, int i10) {
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void j(int i10, RecyclerView.j.c cVar) {
        boolean z10;
        int i11;
        SavedState savedState = this.f2111z;
        int i12 = -1;
        if (savedState == null || !savedState.d()) {
            m1();
            z10 = this.f2106u;
            i11 = this.f2109x;
            if (i11 == -1) {
                i11 = z10 ? i10 - 1 : 0;
            }
        } else {
            SavedState savedState2 = this.f2111z;
            z10 = savedState2.f2114c;
            i11 = savedState2.f2112a;
        }
        if (!z10) {
            i12 = 1;
        }
        for (int i13 = 0; i13 < this.C && i11 >= 0 && i11 < i10; i13++) {
            ((n.b) cVar).a(i11, 0);
            i11 += i12;
        }
    }

    public final void j1(RecyclerView.p pVar, c cVar) {
        if (cVar.f2124a && !cVar.f2134l) {
            int i10 = cVar.f2130g;
            int i11 = cVar.f2131i;
            if (cVar.f2129f == -1) {
                int x8 = x();
                if (i10 >= 0) {
                    int f10 = (this.f2103r.f() - i10) + i11;
                    if (this.f2106u) {
                        for (int i12 = 0; i12 < x8; i12++) {
                            View w10 = w(i12);
                            if (this.f2103r.e(w10) < f10 || this.f2103r.o(w10) < f10) {
                                k1(pVar, 0, i12);
                                return;
                            }
                        }
                        return;
                    }
                    int i13 = x8 - 1;
                    for (int i14 = i13; i14 >= 0; i14--) {
                        View w11 = w(i14);
                        if (this.f2103r.e(w11) < f10 || this.f2103r.o(w11) < f10) {
                            k1(pVar, i13, i14);
                            return;
                        }
                    }
                }
            } else if (i10 >= 0) {
                int i15 = i10 - i11;
                int x10 = x();
                if (this.f2106u) {
                    int i16 = x10 - 1;
                    for (int i17 = i16; i17 >= 0; i17--) {
                        View w12 = w(i17);
                        if (this.f2103r.b(w12) > i15 || this.f2103r.n(w12) > i15) {
                            k1(pVar, i16, i17);
                            return;
                        }
                    }
                    return;
                }
                for (int i18 = 0; i18 < x10; i18++) {
                    View w13 = w(i18);
                    if (this.f2103r.b(w13) > i15 || this.f2103r.n(w13) > i15) {
                        k1(pVar, 0, i18);
                        return;
                    }
                }
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int k(RecyclerView.t tVar) {
        return N0(tVar);
    }

    /* JADX WARNING: Removed duplicated region for block: B:124:0x022d  */
    /* JADX WARNING: Removed duplicated region for block: B:78:0x0180  */
    @Override // androidx.recyclerview.widget.RecyclerView.j
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void k0(androidx.recyclerview.widget.RecyclerView.p r17, androidx.recyclerview.widget.RecyclerView.t r18) {
        /*
        // Method dump skipped, instructions count: 1091
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.LinearLayoutManager.k0(androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t):void");
    }

    public final void k1(RecyclerView.p pVar, int i10, int i11) {
        if (i10 != i11) {
            if (i11 > i10) {
                for (int i12 = i11 - 1; i12 >= i10; i12--) {
                    t0(i12, pVar);
                }
                return;
            }
            while (i10 > i11) {
                t0(i10, pVar);
                i10--;
            }
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int l(RecyclerView.t tVar) {
        return O0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void l0(RecyclerView.t tVar) {
        this.f2111z = null;
        this.f2109x = -1;
        this.f2110y = EventResultPersister.GENERATE_NEW_ID;
        this.A.d();
    }

    public boolean l1() {
        return this.f2103r.i() == 0 && this.f2103r.f() == 0;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int m(RecyclerView.t tVar) {
        return P0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void m0(Parcelable parcelable) {
        if (parcelable instanceof SavedState) {
            this.f2111z = (SavedState) parcelable;
            w0();
        }
    }

    public final void m1() {
        if (this.f2101p == 1 || !g1()) {
            this.f2106u = this.f2105t;
        } else {
            this.f2106u = !this.f2105t;
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int n(RecyclerView.t tVar) {
        return N0(tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public Parcelable n0() {
        SavedState savedState = this.f2111z;
        if (savedState != null) {
            return new SavedState(savedState);
        }
        SavedState savedState2 = new SavedState();
        if (x() > 0) {
            R0();
            boolean z10 = this.f2104s ^ this.f2106u;
            savedState2.f2114c = z10;
            if (z10) {
                View e12 = e1();
                savedState2.f2113b = this.f2103r.g() - this.f2103r.b(e12);
                savedState2.f2112a = Q(e12);
            } else {
                View f12 = f1();
                savedState2.f2112a = Q(f12);
                savedState2.f2113b = this.f2103r.e(f12) - this.f2103r.k();
            }
        } else {
            savedState2.f2112a = -1;
        }
        return savedState2;
    }

    public int n1(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        if (x() == 0 || i10 == 0) {
            return 0;
        }
        R0();
        this.f2102q.f2124a = true;
        int i11 = i10 > 0 ? 1 : -1;
        int abs = Math.abs(i10);
        q1(i11, abs, true, tVar);
        c cVar = this.f2102q;
        int S0 = S0(pVar, cVar, tVar, false) + cVar.f2130g;
        if (S0 < 0) {
            return 0;
        }
        if (abs > S0) {
            i10 = i11 * S0;
        }
        this.f2103r.p(-i10);
        this.f2102q.f2132j = i10;
        return i10;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int o(RecyclerView.t tVar) {
        return O0(tVar);
    }

    public void o1(int i10) {
        if (i10 == 0 || i10 == 1) {
            d(null);
            if (i10 != this.f2101p || this.f2103r == null) {
                v a10 = v.a(this, i10);
                this.f2103r = a10;
                this.A.f2115a = a10;
                this.f2101p = i10;
                w0();
                return;
            }
            return;
        }
        throw new IllegalArgumentException(p.a("invalid orientation:", i10));
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int p(RecyclerView.t tVar) {
        return P0(tVar);
    }

    public void p1(boolean z10) {
        d(null);
        if (this.f2107v != z10) {
            this.f2107v = z10;
            w0();
        }
    }

    public final void q1(int i10, int i11, boolean z10, RecyclerView.t tVar) {
        int i12;
        this.f2102q.f2134l = l1();
        this.f2102q.f2129f = i10;
        int[] iArr = this.D;
        boolean z11 = false;
        iArr[0] = 0;
        int i13 = 1;
        iArr[1] = 0;
        L0(tVar, iArr);
        int max = Math.max(0, this.D[0]);
        int max2 = Math.max(0, this.D[1]);
        if (i10 == 1) {
            z11 = true;
        }
        c cVar = this.f2102q;
        int i14 = z11 ? max2 : max;
        cVar.h = i14;
        if (!z11) {
            max = max2;
        }
        cVar.f2131i = max;
        if (z11) {
            cVar.h = this.f2103r.h() + i14;
            View e12 = e1();
            c cVar2 = this.f2102q;
            if (this.f2106u) {
                i13 = -1;
            }
            cVar2.f2128e = i13;
            int Q = Q(e12);
            c cVar3 = this.f2102q;
            cVar2.f2127d = Q + cVar3.f2128e;
            cVar3.f2125b = this.f2103r.b(e12);
            i12 = this.f2103r.b(e12) - this.f2103r.g();
        } else {
            View f12 = f1();
            c cVar4 = this.f2102q;
            cVar4.h = this.f2103r.k() + cVar4.h;
            c cVar5 = this.f2102q;
            if (!this.f2106u) {
                i13 = -1;
            }
            cVar5.f2128e = i13;
            int Q2 = Q(f12);
            c cVar6 = this.f2102q;
            cVar5.f2127d = Q2 + cVar6.f2128e;
            cVar6.f2125b = this.f2103r.e(f12);
            i12 = (-this.f2103r.e(f12)) + this.f2103r.k();
        }
        c cVar7 = this.f2102q;
        cVar7.f2126c = i11;
        if (z10) {
            cVar7.f2126c = i11 - i12;
        }
        cVar7.f2130g = i12;
    }

    public final void r1(int i10, int i11) {
        this.f2102q.f2126c = this.f2103r.g() - i11;
        c cVar = this.f2102q;
        cVar.f2128e = this.f2106u ? -1 : 1;
        cVar.f2127d = i10;
        cVar.f2129f = 1;
        cVar.f2125b = i11;
        cVar.f2130g = EventResultPersister.GENERATE_NEW_ID;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public View s(int i10) {
        int x8 = x();
        if (x8 == 0) {
            return null;
        }
        int Q = i10 - Q(w(0));
        if (Q >= 0 && Q < x8) {
            View w10 = w(Q);
            if (Q(w10) == i10) {
                return w10;
            }
        }
        return super.s(i10);
    }

    public final void s1(int i10, int i11) {
        this.f2102q.f2126c = i11 - this.f2103r.k();
        c cVar = this.f2102q;
        cVar.f2127d = i10;
        cVar.f2128e = this.f2106u ? 1 : -1;
        cVar.f2129f = -1;
        cVar.f2125b = i11;
        cVar.f2130g = EventResultPersister.GENERATE_NEW_ID;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public RecyclerView.LayoutParams t() {
        return new RecyclerView.LayoutParams(-2, -2);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int x0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2101p == 1) {
            return 0;
        }
        return n1(i10, pVar, tVar);
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public void y0(int i10) {
        this.f2109x = i10;
        this.f2110y = EventResultPersister.GENERATE_NEW_ID;
        SavedState savedState = this.f2111z;
        if (savedState != null) {
            savedState.f2112a = -1;
        }
        w0();
    }

    @Override // androidx.recyclerview.widget.RecyclerView.j
    public int z0(int i10, RecyclerView.p pVar, RecyclerView.t tVar) {
        if (this.f2101p == 0) {
            return 0;
        }
        return n1(i10, pVar, tVar);
    }

    public LinearLayoutManager(Context context, AttributeSet attributeSet, int i10, int i11) {
        this.f2101p = 1;
        this.f2105t = false;
        this.f2106u = false;
        this.f2107v = false;
        this.f2108w = true;
        this.f2109x = -1;
        this.f2110y = EventResultPersister.GENERATE_NEW_ID;
        this.f2111z = null;
        this.A = new a();
        this.B = new b();
        this.C = 2;
        this.D = new int[2];
        RecyclerView.j.d R = RecyclerView.j.R(context, attributeSet, i10, i11);
        o1(R.f2214a);
        boolean z10 = R.f2216c;
        d(null);
        if (z10 != this.f2105t) {
            this.f2105t = z10;
            w0();
        }
        p1(R.f2217d);
    }
}
