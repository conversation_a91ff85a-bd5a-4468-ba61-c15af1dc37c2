package androidx.appcompat.widget;

import android.view.Menu;
import android.view.Window;
import androidx.annotation.RestrictTo;
import androidx.appcompat.view.menu.h;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: DecorContentParent */
public interface s {
    void a(Menu menu, h.a aVar);

    boolean b();

    void c();

    boolean d();

    boolean e();

    boolean f();

    boolean g();

    void k(int i10);

    void l();

    void setWindowCallback(Window.Callback callback);

    void setWindowTitle(CharSequence charSequence);
}
