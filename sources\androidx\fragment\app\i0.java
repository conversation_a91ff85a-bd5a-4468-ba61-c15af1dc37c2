package androidx.fragment.app;

import android.graphics.Rect;
import android.view.View;
import u.a;

/* compiled from: FragmentTransition */
public class i0 implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ Fragment f1916a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Fragment f1917b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ boolean f1918c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ a f1919d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ View f1920e;

    /* renamed from: f  reason: collision with root package name */
    public final /* synthetic */ m0 f1921f;

    /* renamed from: g  reason: collision with root package name */
    public final /* synthetic */ Rect f1922g;

    public i0(Fragment fragment, Fragment fragment2, boolean z10, a aVar, View view, m0 m0Var, Rect rect) {
        this.f1916a = fragment;
        this.f1917b = fragment2;
        this.f1918c = z10;
        this.f1919d = aVar;
        this.f1920e = view;
        this.f1921f = m0Var;
        this.f1922g = rect;
    }

    public void run() {
        k0.c(this.f1916a, this.f1917b, this.f1918c, this.f1919d, false);
        View view = this.f1920e;
        if (view != null) {
            this.f1921f.j(view, this.f1922g);
        }
    }
}
