package androidx.constraintlayout.widget;

public final class R$id {
    public static final int bottom = 2131361883;
    public static final int end = 2131361950;
    public static final int gone = 2131361969;
    public static final int invisible = 2131361998;
    public static final int left = 2131362005;
    public static final int packed = 2131362103;
    public static final int parent = 2131362105;
    public static final int percent = 2131362115;
    public static final int right = 2131362143;
    public static final int spread = 2131362199;
    public static final int spread_inside = 2131362200;
    public static final int start = 2131362205;
    public static final int top = 2131362250;
    public static final int wrap = 2131362297;

    private R$id() {
    }
}
