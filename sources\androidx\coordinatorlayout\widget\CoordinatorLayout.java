package androidx.coordinatorlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import androidx.annotation.AttrRes;
import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.VisibleForTesting;
import androidx.coordinatorlayout.R$attr;
import androidx.coordinatorlayout.R$style;
import androidx.coordinatorlayout.R$styleable;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import com.duokan.airkan.common.Constant;
import j0.g;
import j0.h;
import j0.i;
import j0.j;
import j0.m;
import j0.q;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;

public class CoordinatorLayout extends ViewGroup implements g, h {

    /* renamed from: m0  reason: collision with root package name */
    public static final Comparator<View> f1520m0 = new f();

    /* renamed from: n0  reason: collision with root package name */
    public static final i0.c f1521n0 = new i0.d(12);

    /* renamed from: t  reason: collision with root package name */
    public static final String f1522t;

    /* renamed from: x  reason: collision with root package name */
    public static final Class<?>[] f1523x = {Context.class, AttributeSet.class};

    /* renamed from: y  reason: collision with root package name */
    public static final ThreadLocal<Map<String, Constructor<Behavior>>> f1524y = new ThreadLocal<>();

    /* renamed from: a  reason: collision with root package name */
    public final List<View> f1525a;

    /* renamed from: b  reason: collision with root package name */
    public final x.a<View> f1526b;

    /* renamed from: c  reason: collision with root package name */
    public final List<View> f1527c;

    /* renamed from: d  reason: collision with root package name */
    public final List<View> f1528d;

    /* renamed from: e  reason: collision with root package name */
    public final int[] f1529e;

    /* renamed from: f  reason: collision with root package name */
    public final int[] f1530f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f1531g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public int[] f1532i;

    /* renamed from: j  reason: collision with root package name */
    public View f1533j;

    /* renamed from: k  reason: collision with root package name */
    public View f1534k;

    /* renamed from: l  reason: collision with root package name */
    public e f1535l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1536m;

    /* renamed from: n  reason: collision with root package name */
    public q f1537n;

    /* renamed from: o  reason: collision with root package name */
    public boolean f1538o;

    /* renamed from: p  reason: collision with root package name */
    public Drawable f1539p;

    /* renamed from: q  reason: collision with root package name */
    public ViewGroup.OnHierarchyChangeListener f1540q;

    /* renamed from: r  reason: collision with root package name */
    public j f1541r;

    /* renamed from: s  reason: collision with root package name */
    public final i f1542s;

    public static abstract class Behavior<V extends View> {
        public Behavior() {
        }

        public boolean a(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull Rect rect) {
            return false;
        }

        public boolean b(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view) {
            return false;
        }

        public void c(@NonNull d dVar) {
        }

        public boolean d(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view) {
            return false;
        }

        public void e(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view) {
        }

        public void f() {
        }

        public boolean g(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull MotionEvent motionEvent) {
            return false;
        }

        public boolean h(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, int i10) {
            return false;
        }

        public boolean i(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, int i10, int i11, int i12, int i13) {
            return false;
        }

        public boolean j(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view, float f10, float f11) {
            return false;
        }

        public void k(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view, int i10, int i11, @NonNull int[] iArr, int i12) {
        }

        public void l(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view, int i10, int i11, int i12, int i13, int i14, @NonNull int[] iArr) {
            iArr[0] = iArr[0] + i12;
            iArr[1] = iArr[1] + i13;
        }

        public boolean m(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull Rect rect, boolean z10) {
            return false;
        }

        public void n(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull Parcelable parcelable) {
        }

        @Nullable
        public Parcelable o(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10) {
            return View.BaseSavedState.EMPTY_STATE;
        }

        public boolean p(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view, @NonNull View view2, int i10, int i11) {
            return false;
        }

        public void q(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull View view, int i10) {
        }

        public boolean r(@NonNull CoordinatorLayout coordinatorLayout, @NonNull V v10, @NonNull MotionEvent motionEvent) {
            return false;
        }

        public Behavior(Context context, AttributeSet attributeSet) {
        }
    }

    @Deprecated
    @Retention(RetentionPolicy.RUNTIME)
    public @interface DefaultBehavior {
        Class<? extends Behavior> value();
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface DispatchChangeEvent {
    }

    public class a implements j {
        public a() {
        }

        @Override // j0.j
        public q a(View view, q qVar) {
            CoordinatorLayout coordinatorLayout = CoordinatorLayout.this;
            if (!Objects.equals(coordinatorLayout.f1537n, qVar)) {
                coordinatorLayout.f1537n = qVar;
                boolean z10 = true;
                boolean z11 = qVar.e() > 0;
                coordinatorLayout.f1538o = z11;
                if (z11 || coordinatorLayout.getBackground() != null) {
                    z10 = false;
                }
                coordinatorLayout.setWillNotDraw(z10);
                if (!qVar.h()) {
                    int childCount = coordinatorLayout.getChildCount();
                    for (int i10 = 0; i10 < childCount; i10++) {
                        View childAt = coordinatorLayout.getChildAt(i10);
                        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                        if (childAt.getFitsSystemWindows() && ((d) childAt.getLayoutParams()).f1546a != null && qVar.h()) {
                            break;
                        }
                    }
                }
                coordinatorLayout.requestLayout();
            }
            return qVar;
        }
    }

    public interface b {
        @NonNull
        Behavior getBehavior();
    }

    public class c implements ViewGroup.OnHierarchyChangeListener {
        public c() {
        }

        public void onChildViewAdded(View view, View view2) {
            ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener = CoordinatorLayout.this.f1540q;
            if (onHierarchyChangeListener != null) {
                onHierarchyChangeListener.onChildViewAdded(view, view2);
            }
        }

        public void onChildViewRemoved(View view, View view2) {
            CoordinatorLayout.this.v(2);
            ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener = CoordinatorLayout.this.f1540q;
            if (onHierarchyChangeListener != null) {
                onHierarchyChangeListener.onChildViewRemoved(view, view2);
            }
        }
    }

    public class e implements ViewTreeObserver.OnPreDrawListener {
        public e() {
        }

        public boolean onPreDraw() {
            CoordinatorLayout.this.v(0);
            return true;
        }
    }

    public static class f implements Comparator<View> {
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object, java.lang.Object] */
        @Override // java.util.Comparator
        public int compare(View view, View view2) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            float z10 = view.getZ();
            float z11 = view2.getZ();
            if (z10 > z11) {
                return -1;
            }
            return z10 < z11 ? 1 : 0;
        }
    }

    static {
        Package r02 = CoordinatorLayout.class.getPackage();
        f1522t = r02 != null ? r02.getName() : null;
    }

    public CoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.coordinatorLayoutStyle);
    }

    @NonNull
    public static Rect e() {
        Rect rect = (Rect) f1521n0.a();
        return rect == null ? new Rect() : rect;
    }

    public final void A(boolean z10) {
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            Behavior behavior = ((d) childAt.getLayoutParams()).f1546a;
            if (behavior != null) {
                long uptimeMillis = SystemClock.uptimeMillis();
                MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0);
                if (z10) {
                    behavior.g(this, childAt, obtain);
                } else {
                    behavior.r(this, childAt, obtain);
                }
                obtain.recycle();
            }
        }
        for (int i11 = 0; i11 < childCount; i11++) {
            ((d) getChildAt(i11).getLayoutParams()).f1557m = false;
        }
        this.f1533j = null;
        this.f1531g = false;
    }

    public final void B(View view, int i10) {
        d dVar = (d) view.getLayoutParams();
        int i11 = dVar.f1553i;
        if (i11 != i10) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            view.offsetLeftAndRight(i10 - i11);
            dVar.f1553i = i10;
        }
    }

    public final void C(View view, int i10) {
        d dVar = (d) view.getLayoutParams();
        int i11 = dVar.f1554j;
        if (i11 != i10) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            view.offsetTopAndBottom(i10 - i11);
            dVar.f1554j = i10;
        }
    }

    public final void D() {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        if (getFitsSystemWindows()) {
            if (this.f1541r == null) {
                this.f1541r = new a();
            }
            ViewCompat.l(this, this.f1541r);
            setSystemUiVisibility(1280);
            return;
        }
        ViewCompat.l(this, null);
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof d) && super.checkLayoutParams(layoutParams);
    }

    public boolean drawChild(Canvas canvas, View view, long j10) {
        Behavior behavior = ((d) view.getLayoutParams()).f1546a;
        if (behavior != null) {
            Objects.requireNonNull(behavior);
        }
        return super.drawChild(canvas, view, j10);
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        int[] drawableState = getDrawableState();
        Drawable drawable = this.f1539p;
        boolean z10 = false;
        if (drawable != null && drawable.isStateful()) {
            z10 = false | drawable.setState(drawableState);
        }
        if (z10) {
            invalidate();
        }
    }

    public final void f(d dVar, Rect rect, int i10, int i11) {
        int width = getWidth();
        int height = getHeight();
        int max = Math.max(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) dVar).leftMargin, Math.min(rect.left, ((width - getPaddingRight()) - i10) - ((ViewGroup.MarginLayoutParams) dVar).rightMargin));
        int max2 = Math.max(getPaddingTop() + ((ViewGroup.MarginLayoutParams) dVar).topMargin, Math.min(rect.top, ((height - getPaddingBottom()) - i11) - ((ViewGroup.MarginLayoutParams) dVar).bottomMargin));
        rect.set(max, max2, i10 + max, i11 + max2);
    }

    public void g(@NonNull View view) {
        ArrayList<T> orDefault = this.f1526b.f10703b.getOrDefault(view, null);
        if (!(orDefault == null || orDefault.isEmpty())) {
            for (int i10 = 0; i10 < orDefault.size(); i10++) {
                T t2 = orDefault.get(i10);
                Behavior behavior = ((d) t2.getLayoutParams()).f1546a;
                if (behavior != null) {
                    behavior.d(this, t2, view);
                }
            }
        }
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new d(-2, -2);
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new d(getContext(), attributeSet);
    }

    @VisibleForTesting
    public final List<View> getDependencySortedChildren() {
        z();
        return Collections.unmodifiableList(this.f1525a);
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public final q getLastWindowInsets() {
        return this.f1537n;
    }

    public int getNestedScrollAxes() {
        return this.f1542s.a();
    }

    @Nullable
    public Drawable getStatusBarBackground() {
        return this.f1539p;
    }

    public int getSuggestedMinimumHeight() {
        return Math.max(super.getSuggestedMinimumHeight(), getPaddingBottom() + getPaddingTop());
    }

    public int getSuggestedMinimumWidth() {
        return Math.max(super.getSuggestedMinimumWidth(), getPaddingRight() + getPaddingLeft());
    }

    @Override // j0.g
    public void h(View view, View view2, int i10, int i11) {
        i iVar = this.f1542s;
        if (i11 == 1) {
            iVar.f6908b = i10;
        } else {
            iVar.f6907a = i10;
        }
        this.f1534k = view2;
        int childCount = getChildCount();
        for (int i12 = 0; i12 < childCount; i12++) {
            ((d) getChildAt(i12).getLayoutParams()).a(i11);
        }
    }

    @Override // j0.g
    public void i(View view, int i10) {
        i iVar = this.f1542s;
        if (i10 == 1) {
            iVar.f6908b = 0;
        } else {
            iVar.f6907a = 0;
        }
        int childCount = getChildCount();
        for (int i11 = 0; i11 < childCount; i11++) {
            View childAt = getChildAt(i11);
            d dVar = (d) childAt.getLayoutParams();
            if (dVar.a(i10)) {
                Behavior behavior = dVar.f1546a;
                if (behavior != null) {
                    behavior.q(this, childAt, view, i10);
                }
                dVar.b(i10, false);
                dVar.f1560p = false;
            }
        }
        this.f1534k = null;
    }

    @Override // j0.g
    public void j(View view, int i10, int i11, int[] iArr, int i12) {
        Behavior behavior;
        int i13;
        int i14;
        int childCount = getChildCount();
        boolean z10 = false;
        int i15 = 0;
        int i16 = 0;
        for (int i17 = 0; i17 < childCount; i17++) {
            View childAt = getChildAt(i17);
            if (childAt.getVisibility() != 8) {
                d dVar = (d) childAt.getLayoutParams();
                if (dVar.a(i12) && (behavior = dVar.f1546a) != null) {
                    int[] iArr2 = this.f1529e;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    behavior.k(this, childAt, view, i10, i11, iArr2, i12);
                    int[] iArr3 = this.f1529e;
                    if (i10 > 0) {
                        i13 = Math.max(i15, iArr3[0]);
                    } else {
                        i13 = Math.min(i15, iArr3[0]);
                    }
                    i15 = i13;
                    int[] iArr4 = this.f1529e;
                    if (i11 > 0) {
                        i14 = Math.max(i16, iArr4[1]);
                    } else {
                        i14 = Math.min(i16, iArr4[1]);
                    }
                    i16 = i14;
                    z10 = true;
                }
            }
        }
        iArr[0] = i15;
        iArr[1] = i16;
        if (z10) {
            v(1);
        }
    }

    public void k(View view, boolean z10, Rect rect) {
        if (view.isLayoutRequested() || view.getVisibility() == 8) {
            rect.setEmpty();
        } else if (z10) {
            q(view, rect);
        } else {
            rect.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
        }
    }

    @NonNull
    public List<View> l(@NonNull View view) {
        x.a<View> aVar = this.f1526b;
        int i10 = aVar.f10703b.f10432c;
        ArrayList arrayList = null;
        for (int i11 = 0; i11 < i10; i11++) {
            ArrayList<T> k10 = aVar.f10703b.k(i11);
            if (k10 != null && k10.contains(view)) {
                if (arrayList == null) {
                    arrayList = new ArrayList();
                }
                arrayList.add(aVar.f10703b.h(i11));
            }
        }
        this.f1528d.clear();
        if (arrayList != null) {
            this.f1528d.addAll(arrayList);
        }
        return this.f1528d;
    }

    @Override // j0.h
    public void m(@NonNull View view, int i10, int i11, int i12, int i13, int i14, @NonNull int[] iArr) {
        Behavior behavior;
        int i15;
        boolean z10;
        int i16;
        int childCount = getChildCount();
        boolean z11 = false;
        int i17 = 0;
        int i18 = 0;
        for (int i19 = 0; i19 < childCount; i19++) {
            View childAt = getChildAt(i19);
            if (childAt.getVisibility() != 8) {
                d dVar = (d) childAt.getLayoutParams();
                if (dVar.a(i14) && (behavior = dVar.f1546a) != null) {
                    int[] iArr2 = this.f1529e;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    behavior.l(this, childAt, view, i10, i11, i12, i13, i14, iArr2);
                    int[] iArr3 = this.f1529e;
                    if (i12 > 0) {
                        i15 = Math.max(i17, iArr3[0]);
                    } else {
                        i15 = Math.min(i17, iArr3[0]);
                    }
                    i17 = i15;
                    if (i13 > 0) {
                        z10 = true;
                        i16 = Math.max(i18, this.f1529e[1]);
                    } else {
                        z10 = true;
                        i16 = Math.min(i18, this.f1529e[1]);
                    }
                    i18 = i16;
                    z11 = z10;
                }
            }
        }
        iArr[0] = iArr[0] + i17;
        iArr[1] = iArr[1] + i18;
        if (z11) {
            v(1);
        }
    }

    @Override // j0.g
    public void n(View view, int i10, int i11, int i12, int i13, int i14) {
        m(view, i10, i11, i12, i13, 0, this.f1530f);
    }

    @Override // j0.g
    public boolean o(View view, View view2, int i10, int i11) {
        int childCount = getChildCount();
        boolean z10 = false;
        for (int i12 = 0; i12 < childCount; i12++) {
            View childAt = getChildAt(i12);
            if (childAt.getVisibility() != 8) {
                d dVar = (d) childAt.getLayoutParams();
                Behavior behavior = dVar.f1546a;
                if (behavior != null) {
                    boolean p10 = behavior.p(this, childAt, view, view2, i10, i11);
                    z10 |= p10;
                    dVar.b(i11, p10);
                } else {
                    dVar.b(i11, false);
                }
            }
        }
        return z10;
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        A(false);
        if (this.f1536m) {
            if (this.f1535l == null) {
                this.f1535l = new e();
            }
            getViewTreeObserver().addOnPreDrawListener(this.f1535l);
        }
        if (this.f1537n == null) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (getFitsSystemWindows()) {
                requestApplyInsets();
            }
        }
        this.h = true;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        A(false);
        if (this.f1536m && this.f1535l != null) {
            getViewTreeObserver().removeOnPreDrawListener(this.f1535l);
        }
        View view = this.f1534k;
        if (view != null) {
            onStopNestedScroll(view);
        }
        this.h = false;
    }

    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f1538o && this.f1539p != null) {
            q qVar = this.f1537n;
            int e10 = qVar != null ? qVar.e() : 0;
            if (e10 > 0) {
                this.f1539p.setBounds(0, 0, getWidth(), e10);
                this.f1539p.draw(canvas);
            }
        }
    }

    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            A(true);
        }
        boolean y10 = y(motionEvent, 0);
        if (actionMasked == 1 || actionMasked == 3) {
            A(true);
        }
        return y10;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        Behavior behavior;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int layoutDirection = getLayoutDirection();
        int size = this.f1525a.size();
        for (int i14 = 0; i14 < size; i14++) {
            View view = this.f1525a.get(i14);
            if (view.getVisibility() != 8 && ((behavior = ((d) view.getLayoutParams()).f1546a) == null || !behavior.h(this, view, layoutDirection))) {
                w(view, layoutDirection);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:77:0x0180, code lost:
        if (r0.i(r30, r20, r8, r21, r23, 0) == false) goto L_0x0190;
     */
    /* JADX WARNING: Removed duplicated region for block: B:70:0x0129  */
    /* JADX WARNING: Removed duplicated region for block: B:76:0x0161  */
    /* JADX WARNING: Removed duplicated region for block: B:78:0x0183  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r31, int r32) {
        /*
        // Method dump skipped, instructions count: 494
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.onMeasure(int, int):void");
    }

    public boolean onNestedFling(View view, float f10, float f11, boolean z10) {
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if (childAt.getVisibility() != 8) {
                d dVar = (d) childAt.getLayoutParams();
                if (dVar.a(0)) {
                    Behavior behavior = dVar.f1546a;
                }
            }
        }
        return false;
    }

    public boolean onNestedPreFling(View view, float f10, float f11) {
        Behavior behavior;
        int childCount = getChildCount();
        boolean z10 = false;
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if (childAt.getVisibility() != 8) {
                d dVar = (d) childAt.getLayoutParams();
                if (dVar.a(0) && (behavior = dVar.f1546a) != null) {
                    z10 |= behavior.j(this, childAt, view, f10, f11);
                }
            }
        }
        return z10;
    }

    public void onNestedPreScroll(View view, int i10, int i11, int[] iArr) {
        j(view, i10, i11, iArr, 0);
    }

    public void onNestedScroll(View view, int i10, int i11, int i12, int i13) {
        n(view, i10, i11, i12, i13, 0);
    }

    public void onNestedScrollAccepted(View view, View view2, int i10) {
        h(view, view2, i10, 0);
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.f1662a);
        SparseArray<Parcelable> sparseArray = savedState.f1543c;
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            int id = childAt.getId();
            Behavior behavior = t(childAt).f1546a;
            if (!(id == -1 || behavior == null || (parcelable2 = sparseArray.get(id)) == null)) {
                behavior.n(this, childAt, parcelable2);
            }
        }
    }

    public Parcelable onSaveInstanceState() {
        Parcelable o3;
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        SparseArray<Parcelable> sparseArray = new SparseArray<>();
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            int id = childAt.getId();
            Behavior behavior = ((d) childAt.getLayoutParams()).f1546a;
            if (!(id == -1 || behavior == null || (o3 = behavior.o(this, childAt)) == null)) {
                sparseArray.append(id, o3);
            }
        }
        savedState.f1543c = sparseArray;
        return savedState;
    }

    public boolean onStartNestedScroll(View view, View view2, int i10) {
        return o(view, view2, i10, 0);
    }

    public void onStopNestedScroll(View view) {
        i(view, 0);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:3:0x0012, code lost:
        if (r3 != false) goto L_0x0016;
     */
    /* JADX WARNING: Removed duplicated region for block: B:11:0x002f  */
    /* JADX WARNING: Removed duplicated region for block: B:12:0x0035  */
    /* JADX WARNING: Removed duplicated region for block: B:15:0x004a  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouchEvent(android.view.MotionEvent r18) {
        /*
            r17 = this;
            r0 = r17
            r1 = r18
            int r2 = r18.getActionMasked()
            android.view.View r3 = r0.f1533j
            r4 = 1
            r5 = 0
            if (r3 != 0) goto L_0x0015
            boolean r3 = r0.y(r1, r4)
            if (r3 == 0) goto L_0x0029
            goto L_0x0016
        L_0x0015:
            r3 = r5
        L_0x0016:
            android.view.View r6 = r0.f1533j
            android.view.ViewGroup$LayoutParams r6 = r6.getLayoutParams()
            androidx.coordinatorlayout.widget.CoordinatorLayout$d r6 = (androidx.coordinatorlayout.widget.CoordinatorLayout.d) r6
            androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior r6 = r6.f1546a
            if (r6 == 0) goto L_0x0029
            android.view.View r7 = r0.f1533j
            boolean r6 = r6.r(r0, r7, r1)
            goto L_0x002a
        L_0x0029:
            r6 = r5
        L_0x002a:
            android.view.View r7 = r0.f1533j
            r8 = 0
            if (r7 != 0) goto L_0x0035
            boolean r1 = super.onTouchEvent(r18)
            r6 = r6 | r1
            goto L_0x0048
        L_0x0035:
            if (r3 == 0) goto L_0x0048
            long r11 = android.os.SystemClock.uptimeMillis()
            r13 = 3
            r14 = 0
            r15 = 0
            r16 = 0
            r9 = r11
            android.view.MotionEvent r8 = android.view.MotionEvent.obtain(r9, r11, r13, r14, r15, r16)
            super.onTouchEvent(r8)
        L_0x0048:
            if (r8 == 0) goto L_0x004d
            r8.recycle()
        L_0x004d:
            if (r2 == r4) goto L_0x0052
            r1 = 3
            if (r2 != r1) goto L_0x0055
        L_0x0052:
            r0.A(r5)
        L_0x0055:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.onTouchEvent(android.view.MotionEvent):boolean");
    }

    @NonNull
    public List<View> p(@NonNull View view) {
        ArrayList<T> orDefault = this.f1526b.f10703b.getOrDefault(view, null);
        this.f1528d.clear();
        if (orDefault != null) {
            this.f1528d.addAll(orDefault);
        }
        return this.f1528d;
    }

    public void q(View view, Rect rect) {
        ThreadLocal<Matrix> threadLocal = x.b.f10706a;
        rect.set(0, 0, view.getWidth(), view.getHeight());
        ThreadLocal<Matrix> threadLocal2 = x.b.f10706a;
        Matrix matrix = threadLocal2.get();
        if (matrix == null) {
            matrix = new Matrix();
            threadLocal2.set(matrix);
        } else {
            matrix.reset();
        }
        x.b.a(this, view, matrix);
        ThreadLocal<RectF> threadLocal3 = x.b.f10707b;
        RectF rectF = threadLocal3.get();
        if (rectF == null) {
            rectF = new RectF();
            threadLocal3.set(rectF);
        }
        rectF.set(rect);
        matrix.mapRect(rectF);
        rect.set((int) (rectF.left + 0.5f), (int) (rectF.top + 0.5f), (int) (rectF.right + 0.5f), (int) (rectF.bottom + 0.5f));
    }

    public final void r(int i10, Rect rect, Rect rect2, d dVar, int i11, int i12) {
        int i13;
        int i14;
        int i15 = dVar.f1548c;
        if (i15 == 0) {
            i15 = 17;
        }
        int absoluteGravity = Gravity.getAbsoluteGravity(i15, i10);
        int i16 = dVar.f1549d;
        if ((i16 & 7) == 0) {
            i16 |= 8388611;
        }
        if ((i16 & 112) == 0) {
            i16 |= 48;
        }
        int absoluteGravity2 = Gravity.getAbsoluteGravity(i16, i10);
        int i17 = absoluteGravity & 7;
        int i18 = absoluteGravity & 112;
        int i19 = absoluteGravity2 & 7;
        int i20 = absoluteGravity2 & 112;
        if (i19 == 1) {
            i13 = rect.left + (rect.width() / 2);
        } else if (i19 != 5) {
            i13 = rect.left;
        } else {
            i13 = rect.right;
        }
        if (i20 == 16) {
            i14 = rect.top + (rect.height() / 2);
        } else if (i20 != 80) {
            i14 = rect.top;
        } else {
            i14 = rect.bottom;
        }
        if (i17 == 1) {
            i13 -= i11 / 2;
        } else if (i17 != 5) {
            i13 -= i11;
        }
        if (i18 == 16) {
            i14 -= i12 / 2;
        } else if (i18 != 80) {
            i14 -= i12;
        }
        rect2.set(i13, i14, i11 + i13, i12 + i14);
    }

    public boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z10) {
        Behavior behavior = ((d) view.getLayoutParams()).f1546a;
        if (behavior == null || !behavior.m(this, view, rect, z10)) {
            return super.requestChildRectangleOnScreen(view, rect, z10);
        }
        return true;
    }

    public void requestDisallowInterceptTouchEvent(boolean z10) {
        super.requestDisallowInterceptTouchEvent(z10);
        if (z10 && !this.f1531g) {
            A(false);
            this.f1531g = true;
        }
    }

    public final int s(int i10) {
        int[] iArr = this.f1532i;
        if (iArr == null) {
            Log.e("CoordinatorLayout", "No keylines defined for " + this + " - attempted index lookup " + i10);
            return 0;
        } else if (i10 >= 0 && i10 < iArr.length) {
            return iArr[i10];
        } else {
            Log.e("CoordinatorLayout", "Keyline index " + i10 + " out of range for " + this);
            return 0;
        }
    }

    public void setFitsSystemWindows(boolean z10) {
        super.setFitsSystemWindows(z10);
        D();
    }

    public void setOnHierarchyChangeListener(ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener) {
        this.f1540q = onHierarchyChangeListener;
    }

    public void setStatusBarBackground(@Nullable Drawable drawable) {
        Drawable drawable2 = this.f1539p;
        if (drawable2 != drawable) {
            Drawable drawable3 = null;
            if (drawable2 != null) {
                drawable2.setCallback(null);
            }
            if (drawable != null) {
                drawable3 = drawable.mutate();
            }
            this.f1539p = drawable3;
            if (drawable3 != null) {
                if (drawable3.isStateful()) {
                    this.f1539p.setState(getDrawableState());
                }
                Drawable drawable4 = this.f1539p;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                drawable4.setLayoutDirection(getLayoutDirection());
                this.f1539p.setVisible(getVisibility() == 0, false);
                this.f1539p.setCallback(this);
            }
            WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
            postInvalidateOnAnimation();
        }
    }

    public void setStatusBarBackgroundColor(@ColorInt int i10) {
        setStatusBarBackground(new ColorDrawable(i10));
    }

    public void setStatusBarBackgroundResource(@DrawableRes int i10) {
        Drawable drawable;
        if (i10 != 0) {
            Context context = getContext();
            Object obj = z.a.f11008a;
            drawable = context.getDrawable(i10);
        } else {
            drawable = null;
        }
        setStatusBarBackground(drawable);
    }

    public void setVisibility(int i10) {
        super.setVisibility(i10);
        boolean z10 = i10 == 0;
        Drawable drawable = this.f1539p;
        if (drawable != null && drawable.isVisible() != z10) {
            this.f1539p.setVisible(z10, false);
        }
    }

    public d t(View view) {
        d dVar = (d) view.getLayoutParams();
        if (!dVar.f1547b) {
            if (view instanceof b) {
                Behavior behavior = ((b) view).getBehavior();
                if (behavior == null) {
                    Log.e("CoordinatorLayout", "Attached behavior class is null");
                }
                Behavior behavior2 = dVar.f1546a;
                if (behavior2 != behavior) {
                    if (behavior2 != null) {
                        behavior2.f();
                    }
                    dVar.f1546a = behavior;
                    dVar.f1547b = true;
                    if (behavior != null) {
                        behavior.c(dVar);
                    }
                }
                dVar.f1547b = true;
            } else {
                DefaultBehavior defaultBehavior = null;
                for (Class<?> cls = view.getClass(); cls != null; cls = cls.getSuperclass()) {
                    defaultBehavior = (DefaultBehavior) cls.getAnnotation(DefaultBehavior.class);
                    if (defaultBehavior != null) {
                        break;
                    }
                }
                if (defaultBehavior != null) {
                    try {
                        Behavior behavior3 = (Behavior) defaultBehavior.value().getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
                        Behavior behavior4 = dVar.f1546a;
                        if (behavior4 != behavior3) {
                            if (behavior4 != null) {
                                behavior4.f();
                            }
                            dVar.f1546a = behavior3;
                            dVar.f1547b = true;
                            if (behavior3 != null) {
                                behavior3.c(dVar);
                            }
                        }
                    } catch (Exception e10) {
                        StringBuilder a10 = com.duokan.airkan.server.f.a("Default behavior class ");
                        a10.append(defaultBehavior.value().getName());
                        a10.append(" could not be instantiated. Did you forget a default constructor?");
                        Log.e("CoordinatorLayout", a10.toString(), e10);
                    }
                }
                dVar.f1547b = true;
            }
        }
        return dVar;
    }

    public boolean u(@NonNull View view, int i10, int i11) {
        Rect e10 = e();
        q(view, e10);
        try {
            return e10.contains(i10, i11);
        } finally {
            e10.setEmpty();
            f1521n0.b(e10);
        }
    }

    public final void v(int i10) {
        int i11;
        Rect rect;
        int i12;
        boolean z10;
        boolean z11;
        boolean z12;
        int width;
        int i13;
        int i14;
        int i15;
        int height;
        int i16;
        int i17;
        int i18;
        Rect rect2;
        int i19;
        int i20;
        int i21;
        d dVar;
        Behavior behavior;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int layoutDirection = getLayoutDirection();
        int size = this.f1525a.size();
        Rect e10 = e();
        Rect e11 = e();
        Rect e12 = e();
        int i22 = 0;
        while (i22 < size) {
            View view = this.f1525a.get(i22);
            d dVar2 = (d) view.getLayoutParams();
            if (i10 == 0 && view.getVisibility() == 8) {
                i12 = size;
                rect = e12;
                i11 = i22;
            } else {
                int i23 = 0;
                while (i23 < i22) {
                    if (dVar2.f1556l == this.f1525a.get(i23)) {
                        d dVar3 = (d) view.getLayoutParams();
                        if (dVar3.f1555k != null) {
                            Rect e13 = e();
                            Rect e14 = e();
                            Rect e15 = e();
                            q(dVar3.f1555k, e13);
                            k(view, false, e14);
                            int measuredWidth = view.getMeasuredWidth();
                            i21 = size;
                            int measuredHeight = view.getMeasuredHeight();
                            i20 = i22;
                            i19 = i23;
                            rect2 = e12;
                            dVar = dVar2;
                            r(layoutDirection, e13, e15, dVar3, measuredWidth, measuredHeight);
                            boolean z13 = (e15.left == e14.left && e15.top == e14.top) ? false : true;
                            f(dVar3, e15, measuredWidth, measuredHeight);
                            int i24 = e15.left - e14.left;
                            int i25 = e15.top - e14.top;
                            if (i24 != 0) {
                                WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
                                view.offsetLeftAndRight(i24);
                            }
                            if (i25 != 0) {
                                WeakHashMap<View, m> weakHashMap3 = ViewCompat.f1593a;
                                view.offsetTopAndBottom(i25);
                            }
                            if (z13 && (behavior = dVar3.f1546a) != null) {
                                behavior.d(this, view, dVar3.f1555k);
                            }
                            e13.setEmpty();
                            i0.c cVar = f1521n0;
                            cVar.b(e13);
                            e14.setEmpty();
                            cVar.b(e14);
                            e15.setEmpty();
                            cVar.b(e15);
                            i23 = i19 + 1;
                            dVar2 = dVar;
                            size = i21;
                            i22 = i20;
                            e12 = rect2;
                        }
                    }
                    i19 = i23;
                    i21 = size;
                    rect2 = e12;
                    i20 = i22;
                    dVar = dVar2;
                    i23 = i19 + 1;
                    dVar2 = dVar;
                    size = i21;
                    i22 = i20;
                    e12 = rect2;
                }
                i11 = i22;
                k(view, true, e11);
                if (dVar2.f1552g != 0 && !e11.isEmpty()) {
                    int absoluteGravity = Gravity.getAbsoluteGravity(dVar2.f1552g, layoutDirection);
                    int i26 = absoluteGravity & 112;
                    if (i26 == 48) {
                        e10.top = Math.max(e10.top, e11.bottom);
                    } else if (i26 == 80) {
                        e10.bottom = Math.max(e10.bottom, getHeight() - e11.top);
                    }
                    int i27 = absoluteGravity & 7;
                    if (i27 == 3) {
                        e10.left = Math.max(e10.left, e11.right);
                    } else if (i27 == 5) {
                        e10.right = Math.max(e10.right, getWidth() - e11.left);
                    }
                }
                if (dVar2.h != 0 && view.getVisibility() == 0) {
                    WeakHashMap<View, m> weakHashMap4 = ViewCompat.f1593a;
                    if (view.isLaidOut() && view.getWidth() > 0 && view.getHeight() > 0) {
                        d dVar4 = (d) view.getLayoutParams();
                        Behavior behavior2 = dVar4.f1546a;
                        Rect e16 = e();
                        Rect e17 = e();
                        e17.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
                        if (behavior2 == null || !behavior2.a(this, view, e16)) {
                            e16.set(e17);
                        } else if (!e17.contains(e16)) {
                            StringBuilder a10 = com.duokan.airkan.server.f.a("Rect should be within the child's bounds. Rect:");
                            a10.append(e16.toShortString());
                            a10.append(" | Bounds:");
                            a10.append(e17.toShortString());
                            throw new IllegalArgumentException(a10.toString());
                        }
                        e17.setEmpty();
                        i0.c cVar2 = f1521n0;
                        cVar2.b(e17);
                        if (e16.isEmpty()) {
                            e16.setEmpty();
                            cVar2.b(e16);
                        } else {
                            int absoluteGravity2 = Gravity.getAbsoluteGravity(dVar4.h, layoutDirection);
                            if ((absoluteGravity2 & 48) != 48 || (i17 = (e16.top - ((ViewGroup.MarginLayoutParams) dVar4).topMargin) - dVar4.f1554j) >= (i18 = e10.top)) {
                                z11 = false;
                            } else {
                                C(view, i18 - i17);
                                z11 = true;
                            }
                            if ((absoluteGravity2 & 80) == 80 && (height = ((getHeight() - e16.bottom) - ((ViewGroup.MarginLayoutParams) dVar4).bottomMargin) + dVar4.f1554j) < (i16 = e10.bottom)) {
                                C(view, height - i16);
                                z11 = true;
                            }
                            if (!z11) {
                                C(view, 0);
                            }
                            if ((absoluteGravity2 & 3) != 3 || (i14 = (e16.left - ((ViewGroup.MarginLayoutParams) dVar4).leftMargin) - dVar4.f1553i) >= (i15 = e10.left)) {
                                z12 = false;
                            } else {
                                B(view, i15 - i14);
                                z12 = true;
                            }
                            if ((absoluteGravity2 & 5) == 5 && (width = ((getWidth() - e16.right) - ((ViewGroup.MarginLayoutParams) dVar4).rightMargin) + dVar4.f1553i) < (i13 = e10.right)) {
                                B(view, width - i13);
                                z12 = true;
                            }
                            if (!z12) {
                                B(view, 0);
                            }
                            e16.setEmpty();
                            cVar2.b(e16);
                        }
                    }
                }
                if (i10 != 2) {
                    rect = e12;
                    rect.set(((d) view.getLayoutParams()).f1561q);
                    if (rect.equals(e11)) {
                        i12 = size;
                    } else {
                        ((d) view.getLayoutParams()).f1561q.set(e11);
                    }
                } else {
                    rect = e12;
                }
                i12 = size;
                for (int i28 = i11 + 1; i28 < i12; i28++) {
                    View view2 = this.f1525a.get(i28);
                    d dVar5 = (d) view2.getLayoutParams();
                    Behavior behavior3 = dVar5.f1546a;
                    if (behavior3 != null && behavior3.b(this, view2, view)) {
                        if (i10 != 0 || !dVar5.f1560p) {
                            if (i10 != 2) {
                                z10 = behavior3.d(this, view2, view);
                            } else {
                                behavior3.e(this, view2, view);
                                z10 = true;
                            }
                            if (i10 == 1) {
                                dVar5.f1560p = z10;
                            }
                        } else {
                            dVar5.f1560p = false;
                        }
                    }
                }
            }
            i22 = i11 + 1;
            size = i12;
            e12 = rect;
        }
        e10.setEmpty();
        i0.c cVar3 = f1521n0;
        cVar3.b(e10);
        e11.setEmpty();
        cVar3.b(e11);
        e12.setEmpty();
        cVar3.b(e12);
    }

    public boolean verifyDrawable(Drawable drawable) {
        return super.verifyDrawable(drawable) || drawable == this.f1539p;
    }

    public void w(@NonNull View view, int i10) {
        d dVar = (d) view.getLayoutParams();
        View view2 = dVar.f1555k;
        int i11 = 0;
        if (view2 == null && dVar.f1551f != -1) {
            throw new IllegalStateException("An anchor may not be changed after CoordinatorLayout measurement begins before layout is complete.");
        } else if (view2 != null) {
            Rect e10 = e();
            Rect e11 = e();
            try {
                q(view2, e10);
                d dVar2 = (d) view.getLayoutParams();
                int measuredWidth = view.getMeasuredWidth();
                int measuredHeight = view.getMeasuredHeight();
                r(i10, e10, e11, dVar2, measuredWidth, measuredHeight);
                f(dVar2, e11, measuredWidth, measuredHeight);
                view.layout(e11.left, e11.top, e11.right, e11.bottom);
            } finally {
                e10.setEmpty();
                i0.c cVar = f1521n0;
                cVar.b(e10);
                e11.setEmpty();
                cVar.b(e11);
            }
        } else {
            int i12 = dVar.f1550e;
            if (i12 >= 0) {
                d dVar3 = (d) view.getLayoutParams();
                int i13 = dVar3.f1548c;
                if (i13 == 0) {
                    i13 = 8388661;
                }
                int absoluteGravity = Gravity.getAbsoluteGravity(i13, i10);
                int i14 = absoluteGravity & 7;
                int i15 = absoluteGravity & 112;
                int width = getWidth();
                int height = getHeight();
                int measuredWidth2 = view.getMeasuredWidth();
                int measuredHeight2 = view.getMeasuredHeight();
                if (i10 == 1) {
                    i12 = width - i12;
                }
                int s10 = s(i12) - measuredWidth2;
                if (i14 == 1) {
                    s10 += measuredWidth2 / 2;
                } else if (i14 == 5) {
                    s10 += measuredWidth2;
                }
                if (i15 == 16) {
                    i11 = 0 + (measuredHeight2 / 2);
                } else if (i15 == 80) {
                    i11 = measuredHeight2 + 0;
                }
                int max = Math.max(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) dVar3).leftMargin, Math.min(s10, ((width - getPaddingRight()) - measuredWidth2) - ((ViewGroup.MarginLayoutParams) dVar3).rightMargin));
                int max2 = Math.max(getPaddingTop() + ((ViewGroup.MarginLayoutParams) dVar3).topMargin, Math.min(i11, ((height - getPaddingBottom()) - measuredHeight2) - ((ViewGroup.MarginLayoutParams) dVar3).bottomMargin));
                view.layout(max, max2, measuredWidth2 + max, measuredHeight2 + max2);
                return;
            }
            d dVar4 = (d) view.getLayoutParams();
            Rect e12 = e();
            e12.set(getPaddingLeft() + ((ViewGroup.MarginLayoutParams) dVar4).leftMargin, getPaddingTop() + ((ViewGroup.MarginLayoutParams) dVar4).topMargin, (getWidth() - getPaddingRight()) - ((ViewGroup.MarginLayoutParams) dVar4).rightMargin, (getHeight() - getPaddingBottom()) - ((ViewGroup.MarginLayoutParams) dVar4).bottomMargin);
            if (this.f1537n != null) {
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                if (getFitsSystemWindows() && !view.getFitsSystemWindows()) {
                    e12.left = this.f1537n.c() + e12.left;
                    e12.top = this.f1537n.e() + e12.top;
                    e12.right -= this.f1537n.d();
                    e12.bottom -= this.f1537n.b();
                }
            }
            Rect e13 = e();
            int i16 = dVar4.f1548c;
            if ((i16 & 7) == 0) {
                i16 |= 8388611;
            }
            if ((i16 & 112) == 0) {
                i16 |= 48;
            }
            Gravity.apply(i16, view.getMeasuredWidth(), view.getMeasuredHeight(), e12, e13, i10);
            view.layout(e13.left, e13.top, e13.right, e13.bottom);
            e12.setEmpty();
            i0.c cVar2 = f1521n0;
            cVar2.b(e12);
            e13.setEmpty();
            cVar2.b(e13);
        }
    }

    public void x(View view, int i10, int i11, int i12, int i13) {
        measureChildWithMargins(view, i10, i11, i12, i13);
    }

    public final boolean y(MotionEvent motionEvent, int i10) {
        boolean z10;
        int actionMasked = motionEvent.getActionMasked();
        List<View> list = this.f1527c;
        list.clear();
        boolean isChildrenDrawingOrderEnabled = isChildrenDrawingOrderEnabled();
        int childCount = getChildCount();
        for (int i11 = childCount - 1; i11 >= 0; i11--) {
            list.add(getChildAt(isChildrenDrawingOrderEnabled ? getChildDrawingOrder(childCount, i11) : i11));
        }
        Comparator<View> comparator = f1520m0;
        if (comparator != null) {
            Collections.sort(list, comparator);
        }
        int size = list.size();
        MotionEvent motionEvent2 = null;
        boolean z11 = false;
        boolean z12 = false;
        for (int i12 = 0; i12 < size; i12++) {
            View view = list.get(i12);
            d dVar = (d) view.getLayoutParams();
            Behavior behavior = dVar.f1546a;
            if (!(z11 || z12) || actionMasked == 0) {
                if (!z11 && behavior != null) {
                    if (i10 == 0) {
                        z11 = behavior.g(this, view, motionEvent);
                    } else if (i10 == 1) {
                        z11 = behavior.r(this, view, motionEvent);
                    }
                    if (z11) {
                        this.f1533j = view;
                    }
                }
                if (dVar.f1546a == null) {
                    dVar.f1557m = false;
                }
                boolean z13 = dVar.f1557m;
                if (z13) {
                    z10 = true;
                } else {
                    z10 = z13 | false;
                    dVar.f1557m = z10;
                }
                z12 = z10 && !z13;
                if (z10 && !z12) {
                    break;
                }
            } else if (behavior != null) {
                if (motionEvent2 == null) {
                    long uptimeMillis = SystemClock.uptimeMillis();
                    motionEvent2 = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0);
                }
                if (i10 == 0) {
                    behavior.g(this, view, motionEvent2);
                } else if (i10 == 1) {
                    behavior.r(this, view, motionEvent2);
                }
            }
        }
        list.clear();
        return z11;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:26:0x0074, code lost:
        if (r5 != false) goto L_0x00cb;
     */
    /* JADX WARNING: Removed duplicated region for block: B:125:0x016b A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:71:0x0111  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void z() {
        /*
        // Method dump skipped, instructions count: 455
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.z():void");
    }

    public CoordinatorLayout(@NonNull Context context, @Nullable AttributeSet attributeSet, @AttrRes int i10) {
        super(context, attributeSet, i10);
        TypedArray typedArray;
        this.f1525a = new ArrayList();
        this.f1526b = new x.a<>();
        this.f1527c = new ArrayList();
        this.f1528d = new ArrayList();
        this.f1529e = new int[2];
        this.f1530f = new int[2];
        this.f1542s = new i();
        if (i10 == 0) {
            typedArray = context.obtainStyledAttributes(attributeSet, R$styleable.CoordinatorLayout, 0, R$style.Widget_Support_CoordinatorLayout);
        } else {
            typedArray = context.obtainStyledAttributes(attributeSet, R$styleable.CoordinatorLayout, i10, 0);
        }
        if (Build.VERSION.SDK_INT >= 29) {
            if (i10 == 0) {
                saveAttributeDataForStyleable(context, R$styleable.CoordinatorLayout, attributeSet, typedArray, 0, R$style.Widget_Support_CoordinatorLayout);
            } else {
                saveAttributeDataForStyleable(context, R$styleable.CoordinatorLayout, attributeSet, typedArray, i10, 0);
            }
        }
        int resourceId = typedArray.getResourceId(R$styleable.CoordinatorLayout_keylines, 0);
        if (resourceId != 0) {
            Resources resources = context.getResources();
            this.f1532i = resources.getIntArray(resourceId);
            float f10 = resources.getDisplayMetrics().density;
            int length = this.f1532i.length;
            for (int i11 = 0; i11 < length; i11++) {
                int[] iArr = this.f1532i;
                iArr[i11] = (int) (((float) iArr[i11]) * f10);
            }
        }
        this.f1539p = typedArray.getDrawable(R$styleable.CoordinatorLayout_statusBarBackground);
        typedArray.recycle();
        D();
        super.setOnHierarchyChangeListener(new c());
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof d) {
            return new d((d) layoutParams);
        }
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return new d((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return new d(layoutParams);
    }

    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: c  reason: collision with root package name */
        public SparseArray<Parcelable> f1543c;

        public static class a implements Parcelable.ClassLoaderCreator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }
        }

        public SavedState(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            int readInt = parcel.readInt();
            int[] iArr = new int[readInt];
            parcel.readIntArray(iArr);
            Parcelable[] readParcelableArray = parcel.readParcelableArray(classLoader);
            this.f1543c = new SparseArray<>(readInt);
            for (int i10 = 0; i10 < readInt; i10++) {
                this.f1543c.append(iArr[i10], readParcelableArray[i10]);
            }
        }

        @Override // androidx.customview.view.AbsSavedState
        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeParcelable(this.f1662a, i10);
            SparseArray<Parcelable> sparseArray = this.f1543c;
            int size = sparseArray != null ? sparseArray.size() : 0;
            parcel.writeInt(size);
            int[] iArr = new int[size];
            Parcelable[] parcelableArr = new Parcelable[size];
            for (int i11 = 0; i11 < size; i11++) {
                iArr[i11] = this.f1543c.keyAt(i11);
                parcelableArr[i11] = this.f1543c.valueAt(i11);
            }
            parcel.writeIntArray(iArr);
            parcel.writeParcelableArray(parcelableArr, i10);
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }
    }

    public static class d extends ViewGroup.MarginLayoutParams {

        /* renamed from: a  reason: collision with root package name */
        public Behavior f1546a;

        /* renamed from: b  reason: collision with root package name */
        public boolean f1547b = false;

        /* renamed from: c  reason: collision with root package name */
        public int f1548c = 0;

        /* renamed from: d  reason: collision with root package name */
        public int f1549d = 0;

        /* renamed from: e  reason: collision with root package name */
        public int f1550e = -1;

        /* renamed from: f  reason: collision with root package name */
        public int f1551f = -1;

        /* renamed from: g  reason: collision with root package name */
        public int f1552g = 0;
        public int h = 0;

        /* renamed from: i  reason: collision with root package name */
        public int f1553i;

        /* renamed from: j  reason: collision with root package name */
        public int f1554j;

        /* renamed from: k  reason: collision with root package name */
        public View f1555k;

        /* renamed from: l  reason: collision with root package name */
        public View f1556l;

        /* renamed from: m  reason: collision with root package name */
        public boolean f1557m;

        /* renamed from: n  reason: collision with root package name */
        public boolean f1558n;

        /* renamed from: o  reason: collision with root package name */
        public boolean f1559o;

        /* renamed from: p  reason: collision with root package name */
        public boolean f1560p;

        /* renamed from: q  reason: collision with root package name */
        public final Rect f1561q = new Rect();

        public d(int i10, int i11) {
            super(i10, i11);
        }

        public boolean a(int i10) {
            if (i10 == 0) {
                return this.f1558n;
            }
            if (i10 != 1) {
                return false;
            }
            return this.f1559o;
        }

        public void b(int i10, boolean z10) {
            if (i10 == 0) {
                this.f1558n = z10;
            } else if (i10 == 1) {
                this.f1559o = z10;
            }
        }

        /* JADX DEBUG: Multi-variable search result rejected for r3v17, resolved type: java.lang.Class<?> */
        /* JADX WARN: Multi-variable type inference failed */
        public d(@NonNull Context context, @Nullable AttributeSet attributeSet) {
            super(context, attributeSet);
            Behavior behavior;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.CoordinatorLayout_Layout);
            this.f1548c = obtainStyledAttributes.getInteger(R$styleable.CoordinatorLayout_Layout_android_layout_gravity, 0);
            this.f1551f = obtainStyledAttributes.getResourceId(R$styleable.CoordinatorLayout_Layout_layout_anchor, -1);
            this.f1549d = obtainStyledAttributes.getInteger(R$styleable.CoordinatorLayout_Layout_layout_anchorGravity, 0);
            this.f1550e = obtainStyledAttributes.getInteger(R$styleable.CoordinatorLayout_Layout_layout_keyline, -1);
            this.f1552g = obtainStyledAttributes.getInt(R$styleable.CoordinatorLayout_Layout_layout_insetEdge, 0);
            this.h = obtainStyledAttributes.getInt(R$styleable.CoordinatorLayout_Layout_layout_dodgeInsetEdges, 0);
            int i10 = R$styleable.CoordinatorLayout_Layout_layout_behavior;
            boolean hasValue = obtainStyledAttributes.hasValue(i10);
            this.f1547b = hasValue;
            if (hasValue) {
                String string = obtainStyledAttributes.getString(i10);
                String str = CoordinatorLayout.f1522t;
                if (TextUtils.isEmpty(string)) {
                    behavior = null;
                } else {
                    if (string.startsWith(".")) {
                        string = context.getPackageName() + string;
                    } else if (string.indexOf(46) < 0) {
                        String str2 = CoordinatorLayout.f1522t;
                        if (!TextUtils.isEmpty(str2)) {
                            string = str2 + '.' + string;
                        }
                    }
                    try {
                        ThreadLocal<Map<String, Constructor<Behavior>>> threadLocal = CoordinatorLayout.f1524y;
                        Map<String, Constructor<Behavior>> map = threadLocal.get();
                        if (map == null) {
                            map = new HashMap<>();
                            threadLocal.set(map);
                        }
                        Constructor<Behavior> constructor = map.get(string);
                        if (constructor == null) {
                            constructor = Class.forName(string, false, context.getClassLoader()).getConstructor(CoordinatorLayout.f1523x);
                            constructor.setAccessible(true);
                            map.put(string, constructor);
                        }
                        behavior = constructor.newInstance(context, attributeSet);
                    } catch (Exception e10) {
                        throw new RuntimeException(p.f.a("Could not inflate Behavior subclass ", string), e10);
                    }
                }
                this.f1546a = behavior;
            }
            obtainStyledAttributes.recycle();
            Behavior behavior2 = this.f1546a;
            if (behavior2 != null) {
                behavior2.c(this);
            }
        }

        public d(d dVar) {
            super((ViewGroup.MarginLayoutParams) dVar);
        }

        public d(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }

        public d(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }
    }
}
