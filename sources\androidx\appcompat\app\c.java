package androidx.appcompat.app;

import android.view.View;
import android.widget.AdapterView;
import androidx.appcompat.app.AlertController;

/* compiled from: AlertController */
public class c implements AdapterView.OnItemClickListener {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ AlertController f437a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ AlertController.b f438b;

    public c(AlertController.b bVar, AlertController alertController) {
        this.f438b = bVar;
        this.f437a = alertController;
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
        this.f438b.h.onClick(this.f437a.f330b, i10);
        if (!this.f438b.f364i) {
            this.f437a.f330b.dismiss();
        }
    }
}
