package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.l;
import com.duokan.airkan.common.Constant;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class k extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ l.a f2383a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ ViewPropertyAnimator f2384b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ View f2385c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ l f2386d;

    public k(l lVar, l.a aVar, ViewPropertyAnimator viewPropertyAnimator, View view) {
        this.f2386d = lVar;
        this.f2383a = aVar;
        this.f2384b = viewPropertyAnimator;
        this.f2385c = view;
    }

    public void onAnimationEnd(Animator animator) {
        this.f2384b.setListener(null);
        this.f2385c.setAlpha(1.0f);
        this.f2385c.setTranslationX(Constant.VOLUME_FLOAT_MIN);
        this.f2385c.setTranslationY(Constant.VOLUME_FLOAT_MIN);
        this.f2386d.c(this.f2383a.f2399b);
        this.f2386d.f2397r.remove(this.f2383a.f2399b);
        this.f2386d.k();
    }

    public void onAnimationStart(Animator animator) {
        l lVar = this.f2386d;
        RecyclerView.w wVar = this.f2383a.f2399b;
        Objects.requireNonNull(lVar);
    }
}
