package aa;

import com.duokan.airkan.server.f;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import mb.a;
import nb.d;

/* compiled from: ASN1OctetString */
public abstract class n extends q implements o {

    /* renamed from: a  reason: collision with root package name */
    public byte[] f205a;

    public n(byte[] bArr) {
        Objects.requireNonNull(bArr, "string cannot be null");
        this.f205a = bArr;
    }

    public static n n(Object obj) {
        if (obj == null || (obj instanceof n)) {
            return (n) obj;
        }
        if (obj instanceof byte[]) {
            try {
                return n(q.j((byte[]) obj));
            } catch (IOException e10) {
                StringBuilder a10 = f.a("failed to construct OCTET STRING from byte[]: ");
                a10.append(e10.getMessage());
                throw new IllegalArgumentException(a10.toString());
            }
        } else {
            if (obj instanceof e) {
                q c10 = ((e) obj).c();
                if (c10 instanceof n) {
                    return (n) c10;
                }
            }
            StringBuilder a11 = f.a("illegal object in getInstance: ");
            a11.append(obj.getClass().getName());
            throw new IllegalArgumentException(a11.toString());
        }
    }

    @Override // aa.o
    public InputStream a() {
        return new ByteArrayInputStream(this.f205a);
    }

    @Override // aa.q1
    public q d() {
        return this;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof n)) {
            return false;
        }
        return a.a(this.f205a, ((n) qVar).f205a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(o());
    }

    @Override // aa.q
    public q l() {
        return new w0(this.f205a);
    }

    @Override // aa.q
    public q m() {
        return new w0(this.f205a);
    }

    public byte[] o() {
        return this.f205a;
    }

    public String toString() {
        StringBuilder a10 = f.a("#");
        a10.append(new String(d.b(this.f205a)));
        return a10.toString();
    }
}
