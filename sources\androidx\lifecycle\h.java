package androidx.lifecycle;

import android.annotation.SuppressLint;
import androidx.activity.result.c;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import com.duokan.airkan.server.f;
import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import s.b;

/* compiled from: LifecycleRegistry */
public class h extends Lifecycle {

    /* renamed from: a  reason: collision with root package name */
    public s.a<f, a> f2066a = new s.a<>();

    /* renamed from: b  reason: collision with root package name */
    public Lifecycle.State f2067b;

    /* renamed from: c  reason: collision with root package name */
    public final WeakReference<g> f2068c;

    /* renamed from: d  reason: collision with root package name */
    public int f2069d = 0;

    /* renamed from: e  reason: collision with root package name */
    public boolean f2070e = false;

    /* renamed from: f  reason: collision with root package name */
    public boolean f2071f = false;

    /* renamed from: g  reason: collision with root package name */
    public ArrayList<Lifecycle.State> f2072g = new ArrayList<>();
    public final boolean h;

    /* compiled from: LifecycleRegistry */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public Lifecycle.State f2073a;

        /* renamed from: b  reason: collision with root package name */
        public e f2074b;

        public a(f fVar, Lifecycle.State state) {
            e eVar;
            Map<Class<?>, Integer> map = j.f2075a;
            boolean z10 = fVar instanceof e;
            boolean z11 = fVar instanceof c;
            if (z10 && z11) {
                eVar = new FullLifecycleObserverAdapter((c) fVar, (e) fVar);
            } else if (z11) {
                eVar = new FullLifecycleObserverAdapter((c) fVar, null);
            } else if (z10) {
                eVar = (e) fVar;
            } else {
                Class<?> cls = fVar.getClass();
                if (j.c(cls) == 2) {
                    List list = (List) ((HashMap) j.f2076b).get(cls);
                    if (list.size() == 1) {
                        eVar = new SingleGeneratedAdapterObserver(j.a((Constructor) list.get(0), fVar));
                    } else {
                        d[] dVarArr = new d[list.size()];
                        for (int i10 = 0; i10 < list.size(); i10++) {
                            dVarArr[i10] = j.a((Constructor) list.get(i10), fVar);
                        }
                        eVar = new CompositeGeneratedAdaptersObserver(dVarArr);
                    }
                } else {
                    eVar = new ReflectiveGenericLifecycleObserver(fVar);
                }
            }
            this.f2074b = eVar;
            this.f2073a = state;
        }

        public void a(g gVar, Lifecycle.Event event) {
            Lifecycle.State targetState = event.getTargetState();
            this.f2073a = h.f(this.f2073a, targetState);
            this.f2074b.d(gVar, event);
            this.f2073a = targetState;
        }
    }

    public h(@NonNull g gVar) {
        this.f2068c = new WeakReference<>(gVar);
        this.f2067b = Lifecycle.State.INITIALIZED;
        this.h = true;
    }

    public static Lifecycle.State f(@NonNull Lifecycle.State state, @Nullable Lifecycle.State state2) {
        return (state2 == null || state2.compareTo(state) >= 0) ? state : state2;
    }

    @Override // androidx.lifecycle.Lifecycle
    public void a(@NonNull f fVar) {
        g gVar;
        d("addObserver");
        Lifecycle.State state = this.f2067b;
        Lifecycle.State state2 = Lifecycle.State.DESTROYED;
        if (state != state2) {
            state2 = Lifecycle.State.INITIALIZED;
        }
        a aVar = new a(fVar, state2);
        if (this.f2066a.e(fVar, aVar) == null && (gVar = this.f2068c.get()) != null) {
            boolean z10 = this.f2069d != 0 || this.f2070e;
            Lifecycle.State c10 = c(fVar);
            this.f2069d++;
            while (aVar.f2073a.compareTo((Enum) c10) < 0 && this.f2066a.f9923e.containsKey(fVar)) {
                this.f2072g.add(aVar.f2073a);
                Lifecycle.Event upFrom = Lifecycle.Event.upFrom(aVar.f2073a);
                if (upFrom != null) {
                    aVar.a(gVar, upFrom);
                    h();
                    c10 = c(fVar);
                } else {
                    StringBuilder a10 = f.a("no event up from ");
                    a10.append(aVar.f2073a);
                    throw new IllegalStateException(a10.toString());
                }
            }
            if (!z10) {
                j();
            }
            this.f2069d--;
        }
    }

    @Override // androidx.lifecycle.Lifecycle
    public void b(@NonNull f fVar) {
        d("removeObserver");
        this.f2066a.f(fVar);
    }

    public final Lifecycle.State c(f fVar) {
        s.a<f, a> aVar = this.f2066a;
        Lifecycle.State state = null;
        b.c<K, V> cVar = aVar.f9923e.containsKey(fVar) ? aVar.f9923e.get(fVar).f9931d : null;
        Lifecycle.State state2 = cVar != null ? cVar.f9929b.f2073a : null;
        if (!this.f2072g.isEmpty()) {
            ArrayList<Lifecycle.State> arrayList = this.f2072g;
            state = arrayList.get(arrayList.size() - 1);
        }
        return f(f(this.f2067b, state2), state);
    }

    @SuppressLint({"RestrictedApi"})
    public final void d(String str) {
        if (this.h && !r.a.b().a()) {
            throw new IllegalStateException(c.a("Method ", str, " must be called on the main thread"));
        }
    }

    public void e(@NonNull Lifecycle.Event event) {
        d("handleLifecycleEvent");
        g(event.getTargetState());
    }

    public final void g(Lifecycle.State state) {
        if (this.f2067b != state) {
            this.f2067b = state;
            if (this.f2070e || this.f2069d != 0) {
                this.f2071f = true;
                return;
            }
            this.f2070e = true;
            j();
            this.f2070e = false;
        }
    }

    public final void h() {
        ArrayList<Lifecycle.State> arrayList = this.f2072g;
        arrayList.remove(arrayList.size() - 1);
    }

    @MainThread
    public void i(@NonNull Lifecycle.State state) {
        d("setCurrentState");
        g(state);
    }

    /* JADX DEBUG: Multi-variable search result rejected for r4v4, resolved type: s.a<androidx.lifecycle.f, androidx.lifecycle.h$a> */
    /* JADX DEBUG: Multi-variable search result rejected for r4v13, resolved type: s.a<androidx.lifecycle.f, androidx.lifecycle.h$a> */
    /* JADX WARN: Multi-variable type inference failed */
    public final void j() {
        Lifecycle.State state;
        g gVar = this.f2068c.get();
        if (gVar != null) {
            while (true) {
                s.a<f, a> aVar = this.f2066a;
                boolean z10 = true;
                if (!(aVar.f9927d == 0 || (aVar.f9924a.f9929b.f2073a == (state = aVar.f9925b.f9929b.f2073a) && this.f2067b == state))) {
                    z10 = false;
                }
                if (!z10) {
                    this.f2071f = false;
                    if (this.f2067b.compareTo((Enum) aVar.f9924a.f9929b.f2073a) < 0) {
                        s.a<f, a> aVar2 = this.f2066a;
                        b.C0162b bVar = new b.C0162b(aVar2.f9925b, aVar2.f9924a);
                        aVar2.f9926c.put(bVar, Boolean.FALSE);
                        while (bVar.hasNext() && !this.f2071f) {
                            Map.Entry entry = (Map.Entry) bVar.next();
                            a aVar3 = (a) entry.getValue();
                            while (aVar3.f2073a.compareTo((Enum) this.f2067b) > 0 && !this.f2071f && this.f2066a.contains(entry.getKey())) {
                                Lifecycle.Event downFrom = Lifecycle.Event.downFrom(aVar3.f2073a);
                                if (downFrom != null) {
                                    this.f2072g.add(downFrom.getTargetState());
                                    aVar3.a(gVar, downFrom);
                                    h();
                                } else {
                                    StringBuilder a10 = f.a("no event down from ");
                                    a10.append(aVar3.f2073a);
                                    throw new IllegalStateException(a10.toString());
                                }
                            }
                        }
                    }
                    b.c<K, V> cVar = this.f2066a.f9925b;
                    if (!this.f2071f && cVar != null && this.f2067b.compareTo((Enum) cVar.f9929b.f2073a) > 0) {
                        b<K, V>.d c10 = this.f2066a.c();
                        while (c10.hasNext() && !this.f2071f) {
                            Map.Entry entry2 = (Map.Entry) c10.next();
                            a aVar4 = (a) entry2.getValue();
                            while (aVar4.f2073a.compareTo((Enum) this.f2067b) < 0 && !this.f2071f && this.f2066a.contains(entry2.getKey())) {
                                this.f2072g.add(aVar4.f2073a);
                                Lifecycle.Event upFrom = Lifecycle.Event.upFrom(aVar4.f2073a);
                                if (upFrom != null) {
                                    aVar4.a(gVar, upFrom);
                                    h();
                                } else {
                                    StringBuilder a11 = f.a("no event up from ");
                                    a11.append(aVar4.f2073a);
                                    throw new IllegalStateException(a11.toString());
                                }
                            }
                        }
                    }
                } else {
                    this.f2071f = false;
                    return;
                }
            }
        } else {
            throw new IllegalStateException("LifecycleOwner of this LifecycleRegistry is alreadygarbage collected. It is too late to change lifecycle state.");
        }
    }
}
