package androidx.recyclerview.widget;

import android.content.Context;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.Scroller;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import com.xiaomi.mitv.pie.EventResultPersister;
import java.util.List;

/* compiled from: SnapHelper */
public abstract class c0 extends RecyclerView.l {

    /* renamed from: a  reason: collision with root package name */
    public RecyclerView f2347a;

    /* renamed from: b  reason: collision with root package name */
    public Scroller f2348b;

    /* renamed from: c  reason: collision with root package name */
    public final RecyclerView.n f2349c = new a();

    /* compiled from: SnapHelper */
    public class a extends RecyclerView.n {

        /* renamed from: a  reason: collision with root package name */
        public boolean f2350a = false;

        public a() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.n
        public void a(RecyclerView recyclerView, int i10) {
            if (i10 == 0 && this.f2350a) {
                this.f2350a = false;
                c0.this.f();
            }
        }

        @Override // androidx.recyclerview.widget.RecyclerView.n
        public void b(RecyclerView recyclerView, int i10, int i11) {
            if (i10 != 0 || i11 != 0) {
                this.f2350a = true;
            }
        }
    }

    /* compiled from: SnapHelper */
    public class b extends q {
        public b(Context context) {
            super(context);
        }

        @Override // androidx.recyclerview.widget.RecyclerView.s, androidx.recyclerview.widget.q
        public void c(View view, RecyclerView.t tVar, RecyclerView.s.a aVar) {
            c0 c0Var = c0.this;
            RecyclerView recyclerView = c0Var.f2347a;
            if (recyclerView != null) {
                int[] b10 = c0Var.b(recyclerView.getLayoutManager(), view);
                int i10 = b10[0];
                int i11 = b10[1];
                int g10 = g(Math.max(Math.abs(i10), Math.abs(i11)));
                if (g10 > 0) {
                    aVar.b(i10, i11, g10, this.f2463j);
                }
            }
        }

        @Override // androidx.recyclerview.widget.q
        public float f(DisplayMetrics displayMetrics) {
            return 100.0f / ((float) displayMetrics.densityDpi);
        }
    }

    public void a(@Nullable RecyclerView recyclerView) throws IllegalStateException {
        RecyclerView recyclerView2 = this.f2347a;
        if (recyclerView2 != recyclerView) {
            if (recyclerView2 != null) {
                RecyclerView.n nVar = this.f2349c;
                List<RecyclerView.n> list = recyclerView2.T0;
                if (list != null) {
                    list.remove(nVar);
                }
                this.f2347a.setOnFlingListener(null);
            }
            this.f2347a = recyclerView;
            if (recyclerView == null) {
                return;
            }
            if (recyclerView.getOnFlingListener() == null) {
                this.f2347a.h(this.f2349c);
                this.f2347a.setOnFlingListener(this);
                this.f2348b = new Scroller(this.f2347a.getContext(), new DecelerateInterpolator());
                f();
                return;
            }
            throw new IllegalStateException("An instance of OnFlingListener already set.");
        }
    }

    @Nullable
    public abstract int[] b(@NonNull RecyclerView.j jVar, @NonNull View view);

    @Nullable
    @Deprecated
    public q c(RecyclerView.j jVar) {
        if (!(jVar instanceof RecyclerView.s.b)) {
            return null;
        }
        return new b(this.f2347a.getContext());
    }

    @Nullable
    public abstract View d(RecyclerView.j jVar);

    public abstract int e(RecyclerView.j jVar, int i10, int i11);

    public void f() {
        RecyclerView.j layoutManager;
        View d10;
        RecyclerView recyclerView = this.f2347a;
        if (recyclerView != null && (layoutManager = recyclerView.getLayoutManager()) != null && (d10 = d(layoutManager)) != null) {
            int[] b10 = b(layoutManager, d10);
            if (b10[0] != 0 || b10[1] != 0) {
                this.f2347a.g0(b10[0], b10[1], null, EventResultPersister.GENERATE_NEW_ID, false);
            }
        }
    }
}
