package androidx.core.graphics.drawable;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Icon;
import android.os.Parcelable;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.versionedparcelable.CustomVersionedParcelable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class IconCompat extends CustomVersionedParcelable {

    /* renamed from: j  reason: collision with root package name */
    public static final PorterDuff.Mode f1584j = PorterDuff.Mode.SRC_IN;
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})

    /* renamed from: a  reason: collision with root package name */
    public int f1585a = -1;

    /* renamed from: b  reason: collision with root package name */
    public Object f1586b;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: c  reason: collision with root package name */
    public byte[] f1587c = null;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: d  reason: collision with root package name */
    public Parcelable f1588d = null;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: e  reason: collision with root package name */
    public int f1589e = 0;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: f  reason: collision with root package name */
    public int f1590f = 0;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: g  reason: collision with root package name */
    public ColorStateList f1591g = null;
    public PorterDuff.Mode h = f1584j;
    @RestrictTo({RestrictTo.Scope.LIBRARY})

    /* renamed from: i  reason: collision with root package name */
    public String f1592i = null;

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public @interface IconType {
    }

    @IdRes
    public int a() {
        int i10 = this.f1585a;
        if (i10 == -1) {
            return ((Icon) this.f1586b).getResId();
        }
        if (i10 == 2) {
            return this.f1589e;
        }
        throw new IllegalStateException("called getResId() on " + this);
    }

    @NonNull
    public String b() {
        int i10 = this.f1585a;
        if (i10 == -1) {
            return ((Icon) this.f1586b).getResPackage();
        }
        if (i10 == 2) {
            return ((String) this.f1586b).split(":", -1)[0];
        }
        throw new IllegalStateException("called getResPackage() on " + this);
    }

    @NonNull
    public String toString() {
        String str;
        if (this.f1585a == -1) {
            return String.valueOf(this.f1586b);
        }
        StringBuilder sb = new StringBuilder("Icon(typ=");
        switch (this.f1585a) {
            case 1:
                str = "BITMAP";
                break;
            case 2:
                str = "RESOURCE";
                break;
            case 3:
                str = "DATA";
                break;
            case 4:
                str = "URI";
                break;
            case 5:
                str = "BITMAP_MASKABLE";
                break;
            case 6:
                str = "URI_MASKABLE";
                break;
            default:
                str = "UNKNOWN";
                break;
        }
        sb.append(str);
        switch (this.f1585a) {
            case 1:
            case 5:
                sb.append(" size=");
                sb.append(((Bitmap) this.f1586b).getWidth());
                sb.append("x");
                sb.append(((Bitmap) this.f1586b).getHeight());
                break;
            case 2:
                sb.append(" pkg=");
                sb.append(b());
                sb.append(" id=");
                sb.append(String.format("0x%08x", Integer.valueOf(a())));
                break;
            case 3:
                sb.append(" len=");
                sb.append(this.f1589e);
                if (this.f1590f != 0) {
                    sb.append(" off=");
                    sb.append(this.f1590f);
                    break;
                }
                break;
            case 4:
            case 6:
                sb.append(" uri=");
                sb.append(this.f1586b);
                break;
        }
        if (this.f1591g != null) {
            sb.append(" tint=");
            sb.append(this.f1591g);
        }
        if (this.h != f1584j) {
            sb.append(" mode=");
            sb.append(this.h);
        }
        sb.append(")");
        return sb.toString();
    }
}
