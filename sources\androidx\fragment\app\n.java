package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.SharedElementCallback;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import androidx.activity.ComponentActivity;
import androidx.activity.OnBackPressedDispatcher;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.d;
import androidx.annotation.CallSuper;
import androidx.annotation.ContentView;
import androidx.annotation.LayoutRes;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.h;
import androidx.lifecycle.w;
import androidx.lifecycle.x;
import androidx.savedstate.a;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.Objects;
import y.a;
import y.f;

/* compiled from: FragmentActivity */
public class n extends ComponentActivity implements a.AbstractC0184a {
    public static final String FRAGMENTS_TAG = "android:support:fragments";
    public boolean mCreated;
    public final h mFragmentLifecycleRegistry = new h(this);
    public final t mFragments = new t(new c());
    public boolean mResumed;
    public boolean mStopped = true;

    /* compiled from: FragmentActivity */
    public class a implements a.b {
        public a() {
        }

        @Override // androidx.savedstate.a.b
        @NonNull
        public Bundle a() {
            Bundle bundle = new Bundle();
            n.this.markFragmentsCreated();
            n.this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_STOP);
            Parcelable b02 = n.this.mFragments.f2000a.f2018d.b0();
            if (b02 != null) {
                bundle.putParcelable(n.FRAGMENTS_TAG, b02);
            }
            return bundle;
        }
    }

    /* compiled from: FragmentActivity */
    public class b implements k.b {
        public b() {
        }

        @Override // k.b
        public void a(@NonNull Context context) {
            v<?> vVar = n.this.mFragments.f2000a;
            vVar.f2018d.b(vVar, vVar, null);
            Bundle a10 = n.this.getSavedStateRegistry().a(n.FRAGMENTS_TAG);
            if (a10 != null) {
                Parcelable parcelable = a10.getParcelable(n.FRAGMENTS_TAG);
                v<?> vVar2 = n.this.mFragments.f2000a;
                if (vVar2 instanceof x) {
                    vVar2.f2018d.a0(parcelable);
                    return;
                }
                throw new IllegalStateException("Your FragmentHostCallback must implement ViewModelStoreOwner to call restoreSaveState(). Call restoreAllState()  if you're still using retainNestedNonConfig().");
            }
        }
    }

    /* compiled from: FragmentActivity */
    public class c extends v<n> implements x, androidx.activity.c, d, a0 {
        public c() {
            super(n.this);
        }

        @Override // androidx.fragment.app.a0
        public void a(@NonNull FragmentManager fragmentManager, @NonNull Fragment fragment) {
            n.this.onAttachFragment(fragment);
        }

        @Override // androidx.fragment.app.s
        @Nullable
        public View b(int i10) {
            return n.this.findViewById(i10);
        }

        @Override // androidx.fragment.app.s
        public boolean c() {
            Window window = n.this.getWindow();
            return (window == null || window.peekDecorView() == null) ? false : true;
        }

        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // androidx.fragment.app.v
        public n d() {
            return n.this;
        }

        @Override // androidx.fragment.app.v
        @NonNull
        public LayoutInflater e() {
            return n.this.getLayoutInflater().cloneInContext(n.this);
        }

        @Override // androidx.fragment.app.v
        public boolean f(@NonNull Fragment fragment) {
            return !n.this.isFinishing();
        }

        @Override // androidx.fragment.app.v
        public void g() {
            n.this.supportInvalidateOptionsMenu();
        }

        @Override // androidx.activity.result.d
        @NonNull
        public ActivityResultRegistry getActivityResultRegistry() {
            return n.this.getActivityResultRegistry();
        }

        @Override // androidx.lifecycle.g
        @NonNull
        public Lifecycle getLifecycle() {
            return n.this.mFragmentLifecycleRegistry;
        }

        @Override // androidx.activity.c
        @NonNull
        public OnBackPressedDispatcher getOnBackPressedDispatcher() {
            return n.this.getOnBackPressedDispatcher();
        }

        @Override // androidx.lifecycle.x
        @NonNull
        public w getViewModelStore() {
            return n.this.getViewModelStore();
        }
    }

    public n() {
        init();
    }

    private void init() {
        getSavedStateRegistry().b(FRAGMENTS_TAG, new a());
        addOnContextAvailableListener(new b());
    }

    private static boolean markState(FragmentManager fragmentManager, Lifecycle.State state) {
        boolean z10 = false;
        for (Fragment fragment : fragmentManager.f1775c.i()) {
            if (fragment != null) {
                v<?> vVar = fragment.f1743s;
                if ((vVar == null ? null : vVar.d()) != null) {
                    z10 |= markState(fragment.e(), state);
                }
                p0 p0Var = fragment.D0;
                if (p0Var != null && ((h) p0Var.getLifecycle()).f2067b.isAtLeast(Lifecycle.State.STARTED)) {
                    h hVar = fragment.D0.f1980a;
                    hVar.d("setCurrentState");
                    hVar.g(state);
                    z10 = true;
                }
                if (fragment.C0.f2067b.isAtLeast(Lifecycle.State.STARTED)) {
                    h hVar2 = fragment.C0;
                    hVar2.d("setCurrentState");
                    hVar2.g(state);
                    z10 = true;
                }
            }
        }
        return z10;
    }

    @Nullable
    public final View dispatchFragmentsOnCreateView(@Nullable View view, @NonNull String str, @NonNull Context context, @NonNull AttributeSet attributeSet) {
        return this.mFragments.f2000a.f2018d.f1778f.onCreateView(view, str, context, attributeSet);
    }

    public void dump(@NonNull String str, @Nullable FileDescriptor fileDescriptor, @NonNull PrintWriter printWriter, @Nullable String[] strArr) {
        super.dump(str, fileDescriptor, printWriter, strArr);
        printWriter.print(str);
        printWriter.print("Local FragmentActivity ");
        printWriter.print(Integer.toHexString(System.identityHashCode(this)));
        printWriter.println(" State:");
        String str2 = str + "  ";
        printWriter.print(str2);
        printWriter.print("mCreated=");
        printWriter.print(this.mCreated);
        printWriter.print(" mResumed=");
        printWriter.print(this.mResumed);
        printWriter.print(" mStopped=");
        printWriter.print(this.mStopped);
        if (getApplication() != null) {
            o0.a.b(this).a(str2, fileDescriptor, printWriter, strArr);
        }
        this.mFragments.f2000a.f2018d.y(str, fileDescriptor, printWriter, strArr);
    }

    @NonNull
    public FragmentManager getSupportFragmentManager() {
        return this.mFragments.f2000a.f2018d;
    }

    @NonNull
    @Deprecated
    public o0.a getSupportLoaderManager() {
        return o0.a.b(this);
    }

    public void markFragmentsCreated() {
        do {
        } while (markState(getSupportFragmentManager(), Lifecycle.State.CREATED));
    }

    @Override // androidx.activity.ComponentActivity
    @CallSuper
    public void onActivityResult(int i10, int i11, @Nullable Intent intent) {
        this.mFragments.a();
        super.onActivityResult(i10, i11, intent);
    }

    @MainThread
    @Deprecated
    public void onAttachFragment(@NonNull Fragment fragment) {
    }

    public void onConfigurationChanged(@NonNull Configuration configuration) {
        super.onConfigurationChanged(configuration);
        this.mFragments.a();
        this.mFragments.f2000a.f2018d.k(configuration);
    }

    @Override // androidx.activity.ComponentActivity, y.c
    public void onCreate(@Nullable Bundle bundle) {
        super.onCreate(bundle);
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_CREATE);
        this.mFragments.f2000a.f2018d.m();
    }

    public boolean onCreatePanelMenu(int i10, @NonNull Menu menu) {
        if (i10 != 0) {
            return super.onCreatePanelMenu(i10, menu);
        }
        boolean onCreatePanelMenu = super.onCreatePanelMenu(i10, menu);
        t tVar = this.mFragments;
        return onCreatePanelMenu | tVar.f2000a.f2018d.n(menu, getMenuInflater());
    }

    @Nullable
    public View onCreateView(@Nullable View view, @NonNull String str, @NonNull Context context, @NonNull AttributeSet attributeSet) {
        View dispatchFragmentsOnCreateView = dispatchFragmentsOnCreateView(view, str, context, attributeSet);
        return dispatchFragmentsOnCreateView == null ? super.onCreateView(view, str, context, attributeSet) : dispatchFragmentsOnCreateView;
    }

    public void onDestroy() {
        super.onDestroy();
        this.mFragments.f2000a.f2018d.o();
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_DESTROY);
    }

    public void onLowMemory() {
        super.onLowMemory();
        this.mFragments.f2000a.f2018d.p();
    }

    public boolean onMenuItemSelected(int i10, @NonNull MenuItem menuItem) {
        if (super.onMenuItemSelected(i10, menuItem)) {
            return true;
        }
        if (i10 == 0) {
            return this.mFragments.f2000a.f2018d.r(menuItem);
        }
        if (i10 != 6) {
            return false;
        }
        return this.mFragments.f2000a.f2018d.l(menuItem);
    }

    @CallSuper
    public void onMultiWindowModeChanged(boolean z10) {
        this.mFragments.f2000a.f2018d.q(z10);
    }

    @CallSuper
    public void onNewIntent(@SuppressLint({"UnknownNullness"}) Intent intent) {
        super.onNewIntent(intent);
        this.mFragments.a();
    }

    public void onPanelClosed(int i10, @NonNull Menu menu) {
        if (i10 == 0) {
            this.mFragments.f2000a.f2018d.s(menu);
        }
        super.onPanelClosed(i10, menu);
    }

    public void onPause() {
        super.onPause();
        this.mResumed = false;
        this.mFragments.f2000a.f2018d.w(5);
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_PAUSE);
    }

    @CallSuper
    public void onPictureInPictureModeChanged(boolean z10) {
        this.mFragments.f2000a.f2018d.u(z10);
    }

    public void onPostResume() {
        super.onPostResume();
        onResumeFragments();
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    @Deprecated
    public boolean onPrepareOptionsPanel(@Nullable View view, @NonNull Menu menu) {
        return super.onPreparePanel(0, view, menu);
    }

    public boolean onPreparePanel(int i10, @Nullable View view, @NonNull Menu menu) {
        if (i10 == 0) {
            return onPrepareOptionsPanel(view, menu) | this.mFragments.f2000a.f2018d.v(menu);
        }
        return super.onPreparePanel(i10, view, menu);
    }

    @Override // androidx.activity.ComponentActivity
    @CallSuper
    public void onRequestPermissionsResult(int i10, @NonNull String[] strArr, @NonNull int[] iArr) {
        this.mFragments.a();
        super.onRequestPermissionsResult(i10, strArr, iArr);
    }

    public void onResume() {
        super.onResume();
        this.mResumed = true;
        this.mFragments.a();
        this.mFragments.f2000a.f2018d.C(true);
    }

    public void onResumeFragments() {
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_RESUME);
        FragmentManager fragmentManager = this.mFragments.f2000a.f2018d;
        fragmentManager.B = false;
        fragmentManager.C = false;
        fragmentManager.J.h = false;
        fragmentManager.w(7);
    }

    public void onStart() {
        super.onStart();
        this.mStopped = false;
        if (!this.mCreated) {
            this.mCreated = true;
            FragmentManager fragmentManager = this.mFragments.f2000a.f2018d;
            fragmentManager.B = false;
            fragmentManager.C = false;
            fragmentManager.J.h = false;
            fragmentManager.w(4);
        }
        this.mFragments.a();
        this.mFragments.f2000a.f2018d.C(true);
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_START);
        FragmentManager fragmentManager2 = this.mFragments.f2000a.f2018d;
        fragmentManager2.B = false;
        fragmentManager2.C = false;
        fragmentManager2.J.h = false;
        fragmentManager2.w(5);
    }

    public void onStateNotSaved() {
        this.mFragments.a();
    }

    public void onStop() {
        super.onStop();
        this.mStopped = true;
        markFragmentsCreated();
        FragmentManager fragmentManager = this.mFragments.f2000a.f2018d;
        fragmentManager.C = true;
        fragmentManager.J.h = true;
        fragmentManager.w(4);
        this.mFragmentLifecycleRegistry.e(Lifecycle.Event.ON_STOP);
    }

    public void setEnterSharedElementCallback(@Nullable f fVar) {
        int i10 = y.a.f10932b;
        setEnterSharedElementCallback((SharedElementCallback) null);
    }

    public void setExitSharedElementCallback(@Nullable f fVar) {
        int i10 = y.a.f10932b;
        setExitSharedElementCallback((SharedElementCallback) null);
    }

    public void startActivityFromFragment(@NonNull Fragment fragment, @SuppressLint({"UnknownNullness"}) Intent intent, int i10) {
        startActivityFromFragment(fragment, intent, i10, null);
    }

    @Deprecated
    public void startIntentSenderFromFragment(@NonNull Fragment fragment, @SuppressLint({"UnknownNullness"}) IntentSender intentSender, int i10, @Nullable Intent intent, int i11, int i12, int i13, @Nullable Bundle bundle) throws IntentSender.SendIntentException {
        Intent intent2 = intent;
        if (i10 == -1) {
            int i14 = y.a.f10932b;
            startIntentSenderForResult(intentSender, i10, intent, i11, i12, i13, bundle);
        } else if (fragment.f1743s != null) {
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "Fragment " + fragment + " received the following in startIntentSenderForResult() requestCode: " + i10 + " IntentSender: " + intentSender + " fillInIntent: " + intent2 + " options: " + bundle);
            }
            FragmentManager m10 = fragment.m();
            if (m10.f1795x != null) {
                if (bundle != null) {
                    if (intent2 == null) {
                        intent2 = new Intent();
                        intent2.putExtra("androidx.fragment.extra.ACTIVITY_OPTIONS_BUNDLE", true);
                    }
                    if (FragmentManager.O(2)) {
                        Log.v("FragmentManager", "ActivityOptions " + bundle + " were added to fillInIntent " + intent2 + " for fragment " + fragment);
                    }
                    intent2.putExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE", bundle);
                }
                IntentSenderRequest intentSenderRequest = new IntentSenderRequest(intentSender, intent2, i11, i12);
                m10.f1797z.addLast(new FragmentManager.LaunchedFragmentInfo(fragment.f1724e, i10));
                if (FragmentManager.O(2)) {
                    Log.v("FragmentManager", "Fragment " + fragment + "is launching an IntentSender for result ");
                }
                m10.f1795x.a(intentSenderRequest, null);
                return;
            }
            v<?> vVar = m10.f1788q;
            Objects.requireNonNull(vVar);
            if (i10 == -1) {
                Activity activity = vVar.f2015a;
                int i15 = y.a.f10932b;
                activity.startIntentSenderForResult(intentSender, i10, intent, i11, i12, i13, bundle);
                return;
            }
            throw new IllegalStateException("Starting intent sender with a requestCode requires a FragmentActivity host");
        } else {
            throw new IllegalStateException(m.b("Fragment ", fragment, " not attached to Activity"));
        }
    }

    public void supportFinishAfterTransition() {
        int i10 = y.a.f10932b;
        finishAfterTransition();
    }

    @Deprecated
    public void supportInvalidateOptionsMenu() {
        invalidateOptionsMenu();
    }

    public void supportPostponeEnterTransition() {
        int i10 = y.a.f10932b;
        postponeEnterTransition();
    }

    public void supportStartPostponedEnterTransition() {
        int i10 = y.a.f10932b;
        startPostponedEnterTransition();
    }

    @Override // y.a.AbstractC0184a
    @Deprecated
    public final void validateRequestPermissionsRequestCode(int i10) {
    }

    public void startActivityFromFragment(@NonNull Fragment fragment, @SuppressLint({"UnknownNullness"}) Intent intent, int i10, @Nullable Bundle bundle) {
        if (i10 == -1) {
            int i11 = y.a.f10932b;
            startActivityForResult(intent, -1, bundle);
        } else if (fragment.f1743s != null) {
            FragmentManager m10 = fragment.m();
            if (m10.f1794w != null) {
                m10.f1797z.addLast(new FragmentManager.LaunchedFragmentInfo(fragment.f1724e, i10));
                if (!(intent == null || bundle == null)) {
                    intent.putExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE", bundle);
                }
                m10.f1794w.a(intent, null);
                return;
            }
            v<?> vVar = m10.f1788q;
            Objects.requireNonNull(vVar);
            if (i10 == -1) {
                Context context = vVar.f2016b;
                Object obj = z.a.f11008a;
                context.startActivity(intent, bundle);
                return;
            }
            throw new IllegalStateException("Starting activity with a requestCode requires a FragmentActivity host");
        } else {
            throw new IllegalStateException(m.b("Fragment ", fragment, " not attached to Activity"));
        }
    }

    @Nullable
    public View onCreateView(@NonNull String str, @NonNull Context context, @NonNull AttributeSet attributeSet) {
        View dispatchFragmentsOnCreateView = dispatchFragmentsOnCreateView(null, str, context, attributeSet);
        return dispatchFragmentsOnCreateView == null ? super.onCreateView(str, context, attributeSet) : dispatchFragmentsOnCreateView;
    }

    @ContentView
    public n(@LayoutRes int i10) {
        super(i10);
        init();
    }
}
