package androidx.appcompat.app;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$bool;
import androidx.appcompat.R$id;
import androidx.appcompat.R$styleable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.widget.ActionBarContainer;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import androidx.appcompat.widget.ActionMenuPresenter;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.t;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import j0.m;
import j0.n;
import j0.o;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.WeakHashMap;
import p.a;
import p.g;
import p.h;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: WindowDecorActionBar */
public class p extends ActionBar implements ActionBarOverlayLayout.d {
    public static final Interpolator A = new AccelerateInterpolator();
    public static final Interpolator B = new DecelerateInterpolator();

    /* renamed from: a  reason: collision with root package name */
    public Context f482a;

    /* renamed from: b  reason: collision with root package name */
    public Context f483b;

    /* renamed from: c  reason: collision with root package name */
    public ActionBarOverlayLayout f484c;

    /* renamed from: d  reason: collision with root package name */
    public ActionBarContainer f485d;

    /* renamed from: e  reason: collision with root package name */
    public t f486e;

    /* renamed from: f  reason: collision with root package name */
    public ActionBarContextView f487f;

    /* renamed from: g  reason: collision with root package name */
    public View f488g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public d f489i;

    /* renamed from: j  reason: collision with root package name */
    public p.a f490j;

    /* renamed from: k  reason: collision with root package name */
    public a.AbstractC0146a f491k;

    /* renamed from: l  reason: collision with root package name */
    public boolean f492l;

    /* renamed from: m  reason: collision with root package name */
    public ArrayList<ActionBar.a> f493m = new ArrayList<>();

    /* renamed from: n  reason: collision with root package name */
    public boolean f494n;

    /* renamed from: o  reason: collision with root package name */
    public int f495o = 0;

    /* renamed from: p  reason: collision with root package name */
    public boolean f496p = true;

    /* renamed from: q  reason: collision with root package name */
    public boolean f497q;

    /* renamed from: r  reason: collision with root package name */
    public boolean f498r;

    /* renamed from: s  reason: collision with root package name */
    public boolean f499s;

    /* renamed from: t  reason: collision with root package name */
    public boolean f500t = true;

    /* renamed from: u  reason: collision with root package name */
    public h f501u;

    /* renamed from: v  reason: collision with root package name */
    public boolean f502v;

    /* renamed from: w  reason: collision with root package name */
    public boolean f503w;

    /* renamed from: x  reason: collision with root package name */
    public final n f504x = new a();

    /* renamed from: y  reason: collision with root package name */
    public final n f505y = new b();

    /* renamed from: z  reason: collision with root package name */
    public final j0.p f506z = new c();

    /* compiled from: WindowDecorActionBar */
    public class a extends o {
        public a() {
        }

        @Override // j0.n
        public void d(View view) {
            View view2;
            p pVar = p.this;
            if (pVar.f496p && (view2 = pVar.f488g) != null) {
                view2.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                p.this.f485d.setTranslationY(Constant.VOLUME_FLOAT_MIN);
            }
            p.this.f485d.setVisibility(8);
            p.this.f485d.setTransitioning(false);
            p pVar2 = p.this;
            pVar2.f501u = null;
            a.AbstractC0146a aVar = pVar2.f491k;
            if (aVar != null) {
                aVar.c(pVar2.f490j);
                pVar2.f490j = null;
                pVar2.f491k = null;
            }
            ActionBarOverlayLayout actionBarOverlayLayout = p.this.f484c;
            if (actionBarOverlayLayout != null) {
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                actionBarOverlayLayout.requestApplyInsets();
            }
        }
    }

    /* compiled from: WindowDecorActionBar */
    public class b extends o {
        public b() {
        }

        @Override // j0.n
        public void d(View view) {
            p pVar = p.this;
            pVar.f501u = null;
            pVar.f485d.requestLayout();
        }
    }

    /* compiled from: WindowDecorActionBar */
    public class c implements j0.p {
        public c() {
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    /* compiled from: WindowDecorActionBar */
    public class d extends p.a implements d.a {

        /* renamed from: c  reason: collision with root package name */
        public final Context f510c;

        /* renamed from: d  reason: collision with root package name */
        public final androidx.appcompat.view.menu.d f511d;

        /* renamed from: e  reason: collision with root package name */
        public a.AbstractC0146a f512e;

        /* renamed from: f  reason: collision with root package name */
        public WeakReference<View> f513f;

        public d(Context context, a.AbstractC0146a aVar) {
            this.f510c = context;
            this.f512e = aVar;
            androidx.appcompat.view.menu.d dVar = new androidx.appcompat.view.menu.d(context);
            dVar.f614l = 1;
            this.f511d = dVar;
            dVar.f608e = this;
        }

        @Override // androidx.appcompat.view.menu.d.a
        public boolean a(@NonNull androidx.appcompat.view.menu.d dVar, @NonNull MenuItem menuItem) {
            a.AbstractC0146a aVar = this.f512e;
            if (aVar != null) {
                return aVar.d(this, menuItem);
            }
            return false;
        }

        @Override // androidx.appcompat.view.menu.d.a
        public void b(@NonNull androidx.appcompat.view.menu.d dVar) {
            if (this.f512e != null) {
                i();
                ActionMenuPresenter actionMenuPresenter = p.this.f487f.f1051d;
                if (actionMenuPresenter != null) {
                    actionMenuPresenter.p();
                }
            }
        }

        @Override // p.a
        public void c() {
            p pVar = p.this;
            if (pVar.f489i == this) {
                boolean z10 = pVar.f497q;
                boolean z11 = pVar.f498r;
                boolean z12 = true;
                if (z10 || z11) {
                    z12 = false;
                }
                if (!z12) {
                    pVar.f490j = this;
                    pVar.f491k = this.f512e;
                } else {
                    this.f512e.c(this);
                }
                this.f512e = null;
                p.this.q(false);
                ActionBarContextView actionBarContextView = p.this.f487f;
                if (actionBarContextView.f702k == null) {
                    actionBarContextView.h();
                }
                p.this.f486e.k().sendAccessibilityEvent(32);
                p pVar2 = p.this;
                pVar2.f484c.setHideOnContentScrollEnabled(pVar2.f503w);
                p.this.f489i = null;
            }
        }

        @Override // p.a
        public View d() {
            WeakReference<View> weakReference = this.f513f;
            if (weakReference != null) {
                return weakReference.get();
            }
            return null;
        }

        @Override // p.a
        public Menu e() {
            return this.f511d;
        }

        @Override // p.a
        public MenuInflater f() {
            return new g(this.f510c);
        }

        @Override // p.a
        public CharSequence g() {
            return p.this.f487f.getSubtitle();
        }

        @Override // p.a
        public CharSequence h() {
            return p.this.f487f.getTitle();
        }

        @Override // p.a
        public void i() {
            if (p.this.f489i == this) {
                this.f511d.A();
                try {
                    this.f512e.b(this, this.f511d);
                } finally {
                    this.f511d.z();
                }
            }
        }

        @Override // p.a
        public boolean j() {
            return p.this.f487f.f709r;
        }

        @Override // p.a
        public void k(View view) {
            p.this.f487f.setCustomView(view);
            this.f513f = new WeakReference<>(view);
        }

        @Override // p.a
        public void l(int i10) {
            p.this.f487f.setSubtitle(p.this.f482a.getResources().getString(i10));
        }

        @Override // p.a
        public void m(CharSequence charSequence) {
            p.this.f487f.setSubtitle(charSequence);
        }

        @Override // p.a
        public void n(int i10) {
            p.this.f487f.setTitle(p.this.f482a.getResources().getString(i10));
        }

        @Override // p.a
        public void o(CharSequence charSequence) {
            p.this.f487f.setTitle(charSequence);
        }

        @Override // p.a
        public void p(boolean z10) {
            this.f9494b = z10;
            p.this.f487f.setTitleOptional(z10);
        }
    }

    public p(Activity activity, boolean z10) {
        new ArrayList();
        View decorView = activity.getWindow().getDecorView();
        r(decorView);
        if (!z10) {
            this.f488g = decorView.findViewById(16908290);
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean b() {
        t tVar = this.f486e;
        if (tVar == null || !tVar.m()) {
            return false;
        }
        this.f486e.collapseActionView();
        return true;
    }

    @Override // androidx.appcompat.app.ActionBar
    public void c(boolean z10) {
        if (z10 != this.f492l) {
            this.f492l = z10;
            int size = this.f493m.size();
            for (int i10 = 0; i10 < size; i10++) {
                this.f493m.get(i10).a(z10);
            }
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public int d() {
        return this.f486e.o();
    }

    @Override // androidx.appcompat.app.ActionBar
    public Context e() {
        if (this.f483b == null) {
            TypedValue typedValue = new TypedValue();
            this.f482a.getTheme().resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
            int i10 = typedValue.resourceId;
            if (i10 != 0) {
                this.f483b = new ContextThemeWrapper(this.f482a, i10);
            } else {
                this.f483b = this.f482a;
            }
        }
        return this.f483b;
    }

    @Override // androidx.appcompat.app.ActionBar
    public void f() {
        if (!this.f497q) {
            this.f497q = true;
            t(false);
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public void h(Configuration configuration) {
        s(this.f482a.getResources().getBoolean(R$bool.abc_action_bar_embed_tabs));
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean j(int i10, KeyEvent keyEvent) {
        androidx.appcompat.view.menu.d dVar;
        d dVar2 = this.f489i;
        if (dVar2 == null || (dVar = dVar2.f511d) == null) {
            return false;
        }
        boolean z10 = true;
        if (KeyCharacterMap.load(keyEvent != null ? keyEvent.getDeviceId() : -1).getKeyboardType() == 1) {
            z10 = false;
        }
        dVar.setQwertyMode(z10);
        return dVar.performShortcut(i10, keyEvent, 0);
    }

    @Override // androidx.appcompat.app.ActionBar
    public void m(boolean z10) {
        if (!this.h) {
            int i10 = z10 ? 4 : 0;
            int o3 = this.f486e.o();
            this.h = true;
            this.f486e.n((i10 & 4) | (o3 & -5));
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public void n(boolean z10) {
        h hVar;
        this.f502v = z10;
        if (!z10 && (hVar = this.f501u) != null) {
            hVar.a();
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public void o(CharSequence charSequence) {
        this.f486e.setWindowTitle(charSequence);
    }

    @Override // androidx.appcompat.app.ActionBar
    public p.a p(a.AbstractC0146a aVar) {
        d dVar = this.f489i;
        if (dVar != null) {
            dVar.c();
        }
        this.f484c.setHideOnContentScrollEnabled(false);
        this.f487f.h();
        d dVar2 = new d(this.f487f.getContext(), aVar);
        dVar2.f511d.A();
        try {
            if (!dVar2.f512e.a(dVar2, dVar2.f511d)) {
                return null;
            }
            this.f489i = dVar2;
            dVar2.i();
            this.f487f.f(dVar2);
            q(true);
            this.f487f.sendAccessibilityEvent(32);
            return dVar2;
        } finally {
            dVar2.f511d.z();
        }
    }

    public void q(boolean z10) {
        m mVar;
        m mVar2;
        if (z10) {
            if (!this.f499s) {
                this.f499s = true;
                ActionBarOverlayLayout actionBarOverlayLayout = this.f484c;
                if (actionBarOverlayLayout != null) {
                    actionBarOverlayLayout.setShowingForActionMode(true);
                }
                t(false);
            }
        } else if (this.f499s) {
            this.f499s = false;
            ActionBarOverlayLayout actionBarOverlayLayout2 = this.f484c;
            if (actionBarOverlayLayout2 != null) {
                actionBarOverlayLayout2.setShowingForActionMode(false);
            }
            t(false);
        }
        ActionBarContainer actionBarContainer = this.f485d;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        if (actionBarContainer.isLaidOut()) {
            if (z10) {
                mVar = this.f486e.s(4, 100);
                mVar2 = this.f487f.e(0, 200);
            } else {
                mVar2 = this.f486e.s(0, 200);
                mVar = this.f487f.e(8, 100);
            }
            h hVar = new h();
            hVar.f9545a.add(mVar);
            View view = mVar.f6912a.get();
            long duration = view != null ? view.animate().getDuration() : 0;
            View view2 = mVar2.f6912a.get();
            if (view2 != null) {
                view2.animate().setStartDelay(duration);
            }
            hVar.f9545a.add(mVar2);
            hVar.b();
        } else if (z10) {
            this.f486e.setVisibility(4);
            this.f487f.setVisibility(0);
        } else {
            this.f486e.setVisibility(0);
            this.f487f.setVisibility(8);
        }
    }

    public final void r(View view) {
        t tVar;
        ActionBarOverlayLayout actionBarOverlayLayout = (ActionBarOverlayLayout) view.findViewById(R$id.decor_content_parent);
        this.f484c = actionBarOverlayLayout;
        if (actionBarOverlayLayout != null) {
            actionBarOverlayLayout.setActionBarVisibilityCallback(this);
        }
        View findViewById = view.findViewById(R$id.action_bar);
        if (findViewById instanceof t) {
            tVar = (t) findViewById;
        } else if (findViewById instanceof Toolbar) {
            tVar = ((Toolbar) findViewById).getWrapper();
        } else {
            StringBuilder a10 = f.a("Can't make a decor toolbar out of ");
            a10.append(findViewById != null ? findViewById.getClass().getSimpleName() : "null");
            throw new IllegalStateException(a10.toString());
        }
        this.f486e = tVar;
        this.f487f = (ActionBarContextView) view.findViewById(R$id.action_context_bar);
        ActionBarContainer actionBarContainer = (ActionBarContainer) view.findViewById(R$id.action_bar_container);
        this.f485d = actionBarContainer;
        t tVar2 = this.f486e;
        if (tVar2 == null || this.f487f == null || actionBarContainer == null) {
            throw new IllegalStateException(p.class.getSimpleName() + " can only be used with a compatible window decor layout");
        }
        this.f482a = tVar2.getContext();
        boolean z10 = (this.f486e.o() & 4) != 0;
        if (z10) {
            this.h = true;
        }
        Context context = this.f482a;
        this.f486e.l((context.getApplicationInfo().targetSdkVersion < 14) || z10);
        s(context.getResources().getBoolean(R$bool.abc_action_bar_embed_tabs));
        TypedArray obtainStyledAttributes = this.f482a.obtainStyledAttributes(null, R$styleable.ActionBar, R$attr.actionBarStyle, 0);
        if (obtainStyledAttributes.getBoolean(R$styleable.ActionBar_hideOnContentScroll, false)) {
            ActionBarOverlayLayout actionBarOverlayLayout2 = this.f484c;
            if (actionBarOverlayLayout2.h) {
                this.f503w = true;
                actionBarOverlayLayout2.setHideOnContentScrollEnabled(true);
            } else {
                throw new IllegalStateException("Action bar must be in overlay mode (Window.FEATURE_OVERLAY_ACTION_BAR) to enable hide on content scroll");
            }
        }
        int dimensionPixelSize = obtainStyledAttributes.getDimensionPixelSize(R$styleable.ActionBar_elevation, 0);
        if (dimensionPixelSize != 0) {
            ActionBarContainer actionBarContainer2 = this.f485d;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            actionBarContainer2.setElevation((float) dimensionPixelSize);
        }
        obtainStyledAttributes.recycle();
    }

    public final void s(boolean z10) {
        this.f494n = z10;
        if (!z10) {
            this.f486e.j(null);
            this.f485d.setTabContainer(null);
        } else {
            this.f485d.setTabContainer(null);
            this.f486e.j(null);
        }
        boolean z11 = true;
        boolean z12 = this.f486e.r() == 2;
        this.f486e.v(!this.f494n && z12);
        ActionBarOverlayLayout actionBarOverlayLayout = this.f484c;
        if (this.f494n || !z12) {
            z11 = false;
        }
        actionBarOverlayLayout.setHasNonEmbeddedTabs(z11);
    }

    public final void t(boolean z10) {
        View view;
        View view2;
        View view3;
        if (this.f499s || (!this.f497q && !this.f498r)) {
            if (!this.f500t) {
                this.f500t = true;
                h hVar = this.f501u;
                if (hVar != null) {
                    hVar.a();
                }
                this.f485d.setVisibility(0);
                if (this.f495o != 0 || (!this.f502v && !z10)) {
                    this.f485d.setAlpha(1.0f);
                    this.f485d.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                    if (this.f496p && (view2 = this.f488g) != null) {
                        view2.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                    }
                    this.f505y.d(null);
                } else {
                    this.f485d.setTranslationY(Constant.VOLUME_FLOAT_MIN);
                    float f10 = (float) (-this.f485d.getHeight());
                    if (z10) {
                        int[] iArr = {0, 0};
                        this.f485d.getLocationInWindow(iArr);
                        f10 -= (float) iArr[1];
                    }
                    this.f485d.setTranslationY(f10);
                    h hVar2 = new h();
                    m a10 = ViewCompat.a(this.f485d);
                    a10.g(Constant.VOLUME_FLOAT_MIN);
                    a10.f(this.f506z);
                    if (!hVar2.f9549e) {
                        hVar2.f9545a.add(a10);
                    }
                    if (this.f496p && (view3 = this.f488g) != null) {
                        view3.setTranslationY(f10);
                        m a11 = ViewCompat.a(this.f488g);
                        a11.g(Constant.VOLUME_FLOAT_MIN);
                        if (!hVar2.f9549e) {
                            hVar2.f9545a.add(a11);
                        }
                    }
                    Interpolator interpolator = B;
                    boolean z11 = hVar2.f9549e;
                    if (!z11) {
                        hVar2.f9547c = interpolator;
                    }
                    if (!z11) {
                        hVar2.f9546b = 250;
                    }
                    n nVar = this.f505y;
                    if (!z11) {
                        hVar2.f9548d = nVar;
                    }
                    this.f501u = hVar2;
                    hVar2.b();
                }
                ActionBarOverlayLayout actionBarOverlayLayout = this.f484c;
                if (actionBarOverlayLayout != null) {
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    actionBarOverlayLayout.requestApplyInsets();
                }
            }
        } else if (this.f500t) {
            this.f500t = false;
            h hVar3 = this.f501u;
            if (hVar3 != null) {
                hVar3.a();
            }
            if (this.f495o != 0 || (!this.f502v && !z10)) {
                this.f504x.d(null);
                return;
            }
            this.f485d.setAlpha(1.0f);
            this.f485d.setTransitioning(true);
            h hVar4 = new h();
            float f11 = (float) (-this.f485d.getHeight());
            if (z10) {
                int[] iArr2 = {0, 0};
                this.f485d.getLocationInWindow(iArr2);
                f11 -= (float) iArr2[1];
            }
            m a12 = ViewCompat.a(this.f485d);
            a12.g(f11);
            a12.f(this.f506z);
            if (!hVar4.f9549e) {
                hVar4.f9545a.add(a12);
            }
            if (this.f496p && (view = this.f488g) != null) {
                m a13 = ViewCompat.a(view);
                a13.g(f11);
                if (!hVar4.f9549e) {
                    hVar4.f9545a.add(a13);
                }
            }
            Interpolator interpolator2 = A;
            boolean z12 = hVar4.f9549e;
            if (!z12) {
                hVar4.f9547c = interpolator2;
            }
            if (!z12) {
                hVar4.f9546b = 250;
            }
            n nVar2 = this.f504x;
            if (!z12) {
                hVar4.f9548d = nVar2;
            }
            this.f501u = hVar4;
            hVar4.b();
        }
    }

    public p(Dialog dialog) {
        new ArrayList();
        r(dialog.getWindow().getDecorView());
    }
}
