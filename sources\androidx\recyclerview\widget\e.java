package androidx.recyclerview.widget;

import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.l;
import com.duokan.airkan.common.Constant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class e implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ArrayList f2361a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ l f2362b;

    public e(l lVar, ArrayList arrayList) {
        this.f2362b = lVar;
        this.f2361a = arrayList;
    }

    public void run() {
        View view;
        Iterator it = this.f2361a.iterator();
        while (it.hasNext()) {
            l.a aVar = (l.a) it.next();
            l lVar = this.f2362b;
            Objects.requireNonNull(lVar);
            RecyclerView.w wVar = aVar.f2398a;
            View view2 = null;
            if (wVar == null) {
                view = null;
            } else {
                view = wVar.f2267a;
            }
            RecyclerView.w wVar2 = aVar.f2399b;
            if (wVar2 != null) {
                view2 = wVar2.f2267a;
            }
            if (view != null) {
                ViewPropertyAnimator duration = view.animate().setDuration(lVar.f2185f);
                lVar.f2397r.add(aVar.f2398a);
                duration.translationX((float) (aVar.f2402e - aVar.f2400c));
                duration.translationY((float) (aVar.f2403f - aVar.f2401d));
                duration.alpha(Constant.VOLUME_FLOAT_MIN).setListener(new j(lVar, aVar, duration, view)).start();
            }
            if (view2 != null) {
                ViewPropertyAnimator animate = view2.animate();
                lVar.f2397r.add(aVar.f2399b);
                animate.translationX(Constant.VOLUME_FLOAT_MIN).translationY(Constant.VOLUME_FLOAT_MIN).setDuration(lVar.f2185f).alpha(1.0f).setListener(new k(lVar, aVar, animate, view2)).start();
            }
        }
        this.f2361a.clear();
        this.f2362b.f2393n.remove(this.f2361a);
    }
}
