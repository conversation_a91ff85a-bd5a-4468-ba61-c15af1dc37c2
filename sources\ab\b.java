package ab;

import bb.c;
import bb.g;
import java.math.BigInteger;

/* compiled from: ECNamedCurveParameterSpec */
public class b extends d {

    /* renamed from: f  reason: collision with root package name */
    public String f248f;

    public b(String str, c cVar, g gVar, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr) {
        super(cVar, gVar, bigInteger, bigInteger2, bArr);
        this.f248f = str;
    }
}
