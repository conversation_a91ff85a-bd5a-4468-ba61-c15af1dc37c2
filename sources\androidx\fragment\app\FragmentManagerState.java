package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.fragment.app.FragmentManager;
import java.util.ArrayList;

/* access modifiers changed from: package-private */
@SuppressLint({"BanParcelableUsage"})
public final class FragmentManagerState implements Parcelable {
    public static final Parcelable.Creator<FragmentManagerState> CREATOR = new a();

    /* renamed from: a  reason: collision with root package name */
    public ArrayList<FragmentState> f1814a;

    /* renamed from: b  reason: collision with root package name */
    public ArrayList<String> f1815b;

    /* renamed from: c  reason: collision with root package name */
    public BackStackState[] f1816c;

    /* renamed from: d  reason: collision with root package name */
    public int f1817d;

    /* renamed from: e  reason: collision with root package name */
    public String f1818e = null;

    /* renamed from: f  reason: collision with root package name */
    public ArrayList<String> f1819f = new ArrayList<>();

    /* renamed from: g  reason: collision with root package name */
    public ArrayList<Bundle> f1820g = new ArrayList<>();
    public ArrayList<FragmentManager.LaunchedFragmentInfo> h;

    public class a implements Parcelable.Creator<FragmentManagerState> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // android.os.Parcelable.Creator
        public FragmentManagerState createFromParcel(Parcel parcel) {
            return new FragmentManagerState(parcel);
        }

        /* Return type fixed from 'java.lang.Object[]' to match base method */
        @Override // android.os.Parcelable.Creator
        public FragmentManagerState[] newArray(int i10) {
            return new FragmentManagerState[i10];
        }
    }

    public FragmentManagerState() {
    }

    public int describeContents() {
        return 0;
    }

    public void writeToParcel(Parcel parcel, int i10) {
        parcel.writeTypedList(this.f1814a);
        parcel.writeStringList(this.f1815b);
        parcel.writeTypedArray(this.f1816c, i10);
        parcel.writeInt(this.f1817d);
        parcel.writeString(this.f1818e);
        parcel.writeStringList(this.f1819f);
        parcel.writeTypedList(this.f1820g);
        parcel.writeTypedList(this.h);
    }

    public FragmentManagerState(Parcel parcel) {
        this.f1814a = parcel.createTypedArrayList(FragmentState.CREATOR);
        this.f1815b = parcel.createStringArrayList();
        this.f1816c = (BackStackState[]) parcel.createTypedArray(BackStackState.CREATOR);
        this.f1817d = parcel.readInt();
        this.f1818e = parcel.readString();
        this.f1819f = parcel.createStringArrayList();
        this.f1820g = parcel.createTypedArrayList(Bundle.CREATOR);
        this.h = parcel.createTypedArrayList(FragmentManager.LaunchedFragmentInfo.CREATOR);
    }
}
