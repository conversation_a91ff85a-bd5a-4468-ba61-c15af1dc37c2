package aa;

import java.io.IOException;
import java.io.OutputStream;

/* compiled from: DLOutputStream */
public class l1 extends p {
    public l1(OutputStream outputStream) {
        super(outputStream);
    }

    @Override // aa.p
    public void h(e eVar) throws IOException {
        if (eVar != null) {
            eVar.c().m().h(this);
            return;
        }
        throw new IOException("null object detected");
    }
}
