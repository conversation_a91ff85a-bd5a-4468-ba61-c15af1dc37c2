package androidx.appcompat.app;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresPermission;
import androidx.annotation.VisibleForTesting;

/* compiled from: TwilightManager */
public class o {

    /* renamed from: d  reason: collision with root package name */
    public static o f476d;

    /* renamed from: a  reason: collision with root package name */
    public final Context f477a;

    /* renamed from: b  reason: collision with root package name */
    public final LocationManager f478b;

    /* renamed from: c  reason: collision with root package name */
    public final a f479c = new a();

    /* compiled from: TwilightManager */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public boolean f480a;

        /* renamed from: b  reason: collision with root package name */
        public long f481b;
    }

    @VisibleForTesting
    public o(@NonNull Context context, @NonNull LocationManager locationManager) {
        this.f477a = context;
        this.f478b = locationManager;
    }

    @RequiresPermission(anyOf = {"android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"})
    public final Location a(String str) {
        try {
            if (this.f478b.isProviderEnabled(str)) {
                return this.f478b.getLastKnownLocation(str);
            }
            return null;
        } catch (Exception e10) {
            Log.d("TwilightManager", "Failed to get last known location", e10);
            return null;
        }
    }
}
