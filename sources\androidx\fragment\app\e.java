package androidx.fragment.app;

import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import androidx.fragment.app.b;

/* compiled from: DefaultSpecialEffectsController */
public class e implements Animation.AnimationListener {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ViewGroup f1887a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ View f1888b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ b.C0013b f1889c;

    /* compiled from: DefaultSpecialEffectsController */
    public class a implements Runnable {
        public a() {
        }

        public void run() {
            e eVar = e.this;
            eVar.f1887a.endViewTransition(eVar.f1888b);
            e.this.f1889c.a();
        }
    }

    public e(b bVar, ViewGroup viewGroup, View view, b.C0013b bVar2) {
        this.f1887a = viewGroup;
        this.f1888b = view;
        this.f1889c = bVar2;
    }

    public void onAnimationEnd(Animation animation) {
        this.f1887a.post(new a());
    }

    public void onAnimationRepeat(Animation animation) {
    }

    public void onAnimationStart(Animation animation) {
    }
}
