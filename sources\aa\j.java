package aa;

import com.duokan.airkan.server.f;
import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import java.io.IOException;
import java.math.BigInteger;
import mb.a;

/* compiled from: ASN1Integer */
public class j extends q {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f190a;

    public j(long j10) {
        this.f190a = BigInteger.valueOf(j10).toByteArray();
    }

    public static j n(Object obj) {
        if (obj == null || (obj instanceof j)) {
            return (j) obj;
        }
        if (obj instanceof byte[]) {
            try {
                return (j) q.j((byte[]) obj);
            } catch (Exception e10) {
                StringBuilder a10 = f.a("encoding error in getInstance: ");
                a10.append(e10.toString());
                throw new IllegalArgumentException(a10.toString());
            }
        } else {
            StringBuilder a11 = f.a("illegal object in getInstance: ");
            a11.append(obj.getClass().getName());
            throw new IllegalArgumentException(a11.toString());
        }
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof j)) {
            return false;
        }
        return a.a(this.f190a, ((j) qVar).f190a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(2, this.f190a);
    }

    @Override // aa.l
    public int hashCode() {
        int i10 = 0;
        int i11 = 0;
        while (true) {
            byte[] bArr = this.f190a;
            if (i10 == bArr.length) {
                return i11;
            }
            i11 ^= (bArr[i10] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP) << (i10 % 4);
            i10++;
        }
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f190a.length) + 1 + this.f190a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public BigInteger o() {
        return new BigInteger(1, this.f190a);
    }

    public BigInteger p() {
        return new BigInteger(this.f190a);
    }

    public String toString() {
        return p().toString();
    }

    public j(BigInteger bigInteger) {
        this.f190a = bigInteger.toByteArray();
    }

    public j(byte[] bArr, boolean z10) {
        this.f190a = z10 ? a.c(bArr) : bArr;
    }
}
