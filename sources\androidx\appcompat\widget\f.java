package androidx.appcompat.widget;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.widget.CompoundButton;
import androidx.annotation.NonNull;

/* compiled from: AppCompatCompoundButtonHelper */
public class f {
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final CompoundButton f1085a;

    /* renamed from: b  reason: collision with root package name */
    public ColorStateList f1086b = null;

    /* renamed from: c  reason: collision with root package name */
    public PorterDuff.Mode f1087c = null;

    /* renamed from: d  reason: collision with root package name */
    public boolean f1088d = false;

    /* renamed from: e  reason: collision with root package name */
    public boolean f1089e = false;

    /* renamed from: f  reason: collision with root package name */
    public boolean f1090f;

    public f(@NonNull CompoundButton compoundButton) {
        this.f1085a = compoundButton;
    }

    public void a() {
        Drawable buttonDrawable = this.f1085a.getButtonDrawable();
        if (buttonDrawable == null) {
            return;
        }
        if (this.f1088d || this.f1089e) {
            Drawable mutate = buttonDrawable.mutate();
            if (this.f1088d) {
                mutate.setTintList(this.f1086b);
            }
            if (this.f1089e) {
                mutate.setTintMode(this.f1087c);
            }
            if (mutate.isStateful()) {
                mutate.setState(this.f1085a.getDrawableState());
            }
            this.f1085a.setButtonDrawable(mutate);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x005e  */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x0072  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void b(@androidx.annotation.Nullable android.util.AttributeSet r10, int r11) {
        /*
        // Method dump skipped, instructions count: 141
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.f.b(android.util.AttributeSet, int):void");
    }
}
