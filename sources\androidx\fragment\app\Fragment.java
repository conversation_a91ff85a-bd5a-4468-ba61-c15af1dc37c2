package androidx.fragment.app;

import android.animation.Animator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ComponentCallbacks;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.ContextMenu;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.CallSuper;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.annotation.UiThread;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.g;
import androidx.lifecycle.h;
import androidx.lifecycle.l;
import androidx.lifecycle.runtime.R$id;
import androidx.lifecycle.w;
import androidx.lifecycle.x;
import com.duokan.airkan.server.f;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import o0.b;

public class Fragment implements ComponentCallbacks, View.OnCreateContextMenuListener, g, x, androidx.savedstate.c {
    public static final Object H0 = new Object();
    public boolean A0;
    public Lifecycle.State B0 = Lifecycle.State.RESUMED;
    public h C0;
    @Nullable
    public p0 D0;
    public l<g> E0 = new l<>();
    public androidx.savedstate.b F0;
    public final ArrayList<c> G0;

    /* renamed from: a  reason: collision with root package name */
    public int f1720a = -1;

    /* renamed from: b  reason: collision with root package name */
    public Bundle f1721b;

    /* renamed from: c  reason: collision with root package name */
    public SparseArray<Parcelable> f1722c;

    /* renamed from: d  reason: collision with root package name */
    public Bundle f1723d;
    @NonNull

    /* renamed from: e  reason: collision with root package name */
    public String f1724e = UUID.randomUUID().toString();

    /* renamed from: f  reason: collision with root package name */
    public Bundle f1725f;

    /* renamed from: g  reason: collision with root package name */
    public Fragment f1726g;
    public String h = null;

    /* renamed from: i  reason: collision with root package name */
    public int f1727i;

    /* renamed from: j  reason: collision with root package name */
    public Boolean f1728j = null;

    /* renamed from: k  reason: collision with root package name */
    public boolean f1729k;

    /* renamed from: l  reason: collision with root package name */
    public boolean f1730l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1731m;

    /* renamed from: m0  reason: collision with root package name */
    public int f1732m0;

    /* renamed from: n  reason: collision with root package name */
    public boolean f1733n;

    /* renamed from: n0  reason: collision with root package name */
    public String f1734n0;

    /* renamed from: o  reason: collision with root package name */
    public boolean f1735o;

    /* renamed from: o0  reason: collision with root package name */
    public boolean f1736o0;

    /* renamed from: p  reason: collision with root package name */
    public boolean f1737p;

    /* renamed from: p0  reason: collision with root package name */
    public boolean f1738p0;

    /* renamed from: q  reason: collision with root package name */
    public int f1739q;

    /* renamed from: q0  reason: collision with root package name */
    public boolean f1740q0;

    /* renamed from: r  reason: collision with root package name */
    public FragmentManager f1741r;

    /* renamed from: r0  reason: collision with root package name */
    public boolean f1742r0 = true;

    /* renamed from: s  reason: collision with root package name */
    public v<?> f1743s;

    /* renamed from: s0  reason: collision with root package name */
    public boolean f1744s0;
    @NonNull

    /* renamed from: t  reason: collision with root package name */
    public FragmentManager f1745t = new y();

    /* renamed from: t0  reason: collision with root package name */
    public ViewGroup f1746t0;

    /* renamed from: u0  reason: collision with root package name */
    public View f1747u0;

    /* renamed from: v0  reason: collision with root package name */
    public boolean f1748v0;

    /* renamed from: w0  reason: collision with root package name */
    public boolean f1749w0 = true;

    /* renamed from: x  reason: collision with root package name */
    public Fragment f1750x;

    /* renamed from: x0  reason: collision with root package name */
    public b f1751x0;

    /* renamed from: y  reason: collision with root package name */
    public int f1752y;

    /* renamed from: y0  reason: collision with root package name */
    public boolean f1753y0;

    /* renamed from: z0  reason: collision with root package name */
    public float f1754z0;

    public static class InstantiationException extends RuntimeException {
        public InstantiationException(@NonNull String str, @Nullable Exception exc) {
            super(str, exc);
        }
    }

    @SuppressLint({"BanParcelableUsage, ParcelClassLoader"})
    public static class SavedState implements Parcelable {
        @NonNull
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public final Bundle f1756a;

        public class a implements Parcelable.ClassLoaderCreator<SavedState> {
            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }
        }

        public SavedState(@NonNull Parcel parcel, @Nullable ClassLoader classLoader) {
            Bundle readBundle = parcel.readBundle();
            this.f1756a = readBundle;
            if (classLoader != null && readBundle != null) {
                readBundle.setClassLoader(classLoader);
            }
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(@NonNull Parcel parcel, int i10) {
            parcel.writeBundle(this.f1756a);
        }
    }

    public class a extends s {
        public a() {
        }

        @Override // androidx.fragment.app.s
        @Nullable
        public View b(int i10) {
            View view = Fragment.this.f1747u0;
            if (view != null) {
                return view.findViewById(i10);
            }
            StringBuilder a10 = f.a("Fragment ");
            a10.append(Fragment.this);
            a10.append(" does not have a view");
            throw new IllegalStateException(a10.toString());
        }

        @Override // androidx.fragment.app.s
        public boolean c() {
            return Fragment.this.f1747u0 != null;
        }
    }

    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public View f1758a;

        /* renamed from: b  reason: collision with root package name */
        public Animator f1759b;

        /* renamed from: c  reason: collision with root package name */
        public int f1760c;

        /* renamed from: d  reason: collision with root package name */
        public int f1761d;

        /* renamed from: e  reason: collision with root package name */
        public ArrayList<String> f1762e;

        /* renamed from: f  reason: collision with root package name */
        public ArrayList<String> f1763f;

        /* renamed from: g  reason: collision with root package name */
        public Object f1764g;
        public Object h;

        /* renamed from: i  reason: collision with root package name */
        public Object f1765i;

        /* renamed from: j  reason: collision with root package name */
        public float f1766j = 1.0f;

        /* renamed from: k  reason: collision with root package name */
        public View f1767k = null;

        /* renamed from: l  reason: collision with root package name */
        public d f1768l;

        /* renamed from: m  reason: collision with root package name */
        public boolean f1769m;

        public b() {
            Object obj = Fragment.H0;
            this.f1764g = obj;
            this.h = obj;
            this.f1765i = obj;
        }
    }

    public static abstract class c {
        public abstract void a();
    }

    public interface d {
    }

    public Fragment() {
        new AtomicInteger();
        this.G0 = new ArrayList<>();
        this.C0 = new h(this);
        this.F0 = new androidx.savedstate.b(this);
    }

    @CallSuper
    @MainThread
    public void A() {
        this.f1744s0 = true;
    }

    @NonNull
    public LayoutInflater B(@Nullable Bundle bundle) {
        v<?> vVar = this.f1743s;
        if (vVar != null) {
            LayoutInflater e10 = vVar.e();
            e10.setFactory2(this.f1745t.f1778f);
            return e10;
        }
        throw new IllegalStateException("onGetLayoutInflater() cannot be executed until the Fragment is attached to the FragmentManager.");
    }

    @CallSuper
    @UiThread
    public void C(@NonNull Context context, @NonNull AttributeSet attributeSet, @Nullable Bundle bundle) {
        Activity activity;
        this.f1744s0 = true;
        v<?> vVar = this.f1743s;
        if (vVar == null) {
            activity = null;
        } else {
            activity = vVar.f2015a;
        }
        if (activity != null) {
            this.f1744s0 = false;
            this.f1744s0 = true;
        }
    }

    @MainThread
    public void D(@NonNull Bundle bundle) {
    }

    @CallSuper
    @MainThread
    public void E() {
        this.f1744s0 = true;
    }

    @CallSuper
    @MainThread
    public void F() {
        this.f1744s0 = true;
    }

    @CallSuper
    @MainThread
    public void G(@Nullable Bundle bundle) {
        this.f1744s0 = true;
    }

    public boolean H(@NonNull MenuItem menuItem) {
        if (!this.f1736o0) {
            return this.f1745t.l(menuItem);
        }
        return false;
    }

    public void I(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        this.f1745t.V();
        boolean z10 = true;
        this.f1737p = true;
        this.D0 = new p0();
        View y10 = y(layoutInflater, viewGroup, bundle);
        this.f1747u0 = y10;
        if (y10 != null) {
            p0 p0Var = this.D0;
            if (p0Var.f1980a == null) {
                p0Var.f1980a = new h(p0Var);
                p0Var.f1981b = new androidx.savedstate.b(p0Var);
            }
            this.f1747u0.setTag(R$id.view_tree_lifecycle_owner, this.D0);
            this.f1747u0.setTag(androidx.lifecycle.viewmodel.R$id.view_tree_view_model_store_owner, this);
            this.f1747u0.setTag(androidx.savedstate.R$id.view_tree_saved_state_registry_owner, this.D0);
            this.E0.h(this.D0);
            return;
        }
        if (this.D0.f1980a == null) {
            z10 = false;
        }
        if (!z10) {
            this.D0 = null;
            return;
        }
        throw new IllegalStateException("Called getViewLifecycleOwner() but onCreateView() returned null");
    }

    public void J() {
        this.f1745t.w(1);
        if (this.f1747u0 != null && ((h) this.D0.getLifecycle()).f2067b.isAtLeast(Lifecycle.State.CREATED)) {
            this.D0.a(Lifecycle.Event.ON_DESTROY);
        }
        this.f1720a = 1;
        this.f1744s0 = false;
        z();
        if (this.f1744s0) {
            b.C0138b bVar = ((o0.b) o0.a.b(this)).f9187b;
            int h6 = bVar.f9189c.h();
            for (int i10 = 0; i10 < h6; i10++) {
                Objects.requireNonNull(bVar.f9189c.i(i10));
            }
            this.f1737p = false;
            return;
        }
        throw new z0(m.b("Fragment ", this, " did not call through to super.onDestroyView()"));
    }

    public void K() {
        onLowMemory();
        this.f1745t.p();
    }

    public boolean L(@NonNull MenuItem menuItem) {
        if (!this.f1736o0) {
            return this.f1745t.r(menuItem);
        }
        return false;
    }

    public boolean M(@NonNull Menu menu) {
        if (!this.f1736o0) {
            return false | this.f1745t.v(menu);
        }
        return false;
    }

    @NonNull
    public final Context N() {
        Context f10 = f();
        if (f10 != null) {
            return f10;
        }
        throw new IllegalStateException(m.b("Fragment ", this, " not attached to a context."));
    }

    @NonNull
    public final View O() {
        View view = this.f1747u0;
        if (view != null) {
            return view;
        }
        throw new IllegalStateException(m.b("Fragment ", this, " did not return a View from onCreateView() or this was called before onCreateView()."));
    }

    public void P(View view) {
        c().f1758a = view;
    }

    public void Q(Animator animator) {
        c().f1759b = animator;
    }

    public void R(@Nullable Bundle bundle) {
        boolean z10;
        FragmentManager fragmentManager = this.f1741r;
        if (fragmentManager != null) {
            if (fragmentManager == null) {
                z10 = false;
            } else {
                z10 = fragmentManager.S();
            }
            if (z10) {
                throw new IllegalStateException("Fragment already added and state has been saved");
            }
        }
        this.f1725f = bundle;
    }

    public void S(View view) {
        c().f1767k = null;
    }

    public void T(boolean z10) {
        c().f1769m = z10;
    }

    public void U(int i10) {
        if (this.f1751x0 != null || i10 != 0) {
            c().f1760c = i10;
        }
    }

    public void V(d dVar) {
        c();
        d dVar2 = this.f1751x0.f1768l;
        if (dVar != dVar2) {
            if (dVar != null && dVar2 != null) {
                throw new IllegalStateException("Trying to set a replacement startPostponedEnterTransition on " + this);
            } else if (dVar != null) {
                ((FragmentManager.m) dVar).f1813c++;
            }
        }
    }

    @NonNull
    public s a() {
        return new a();
    }

    public void b(@NonNull String str, @Nullable FileDescriptor fileDescriptor, @NonNull PrintWriter printWriter, @Nullable String[] strArr) {
        String str2;
        printWriter.print(str);
        printWriter.print("mFragmentId=#");
        printWriter.print(Integer.toHexString(this.f1752y));
        printWriter.print(" mContainerId=#");
        printWriter.print(Integer.toHexString(this.f1732m0));
        printWriter.print(" mTag=");
        printWriter.println(this.f1734n0);
        printWriter.print(str);
        printWriter.print("mState=");
        printWriter.print(this.f1720a);
        printWriter.print(" mWho=");
        printWriter.print(this.f1724e);
        printWriter.print(" mBackStackNesting=");
        printWriter.println(this.f1739q);
        printWriter.print(str);
        printWriter.print("mAdded=");
        printWriter.print(this.f1729k);
        printWriter.print(" mRemoving=");
        printWriter.print(this.f1730l);
        printWriter.print(" mFromLayout=");
        printWriter.print(this.f1731m);
        printWriter.print(" mInLayout=");
        printWriter.println(this.f1733n);
        printWriter.print(str);
        printWriter.print("mHidden=");
        printWriter.print(this.f1736o0);
        printWriter.print(" mDetached=");
        printWriter.print(this.f1738p0);
        printWriter.print(" mMenuVisible=");
        printWriter.print(this.f1742r0);
        printWriter.print(" mHasMenu=");
        printWriter.println(false);
        printWriter.print(str);
        printWriter.print("mRetainInstance=");
        printWriter.print(this.f1740q0);
        printWriter.print(" mUserVisibleHint=");
        printWriter.println(this.f1749w0);
        if (this.f1741r != null) {
            printWriter.print(str);
            printWriter.print("mFragmentManager=");
            printWriter.println(this.f1741r);
        }
        if (this.f1743s != null) {
            printWriter.print(str);
            printWriter.print("mHost=");
            printWriter.println(this.f1743s);
        }
        if (this.f1750x != null) {
            printWriter.print(str);
            printWriter.print("mParentFragment=");
            printWriter.println(this.f1750x);
        }
        if (this.f1725f != null) {
            printWriter.print(str);
            printWriter.print("mArguments=");
            printWriter.println(this.f1725f);
        }
        if (this.f1721b != null) {
            printWriter.print(str);
            printWriter.print("mSavedFragmentState=");
            printWriter.println(this.f1721b);
        }
        if (this.f1722c != null) {
            printWriter.print(str);
            printWriter.print("mSavedViewState=");
            printWriter.println(this.f1722c);
        }
        if (this.f1723d != null) {
            printWriter.print(str);
            printWriter.print("mSavedViewRegistryState=");
            printWriter.println(this.f1723d);
        }
        Fragment fragment = this.f1726g;
        if (fragment == null) {
            FragmentManager fragmentManager = this.f1741r;
            fragment = (fragmentManager == null || (str2 = this.h) == null) ? null : fragmentManager.f1775c.d(str2);
        }
        if (fragment != null) {
            printWriter.print(str);
            printWriter.print("mTarget=");
            printWriter.print(fragment);
            printWriter.print(" mTargetRequestCode=");
            printWriter.println(this.f1727i);
        }
        if (l() != 0) {
            printWriter.print(str);
            printWriter.print("mNextAnim=");
            printWriter.println(l());
        }
        if (this.f1746t0 != null) {
            printWriter.print(str);
            printWriter.print("mContainer=");
            printWriter.println(this.f1746t0);
        }
        if (this.f1747u0 != null) {
            printWriter.print(str);
            printWriter.print("mView=");
            printWriter.println(this.f1747u0);
        }
        if (d() != null) {
            printWriter.print(str);
            printWriter.print("mAnimatingAway=");
            printWriter.println(d());
        }
        if (f() != null) {
            o0.a.b(this).a(str, fileDescriptor, printWriter, strArr);
        }
        printWriter.print(str);
        printWriter.println("Child " + this.f1745t + ":");
        this.f1745t.y(p.f.a(str, "  "), fileDescriptor, printWriter, strArr);
    }

    public final b c() {
        if (this.f1751x0 == null) {
            this.f1751x0 = new b();
        }
        return this.f1751x0;
    }

    public View d() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        return bVar.f1758a;
    }

    @NonNull
    public final FragmentManager e() {
        if (this.f1743s != null) {
            return this.f1745t;
        }
        throw new IllegalStateException(m.b("Fragment ", this, " has not been attached yet."));
    }

    public final boolean equals(@Nullable Object obj) {
        return super.equals(obj);
    }

    @Nullable
    public Context f() {
        v<?> vVar = this.f1743s;
        if (vVar == null) {
            return null;
        }
        return vVar.f2016b;
    }

    @Nullable
    public Object g() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Objects.requireNonNull(bVar);
        return null;
    }

    @Override // androidx.lifecycle.g
    @NonNull
    public Lifecycle getLifecycle() {
        return this.C0;
    }

    @Override // androidx.savedstate.c
    @NonNull
    public final androidx.savedstate.a getSavedStateRegistry() {
        return this.F0.f2505b;
    }

    @Override // androidx.lifecycle.x
    @NonNull
    public w getViewModelStore() {
        if (this.f1741r == null) {
            throw new IllegalStateException("Can't access ViewModels from detached fragment");
        } else if (k() != Lifecycle.State.INITIALIZED.ordinal()) {
            z zVar = this.f1741r.J;
            w wVar = zVar.f2026e.get(this.f1724e);
            if (wVar != null) {
                return wVar;
            }
            w wVar2 = new w();
            zVar.f2026e.put(this.f1724e, wVar2);
            return wVar2;
        } else {
            throw new IllegalStateException("Calling getViewModelStore() before a Fragment reaches onCreate() when using setMaxLifecycle(INITIALIZED) is not supported");
        }
    }

    public void h() {
        b bVar = this.f1751x0;
        if (bVar != null) {
            Objects.requireNonNull(bVar);
        }
    }

    public final int hashCode() {
        return super.hashCode();
    }

    @Nullable
    public Object i() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Objects.requireNonNull(bVar);
        return null;
    }

    public void j() {
        b bVar = this.f1751x0;
        if (bVar != null) {
            Objects.requireNonNull(bVar);
        }
    }

    public final int k() {
        Lifecycle.State state = this.B0;
        if (state == Lifecycle.State.INITIALIZED || this.f1750x == null) {
            return state.ordinal();
        }
        return Math.min(state.ordinal(), this.f1750x.k());
    }

    public int l() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return 0;
        }
        return bVar.f1760c;
    }

    @NonNull
    public final FragmentManager m() {
        FragmentManager fragmentManager = this.f1741r;
        if (fragmentManager != null) {
            return fragmentManager;
        }
        throw new IllegalStateException(m.b("Fragment ", this, " not associated with a fragment manager."));
    }

    @Nullable
    public Object n() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Object obj = bVar.h;
        if (obj != H0) {
            return obj;
        }
        i();
        return null;
    }

    @NonNull
    public final Resources o() {
        return N().getResources();
    }

    @CallSuper
    public void onConfigurationChanged(@NonNull Configuration configuration) {
        this.f1744s0 = true;
    }

    @MainThread
    public void onCreateContextMenu(@NonNull ContextMenu contextMenu, @NonNull View view, @Nullable ContextMenu.ContextMenuInfo contextMenuInfo) {
        n nVar;
        v<?> vVar = this.f1743s;
        if (vVar == null) {
            nVar = null;
        } else {
            nVar = (n) vVar.f2015a;
        }
        if (nVar != null) {
            nVar.onCreateContextMenu(contextMenu, view, contextMenuInfo);
            return;
        }
        throw new IllegalStateException(m.b("Fragment ", this, " not attached to an activity."));
    }

    @CallSuper
    @MainThread
    public void onLowMemory() {
        this.f1744s0 = true;
    }

    @Nullable
    public Object p() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Object obj = bVar.f1764g;
        if (obj != H0) {
            return obj;
        }
        g();
        return null;
    }

    @Nullable
    public Object q() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Objects.requireNonNull(bVar);
        return null;
    }

    @Nullable
    public Object r() {
        b bVar = this.f1751x0;
        if (bVar == null) {
            return null;
        }
        Object obj = bVar.f1765i;
        if (obj != H0) {
            return obj;
        }
        q();
        return null;
    }

    @NonNull
    public final String s(@StringRes int i10) {
        return o().getString(i10);
    }

    public final boolean t() {
        return this.f1739q > 0;
    }

    @NonNull
    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append(getClass().getSimpleName());
        sb.append("{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append("}");
        sb.append(" (");
        sb.append(this.f1724e);
        sb.append(")");
        if (this.f1752y != 0) {
            sb.append(" id=0x");
            sb.append(Integer.toHexString(this.f1752y));
        }
        if (this.f1734n0 != null) {
            sb.append(" ");
            sb.append(this.f1734n0);
        }
        sb.append('}');
        return sb.toString();
    }

    public final boolean u() {
        Fragment fragment = this.f1750x;
        return fragment != null && (fragment.f1730l || fragment.u());
    }

    @Deprecated
    public void v(int i10, int i11, @Nullable Intent intent) {
        if (FragmentManager.O(2)) {
            Log.v("FragmentManager", "Fragment " + this + " received the following in onActivityResult(): requestCode: " + i10 + " resultCode: " + i11 + " data: " + intent);
        }
    }

    @CallSuper
    @MainThread
    public void w(@NonNull Context context) {
        Activity activity;
        this.f1744s0 = true;
        v<?> vVar = this.f1743s;
        if (vVar == null) {
            activity = null;
        } else {
            activity = vVar.f2015a;
        }
        if (activity != null) {
            this.f1744s0 = false;
            this.f1744s0 = true;
        }
    }

    @CallSuper
    @MainThread
    public void x(@Nullable Bundle bundle) {
        Parcelable parcelable;
        boolean z10 = true;
        this.f1744s0 = true;
        if (!(bundle == null || (parcelable = bundle.getParcelable(n.FRAGMENTS_TAG)) == null)) {
            this.f1745t.a0(parcelable);
            this.f1745t.m();
        }
        FragmentManager fragmentManager = this.f1745t;
        if (fragmentManager.f1787p < 1) {
            z10 = false;
        }
        if (!z10) {
            fragmentManager.m();
        }
    }

    @Nullable
    @MainThread
    public View y(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        return null;
    }

    @CallSuper
    @MainThread
    public void z() {
        this.f1744s0 = true;
    }
}
