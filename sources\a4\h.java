package a4;

import android.accounts.Account;
import android.accounts.AccountManager;
import com.duokan.airkan.common.Log;
import com.xiaomi.milink.discover.core.udt.UDTDiscoverService;
import p.f;
import z3.b;

public class h implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ UDTDiscoverService f39a;

    public h(UDTDiscoverService uDTDiscoverService) {
        this.f39a = uDTDiscoverService;
    }

    public void run() {
        String str;
        Account[] accountsByType = AccountManager.get(this.f39a).getAccountsByType("com.xiaomi");
        if (accountsByType == null || accountsByType.length <= 0) {
            str = null;
        } else {
            str = String.format("\"%s\"", accountsByType[0].name);
        }
        String format = String.format("{\"account\":%s}", str);
        String string = this.f39a.getSharedPreferences("DevAccount", 0).getString("AccountBody", "");
        boolean z10 = this.f39a.getSharedPreferences("DevAccount", 0).getBoolean("IsPublishOK", false);
        if (!format.equals(string) || !z10) {
            this.f39a.getSharedPreferences("DevAccount", 0).edit().putBoolean("IsPublishOK", false).commit();
            this.f39a.getSharedPreferences("DevAccount", 0).edit().putString("AccountBody", format).commit();
            String str2 = String.format("{\"account\":%s,", str) + "\"profile\":%s}";
            f.c("NewPublishBody: ", str2, "UDTDiscoverService");
            b.b(this.f39a, "$SYS/report/account", str2);
            return;
        }
        Log.d("UDTDiscoverService", "Account not changed, no need publish again");
    }
}
