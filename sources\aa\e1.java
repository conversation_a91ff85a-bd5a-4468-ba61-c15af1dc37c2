package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERT61String */
public class e1 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f176a;

    public e1(byte[] bArr) {
        this.f176a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof e1)) {
            return false;
        }
        return a.a(this.f176a, ((e1) qVar).f176a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f176a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(20, this.f176a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f176a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f176a.length) + 1 + this.f176a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
