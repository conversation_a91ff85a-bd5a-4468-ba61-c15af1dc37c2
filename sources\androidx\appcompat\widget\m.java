package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.SeekBar;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import java.util.WeakHashMap;

/* compiled from: AppCompatSeekBarHelper */
public class m extends l {

    /* renamed from: d  reason: collision with root package name */
    public final SeekBar f1139d;

    /* renamed from: e  reason: collision with root package name */
    public Drawable f1140e;

    /* renamed from: f  reason: collision with root package name */
    public ColorStateList f1141f = null;

    /* renamed from: g  reason: collision with root package name */
    public PorterDuff.Mode f1142g = null;
    public boolean h = false;

    /* renamed from: i  reason: collision with root package name */
    public boolean f1143i = false;

    public m(SeekBar seekBar) {
        super(seekBar);
        this.f1139d = seekBar;
    }

    @Override // androidx.appcompat.widget.l
    public void a(AttributeSet attributeSet, int i10) {
        super.a(attributeSet, i10);
        Context context = this.f1139d.getContext();
        int[] iArr = R$styleable.AppCompatSeekBar;
        m0 r10 = m0.r(context, attributeSet, iArr, i10, 0);
        SeekBar seekBar = this.f1139d;
        ViewCompat.j(seekBar, seekBar.getContext(), iArr, attributeSet, r10.f1145b, i10, 0);
        Drawable h6 = r10.h(R$styleable.AppCompatSeekBar_android_thumb);
        if (h6 != null) {
            this.f1139d.setThumb(h6);
        }
        Drawable g10 = r10.g(R$styleable.AppCompatSeekBar_tickMark);
        Drawable drawable = this.f1140e;
        if (drawable != null) {
            drawable.setCallback(null);
        }
        this.f1140e = g10;
        if (g10 != null) {
            g10.setCallback(this.f1139d);
            SeekBar seekBar2 = this.f1139d;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            g10.setLayoutDirection(seekBar2.getLayoutDirection());
            if (g10.isStateful()) {
                g10.setState(this.f1139d.getDrawableState());
            }
            c();
        }
        this.f1139d.invalidate();
        int i11 = R$styleable.AppCompatSeekBar_tickMarkTintMode;
        if (r10.p(i11)) {
            this.f1142g = u.c(r10.j(i11, -1), this.f1142g);
            this.f1143i = true;
        }
        int i12 = R$styleable.AppCompatSeekBar_tickMarkTint;
        if (r10.p(i12)) {
            this.f1141f = r10.c(i12);
            this.h = true;
        }
        r10.f1145b.recycle();
        c();
    }

    public final void c() {
        Drawable drawable = this.f1140e;
        if (drawable == null) {
            return;
        }
        if (this.h || this.f1143i) {
            Drawable mutate = drawable.mutate();
            this.f1140e = mutate;
            if (this.h) {
                mutate.setTintList(this.f1141f);
            }
            if (this.f1143i) {
                this.f1140e.setTintMode(this.f1142g);
            }
            if (this.f1140e.isStateful()) {
                this.f1140e.setState(this.f1139d.getDrawableState());
            }
        }
    }

    public void d(Canvas canvas) {
        if (this.f1140e != null) {
            int max = this.f1139d.getMax();
            int i10 = 1;
            if (max > 1) {
                int intrinsicWidth = this.f1140e.getIntrinsicWidth();
                int intrinsicHeight = this.f1140e.getIntrinsicHeight();
                int i11 = intrinsicWidth >= 0 ? intrinsicWidth / 2 : 1;
                if (intrinsicHeight >= 0) {
                    i10 = intrinsicHeight / 2;
                }
                this.f1140e.setBounds(-i11, -i10, i11, i10);
                float width = ((float) ((this.f1139d.getWidth() - this.f1139d.getPaddingLeft()) - this.f1139d.getPaddingRight())) / ((float) max);
                int save = canvas.save();
                canvas.translate((float) this.f1139d.getPaddingLeft(), (float) (this.f1139d.getHeight() / 2));
                for (int i12 = 0; i12 <= max; i12++) {
                    this.f1140e.draw(canvas);
                    canvas.translate(width, Constant.VOLUME_FLOAT_MIN);
                }
                canvas.restoreToCount(save);
            }
        }
    }
}
