package androidx.fragment.app;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.widget.FrameLayout;
import androidx.activity.result.c;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.R$id;
import androidx.fragment.R$styleable;
import com.xiaomi.onetrack.api.g;
import java.util.ArrayList;
import p.f;

public final class FragmentContainerView extends FrameLayout {

    /* renamed from: a  reason: collision with root package name */
    public ArrayList<View> f1770a;

    /* renamed from: b  reason: collision with root package name */
    public ArrayList<View> f1771b;

    /* renamed from: c  reason: collision with root package name */
    public boolean f1772c;

    public FragmentContainerView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public final void a(@NonNull View view) {
        ArrayList<View> arrayList = this.f1771b;
        if (arrayList != null && arrayList.contains(view)) {
            if (this.f1770a == null) {
                this.f1770a = new ArrayList<>();
            }
            this.f1770a.add(view);
        }
    }

    @Override // android.view.ViewGroup
    public void addView(@NonNull View view, int i10, @Nullable ViewGroup.LayoutParams layoutParams) {
        Object tag = view.getTag(R$id.fragment_container_view_tag);
        if ((tag instanceof Fragment ? (Fragment) tag : null) != null) {
            super.addView(view, i10, layoutParams);
            return;
        }
        throw new IllegalStateException("Views added to a FragmentContainerView must be associated with a Fragment. View " + view + " is not associated with a Fragment.");
    }

    public boolean addViewInLayout(@NonNull View view, int i10, @Nullable ViewGroup.LayoutParams layoutParams, boolean z10) {
        Object tag = view.getTag(R$id.fragment_container_view_tag);
        if ((tag instanceof Fragment ? (Fragment) tag : null) != null) {
            return super.addViewInLayout(view, i10, layoutParams, z10);
        }
        throw new IllegalStateException("Views added to a FragmentContainerView must be associated with a Fragment. View " + view + " is not associated with a Fragment.");
    }

    public void dispatchDraw(@NonNull Canvas canvas) {
        if (this.f1772c && this.f1770a != null) {
            for (int i10 = 0; i10 < this.f1770a.size(); i10++) {
                super.drawChild(canvas, this.f1770a.get(i10), getDrawingTime());
            }
        }
        super.dispatchDraw(canvas);
    }

    public boolean drawChild(@NonNull Canvas canvas, @NonNull View view, long j10) {
        ArrayList<View> arrayList;
        if (!this.f1772c || (arrayList = this.f1770a) == null || arrayList.size() <= 0 || !this.f1770a.contains(view)) {
            return super.drawChild(canvas, view, j10);
        }
        return false;
    }

    public void endViewTransition(@NonNull View view) {
        ArrayList<View> arrayList = this.f1771b;
        if (arrayList != null) {
            arrayList.remove(view);
            ArrayList<View> arrayList2 = this.f1770a;
            if (arrayList2 != null && arrayList2.remove(view)) {
                this.f1772c = true;
            }
        }
        super.endViewTransition(view);
    }

    @NonNull
    @RequiresApi(20)
    public WindowInsets onApplyWindowInsets(@NonNull WindowInsets windowInsets) {
        for (int i10 = 0; i10 < getChildCount(); i10++) {
            getChildAt(i10).dispatchApplyWindowInsets(new WindowInsets(windowInsets));
        }
        return windowInsets;
    }

    public void removeAllViewsInLayout() {
        for (int childCount = getChildCount() - 1; childCount >= 0; childCount--) {
            a(getChildAt(childCount));
        }
        super.removeAllViewsInLayout();
    }

    public void removeDetachedView(@NonNull View view, boolean z10) {
        if (z10) {
            a(view);
        }
        super.removeDetachedView(view, z10);
    }

    public void removeView(@NonNull View view) {
        a(view);
        super.removeView(view);
    }

    public void removeViewAt(int i10) {
        a(getChildAt(i10));
        super.removeViewAt(i10);
    }

    public void removeViewInLayout(@NonNull View view) {
        a(view);
        super.removeViewInLayout(view);
    }

    public void removeViews(int i10, int i11) {
        for (int i12 = i10; i12 < i10 + i11; i12++) {
            a(getChildAt(i12));
        }
        super.removeViews(i10, i11);
    }

    public void removeViewsInLayout(int i10, int i11) {
        for (int i12 = i10; i12 < i10 + i11; i12++) {
            a(getChildAt(i12));
        }
        super.removeViewsInLayout(i10, i11);
    }

    public void setDrawDisappearingViewsLast(boolean z10) {
        this.f1772c = z10;
    }

    public void setLayoutTransition(@Nullable LayoutTransition layoutTransition) {
        throw new UnsupportedOperationException("FragmentContainerView does not support Layout Transitions or animateLayoutChanges=\"true\".");
    }

    public void startViewTransition(@NonNull View view) {
        if (view.getParent() == this) {
            if (this.f1771b == null) {
                this.f1771b = new ArrayList<>();
            }
            this.f1771b.add(view);
        }
        super.startViewTransition(view);
    }

    public FragmentContainerView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        String str;
        this.f1772c = true;
        if (attributeSet != null) {
            String classAttribute = attributeSet.getClassAttribute();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.FragmentContainerView);
            if (classAttribute == null) {
                classAttribute = obtainStyledAttributes.getString(R$styleable.FragmentContainerView_android_name);
                str = "android:name";
            } else {
                str = g.f5192r;
            }
            obtainStyledAttributes.recycle();
            if (classAttribute != null && !isInEditMode()) {
                throw new UnsupportedOperationException("FragmentContainerView must be within a FragmentActivity to use " + str + "=\"" + classAttribute + "\"");
            }
        }
    }

    public FragmentContainerView(@NonNull Context context, @NonNull AttributeSet attributeSet, @NonNull FragmentManager fragmentManager) {
        super(context, attributeSet);
        this.f1772c = true;
        String classAttribute = attributeSet.getClassAttribute();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.FragmentContainerView);
        classAttribute = classAttribute == null ? obtainStyledAttributes.getString(R$styleable.FragmentContainerView_android_name) : classAttribute;
        String string = obtainStyledAttributes.getString(R$styleable.FragmentContainerView_android_tag);
        obtainStyledAttributes.recycle();
        int id = getId();
        Fragment H = fragmentManager.H(id);
        if (classAttribute != null && H == null) {
            if (id <= 0) {
                throw new IllegalStateException(c.a("FragmentContainerView must have an android:id to add Fragment ", classAttribute, string != null ? f.a(" with tag ", string) : ""));
            }
            Fragment a10 = fragmentManager.L().a(context.getClassLoader(), classAttribute);
            a10.C(context, attributeSet, null);
            a aVar = new a(fragmentManager);
            aVar.f1879o = true;
            a10.f1746t0 = this;
            aVar.f(getId(), a10, string, 1);
            if (!aVar.f1872g) {
                aVar.f1837p.D(aVar, true);
                return;
            }
            throw new IllegalStateException("This transaction is already being added to the back stack");
        }
    }
}
