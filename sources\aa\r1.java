package aa;

import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;

/* compiled from: IndefiniteLengthInputStream */
public class r1 extends u1 {

    /* renamed from: c  reason: collision with root package name */
    public int f222c;

    /* renamed from: d  reason: collision with root package name */
    public int f223d;

    /* renamed from: e  reason: collision with root package name */
    public boolean f224e = false;

    /* renamed from: f  reason: collision with root package name */
    public boolean f225f = true;

    public r1(InputStream inputStream, int i10) throws IOException {
        super(inputStream, i10);
        this.f222c = inputStream.read();
        int read = inputStream.read();
        this.f223d = read;
        if (read >= 0) {
            e();
            return;
        }
        throw new EOFException();
    }

    public final boolean e() {
        if (!this.f224e && this.f225f && this.f222c == 0 && this.f223d == 0) {
            this.f224e = true;
            d(true);
        }
        return this.f224e;
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i10, int i11) throws IOException {
        if (this.f225f || i11 < 3) {
            return super.read(bArr, i10, i11);
        }
        if (this.f224e) {
            return -1;
        }
        int read = this.f235a.read(bArr, i10 + 2, i11 - 2);
        if (read >= 0) {
            bArr[i10] = (byte) this.f222c;
            bArr[i10 + 1] = (byte) this.f223d;
            this.f222c = this.f235a.read();
            int read2 = this.f235a.read();
            this.f223d = read2;
            if (read2 >= 0) {
                return read + 2;
            }
            throw new EOFException();
        }
        throw new EOFException();
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (e()) {
            return -1;
        }
        int read = this.f235a.read();
        if (read >= 0) {
            int i10 = this.f222c;
            this.f222c = this.f223d;
            this.f223d = read;
            return i10;
        }
        throw new EOFException();
    }
}
