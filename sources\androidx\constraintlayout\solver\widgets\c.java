package androidx.constraintlayout.solver.widgets;

/* compiled from: Chain */
public class c {
    /* JADX WARNING: Code restructure failed: missing block: B:150:0x023a, code lost:
        if (r1[r7].f1279d.f1277b == r13) goto L_0x023d;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:22:0x0065, code lost:
        if (r14[r4] != 2) goto L_0x0068;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:65:0x00fa, code lost:
        if (r4[r12].f1279d.f1277b == r15) goto L_0x00fd;
     */
    /* JADX WARNING: Removed duplicated region for block: B:113:0x019b  */
    /* JADX WARNING: Removed duplicated region for block: B:272:0x0475  */
    /* JADX WARNING: Removed duplicated region for block: B:282:0x0103 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:289:0x0250 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:59:0x00da  */
    /* JADX WARNING: Removed duplicated region for block: B:62:0x00ea  */
    /* JADX WARNING: Removed duplicated region for block: B:68:0x0100  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void a(androidx.constraintlayout.solver.widgets.e r27, androidx.constraintlayout.solver.c r28, int r29) {
        /*
        // Method dump skipped, instructions count: 1168
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.c.a(androidx.constraintlayout.solver.widgets.e, androidx.constraintlayout.solver.c, int):void");
    }

    /* JADX DEBUG: Multi-variable search result rejected for r2v50, resolved type: androidx.constraintlayout.solver.widgets.ConstraintWidget */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARNING: Code restructure failed: missing block: B:13:0x002d, code lost:
        if (r8 == 2) goto L_0x003e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:21:0x003c, code lost:
        if (r8 == 2) goto L_0x003e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:23:0x0040, code lost:
        r5 = false;
     */
    /* JADX WARNING: Removed duplicated region for block: B:200:0x03db  */
    /* JADX WARNING: Removed duplicated region for block: B:213:0x0401  */
    /* JADX WARNING: Removed duplicated region for block: B:256:0x04c7  */
    /* JADX WARNING: Removed duplicated region for block: B:261:0x04fc  */
    /* JADX WARNING: Removed duplicated region for block: B:270:0x0521  */
    /* JADX WARNING: Removed duplicated region for block: B:271:0x0524  */
    /* JADX WARNING: Removed duplicated region for block: B:274:0x052a  */
    /* JADX WARNING: Removed duplicated region for block: B:275:0x052d  */
    /* JADX WARNING: Removed duplicated region for block: B:277:0x0531  */
    /* JADX WARNING: Removed duplicated region for block: B:282:0x0541  */
    /* JADX WARNING: Removed duplicated region for block: B:294:0x03dc A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:78:0x0143  */
    /* JADX WARNING: Removed duplicated region for block: B:91:0x017b  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void b(androidx.constraintlayout.solver.widgets.e r33, androidx.constraintlayout.solver.c r34, int r35, int r36, androidx.constraintlayout.solver.widgets.d r37) {
        /*
        // Method dump skipped, instructions count: 1384
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.c.b(androidx.constraintlayout.solver.widgets.e, androidx.constraintlayout.solver.c, int, int, androidx.constraintlayout.solver.widgets.d):void");
    }
}
