package a;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

public interface a extends IInterface {

    /* renamed from: a.a$a  reason: collision with other inner class name */
    public static abstract class AbstractBinderC0000a extends Binder implements a {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int f0a = 0;

        /* renamed from: a.a$a$a  reason: collision with other inner class name */
        public static class C0001a implements a {

            /* renamed from: a  reason: collision with root package name */
            public IBinder f1a;

            public C0001a(IBinder iBinder) {
                this.f1a = iBinder;
            }

            @Override // a.a
            public void a(String str, String str2) {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.cleanmaster.sdk.IKSCleaner");
                    obtain.writeString(str);
                    obtain.writeString(str2);
                    if (!this.f1a.transact(2, obtain, obtain2, 0)) {
                        int i10 = AbstractBinderC0000a.f0a;
                    }
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }

            public IBinder asBinder() {
                return this.f1a;
            }

            @Override // a.a
            public void w(b bVar) {
                Parcel obtain = Parcel.obtain();
                Parcel obtain2 = Parcel.obtain();
                try {
                    obtain.writeInterfaceToken("com.cleanmaster.sdk.IKSCleaner");
                    obtain.writeStrongBinder(bVar.asBinder());
                    if (!this.f1a.transact(15, obtain, obtain2, 0)) {
                        int i10 = AbstractBinderC0000a.f0a;
                    }
                    obtain2.readException();
                } finally {
                    obtain2.recycle();
                    obtain.recycle();
                }
            }
        }
    }

    void a(String str, String str2);

    void w(b bVar);
}
