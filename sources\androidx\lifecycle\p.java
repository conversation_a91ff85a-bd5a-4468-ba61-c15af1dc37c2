package androidx.lifecycle;

import android.annotation.SuppressLint;
import android.app.Application;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.savedstate.a;
import androidx.savedstate.c;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

/* compiled from: SavedStateViewModelFactory */
public final class p extends t {

    /* renamed from: f  reason: collision with root package name */
    public static final Class<?>[] f2083f = {Application.class, o.class};

    /* renamed from: g  reason: collision with root package name */
    public static final Class<?>[] f2084g = {o.class};

    /* renamed from: a  reason: collision with root package name */
    public final Application f2085a;

    /* renamed from: b  reason: collision with root package name */
    public final s f2086b;

    /* renamed from: c  reason: collision with root package name */
    public final Bundle f2087c;

    /* renamed from: d  reason: collision with root package name */
    public final Lifecycle f2088d;

    /* renamed from: e  reason: collision with root package name */
    public final a f2089e;

    @SuppressLint({"LambdaLast"})
    public p(@Nullable Application application, @NonNull c cVar, @Nullable Bundle bundle) {
        s sVar;
        this.f2089e = cVar.getSavedStateRegistry();
        this.f2088d = cVar.getLifecycle();
        this.f2087c = bundle;
        this.f2085a = application;
        if (application != null) {
            if (r.f2092c == null) {
                r.f2092c = new r(application);
            }
            sVar = r.f2092c;
        } else {
            if (u.f2094a == null) {
                u.f2094a = new u();
            }
            sVar = u.f2094a;
        }
        this.f2086b = sVar;
    }

    public static <T> Constructor<T> d(Class<T> cls, Class<?>[] clsArr) {
        for (Constructor<?> constructor : cls.getConstructors()) {
            Constructor<T> constructor2 = (Constructor<T>) constructor;
            if (Arrays.equals(clsArr, constructor2.getParameterTypes())) {
                return constructor2;
            }
        }
        return null;
    }

    @Override // androidx.lifecycle.t, androidx.lifecycle.s
    @NonNull
    public <T extends q> T a(@NonNull Class<T> cls) {
        String canonicalName = cls.getCanonicalName();
        if (canonicalName != null) {
            return (T) c(canonicalName, cls);
        }
        throw new IllegalArgumentException("Local and anonymous classes can not be ViewModels");
    }

    @Override // androidx.lifecycle.v
    public void b(@NonNull q qVar) {
        SavedStateHandleController.h(qVar, this.f2089e, this.f2088d);
    }

    @Override // androidx.lifecycle.t
    @NonNull
    public <T extends q> T c(@NonNull String str, @NonNull Class<T> cls) {
        Constructor constructor;
        o oVar;
        T t2;
        boolean isAssignableFrom = a.class.isAssignableFrom(cls);
        if (!isAssignableFrom || this.f2085a == null) {
            constructor = d(cls, f2084g);
        } else {
            constructor = d(cls, f2083f);
        }
        if (constructor == null) {
            return (T) this.f2086b.a(cls);
        }
        a aVar = this.f2089e;
        Lifecycle lifecycle = this.f2088d;
        Bundle bundle = this.f2087c;
        Bundle a10 = aVar.a(str);
        Class[] clsArr = o.f2077e;
        if (a10 == null && bundle == null) {
            oVar = new o();
        } else {
            HashMap hashMap = new HashMap();
            if (bundle != null) {
                for (String str2 : bundle.keySet()) {
                    hashMap.put(str2, bundle.get(str2));
                }
            }
            if (a10 == null) {
                oVar = new o(hashMap);
            } else {
                ArrayList parcelableArrayList = a10.getParcelableArrayList("keys");
                ArrayList parcelableArrayList2 = a10.getParcelableArrayList("values");
                if (parcelableArrayList == null || parcelableArrayList2 == null || parcelableArrayList.size() != parcelableArrayList2.size()) {
                    throw new IllegalStateException("Invalid bundle passed as restored state");
                }
                for (int i10 = 0; i10 < parcelableArrayList.size(); i10++) {
                    hashMap.put((String) parcelableArrayList.get(i10), parcelableArrayList2.get(i10));
                }
                oVar = new o(hashMap);
            }
        }
        SavedStateHandleController savedStateHandleController = new SavedStateHandleController(str, oVar);
        savedStateHandleController.i(aVar, lifecycle);
        SavedStateHandleController.j(aVar, lifecycle);
        if (isAssignableFrom) {
            try {
                Application application = this.f2085a;
                if (application != null) {
                    t2 = (T) ((q) constructor.newInstance(application, oVar));
                    t2.b("androidx.lifecycle.savedstate.vm.tag", savedStateHandleController);
                    return t2;
                }
            } catch (IllegalAccessException e10) {
                throw new RuntimeException("Failed to access " + cls, e10);
            } catch (InstantiationException e11) {
                throw new RuntimeException("A " + cls + " cannot be instantiated.", e11);
            } catch (InvocationTargetException e12) {
                throw new RuntimeException("An exception happened in constructor of " + cls, e12.getCause());
            }
        }
        t2 = (T) ((q) constructor.newInstance(oVar));
        t2.b("androidx.lifecycle.savedstate.vm.tag", savedStateHandleController);
        return t2;
    }
}
