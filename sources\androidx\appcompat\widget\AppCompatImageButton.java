package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.RippleDrawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.widget.ImageButton;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;

public class AppCompatImageButton extends ImageButton {

    /* renamed from: a  reason: collision with root package name */
    public final e f817a;

    /* renamed from: b  reason: collision with root package name */
    public final k f818b;

    public AppCompatImageButton(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.imageButtonStyle);
    }

    public void drawableStateChanged() {
        super.drawableStateChanged();
        e eVar = this.f817a;
        if (eVar != null) {
            eVar.a();
        }
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.a();
        }
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public ColorStateList getSupportBackgroundTintList() {
        e eVar = this.f817a;
        if (eVar != null) {
            return eVar.b();
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public PorterDuff.Mode getSupportBackgroundTintMode() {
        e eVar = this.f817a;
        if (eVar != null) {
            return eVar.c();
        }
        return null;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public ColorStateList getSupportImageTintList() {
        k0 k0Var;
        k kVar = this.f818b;
        if (kVar == null || (k0Var = kVar.f1131b) == null) {
            return null;
        }
        return k0Var.f1132a;
    }

    @Nullable
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public PorterDuff.Mode getSupportImageTintMode() {
        k0 k0Var;
        k kVar = this.f818b;
        if (kVar == null || (k0Var = kVar.f1131b) == null) {
            return null;
        }
        return k0Var.f1133b;
    }

    public boolean hasOverlappingRendering() {
        if (!(!(this.f818b.f1130a.getBackground() instanceof RippleDrawable)) || !super.hasOverlappingRendering()) {
            return false;
        }
        return true;
    }

    public void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        e eVar = this.f817a;
        if (eVar != null) {
            eVar.e();
        }
    }

    public void setBackgroundResource(@DrawableRes int i10) {
        super.setBackgroundResource(i10);
        e eVar = this.f817a;
        if (eVar != null) {
            eVar.f(i10);
        }
    }

    public void setImageBitmap(Bitmap bitmap) {
        super.setImageBitmap(bitmap);
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.a();
        }
    }

    public void setImageDrawable(@Nullable Drawable drawable) {
        super.setImageDrawable(drawable);
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.a();
        }
    }

    public void setImageResource(@DrawableRes int i10) {
        this.f818b.c(i10);
    }

    public void setImageURI(@Nullable Uri uri) {
        super.setImageURI(uri);
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.a();
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintList(@Nullable ColorStateList colorStateList) {
        e eVar = this.f817a;
        if (eVar != null) {
            eVar.h(colorStateList);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportBackgroundTintMode(@Nullable PorterDuff.Mode mode) {
        e eVar = this.f817a;
        if (eVar != null) {
            eVar.i(mode);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportImageTintList(@Nullable ColorStateList colorStateList) {
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.d(colorStateList);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setSupportImageTintMode(@Nullable PorterDuff.Mode mode) {
        k kVar = this.f818b;
        if (kVar != null) {
            kVar.e(mode);
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public AppCompatImageButton(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        j0.a(context);
        h0.a(this, getContext());
        e eVar = new e(this);
        this.f817a = eVar;
        eVar.d(attributeSet, i10);
        k kVar = new k(this);
        this.f818b = kVar;
        kVar.b(attributeSet, i10);
    }
}
