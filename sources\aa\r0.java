package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERGeneralString */
public class r0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f221a;

    public r0(byte[] bArr) {
        this.f221a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof r0)) {
            return false;
        }
        return a.a(this.f221a, ((r0) qVar).f221a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f221a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(27, this.f221a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f221a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f221a.length) + 1 + this.f221a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
