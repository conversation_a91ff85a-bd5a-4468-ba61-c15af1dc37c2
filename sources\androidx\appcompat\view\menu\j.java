package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Rect;
import android.os.Parcelable;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.appcompat.R$dimen;
import androidx.appcompat.R$layout;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.widget.a0;
import androidx.appcompat.widget.v;
import java.util.Objects;
import q.d;

/* compiled from: StandardMenuPopup */
public final class j extends d implements PopupWindow.OnDismissListener, View.OnKeyListener {

    /* renamed from: y  reason: collision with root package name */
    public static final int f668y = R$layout.abc_popup_menu_item_layout;

    /* renamed from: b  reason: collision with root package name */
    public final Context f669b;

    /* renamed from: c  reason: collision with root package name */
    public final d f670c;

    /* renamed from: d  reason: collision with root package name */
    public final c f671d;

    /* renamed from: e  reason: collision with root package name */
    public final boolean f672e;

    /* renamed from: f  reason: collision with root package name */
    public final int f673f;

    /* renamed from: g  reason: collision with root package name */
    public final int f674g;
    public final int h;

    /* renamed from: i  reason: collision with root package name */
    public final a0 f675i;

    /* renamed from: j  reason: collision with root package name */
    public final ViewTreeObserver.OnGlobalLayoutListener f676j = new a();

    /* renamed from: k  reason: collision with root package name */
    public final View.OnAttachStateChangeListener f677k = new b();

    /* renamed from: l  reason: collision with root package name */
    public PopupWindow.OnDismissListener f678l;

    /* renamed from: m  reason: collision with root package name */
    public View f679m;

    /* renamed from: n  reason: collision with root package name */
    public View f680n;

    /* renamed from: o  reason: collision with root package name */
    public h.a f681o;

    /* renamed from: p  reason: collision with root package name */
    public ViewTreeObserver f682p;

    /* renamed from: q  reason: collision with root package name */
    public boolean f683q;

    /* renamed from: r  reason: collision with root package name */
    public boolean f684r;

    /* renamed from: s  reason: collision with root package name */
    public int f685s;

    /* renamed from: t  reason: collision with root package name */
    public int f686t = 0;

    /* renamed from: x  reason: collision with root package name */
    public boolean f687x;

    /* compiled from: StandardMenuPopup */
    public class a implements ViewTreeObserver.OnGlobalLayoutListener {
        public a() {
        }

        public void onGlobalLayout() {
            if (j.this.isShowing()) {
                j jVar = j.this;
                if (!jVar.f675i.f897n0) {
                    View view = jVar.f680n;
                    if (view == null || !view.isShown()) {
                        j.this.dismiss();
                    } else {
                        j.this.f675i.j();
                    }
                }
            }
        }
    }

    /* compiled from: StandardMenuPopup */
    public class b implements View.OnAttachStateChangeListener {
        public b() {
        }

        public void onViewAttachedToWindow(View view) {
        }

        public void onViewDetachedFromWindow(View view) {
            ViewTreeObserver viewTreeObserver = j.this.f682p;
            if (viewTreeObserver != null) {
                if (!viewTreeObserver.isAlive()) {
                    j.this.f682p = view.getViewTreeObserver();
                }
                j jVar = j.this;
                jVar.f682p.removeGlobalOnLayoutListener(jVar.f676j);
            }
            view.removeOnAttachStateChangeListener(this);
        }
    }

    public j(Context context, d dVar, View view, int i10, int i11, boolean z10) {
        this.f669b = context;
        this.f670c = dVar;
        this.f672e = z10;
        this.f671d = new c(dVar, LayoutInflater.from(context), z10, f668y);
        this.f674g = i10;
        this.h = i11;
        Resources resources = context.getResources();
        this.f673f = Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R$dimen.abc_config_prefDialogWidth));
        this.f679m = view;
        this.f675i = new a0(context, null, i10, i11);
        dVar.b(this, context);
    }

    @Override // androidx.appcompat.view.menu.h
    public void a(d dVar, boolean z10) {
        if (dVar == this.f670c) {
            dismiss();
            h.a aVar = this.f681o;
            if (aVar != null) {
                aVar.a(dVar, z10);
            }
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void b(boolean z10) {
        this.f684r = false;
        c cVar = this.f671d;
        if (cVar != null) {
            cVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean c() {
        return false;
    }

    @Override // q.f
    public void dismiss() {
        if (isShowing()) {
            this.f675i.dismiss();
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void e(Parcelable parcelable) {
    }

    /* JADX WARNING: Removed duplicated region for block: B:20:0x0070  */
    @Override // androidx.appcompat.view.menu.h
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean f(androidx.appcompat.view.menu.k r10) {
        /*
        // Method dump skipped, instructions count: 121
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.view.menu.j.f(androidx.appcompat.view.menu.k):boolean");
    }

    @Override // androidx.appcompat.view.menu.h
    public Parcelable g() {
        return null;
    }

    @Override // q.f
    public boolean isShowing() {
        return !this.f683q && this.f675i.isShowing();
    }

    @Override // q.f
    public void j() {
        View view;
        boolean z10 = true;
        if (!isShowing()) {
            if (this.f683q || (view = this.f679m) == null) {
                z10 = false;
            } else {
                this.f680n = view;
                this.f675i.f899o0.setOnDismissListener(this);
                a0 a0Var = this.f675i;
                a0Var.f900p = this;
                a0Var.p(true);
                View view2 = this.f680n;
                boolean z11 = this.f682p == null;
                ViewTreeObserver viewTreeObserver = view2.getViewTreeObserver();
                this.f682p = viewTreeObserver;
                if (z11) {
                    viewTreeObserver.addOnGlobalLayoutListener(this.f676j);
                }
                view2.addOnAttachStateChangeListener(this.f677k);
                a0 a0Var2 = this.f675i;
                a0Var2.f898o = view2;
                a0Var2.f893l = this.f686t;
                if (!this.f684r) {
                    this.f685s = d.n(this.f671d, null, this.f669b, this.f673f);
                    this.f684r = true;
                }
                this.f675i.o(this.f685s);
                this.f675i.f899o0.setInputMethodMode(2);
                a0 a0Var3 = this.f675i;
                Rect rect = this.f9729a;
                Objects.requireNonNull(a0Var3);
                a0Var3.f895m0 = rect != null ? new Rect(rect) : null;
                this.f675i.j();
                v vVar = this.f675i.f885c;
                vVar.setOnKeyListener(this);
                if (this.f687x && this.f670c.f615m != null) {
                    FrameLayout frameLayout = (FrameLayout) LayoutInflater.from(this.f669b).inflate(R$layout.abc_popup_menu_header_item_layout, (ViewGroup) vVar, false);
                    TextView textView = (TextView) frameLayout.findViewById(16908310);
                    if (textView != null) {
                        textView.setText(this.f670c.f615m);
                    }
                    frameLayout.setEnabled(false);
                    vVar.addHeaderView(frameLayout, null, false);
                }
                this.f675i.h(this.f671d);
                this.f675i.j();
            }
        }
        if (!z10) {
            throw new IllegalStateException("StandardMenuPopup cannot be used without an anchor");
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void k(h.a aVar) {
        this.f681o = aVar;
    }

    @Override // q.f
    public ListView l() {
        return this.f675i.f885c;
    }

    @Override // q.d
    public void m(d dVar) {
    }

    @Override // q.d
    public void o(View view) {
        this.f679m = view;
    }

    public void onDismiss() {
        this.f683q = true;
        this.f670c.c(true);
        ViewTreeObserver viewTreeObserver = this.f682p;
        if (viewTreeObserver != null) {
            if (!viewTreeObserver.isAlive()) {
                this.f682p = this.f680n.getViewTreeObserver();
            }
            this.f682p.removeGlobalOnLayoutListener(this.f676j);
            this.f682p = null;
        }
        this.f680n.removeOnAttachStateChangeListener(this.f677k);
        PopupWindow.OnDismissListener onDismissListener = this.f678l;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    public boolean onKey(View view, int i10, KeyEvent keyEvent) {
        if (keyEvent.getAction() != 1 || i10 != 82) {
            return false;
        }
        dismiss();
        return true;
    }

    @Override // q.d
    public void p(boolean z10) {
        this.f671d.f599c = z10;
    }

    @Override // q.d
    public void q(int i10) {
        this.f686t = i10;
    }

    @Override // q.d
    public void r(int i10) {
        this.f675i.f888f = i10;
    }

    @Override // q.d
    public void s(PopupWindow.OnDismissListener onDismissListener) {
        this.f678l = onDismissListener;
    }

    @Override // q.d
    public void t(boolean z10) {
        this.f687x = z10;
    }

    @Override // q.d
    public void u(int i10) {
        a0 a0Var = this.f675i;
        a0Var.f889g = i10;
        a0Var.f890i = true;
    }
}
