package androidx.appcompat.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$layout;
import androidx.appcompat.view.menu.ActionMenuItemView;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.g;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.view.menu.i;
import androidx.appcompat.view.menu.k;
import androidx.appcompat.widget.ActionMenuView;
import j0.a;
import java.util.ArrayList;

public class ActionMenuPresenter extends androidx.appcompat.view.menu.a implements a.AbstractC0102a {

    /* renamed from: j  reason: collision with root package name */
    public d f742j;

    /* renamed from: k  reason: collision with root package name */
    public Drawable f743k;

    /* renamed from: l  reason: collision with root package name */
    public boolean f744l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f745m;

    /* renamed from: m0  reason: collision with root package name */
    public b f746m0;

    /* renamed from: n  reason: collision with root package name */
    public boolean f747n;

    /* renamed from: n0  reason: collision with root package name */
    public final f f748n0 = new f();

    /* renamed from: o  reason: collision with root package name */
    public int f749o;

    /* renamed from: o0  reason: collision with root package name */
    public int f750o0;

    /* renamed from: p  reason: collision with root package name */
    public int f751p;

    /* renamed from: q  reason: collision with root package name */
    public int f752q;

    /* renamed from: r  reason: collision with root package name */
    public boolean f753r;

    /* renamed from: s  reason: collision with root package name */
    public final SparseBooleanArray f754s = new SparseBooleanArray();

    /* renamed from: t  reason: collision with root package name */
    public e f755t;

    /* renamed from: x  reason: collision with root package name */
    public a f756x;

    /* renamed from: y  reason: collision with root package name */
    public c f757y;

    @SuppressLint({"BanParcelableUsage"})
    public static class SavedState implements Parcelable {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public int f758a;

        public class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState() {
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeInt(this.f758a);
        }

        public SavedState(Parcel parcel) {
            this.f758a = parcel.readInt();
        }
    }

    public class a extends g {
        public a(Context context, k kVar, View view) {
            super(context, kVar, view, false, R$attr.actionOverflowMenuStyle, 0);
            if (!kVar.A.g()) {
                View view2 = ActionMenuPresenter.this.f742j;
                this.f661f = view2 == null ? (View) ActionMenuPresenter.this.h : view2;
            }
            d(ActionMenuPresenter.this.f748n0);
        }

        @Override // androidx.appcompat.view.menu.g
        public void c() {
            ActionMenuPresenter actionMenuPresenter = ActionMenuPresenter.this;
            actionMenuPresenter.f756x = null;
            actionMenuPresenter.f750o0 = 0;
            super.c();
        }
    }

    public class b extends ActionMenuItemView.b {
        public b() {
        }
    }

    public class c implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public e f761a;

        public c(e eVar) {
            this.f761a = eVar;
        }

        public void run() {
            d.a aVar;
            androidx.appcompat.view.menu.d dVar = ActionMenuPresenter.this.f582c;
            if (!(dVar == null || (aVar = dVar.f608e) == null)) {
                aVar.b(dVar);
            }
            View view = (View) ActionMenuPresenter.this.h;
            if (!(view == null || view.getWindowToken() == null || !this.f761a.f())) {
                ActionMenuPresenter.this.f755t = this.f761a;
            }
            ActionMenuPresenter.this.f757y = null;
        }
    }

    public class d extends AppCompatImageView implements ActionMenuView.a {

        public class a extends x {
            public a(View view, ActionMenuPresenter actionMenuPresenter) {
                super(view);
            }

            @Override // androidx.appcompat.widget.x
            public q.f b() {
                e eVar = ActionMenuPresenter.this.f755t;
                if (eVar == null) {
                    return null;
                }
                return eVar.a();
            }

            @Override // androidx.appcompat.widget.x
            public boolean c() {
                ActionMenuPresenter.this.p();
                return true;
            }

            @Override // androidx.appcompat.widget.x
            public boolean d() {
                ActionMenuPresenter actionMenuPresenter = ActionMenuPresenter.this;
                if (actionMenuPresenter.f757y != null) {
                    return false;
                }
                actionMenuPresenter.m();
                return true;
            }
        }

        public d(Context context) {
            super(context, null, R$attr.actionOverflowButtonStyle);
            setClickable(true);
            setFocusable(true);
            setVisibility(0);
            setEnabled(true);
            setTooltipText(getContentDescription());
            setOnTouchListener(new a(this, ActionMenuPresenter.this));
        }

        @Override // androidx.appcompat.widget.ActionMenuView.a
        public boolean a() {
            return false;
        }

        @Override // androidx.appcompat.widget.ActionMenuView.a
        public boolean b() {
            return false;
        }

        public boolean performClick() {
            if (super.performClick()) {
                return true;
            }
            playSoundEffect(0);
            ActionMenuPresenter.this.p();
            return true;
        }

        public boolean setFrame(int i10, int i11, int i12, int i13) {
            boolean frame = super.setFrame(i10, i11, i12, i13);
            Drawable drawable = getDrawable();
            Drawable background = getBackground();
            if (!(drawable == null || background == null)) {
                int width = getWidth();
                int height = getHeight();
                int max = Math.max(width, height) / 2;
                int paddingLeft = (width + (getPaddingLeft() - getPaddingRight())) / 2;
                int paddingTop = (height + (getPaddingTop() - getPaddingBottom())) / 2;
                background.setHotspotBounds(paddingLeft - max, paddingTop - max, paddingLeft + max, paddingTop + max);
            }
            return frame;
        }
    }

    public class e extends g {
        public e(Context context, androidx.appcompat.view.menu.d dVar, View view, boolean z10) {
            super(context, dVar, view, z10, R$attr.actionOverflowMenuStyle, 0);
            this.f662g = 8388613;
            d(ActionMenuPresenter.this.f748n0);
        }

        @Override // androidx.appcompat.view.menu.g
        public void c() {
            androidx.appcompat.view.menu.d dVar = ActionMenuPresenter.this.f582c;
            if (dVar != null) {
                dVar.c(true);
            }
            ActionMenuPresenter.this.f755t = null;
            super.c();
        }
    }

    public class f implements h.a {
        public f() {
        }

        @Override // androidx.appcompat.view.menu.h.a
        public void a(@NonNull androidx.appcompat.view.menu.d dVar, boolean z10) {
            if (dVar instanceof k) {
                dVar.k().c(false);
            }
            h.a aVar = ActionMenuPresenter.this.f584e;
            if (aVar != null) {
                aVar.a(dVar, z10);
            }
        }

        @Override // androidx.appcompat.view.menu.h.a
        public boolean b(@NonNull androidx.appcompat.view.menu.d dVar) {
            ActionMenuPresenter actionMenuPresenter = ActionMenuPresenter.this;
            if (dVar == actionMenuPresenter.f582c) {
                return false;
            }
            actionMenuPresenter.f750o0 = ((k) dVar).A.f630a;
            h.a aVar = actionMenuPresenter.f584e;
            if (aVar != null) {
                return aVar.b(dVar);
            }
            return false;
        }
    }

    public ActionMenuPresenter(Context context) {
        super(context, R$layout.abc_action_menu_layout, R$layout.abc_action_menu_item_layout);
    }

    @Override // androidx.appcompat.view.menu.h
    public void a(androidx.appcompat.view.menu.d dVar, boolean z10) {
        j();
        h.a aVar = this.f584e;
        if (aVar != null) {
            aVar.a(dVar, z10);
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void b(boolean z10) {
        i iVar;
        int i10;
        boolean z11;
        ViewGroup viewGroup = (ViewGroup) this.h;
        ArrayList<androidx.appcompat.view.menu.f> arrayList = null;
        boolean z12 = false;
        if (viewGroup != null) {
            androidx.appcompat.view.menu.d dVar = this.f582c;
            if (dVar != null) {
                dVar.i();
                ArrayList<androidx.appcompat.view.menu.f> l10 = this.f582c.l();
                int size = l10.size();
                i10 = 0;
                for (int i11 = 0; i11 < size; i11++) {
                    androidx.appcompat.view.menu.f fVar = l10.get(i11);
                    if (fVar.g()) {
                        View childAt = viewGroup.getChildAt(i10);
                        androidx.appcompat.view.menu.f itemData = childAt instanceof i.a ? ((i.a) childAt).getItemData() : null;
                        View l11 = l(fVar, childAt, viewGroup);
                        if (fVar != itemData) {
                            l11.setPressed(false);
                            l11.jumpDrawablesToCurrentState();
                        }
                        if (l11 != childAt) {
                            ViewGroup viewGroup2 = (ViewGroup) l11.getParent();
                            if (viewGroup2 != null) {
                                viewGroup2.removeView(l11);
                            }
                            ((ViewGroup) this.h).addView(l11, i10);
                        }
                        i10++;
                    }
                }
            } else {
                i10 = 0;
            }
            while (i10 < viewGroup.getChildCount()) {
                if (viewGroup.getChildAt(i10) == this.f742j) {
                    z11 = false;
                } else {
                    viewGroup.removeViewAt(i10);
                    z11 = true;
                }
                if (!z11) {
                    i10++;
                }
            }
        }
        ((View) this.h).requestLayout();
        androidx.appcompat.view.menu.d dVar2 = this.f582c;
        if (dVar2 != null) {
            dVar2.i();
            ArrayList<androidx.appcompat.view.menu.f> arrayList2 = dVar2.f611i;
            int size2 = arrayList2.size();
            for (int i12 = 0; i12 < size2; i12++) {
                j0.a aVar = arrayList2.get(i12).A;
                if (aVar != null) {
                    aVar.f6899a = this;
                }
            }
        }
        androidx.appcompat.view.menu.d dVar3 = this.f582c;
        if (dVar3 != null) {
            dVar3.i();
            arrayList = dVar3.f612j;
        }
        if (this.f745m && arrayList != null) {
            int size3 = arrayList.size();
            if (size3 == 1) {
                z12 = !arrayList.get(0).C;
            } else if (size3 > 0) {
                z12 = true;
            }
        }
        if (z12) {
            if (this.f742j == null) {
                this.f742j = new d(this.f580a);
            }
            ViewGroup viewGroup3 = (ViewGroup) this.f742j.getParent();
            if (viewGroup3 != this.h) {
                if (viewGroup3 != null) {
                    viewGroup3.removeView(this.f742j);
                }
                ActionMenuView actionMenuView = (ActionMenuView) this.h;
                d dVar4 = this.f742j;
                ActionMenuView.LayoutParams l12 = actionMenuView.h();
                l12.f779c = true;
                actionMenuView.addView(dVar4, l12);
            }
        } else {
            d dVar5 = this.f742j;
            if (dVar5 != null && dVar5.getParent() == (iVar = this.h)) {
                ((ViewGroup) iVar).removeView(this.f742j);
            }
        }
        ((ActionMenuView) this.h).setOverflowReserved(this.f745m);
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean c() {
        int i10;
        ArrayList<androidx.appcompat.view.menu.f> arrayList;
        int i11;
        boolean z10;
        androidx.appcompat.view.menu.d dVar = this.f582c;
        if (dVar != null) {
            arrayList = dVar.l();
            i10 = arrayList.size();
        } else {
            i10 = 0;
            arrayList = null;
        }
        int i12 = this.f752q;
        int i13 = this.f751p;
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewGroup viewGroup = (ViewGroup) this.h;
        int i14 = 0;
        boolean z11 = false;
        int i15 = 0;
        int i16 = 0;
        while (true) {
            i11 = 2;
            z10 = true;
            if (i14 >= i10) {
                break;
            }
            androidx.appcompat.view.menu.f fVar = arrayList.get(i14);
            int i17 = fVar.f653y;
            if ((i17 & 2) == 2) {
                i16++;
            } else if ((i17 & 1) == 1) {
                i15++;
            } else {
                z11 = true;
            }
            if (this.f753r && fVar.C) {
                i12 = 0;
            }
            i14++;
        }
        if (this.f745m && (z11 || i15 + i16 > i12)) {
            i12--;
        }
        int i18 = i12 - i16;
        SparseBooleanArray sparseBooleanArray = this.f754s;
        sparseBooleanArray.clear();
        int i19 = 0;
        int i20 = 0;
        while (i19 < i10) {
            androidx.appcompat.view.menu.f fVar2 = arrayList.get(i19);
            int i21 = fVar2.f653y;
            if ((i21 & 2) == i11 ? z10 : false) {
                View l10 = l(fVar2, null, viewGroup);
                l10.measure(makeMeasureSpec, makeMeasureSpec);
                int measuredWidth = l10.getMeasuredWidth();
                i13 -= measuredWidth;
                if (i20 == 0) {
                    i20 = measuredWidth;
                }
                int i22 = fVar2.f631b;
                if (i22 != 0) {
                    sparseBooleanArray.put(i22, z10);
                }
                fVar2.l(z10);
            } else if ((i21 & 1) == z10 ? z10 : false) {
                int i23 = fVar2.f631b;
                boolean z12 = sparseBooleanArray.get(i23);
                boolean z13 = ((i18 > 0 || z12) && i13 > 0) ? z10 : false;
                if (z13) {
                    View l11 = l(fVar2, null, viewGroup);
                    l11.measure(makeMeasureSpec, makeMeasureSpec);
                    int measuredWidth2 = l11.getMeasuredWidth();
                    i13 -= measuredWidth2;
                    if (i20 == 0) {
                        i20 = measuredWidth2;
                    }
                    z13 &= i13 + i20 > 0;
                }
                if (z13 && i23 != 0) {
                    sparseBooleanArray.put(i23, true);
                } else if (z12) {
                    sparseBooleanArray.put(i23, false);
                    for (int i24 = 0; i24 < i19; i24++) {
                        androidx.appcompat.view.menu.f fVar3 = arrayList.get(i24);
                        if (fVar3.f631b == i23) {
                            if (fVar3.g()) {
                                i18++;
                            }
                            fVar3.l(false);
                        }
                    }
                }
                if (z13) {
                    i18--;
                }
                fVar2.l(z13);
            } else {
                fVar2.l(false);
                i19++;
                i11 = 2;
                z10 = true;
            }
            i19++;
            i11 = 2;
            z10 = true;
        }
        return z10;
    }

    @Override // androidx.appcompat.view.menu.h
    public void d(@NonNull Context context, @Nullable androidx.appcompat.view.menu.d dVar) {
        this.f581b = context;
        LayoutInflater.from(context);
        this.f582c = dVar;
        Resources resources = context.getResources();
        if (!this.f747n) {
            this.f745m = true;
        }
        int i10 = 2;
        this.f749o = context.getResources().getDisplayMetrics().widthPixels / 2;
        Configuration configuration = context.getResources().getConfiguration();
        int i11 = configuration.screenWidthDp;
        int i12 = configuration.screenHeightDp;
        if (configuration.smallestScreenWidthDp > 600 || i11 > 600 || ((i11 > 960 && i12 > 720) || (i11 > 720 && i12 > 960))) {
            i10 = 5;
        } else if (i11 >= 500 || ((i11 > 640 && i12 > 480) || (i11 > 480 && i12 > 640))) {
            i10 = 4;
        } else if (i11 >= 360) {
            i10 = 3;
        }
        this.f752q = i10;
        int i13 = this.f749o;
        if (this.f745m) {
            if (this.f742j == null) {
                d dVar2 = new d(this.f580a);
                this.f742j = dVar2;
                if (this.f744l) {
                    dVar2.setImageDrawable(this.f743k);
                    this.f743k = null;
                    this.f744l = false;
                }
                int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, 0);
                this.f742j.measure(makeMeasureSpec, makeMeasureSpec);
            }
            i13 -= this.f742j.getMeasuredWidth();
        } else {
            this.f742j = null;
        }
        this.f751p = i13;
        float f10 = resources.getDisplayMetrics().density;
    }

    @Override // androidx.appcompat.view.menu.h
    public void e(Parcelable parcelable) {
        int i10;
        MenuItem findItem;
        if ((parcelable instanceof SavedState) && (i10 = ((SavedState) parcelable).f758a) > 0 && (findItem = this.f582c.findItem(i10)) != null) {
            f((k) findItem.getSubMenu());
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean f(k kVar) {
        boolean z10 = false;
        if (!kVar.hasVisibleItems()) {
            return false;
        }
        k kVar2 = kVar;
        while (true) {
            androidx.appcompat.view.menu.d dVar = kVar2.f690z;
            if (dVar == this.f582c) {
                break;
            }
            kVar2 = (k) dVar;
        }
        androidx.appcompat.view.menu.f fVar = kVar2.A;
        ViewGroup viewGroup = (ViewGroup) this.h;
        View view = null;
        if (viewGroup != null) {
            int childCount = viewGroup.getChildCount();
            int i10 = 0;
            while (true) {
                if (i10 >= childCount) {
                    break;
                }
                View childAt = viewGroup.getChildAt(i10);
                if ((childAt instanceof i.a) && ((i.a) childAt).getItemData() == fVar) {
                    view = childAt;
                    break;
                }
                i10++;
            }
        }
        if (view == null) {
            return false;
        }
        this.f750o0 = kVar.A.f630a;
        int size = kVar.size();
        int i11 = 0;
        while (true) {
            if (i11 >= size) {
                break;
            }
            MenuItem item = kVar.getItem(i11);
            if (item.isVisible() && item.getIcon() != null) {
                z10 = true;
                break;
            }
            i11++;
        }
        a aVar = new a(this.f581b, kVar, view);
        this.f756x = aVar;
        aVar.h = z10;
        q.d dVar2 = aVar.f664j;
        if (dVar2 != null) {
            dVar2.p(z10);
        }
        if (this.f756x.f()) {
            h.a aVar2 = this.f584e;
            if (aVar2 != null) {
                aVar2.b(kVar);
            }
            return true;
        }
        throw new IllegalStateException("MenuPopupHelper cannot be used without an anchor");
    }

    @Override // androidx.appcompat.view.menu.h
    public Parcelable g() {
        SavedState savedState = new SavedState();
        savedState.f758a = this.f750o0;
        return savedState;
    }

    public boolean j() {
        return m() | n();
    }

    public View l(androidx.appcompat.view.menu.f fVar, View view, ViewGroup viewGroup) {
        i.a aVar;
        View actionView = fVar.getActionView();
        int i10 = 0;
        if (actionView == null || fVar.f()) {
            if (view instanceof i.a) {
                aVar = (i.a) view;
            } else {
                aVar = (i.a) this.f583d.inflate(this.f586g, viewGroup, false);
            }
            aVar.d(fVar, 0);
            ActionMenuItemView actionMenuItemView = (ActionMenuItemView) aVar;
            actionMenuItemView.setItemInvoker((ActionMenuView) this.h);
            if (this.f746m0 == null) {
                this.f746m0 = new b();
            }
            actionMenuItemView.setPopupCallback(this.f746m0);
            actionView = (View) aVar;
        }
        if (fVar.C) {
            i10 = 8;
        }
        actionView.setVisibility(i10);
        ActionMenuView actionMenuView = (ActionMenuView) viewGroup;
        ViewGroup.LayoutParams layoutParams = actionView.getLayoutParams();
        if (!actionMenuView.checkLayoutParams(layoutParams)) {
            actionView.setLayoutParams(actionMenuView.j(layoutParams));
        }
        return actionView;
    }

    public boolean m() {
        i iVar;
        c cVar = this.f757y;
        if (cVar == null || (iVar = this.h) == null) {
            e eVar = this.f755t;
            if (eVar == null) {
                return false;
            }
            if (eVar.b()) {
                eVar.f664j.dismiss();
            }
            return true;
        }
        ((View) iVar).removeCallbacks(cVar);
        this.f757y = null;
        return true;
    }

    public boolean n() {
        a aVar = this.f756x;
        if (aVar == null) {
            return false;
        }
        if (!aVar.b()) {
            return true;
        }
        aVar.f664j.dismiss();
        return true;
    }

    public boolean o() {
        e eVar = this.f755t;
        return eVar != null && eVar.b();
    }

    public boolean p() {
        androidx.appcompat.view.menu.d dVar;
        if (!this.f745m || o() || (dVar = this.f582c) == null || this.h == null || this.f757y != null) {
            return false;
        }
        dVar.i();
        if (dVar.f612j.isEmpty()) {
            return false;
        }
        c cVar = new c(new e(this.f581b, this.f582c, this.f742j, true));
        this.f757y = cVar;
        ((View) this.h).post(cVar);
        return true;
    }
}
