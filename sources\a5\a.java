package a5;

import a5.a;
import java.util.concurrent.atomic.AtomicLong;

/* compiled from: AbstractConstant */
public abstract class a<T extends a<T>> implements e<T> {

    /* renamed from: d  reason: collision with root package name */
    public static final AtomicLong f67d = new AtomicLong();

    /* renamed from: a  reason: collision with root package name */
    public final int f68a;

    /* renamed from: b  reason: collision with root package name */
    public final String f69b;

    /* renamed from: c  reason: collision with root package name */
    public final long f70c = f67d.getAndIncrement();

    public a(int i10, String str) {
        this.f68a = i10;
        this.f69b = str;
    }

    /* renamed from: a */
    public final int compareTo(T t2) {
        if (this == t2) {
            return 0;
        }
        int hashCode = super.hashCode() - super.hashCode();
        if (hashCode != 0) {
            return hashCode;
        }
        long j10 = this.f70c;
        long j11 = t2.f70c;
        if (j10 < j11) {
            return -1;
        }
        if (j10 > j11) {
            return 1;
        }
        throw new Error("failed to compare two different constants");
    }

    public final boolean equals(Object obj) {
        return super.equals(obj);
    }

    public final int hashCode() {
        return super.hashCode();
    }

    public final String toString() {
        return this.f69b;
    }
}
