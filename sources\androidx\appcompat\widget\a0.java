package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.widget.HeaderViewListAdapter;
import android.widget.ListAdapter;
import android.widget.PopupWindow;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.view.menu.ListMenuItemView;
import androidx.appcompat.view.menu.c;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.f;
import java.lang.reflect.Method;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuPopupWindow */
public class a0 extends ListPopupWindow implements z {

    /* renamed from: s0  reason: collision with root package name */
    public static Method f1042s0;

    /* renamed from: r0  reason: collision with root package name */
    public z f1043r0;

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    /* compiled from: MenuPopupWindow */
    public static class a extends v {

        /* renamed from: n  reason: collision with root package name */
        public final int f1044n;

        /* renamed from: o  reason: collision with root package name */
        public final int f1045o;

        /* renamed from: p  reason: collision with root package name */
        public z f1046p;

        /* renamed from: q  reason: collision with root package name */
        public MenuItem f1047q;

        public a(Context context, boolean z10) {
            super(context, z10);
            if (1 == context.getResources().getConfiguration().getLayoutDirection()) {
                this.f1044n = 21;
                this.f1045o = 22;
                return;
            }
            this.f1044n = 22;
            this.f1045o = 21;
        }

        @Override // androidx.appcompat.widget.v
        public boolean onHoverEvent(MotionEvent motionEvent) {
            int i10;
            c cVar;
            int pointToPosition;
            int i11;
            if (this.f1046p != null) {
                ListAdapter adapter = getAdapter();
                if (adapter instanceof HeaderViewListAdapter) {
                    HeaderViewListAdapter headerViewListAdapter = (HeaderViewListAdapter) adapter;
                    i10 = headerViewListAdapter.getHeadersCount();
                    cVar = (c) headerViewListAdapter.getWrappedAdapter();
                } else {
                    i10 = 0;
                    cVar = (c) adapter;
                }
                f fVar = null;
                if (motionEvent.getAction() != 10 && (pointToPosition = pointToPosition((int) motionEvent.getX(), (int) motionEvent.getY())) != -1 && (i11 = pointToPosition - i10) >= 0 && i11 < cVar.getCount()) {
                    fVar = cVar.getItem(i11);
                }
                MenuItem menuItem = this.f1047q;
                if (menuItem != fVar) {
                    d dVar = cVar.f597a;
                    if (menuItem != null) {
                        this.f1046p.k(dVar, menuItem);
                    }
                    this.f1047q = fVar;
                    if (fVar != null) {
                        this.f1046p.i(dVar, fVar);
                    }
                }
            }
            return super.onHoverEvent(motionEvent);
        }

        public boolean onKeyDown(int i10, KeyEvent keyEvent) {
            ListMenuItemView listMenuItemView = (ListMenuItemView) getSelectedView();
            if (listMenuItemView != null && i10 == this.f1044n) {
                if (listMenuItemView.isEnabled() && listMenuItemView.getItemData().hasSubMenu()) {
                    performItemClick(listMenuItemView, getSelectedItemPosition(), getSelectedItemId());
                }
                return true;
            } else if (listMenuItemView == null || i10 != this.f1045o) {
                return super.onKeyDown(i10, keyEvent);
            } else {
                setSelection(-1);
                ((c) getAdapter()).f597a.c(false);
                return true;
            }
        }

        public void setHoverListener(z zVar) {
            this.f1046p = zVar;
        }

        @Override // android.widget.AbsListView, androidx.appcompat.widget.v
        public /* bridge */ /* synthetic */ void setSelector(Drawable drawable) {
            super.setSelector(drawable);
        }
    }

    static {
        try {
            if (Build.VERSION.SDK_INT <= 28) {
                f1042s0 = PopupWindow.class.getDeclaredMethod("setTouchModal", Boolean.TYPE);
            }
        } catch (NoSuchMethodException unused) {
            Log.i("MenuPopupWindow", "Could not find method setTouchModal() on PopupWindow. Oh well.");
        }
    }

    public a0(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10, int i11) {
        super(context, null, i10, i11);
    }

    @Override // androidx.appcompat.widget.z
    public void i(@NonNull d dVar, @NonNull MenuItem menuItem) {
        z zVar = this.f1043r0;
        if (zVar != null) {
            zVar.i(dVar, menuItem);
        }
    }

    @Override // androidx.appcompat.widget.z
    public void k(@NonNull d dVar, @NonNull MenuItem menuItem) {
        z zVar = this.f1043r0;
        if (zVar != null) {
            zVar.k(dVar, menuItem);
        }
    }

    @Override // androidx.appcompat.widget.ListPopupWindow
    @NonNull
    public v n(Context context, boolean z10) {
        a aVar = new a(context, z10);
        aVar.setHoverListener(this);
        return aVar;
    }
}
