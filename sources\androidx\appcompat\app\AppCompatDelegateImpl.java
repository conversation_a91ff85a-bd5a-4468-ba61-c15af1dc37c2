package androidx.appcompat.app;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.UiModeManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.AndroidRuntimeException;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.ActionMode;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.KeyboardShortcutGroup;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleRes;
import androidx.annotation.VisibleForTesting;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$color;
import androidx.appcompat.R$id;
import androidx.appcompat.R$layout;
import androidx.appcompat.R$styleable;
import androidx.appcompat.app.o;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ContentFrameLayout;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.b0;
import androidx.appcompat.widget.m0;
import androidx.appcompat.widget.q0;
import androidx.appcompat.widget.s;
import androidx.core.content.PermissionChecker;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import j0.m;
import j0.o;
import j0.q;
import java.lang.ref.WeakReference;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import p.a;
import p.e;

@RestrictTo({RestrictTo.Scope.LIBRARY})
public class AppCompatDelegateImpl extends AppCompatDelegate implements d.a, LayoutInflater.Factory2 {
    public static final u.g<String, Integer> O0 = new u.g<>();
    public static final int[] P0 = {16842836};
    public static final boolean Q0 = (!"robolectric".equals(Build.FINGERPRINT));
    public static final boolean R0 = true;
    public boolean A0;
    public int B0 = -100;
    public int C0;
    public boolean D0;
    public boolean E0;
    public g F0;
    public g G0;
    public boolean H0;
    public int I0;
    public final Runnable J0 = new a();
    public boolean K0;
    public Rect L0;
    public Rect M0;
    public l N0;

    /* renamed from: c  reason: collision with root package name */
    public final Object f369c;

    /* renamed from: d  reason: collision with root package name */
    public final Context f370d;

    /* renamed from: e  reason: collision with root package name */
    public Window f371e;

    /* renamed from: f  reason: collision with root package name */
    public e f372f;

    /* renamed from: g  reason: collision with root package name */
    public final f f373g;
    public ActionBar h;

    /* renamed from: i  reason: collision with root package name */
    public MenuInflater f374i;

    /* renamed from: j  reason: collision with root package name */
    public CharSequence f375j;

    /* renamed from: k  reason: collision with root package name */
    public s f376k;

    /* renamed from: l  reason: collision with root package name */
    public c f377l;

    /* renamed from: m  reason: collision with root package name */
    public j f378m;

    /* renamed from: m0  reason: collision with root package name */
    public boolean f379m0;

    /* renamed from: n  reason: collision with root package name */
    public p.a f380n;

    /* renamed from: n0  reason: collision with root package name */
    public boolean f381n0;

    /* renamed from: o  reason: collision with root package name */
    public ActionBarContextView f382o;

    /* renamed from: o0  reason: collision with root package name */
    public boolean f383o0;

    /* renamed from: p  reason: collision with root package name */
    public PopupWindow f384p;

    /* renamed from: p0  reason: collision with root package name */
    public boolean f385p0;

    /* renamed from: q  reason: collision with root package name */
    public Runnable f386q;

    /* renamed from: q0  reason: collision with root package name */
    public boolean f387q0;

    /* renamed from: r  reason: collision with root package name */
    public m f388r = null;

    /* renamed from: r0  reason: collision with root package name */
    public boolean f389r0;

    /* renamed from: s  reason: collision with root package name */
    public boolean f390s;

    /* renamed from: s0  reason: collision with root package name */
    public boolean f391s0;

    /* renamed from: t  reason: collision with root package name */
    public ViewGroup f392t;

    /* renamed from: t0  reason: collision with root package name */
    public boolean f393t0;

    /* renamed from: u0  reason: collision with root package name */
    public PanelFeatureState[] f394u0;

    /* renamed from: v0  reason: collision with root package name */
    public PanelFeatureState f395v0;

    /* renamed from: w0  reason: collision with root package name */
    public boolean f396w0;

    /* renamed from: x  reason: collision with root package name */
    public TextView f397x;

    /* renamed from: x0  reason: collision with root package name */
    public boolean f398x0;

    /* renamed from: y  reason: collision with root package name */
    public View f399y;

    /* renamed from: y0  reason: collision with root package name */
    public boolean f400y0;

    /* renamed from: z0  reason: collision with root package name */
    public boolean f401z0;

    public static final class PanelFeatureState {

        /* renamed from: a  reason: collision with root package name */
        public int f402a;

        /* renamed from: b  reason: collision with root package name */
        public int f403b;

        /* renamed from: c  reason: collision with root package name */
        public int f404c;

        /* renamed from: d  reason: collision with root package name */
        public int f405d;

        /* renamed from: e  reason: collision with root package name */
        public ViewGroup f406e;

        /* renamed from: f  reason: collision with root package name */
        public View f407f;

        /* renamed from: g  reason: collision with root package name */
        public View f408g;
        public androidx.appcompat.view.menu.d h;

        /* renamed from: i  reason: collision with root package name */
        public androidx.appcompat.view.menu.b f409i;

        /* renamed from: j  reason: collision with root package name */
        public Context f410j;

        /* renamed from: k  reason: collision with root package name */
        public boolean f411k;

        /* renamed from: l  reason: collision with root package name */
        public boolean f412l;

        /* renamed from: m  reason: collision with root package name */
        public boolean f413m;

        /* renamed from: n  reason: collision with root package name */
        public boolean f414n = false;

        /* renamed from: o  reason: collision with root package name */
        public boolean f415o;

        /* renamed from: p  reason: collision with root package name */
        public Bundle f416p;

        @SuppressLint({"BanParcelableUsage"})
        public static class SavedState implements Parcelable {
            public static final Parcelable.Creator<SavedState> CREATOR = new a();

            /* renamed from: a  reason: collision with root package name */
            public int f417a;

            /* renamed from: b  reason: collision with root package name */
            public boolean f418b;

            /* renamed from: c  reason: collision with root package name */
            public Bundle f419c;

            public class a implements Parcelable.ClassLoaderCreator<SavedState> {
                /* Return type fixed from 'java.lang.Object' to match base method */
                @Override // android.os.Parcelable.ClassLoaderCreator
                public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                    return SavedState.d(parcel, classLoader);
                }

                @Override // android.os.Parcelable.Creator
                public Object[] newArray(int i10) {
                    return new SavedState[i10];
                }

                @Override // android.os.Parcelable.Creator
                public Object createFromParcel(Parcel parcel) {
                    return SavedState.d(parcel, null);
                }
            }

            public static SavedState d(Parcel parcel, ClassLoader classLoader) {
                SavedState savedState = new SavedState();
                savedState.f417a = parcel.readInt();
                boolean z10 = true;
                if (parcel.readInt() != 1) {
                    z10 = false;
                }
                savedState.f418b = z10;
                if (z10) {
                    savedState.f419c = parcel.readBundle(classLoader);
                }
                return savedState;
            }

            public int describeContents() {
                return 0;
            }

            public void writeToParcel(Parcel parcel, int i10) {
                parcel.writeInt(this.f417a);
                parcel.writeInt(this.f418b ? 1 : 0);
                if (this.f418b) {
                    parcel.writeBundle(this.f419c);
                }
            }
        }

        public PanelFeatureState(int i10) {
            this.f402a = i10;
        }

        public void a(androidx.appcompat.view.menu.d dVar) {
            androidx.appcompat.view.menu.b bVar;
            androidx.appcompat.view.menu.d dVar2 = this.h;
            if (dVar != dVar2) {
                if (dVar2 != null) {
                    dVar2.t(this.f409i);
                }
                this.h = dVar;
                if (dVar != null && (bVar = this.f409i) != null) {
                    dVar.b(bVar, dVar.f604a);
                }
            }
        }
    }

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            if ((appCompatDelegateImpl.I0 & 1) != 0) {
                appCompatDelegateImpl.K(0);
            }
            AppCompatDelegateImpl appCompatDelegateImpl2 = AppCompatDelegateImpl.this;
            if ((appCompatDelegateImpl2.I0 & 4096) != 0) {
                appCompatDelegateImpl2.K(108);
            }
            AppCompatDelegateImpl appCompatDelegateImpl3 = AppCompatDelegateImpl.this;
            appCompatDelegateImpl3.H0 = false;
            appCompatDelegateImpl3.I0 = 0;
        }
    }

    public class b implements a {
        public b(AppCompatDelegateImpl appCompatDelegateImpl) {
        }
    }

    public final class c implements h.a {
        public c() {
        }

        @Override // androidx.appcompat.view.menu.h.a
        public void a(@NonNull androidx.appcompat.view.menu.d dVar, boolean z10) {
            AppCompatDelegateImpl.this.G(dVar);
        }

        @Override // androidx.appcompat.view.menu.h.a
        public boolean b(@NonNull androidx.appcompat.view.menu.d dVar) {
            Window.Callback R = AppCompatDelegateImpl.this.R();
            if (R == null) {
                return true;
            }
            R.onMenuOpened(108, dVar);
            return true;
        }
    }

    public class d implements a.AbstractC0146a {

        /* renamed from: a  reason: collision with root package name */
        public a.AbstractC0146a f422a;

        public class a extends o {
            public a() {
            }

            @Override // j0.n
            public void d(View view) {
                AppCompatDelegateImpl.this.f382o.setVisibility(8);
                AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
                PopupWindow popupWindow = appCompatDelegateImpl.f384p;
                if (popupWindow != null) {
                    popupWindow.dismiss();
                } else if (appCompatDelegateImpl.f382o.getParent() instanceof View) {
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    ((View) AppCompatDelegateImpl.this.f382o.getParent()).requestApplyInsets();
                }
                AppCompatDelegateImpl.this.f382o.removeAllViews();
                AppCompatDelegateImpl.this.f388r.d(null);
                AppCompatDelegateImpl appCompatDelegateImpl2 = AppCompatDelegateImpl.this;
                appCompatDelegateImpl2.f388r = null;
                ViewGroup viewGroup = appCompatDelegateImpl2.f392t;
                WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
                viewGroup.requestApplyInsets();
            }
        }

        public d(a.AbstractC0146a aVar) {
            this.f422a = aVar;
        }

        @Override // p.a.AbstractC0146a
        public boolean a(p.a aVar, Menu menu) {
            return this.f422a.a(aVar, menu);
        }

        @Override // p.a.AbstractC0146a
        public boolean b(p.a aVar, Menu menu) {
            ViewGroup viewGroup = AppCompatDelegateImpl.this.f392t;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            viewGroup.requestApplyInsets();
            return this.f422a.b(aVar, menu);
        }

        @Override // p.a.AbstractC0146a
        public void c(p.a aVar) {
            this.f422a.c(aVar);
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            if (appCompatDelegateImpl.f384p != null) {
                appCompatDelegateImpl.f371e.getDecorView().removeCallbacks(AppCompatDelegateImpl.this.f386q);
            }
            AppCompatDelegateImpl appCompatDelegateImpl2 = AppCompatDelegateImpl.this;
            if (appCompatDelegateImpl2.f382o != null) {
                appCompatDelegateImpl2.L();
                AppCompatDelegateImpl appCompatDelegateImpl3 = AppCompatDelegateImpl.this;
                m a10 = ViewCompat.a(appCompatDelegateImpl3.f382o);
                a10.a(Constant.VOLUME_FLOAT_MIN);
                appCompatDelegateImpl3.f388r = a10;
                m mVar = AppCompatDelegateImpl.this.f388r;
                a aVar2 = new a();
                View view = mVar.f6912a.get();
                if (view != null) {
                    mVar.e(view, aVar2);
                }
            }
            AppCompatDelegateImpl appCompatDelegateImpl4 = AppCompatDelegateImpl.this;
            f fVar = appCompatDelegateImpl4.f373g;
            if (fVar != null) {
                fVar.onSupportActionModeFinished(appCompatDelegateImpl4.f380n);
            }
            AppCompatDelegateImpl appCompatDelegateImpl5 = AppCompatDelegateImpl.this;
            appCompatDelegateImpl5.f380n = null;
            ViewGroup viewGroup = appCompatDelegateImpl5.f392t;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            viewGroup.requestApplyInsets();
        }

        @Override // p.a.AbstractC0146a
        public boolean d(p.a aVar, MenuItem menuItem) {
            return this.f422a.d(aVar, menuItem);
        }
    }

    public class e extends p.i {
        public e(Window.Callback callback) {
            super(callback);
        }

        public final ActionMode a(ActionMode.Callback callback) {
            e.a aVar = new e.a(AppCompatDelegateImpl.this.f370d, callback);
            p.a B = AppCompatDelegateImpl.this.B(aVar);
            if (B != null) {
                return aVar.e(B);
            }
            return null;
        }

        @Override // p.i
        public boolean dispatchKeyEvent(KeyEvent keyEvent) {
            return AppCompatDelegateImpl.this.J(keyEvent) || this.f9554a.dispatchKeyEvent(keyEvent);
        }

        /* JADX WARNING: Code restructure failed: missing block: B:17:0x0049, code lost:
            if (r6 != false) goto L_0x001d;
         */
        /* JADX WARNING: Removed duplicated region for block: B:22:? A[RETURN, SYNTHETIC] */
        @Override // p.i
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public boolean dispatchKeyShortcutEvent(android.view.KeyEvent r6) {
            /*
                r5 = this;
                android.view.Window$Callback r0 = r5.f9554a
                boolean r0 = r0.dispatchKeyShortcutEvent(r6)
                r1 = 0
                r2 = 1
                if (r0 != 0) goto L_0x004f
                androidx.appcompat.app.AppCompatDelegateImpl r0 = androidx.appcompat.app.AppCompatDelegateImpl.this
                int r3 = r6.getKeyCode()
                r0.S()
                androidx.appcompat.app.ActionBar r4 = r0.h
                if (r4 == 0) goto L_0x001f
                boolean r3 = r4.j(r3, r6)
                if (r3 == 0) goto L_0x001f
            L_0x001d:
                r6 = r2
                goto L_0x004d
            L_0x001f:
                androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState r3 = r0.f395v0
                if (r3 == 0) goto L_0x0034
                int r4 = r6.getKeyCode()
                boolean r3 = r0.W(r3, r4, r6, r2)
                if (r3 == 0) goto L_0x0034
                androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState r6 = r0.f395v0
                if (r6 == 0) goto L_0x001d
                r6.f412l = r2
                goto L_0x001d
            L_0x0034:
                androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState r3 = r0.f395v0
                if (r3 != 0) goto L_0x004c
                androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState r3 = r0.Q(r1)
                r0.X(r3, r6)
                int r4 = r6.getKeyCode()
                boolean r6 = r0.W(r3, r4, r6, r2)
                r3.f411k = r1
                if (r6 == 0) goto L_0x004c
                goto L_0x001d
            L_0x004c:
                r6 = r1
            L_0x004d:
                if (r6 == 0) goto L_0x0050
            L_0x004f:
                r1 = r2
            L_0x0050:
                return r1
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.e.dispatchKeyShortcutEvent(android.view.KeyEvent):boolean");
        }

        @Override // p.i
        public void onContentChanged() {
        }

        @Override // p.i
        public boolean onCreatePanelMenu(int i10, Menu menu) {
            if (i10 != 0 || (menu instanceof androidx.appcompat.view.menu.d)) {
                return this.f9554a.onCreatePanelMenu(i10, menu);
            }
            return false;
        }

        @Override // p.i
        public boolean onMenuOpened(int i10, Menu menu) {
            this.f9554a.onMenuOpened(i10, menu);
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            Objects.requireNonNull(appCompatDelegateImpl);
            if (i10 == 108) {
                appCompatDelegateImpl.S();
                ActionBar actionBar = appCompatDelegateImpl.h;
                if (actionBar != null) {
                    actionBar.c(true);
                }
            }
            return true;
        }

        @Override // p.i
        public void onPanelClosed(int i10, Menu menu) {
            this.f9554a.onPanelClosed(i10, menu);
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            Objects.requireNonNull(appCompatDelegateImpl);
            if (i10 == 108) {
                appCompatDelegateImpl.S();
                ActionBar actionBar = appCompatDelegateImpl.h;
                if (actionBar != null) {
                    actionBar.c(false);
                }
            } else if (i10 == 0) {
                PanelFeatureState Q = appCompatDelegateImpl.Q(i10);
                if (Q.f413m) {
                    appCompatDelegateImpl.H(Q, false);
                }
            }
        }

        @Override // p.i
        public boolean onPreparePanel(int i10, View view, Menu menu) {
            androidx.appcompat.view.menu.d dVar = menu instanceof androidx.appcompat.view.menu.d ? (androidx.appcompat.view.menu.d) menu : null;
            if (i10 == 0 && dVar == null) {
                return false;
            }
            if (dVar != null) {
                dVar.f626x = true;
            }
            boolean onPreparePanel = this.f9554a.onPreparePanel(i10, view, menu);
            if (dVar != null) {
                dVar.f626x = false;
            }
            return onPreparePanel;
        }

        @Override // p.i, android.view.Window.Callback
        @RequiresApi(24)
        public void onProvideKeyboardShortcuts(List<KeyboardShortcutGroup> list, Menu menu, int i10) {
            androidx.appcompat.view.menu.d dVar = AppCompatDelegateImpl.this.Q(0).h;
            if (dVar != null) {
                this.f9554a.onProvideKeyboardShortcuts(list, dVar, i10);
            } else {
                this.f9554a.onProvideKeyboardShortcuts(list, menu, i10);
            }
        }

        @Override // p.i
        public ActionMode onWindowStartingActionMode(ActionMode.Callback callback) {
            return null;
        }

        @Override // p.i
        @RequiresApi(23)
        public ActionMode onWindowStartingActionMode(ActionMode.Callback callback, int i10) {
            Objects.requireNonNull(AppCompatDelegateImpl.this);
            if (i10 != 0) {
                return this.f9554a.onWindowStartingActionMode(callback, i10);
            }
            return a(callback);
        }
    }

    public class f extends g {

        /* renamed from: c  reason: collision with root package name */
        public final PowerManager f426c;

        public f(@NonNull Context context) {
            super();
            this.f426c = (PowerManager) context.getApplicationContext().getSystemService("power");
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public IntentFilter b() {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("android.os.action.POWER_SAVE_MODE_CHANGED");
            return intentFilter;
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public int c() {
            return this.f426c.isPowerSaveMode() ? 2 : 1;
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public void d() {
            AppCompatDelegateImpl.this.C();
        }
    }

    @VisibleForTesting
    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public abstract class g {

        /* renamed from: a  reason: collision with root package name */
        public BroadcastReceiver f428a;

        public class a extends BroadcastReceiver {
            public a() {
            }

            public void onReceive(Context context, Intent intent) {
                g.this.d();
            }
        }

        public g() {
        }

        public void a() {
            BroadcastReceiver broadcastReceiver = this.f428a;
            if (broadcastReceiver != null) {
                try {
                    AppCompatDelegateImpl.this.f370d.unregisterReceiver(broadcastReceiver);
                } catch (IllegalArgumentException unused) {
                }
                this.f428a = null;
            }
        }

        @Nullable
        public abstract IntentFilter b();

        public abstract int c();

        public abstract void d();

        public void e() {
            a();
            IntentFilter b10 = b();
            if (b10 != null && b10.countActions() != 0) {
                if (this.f428a == null) {
                    this.f428a = new a();
                }
                AppCompatDelegateImpl.this.f370d.registerReceiver(this.f428a, b10);
            }
        }
    }

    public class h extends g {

        /* renamed from: c  reason: collision with root package name */
        public final o f431c;

        public h(@NonNull o oVar) {
            super();
            this.f431c = oVar;
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public IntentFilter b() {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("android.intent.action.TIME_SET");
            intentFilter.addAction("android.intent.action.TIMEZONE_CHANGED");
            intentFilter.addAction("android.intent.action.TIME_TICK");
            return intentFilter;
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public int c() {
            boolean z10;
            long j10;
            o oVar = this.f431c;
            o.a aVar = oVar.f479c;
            if (aVar.f481b > System.currentTimeMillis()) {
                z10 = aVar.f480a;
            } else {
                Location location = null;
                Location a10 = PermissionChecker.a(oVar.f477a, "android.permission.ACCESS_COARSE_LOCATION") == 0 ? oVar.a("network") : null;
                if (PermissionChecker.a(oVar.f477a, "android.permission.ACCESS_FINE_LOCATION") == 0) {
                    location = oVar.a("gps");
                }
                if (location == null || a10 == null ? location != null : location.getTime() > a10.getTime()) {
                    a10 = location;
                }
                if (a10 != null) {
                    o.a aVar2 = oVar.f479c;
                    long currentTimeMillis = System.currentTimeMillis();
                    if (n.f472d == null) {
                        n.f472d = new n();
                    }
                    n nVar = n.f472d;
                    nVar.a(currentTimeMillis - 86400000, a10.getLatitude(), a10.getLongitude());
                    nVar.a(currentTimeMillis, a10.getLatitude(), a10.getLongitude());
                    boolean z11 = nVar.f475c == 1;
                    long j11 = nVar.f474b;
                    long j12 = nVar.f473a;
                    nVar.a(currentTimeMillis + 86400000, a10.getLatitude(), a10.getLongitude());
                    long j13 = nVar.f474b;
                    if (j11 == -1 || j12 == -1) {
                        j10 = currentTimeMillis + 43200000;
                    } else {
                        j10 = (currentTimeMillis > j12 ? j13 + 0 : currentTimeMillis > j11 ? j12 + 0 : j11 + 0) + 60000;
                    }
                    aVar2.f480a = z11;
                    aVar2.f481b = j10;
                    z10 = aVar.f480a;
                } else {
                    Log.i("TwilightManager", "Could not get last known location. This is probably because the app does not have any location permissions. Falling back to hardcoded sunrise/sunset values.");
                    int i10 = Calendar.getInstance().get(11);
                    z10 = i10 < 6 || i10 >= 22;
                }
            }
            if (z10) {
                return 2;
            }
            return 1;
        }

        @Override // androidx.appcompat.app.AppCompatDelegateImpl.g
        public void d() {
            AppCompatDelegateImpl.this.C();
        }
    }

    public class i extends ContentFrameLayout {
        public i(Context context) {
            super(context, null);
        }

        public boolean dispatchKeyEvent(KeyEvent keyEvent) {
            return AppCompatDelegateImpl.this.J(keyEvent) || super.dispatchKeyEvent(keyEvent);
        }

        public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
            if (motionEvent.getAction() == 0) {
                int x8 = (int) motionEvent.getX();
                int y10 = (int) motionEvent.getY();
                if (x8 < -5 || y10 < -5 || x8 > getWidth() + 5 || y10 > getHeight() + 5) {
                    AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
                    appCompatDelegateImpl.H(appCompatDelegateImpl.Q(0), true);
                    return true;
                }
            }
            return super.onInterceptTouchEvent(motionEvent);
        }

        public void setBackgroundResource(int i10) {
            setBackgroundDrawable(m.a.a(getContext(), i10));
        }
    }

    public final class j implements h.a {
        public j() {
        }

        @Override // androidx.appcompat.view.menu.h.a
        public void a(@NonNull androidx.appcompat.view.menu.d dVar, boolean z10) {
            androidx.appcompat.view.menu.d k10 = dVar.k();
            boolean z11 = k10 != dVar;
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            if (z11) {
                dVar = k10;
            }
            PanelFeatureState O = appCompatDelegateImpl.O(dVar);
            if (O == null) {
                return;
            }
            if (z11) {
                AppCompatDelegateImpl.this.F(O.f402a, O, k10);
                AppCompatDelegateImpl.this.H(O, true);
                return;
            }
            AppCompatDelegateImpl.this.H(O, z10);
        }

        @Override // androidx.appcompat.view.menu.h.a
        public boolean b(@NonNull androidx.appcompat.view.menu.d dVar) {
            Window.Callback R;
            if (dVar != dVar.k()) {
                return true;
            }
            AppCompatDelegateImpl appCompatDelegateImpl = AppCompatDelegateImpl.this;
            if (!appCompatDelegateImpl.f383o0 || (R = appCompatDelegateImpl.R()) == null || AppCompatDelegateImpl.this.A0) {
                return true;
            }
            R.onMenuOpened(108, dVar);
            return true;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:9:0x002f, code lost:
        r4 = null;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public AppCompatDelegateImpl(android.content.Context r4, android.view.Window r5, androidx.appcompat.app.f r6, java.lang.Object r7) {
        /*
        // Method dump skipped, instructions count: 112
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.<init>(android.content.Context, android.view.Window, androidx.appcompat.app.f, java.lang.Object):void");
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public final void A(CharSequence charSequence) {
        this.f375j = charSequence;
        s sVar = this.f376k;
        if (sVar != null) {
            sVar.setWindowTitle(charSequence);
            return;
        }
        ActionBar actionBar = this.h;
        if (actionBar != null) {
            actionBar.o(charSequence);
            return;
        }
        TextView textView = this.f397x;
        if (textView != null) {
            textView.setText(charSequence);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:25:0x0043  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x0047  */
    @Override // androidx.appcompat.app.AppCompatDelegate
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public p.a B(@androidx.annotation.NonNull p.a.AbstractC0146a r8) {
        /*
        // Method dump skipped, instructions count: 434
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.B(p.a$a):p.a");
    }

    public boolean C() {
        return D(true);
    }

    /* JADX WARNING: Removed duplicated region for block: B:61:0x0109  */
    /* JADX WARNING: Removed duplicated region for block: B:65:0x0116  */
    /* JADX WARNING: Removed duplicated region for block: B:66:0x0120  */
    /* JADX WARNING: Removed duplicated region for block: B:71:0x012a  */
    /* JADX WARNING: Removed duplicated region for block: B:75:0x013d  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean D(boolean r12) {
        /*
        // Method dump skipped, instructions count: 325
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.D(boolean):boolean");
    }

    public final void E(@NonNull Window window) {
        if (this.f371e == null) {
            Window.Callback callback = window.getCallback();
            if (!(callback instanceof e)) {
                e eVar = new e(callback);
                this.f372f = eVar;
                window.setCallback(eVar);
                m0 q10 = m0.q(this.f370d, null, P0);
                Drawable h6 = q10.h(0);
                if (h6 != null) {
                    window.setBackgroundDrawable(h6);
                }
                q10.f1145b.recycle();
                this.f371e = window;
                return;
            }
            throw new IllegalStateException("AppCompat has already installed itself into the Window");
        }
        throw new IllegalStateException("AppCompat has already installed itself into the Window");
    }

    public void F(int i10, PanelFeatureState panelFeatureState, Menu menu) {
        if (menu == null) {
            menu = panelFeatureState.h;
        }
        if (panelFeatureState.f413m && !this.A0) {
            this.f372f.f9554a.onPanelClosed(i10, menu);
        }
    }

    public void G(@NonNull androidx.appcompat.view.menu.d dVar) {
        if (!this.f393t0) {
            this.f393t0 = true;
            this.f376k.l();
            Window.Callback R = R();
            if (R != null && !this.A0) {
                R.onPanelClosed(108, dVar);
            }
            this.f393t0 = false;
        }
    }

    public void H(PanelFeatureState panelFeatureState, boolean z10) {
        ViewGroup viewGroup;
        s sVar;
        if (!z10 || panelFeatureState.f402a != 0 || (sVar = this.f376k) == null || !sVar.b()) {
            WindowManager windowManager = (WindowManager) this.f370d.getSystemService("window");
            if (!(windowManager == null || !panelFeatureState.f413m || (viewGroup = panelFeatureState.f406e) == null)) {
                windowManager.removeView(viewGroup);
                if (z10) {
                    F(panelFeatureState.f402a, panelFeatureState, null);
                }
            }
            panelFeatureState.f411k = false;
            panelFeatureState.f412l = false;
            panelFeatureState.f413m = false;
            panelFeatureState.f407f = null;
            panelFeatureState.f414n = true;
            if (this.f395v0 == panelFeatureState) {
                this.f395v0 = null;
                return;
            }
            return;
        }
        G(panelFeatureState.h);
    }

    @NonNull
    public final Configuration I(@NonNull Context context, int i10, @Nullable Configuration configuration) {
        int i11 = i10 != 1 ? i10 != 2 ? context.getApplicationContext().getResources().getConfiguration().uiMode & 48 : 32 : 16;
        Configuration configuration2 = new Configuration();
        configuration2.fontScale = Constant.VOLUME_FLOAT_MIN;
        if (configuration != null) {
            configuration2.setTo(configuration);
        }
        configuration2.uiMode = i11 | (configuration2.uiMode & -49);
        return configuration2;
    }

    /* JADX WARNING: Removed duplicated region for block: B:63:0x00cd  */
    /* JADX WARNING: Removed duplicated region for block: B:85:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:90:? A[RETURN, SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean J(android.view.KeyEvent r7) {
        /*
        // Method dump skipped, instructions count: 278
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.J(android.view.KeyEvent):boolean");
    }

    public void K(int i10) {
        PanelFeatureState Q = Q(i10);
        if (Q.h != null) {
            Bundle bundle = new Bundle();
            Q.h.w(bundle);
            if (bundle.size() > 0) {
                Q.f416p = bundle;
            }
            Q.h.A();
            Q.h.clear();
        }
        Q.f415o = true;
        Q.f414n = true;
        if ((i10 == 108 || i10 == 0) && this.f376k != null) {
            PanelFeatureState Q2 = Q(0);
            Q2.f411k = false;
            X(Q2, null);
        }
    }

    public void L() {
        m mVar = this.f388r;
        if (mVar != null) {
            mVar.b();
        }
    }

    public final void M() {
        ViewGroup viewGroup;
        CharSequence charSequence;
        Context context;
        if (!this.f390s) {
            TypedArray obtainStyledAttributes = this.f370d.obtainStyledAttributes(R$styleable.AppCompatTheme);
            int i10 = R$styleable.AppCompatTheme_windowActionBar;
            if (obtainStyledAttributes.hasValue(i10)) {
                if (obtainStyledAttributes.getBoolean(R$styleable.AppCompatTheme_windowNoTitle, false)) {
                    u(1);
                } else if (obtainStyledAttributes.getBoolean(i10, false)) {
                    u(108);
                }
                if (obtainStyledAttributes.getBoolean(R$styleable.AppCompatTheme_windowActionBarOverlay, false)) {
                    u(109);
                }
                if (obtainStyledAttributes.getBoolean(R$styleable.AppCompatTheme_windowActionModeOverlay, false)) {
                    u(10);
                }
                this.f389r0 = obtainStyledAttributes.getBoolean(R$styleable.AppCompatTheme_android_windowIsFloating, false);
                obtainStyledAttributes.recycle();
                N();
                this.f371e.getDecorView();
                LayoutInflater from = LayoutInflater.from(this.f370d);
                if (this.f391s0) {
                    viewGroup = this.f387q0 ? (ViewGroup) from.inflate(R$layout.abc_screen_simple_overlay_action_mode, (ViewGroup) null) : (ViewGroup) from.inflate(R$layout.abc_screen_simple, (ViewGroup) null);
                } else if (this.f389r0) {
                    viewGroup = (ViewGroup) from.inflate(R$layout.abc_dialog_title_material, (ViewGroup) null);
                    this.f385p0 = false;
                    this.f383o0 = false;
                } else if (this.f383o0) {
                    TypedValue typedValue = new TypedValue();
                    this.f370d.getTheme().resolveAttribute(R$attr.actionBarTheme, typedValue, true);
                    if (typedValue.resourceId != 0) {
                        context = new p.c(this.f370d, typedValue.resourceId);
                    } else {
                        context = this.f370d;
                    }
                    viewGroup = (ViewGroup) LayoutInflater.from(context).inflate(R$layout.abc_screen_toolbar, (ViewGroup) null);
                    s sVar = (s) viewGroup.findViewById(R$id.decor_content_parent);
                    this.f376k = sVar;
                    sVar.setWindowCallback(R());
                    if (this.f385p0) {
                        this.f376k.k(109);
                    }
                    if (this.f379m0) {
                        this.f376k.k(2);
                    }
                    if (this.f381n0) {
                        this.f376k.k(5);
                    }
                } else {
                    viewGroup = null;
                }
                if (viewGroup != null) {
                    ViewCompat.l(viewGroup, new g(this));
                    if (this.f376k == null) {
                        this.f397x = (TextView) viewGroup.findViewById(R$id.title);
                    }
                    Method method = q0.f1187a;
                    try {
                        Method method2 = viewGroup.getClass().getMethod("makeOptionalFitsSystemWindows", new Class[0]);
                        if (!method2.isAccessible()) {
                            method2.setAccessible(true);
                        }
                        method2.invoke(viewGroup, new Object[0]);
                    } catch (NoSuchMethodException unused) {
                        Log.d("ViewUtils", "Could not find method makeOptionalFitsSystemWindows. Oh well...");
                    } catch (InvocationTargetException e10) {
                        Log.d("ViewUtils", "Could not invoke makeOptionalFitsSystemWindows", e10);
                    } catch (IllegalAccessException e11) {
                        Log.d("ViewUtils", "Could not invoke makeOptionalFitsSystemWindows", e11);
                    }
                    ContentFrameLayout contentFrameLayout = (ContentFrameLayout) viewGroup.findViewById(R$id.action_bar_activity_content);
                    ViewGroup viewGroup2 = (ViewGroup) this.f371e.findViewById(16908290);
                    if (viewGroup2 != null) {
                        while (viewGroup2.getChildCount() > 0) {
                            View childAt = viewGroup2.getChildAt(0);
                            viewGroup2.removeViewAt(0);
                            contentFrameLayout.addView(childAt);
                        }
                        viewGroup2.setId(-1);
                        contentFrameLayout.setId(16908290);
                        if (viewGroup2 instanceof FrameLayout) {
                            ((FrameLayout) viewGroup2).setForeground(null);
                        }
                    }
                    this.f371e.setContentView(viewGroup);
                    contentFrameLayout.setAttachListener(new h(this));
                    this.f392t = viewGroup;
                    Object obj = this.f369c;
                    if (obj instanceof Activity) {
                        charSequence = ((Activity) obj).getTitle();
                    } else {
                        charSequence = this.f375j;
                    }
                    if (!TextUtils.isEmpty(charSequence)) {
                        s sVar2 = this.f376k;
                        if (sVar2 != null) {
                            sVar2.setWindowTitle(charSequence);
                        } else {
                            ActionBar actionBar = this.h;
                            if (actionBar != null) {
                                actionBar.o(charSequence);
                            } else {
                                TextView textView = this.f397x;
                                if (textView != null) {
                                    textView.setText(charSequence);
                                }
                            }
                        }
                    }
                    ContentFrameLayout contentFrameLayout2 = (ContentFrameLayout) this.f392t.findViewById(16908290);
                    View decorView = this.f371e.getDecorView();
                    contentFrameLayout2.f862g.set(decorView.getPaddingLeft(), decorView.getPaddingTop(), decorView.getPaddingRight(), decorView.getPaddingBottom());
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    if (contentFrameLayout2.isLaidOut()) {
                        contentFrameLayout2.requestLayout();
                    }
                    TypedArray obtainStyledAttributes2 = this.f370d.obtainStyledAttributes(R$styleable.AppCompatTheme);
                    obtainStyledAttributes2.getValue(R$styleable.AppCompatTheme_windowMinWidthMajor, contentFrameLayout2.getMinWidthMajor());
                    obtainStyledAttributes2.getValue(R$styleable.AppCompatTheme_windowMinWidthMinor, contentFrameLayout2.getMinWidthMinor());
                    int i11 = R$styleable.AppCompatTheme_windowFixedWidthMajor;
                    if (obtainStyledAttributes2.hasValue(i11)) {
                        obtainStyledAttributes2.getValue(i11, contentFrameLayout2.getFixedWidthMajor());
                    }
                    int i12 = R$styleable.AppCompatTheme_windowFixedWidthMinor;
                    if (obtainStyledAttributes2.hasValue(i12)) {
                        obtainStyledAttributes2.getValue(i12, contentFrameLayout2.getFixedWidthMinor());
                    }
                    int i13 = R$styleable.AppCompatTheme_windowFixedHeightMajor;
                    if (obtainStyledAttributes2.hasValue(i13)) {
                        obtainStyledAttributes2.getValue(i13, contentFrameLayout2.getFixedHeightMajor());
                    }
                    int i14 = R$styleable.AppCompatTheme_windowFixedHeightMinor;
                    if (obtainStyledAttributes2.hasValue(i14)) {
                        obtainStyledAttributes2.getValue(i14, contentFrameLayout2.getFixedHeightMinor());
                    }
                    obtainStyledAttributes2.recycle();
                    contentFrameLayout2.requestLayout();
                    this.f390s = true;
                    PanelFeatureState Q = Q(0);
                    if (!this.A0 && Q.h == null) {
                        T(108);
                        return;
                    }
                    return;
                }
                StringBuilder a10 = com.duokan.airkan.server.f.a("AppCompat does not support the current theme features: { windowActionBar: ");
                a10.append(this.f383o0);
                a10.append(", windowActionBarOverlay: ");
                a10.append(this.f385p0);
                a10.append(", android:windowIsFloating: ");
                a10.append(this.f389r0);
                a10.append(", windowActionModeOverlay: ");
                a10.append(this.f387q0);
                a10.append(", windowNoTitle: ");
                a10.append(this.f391s0);
                a10.append(" }");
                throw new IllegalArgumentException(a10.toString());
            }
            obtainStyledAttributes.recycle();
            throw new IllegalStateException("You need to use a Theme.AppCompat theme (or descendant) with this activity.");
        }
    }

    public final void N() {
        if (this.f371e == null) {
            Object obj = this.f369c;
            if (obj instanceof Activity) {
                E(((Activity) obj).getWindow());
            }
        }
        if (this.f371e == null) {
            throw new IllegalStateException("We have not been given a Window");
        }
    }

    public PanelFeatureState O(Menu menu) {
        PanelFeatureState[] panelFeatureStateArr = this.f394u0;
        int length = panelFeatureStateArr != null ? panelFeatureStateArr.length : 0;
        for (int i10 = 0; i10 < length; i10++) {
            PanelFeatureState panelFeatureState = panelFeatureStateArr[i10];
            if (panelFeatureState != null && panelFeatureState.h == menu) {
                return panelFeatureState;
            }
        }
        return null;
    }

    public final g P(@NonNull Context context) {
        if (this.F0 == null) {
            if (o.f476d == null) {
                Context applicationContext = context.getApplicationContext();
                o.f476d = new o(applicationContext, (LocationManager) applicationContext.getSystemService("location"));
            }
            this.F0 = new h(o.f476d);
        }
        return this.F0;
    }

    public PanelFeatureState Q(int i10) {
        PanelFeatureState[] panelFeatureStateArr = this.f394u0;
        if (panelFeatureStateArr == null || panelFeatureStateArr.length <= i10) {
            PanelFeatureState[] panelFeatureStateArr2 = new PanelFeatureState[(i10 + 1)];
            if (panelFeatureStateArr != null) {
                System.arraycopy(panelFeatureStateArr, 0, panelFeatureStateArr2, 0, panelFeatureStateArr.length);
            }
            this.f394u0 = panelFeatureStateArr2;
            panelFeatureStateArr = panelFeatureStateArr2;
        }
        PanelFeatureState panelFeatureState = panelFeatureStateArr[i10];
        if (panelFeatureState != null) {
            return panelFeatureState;
        }
        PanelFeatureState panelFeatureState2 = new PanelFeatureState(i10);
        panelFeatureStateArr[i10] = panelFeatureState2;
        return panelFeatureState2;
    }

    public final Window.Callback R() {
        return this.f371e.getCallback();
    }

    public final void S() {
        M();
        if (this.f383o0 && this.h == null) {
            Object obj = this.f369c;
            if (obj instanceof Activity) {
                this.h = new p((Activity) this.f369c, this.f385p0);
            } else if (obj instanceof Dialog) {
                this.h = new p((Dialog) this.f369c);
            }
            ActionBar actionBar = this.h;
            if (actionBar != null) {
                actionBar.m(this.K0);
            }
        }
    }

    public final void T(int i10) {
        this.I0 = (1 << i10) | this.I0;
        if (!this.H0) {
            View decorView = this.f371e.getDecorView();
            Runnable runnable = this.J0;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            decorView.postOnAnimation(runnable);
            this.H0 = true;
        }
    }

    public int U(@NonNull Context context, int i10) {
        if (i10 == -100) {
            return -1;
        }
        if (i10 != -1) {
            if (i10 != 0) {
                if (!(i10 == 1 || i10 == 2)) {
                    if (i10 == 3) {
                        if (this.G0 == null) {
                            this.G0 = new f(context);
                        }
                        return this.G0.c();
                    }
                    throw new IllegalStateException("Unknown value set for night mode. Please use one of the MODE_NIGHT values from AppCompatDelegate.");
                }
            } else if (((UiModeManager) context.getApplicationContext().getSystemService(UiModeManager.class)).getNightMode() == 0) {
                return -1;
            } else {
                return P(context).c();
            }
        }
        return i10;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:73:0x0154, code lost:
        if (r15 != null) goto L_0x0156;
     */
    /* JADX WARNING: Removed duplicated region for block: B:77:0x015b  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void V(androidx.appcompat.app.AppCompatDelegateImpl.PanelFeatureState r14, android.view.KeyEvent r15) {
        /*
        // Method dump skipped, instructions count: 470
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.V(androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState, android.view.KeyEvent):void");
    }

    public final boolean W(PanelFeatureState panelFeatureState, int i10, KeyEvent keyEvent, int i11) {
        androidx.appcompat.view.menu.d dVar;
        boolean z10 = false;
        if (keyEvent.isSystem()) {
            return false;
        }
        if ((panelFeatureState.f411k || X(panelFeatureState, keyEvent)) && (dVar = panelFeatureState.h) != null) {
            z10 = dVar.performShortcut(i10, keyEvent, i11);
        }
        if (z10 && (i11 & 1) == 0 && this.f376k == null) {
            H(panelFeatureState, true);
        }
        return z10;
    }

    public final boolean X(PanelFeatureState panelFeatureState, KeyEvent keyEvent) {
        s sVar;
        s sVar2;
        s sVar3;
        Resources.Theme theme;
        s sVar4;
        if (this.A0) {
            return false;
        }
        if (panelFeatureState.f411k) {
            return true;
        }
        PanelFeatureState panelFeatureState2 = this.f395v0;
        if (!(panelFeatureState2 == null || panelFeatureState2 == panelFeatureState)) {
            H(panelFeatureState2, false);
        }
        Window.Callback R = R();
        if (R != null) {
            panelFeatureState.f408g = R.onCreatePanelView(panelFeatureState.f402a);
        }
        int i10 = panelFeatureState.f402a;
        boolean z10 = i10 == 0 || i10 == 108;
        if (z10 && (sVar4 = this.f376k) != null) {
            sVar4.c();
        }
        if (panelFeatureState.f408g == null && (!z10 || !(this.h instanceof m))) {
            androidx.appcompat.view.menu.d dVar = panelFeatureState.h;
            if (dVar == null || panelFeatureState.f415o) {
                if (dVar == null) {
                    Context context = this.f370d;
                    int i11 = panelFeatureState.f402a;
                    if ((i11 == 0 || i11 == 108) && this.f376k != null) {
                        TypedValue typedValue = new TypedValue();
                        Resources.Theme theme2 = context.getTheme();
                        theme2.resolveAttribute(R$attr.actionBarTheme, typedValue, true);
                        if (typedValue.resourceId != 0) {
                            theme = context.getResources().newTheme();
                            theme.setTo(theme2);
                            theme.applyStyle(typedValue.resourceId, true);
                            theme.resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
                        } else {
                            theme2.resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
                            theme = null;
                        }
                        if (typedValue.resourceId != 0) {
                            if (theme == null) {
                                theme = context.getResources().newTheme();
                                theme.setTo(theme2);
                            }
                            theme.applyStyle(typedValue.resourceId, true);
                        }
                        if (theme != null) {
                            p.c cVar = new p.c(context, 0);
                            cVar.getTheme().setTo(theme);
                            context = cVar;
                        }
                    }
                    androidx.appcompat.view.menu.d dVar2 = new androidx.appcompat.view.menu.d(context);
                    dVar2.f608e = this;
                    panelFeatureState.a(dVar2);
                    if (panelFeatureState.h == null) {
                        return false;
                    }
                }
                if (z10 && (sVar3 = this.f376k) != null) {
                    if (this.f377l == null) {
                        this.f377l = new c();
                    }
                    sVar3.a(panelFeatureState.h, this.f377l);
                }
                panelFeatureState.h.A();
                if (!R.onCreatePanelMenu(panelFeatureState.f402a, panelFeatureState.h)) {
                    panelFeatureState.a(null);
                    if (z10 && (sVar2 = this.f376k) != null) {
                        sVar2.a(null, this.f377l);
                    }
                    return false;
                }
                panelFeatureState.f415o = false;
            }
            panelFeatureState.h.A();
            Bundle bundle = panelFeatureState.f416p;
            if (bundle != null) {
                panelFeatureState.h.u(bundle);
                panelFeatureState.f416p = null;
            }
            if (!R.onPreparePanel(0, panelFeatureState.f408g, panelFeatureState.h)) {
                if (z10 && (sVar = this.f376k) != null) {
                    sVar.a(null, this.f377l);
                }
                panelFeatureState.h.z();
                return false;
            }
            panelFeatureState.h.setQwertyMode(KeyCharacterMap.load(keyEvent != null ? keyEvent.getDeviceId() : -1).getKeyboardType() != 1);
            panelFeatureState.h.z();
        }
        panelFeatureState.f411k = true;
        panelFeatureState.f412l = false;
        this.f395v0 = panelFeatureState;
        return true;
    }

    public final boolean Y() {
        ViewGroup viewGroup;
        if (this.f390s && (viewGroup = this.f392t) != null) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (viewGroup.isLaidOut()) {
                return true;
            }
        }
        return false;
    }

    public final void Z() {
        if (this.f390s) {
            throw new AndroidRuntimeException("Window feature must be requested before adding content");
        }
    }

    @Override // androidx.appcompat.view.menu.d.a
    public boolean a(@NonNull androidx.appcompat.view.menu.d dVar, @NonNull MenuItem menuItem) {
        PanelFeatureState O;
        Window.Callback R = R();
        if (R == null || this.A0 || (O = O(dVar.k())) == null) {
            return false;
        }
        return R.onMenuItemSelected(O.f402a, menuItem);
    }

    public final int a0(@Nullable q qVar, @Nullable Rect rect) {
        boolean z10;
        boolean z11;
        int i10;
        ViewGroup.MarginLayoutParams marginLayoutParams;
        int i11;
        int e10 = qVar.e();
        ActionBarContextView actionBarContextView = this.f382o;
        int i12 = 8;
        if (actionBarContextView == null || !(actionBarContextView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams)) {
            z10 = false;
        } else {
            ViewGroup.MarginLayoutParams marginLayoutParams2 = (ViewGroup.MarginLayoutParams) this.f382o.getLayoutParams();
            boolean z12 = true;
            if (this.f382o.isShown()) {
                if (this.L0 == null) {
                    this.L0 = new Rect();
                    this.M0 = new Rect();
                }
                Rect rect2 = this.L0;
                Rect rect3 = this.M0;
                rect2.set(qVar.c(), qVar.e(), qVar.d(), qVar.b());
                q0.a(this.f392t, rect2, rect3);
                int i13 = rect2.top;
                int i14 = rect2.left;
                int i15 = rect2.right;
                ViewGroup viewGroup = this.f392t;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                q k10 = q.k(ViewCompat.c.a(viewGroup));
                int c10 = k10.c();
                int d10 = k10.d();
                if (marginLayoutParams2.topMargin == i13 && marginLayoutParams2.leftMargin == i14 && marginLayoutParams2.rightMargin == i15) {
                    z11 = false;
                } else {
                    marginLayoutParams2.topMargin = i13;
                    marginLayoutParams2.leftMargin = i14;
                    marginLayoutParams2.rightMargin = i15;
                    z11 = true;
                }
                if (i13 <= 0 || this.f399y != null) {
                    View view = this.f399y;
                    if (!(view == null || ((marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams()).height == (i11 = marginLayoutParams2.topMargin) && marginLayoutParams.leftMargin == c10 && marginLayoutParams.rightMargin == d10))) {
                        marginLayoutParams.height = i11;
                        marginLayoutParams.leftMargin = c10;
                        marginLayoutParams.rightMargin = d10;
                        this.f399y.setLayoutParams(marginLayoutParams);
                    }
                } else {
                    View view2 = new View(this.f370d);
                    this.f399y = view2;
                    view2.setVisibility(8);
                    FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(-1, marginLayoutParams2.topMargin, 51);
                    layoutParams.leftMargin = c10;
                    layoutParams.rightMargin = d10;
                    this.f392t.addView(this.f399y, -1, layoutParams);
                }
                View view3 = this.f399y;
                z10 = view3 != null;
                if (z10 && view3.getVisibility() != 0) {
                    View view4 = this.f399y;
                    if ((view4.getWindowSystemUiVisibility() & 8192) == 0) {
                        z12 = false;
                    }
                    if (z12) {
                        Context context = this.f370d;
                        int i16 = R$color.abc_decor_view_status_guard_light;
                        Object obj = z.a.f11008a;
                        i10 = context.getColor(i16);
                    } else {
                        Context context2 = this.f370d;
                        int i17 = R$color.abc_decor_view_status_guard;
                        Object obj2 = z.a.f11008a;
                        i10 = context2.getColor(i17);
                    }
                    view4.setBackgroundColor(i10);
                }
                if (!this.f387q0 && z10) {
                    e10 = 0;
                }
                z12 = z11;
            } else if (marginLayoutParams2.topMargin != 0) {
                marginLayoutParams2.topMargin = 0;
                z10 = false;
            } else {
                z12 = false;
                z10 = false;
            }
            if (z12) {
                this.f382o.setLayoutParams(marginLayoutParams2);
            }
        }
        View view5 = this.f399y;
        if (view5 != null) {
            if (z10) {
                i12 = 0;
            }
            view5.setVisibility(i12);
        }
        return e10;
    }

    @Override // androidx.appcompat.view.menu.d.a
    public void b(@NonNull androidx.appcompat.view.menu.d dVar) {
        s sVar = this.f376k;
        if (sVar == null || !sVar.g() || (ViewConfiguration.get(this.f370d).hasPermanentMenuKey() && !this.f376k.d())) {
            PanelFeatureState Q = Q(0);
            Q.f414n = true;
            H(Q, false);
            V(Q, null);
            return;
        }
        Window.Callback R = R();
        if (this.f376k.b()) {
            this.f376k.e();
            if (!this.A0) {
                R.onPanelClosed(108, Q(0).h);
            }
        } else if (R != null && !this.A0) {
            if (this.H0 && (1 & this.I0) != 0) {
                this.f371e.getDecorView().removeCallbacks(this.J0);
                this.J0.run();
            }
            PanelFeatureState Q2 = Q(0);
            androidx.appcompat.view.menu.d dVar2 = Q2.h;
            if (dVar2 != null && !Q2.f415o && R.onPreparePanel(0, Q2.f408g, dVar2)) {
                R.onMenuOpened(108, Q2.h);
                this.f376k.f();
            }
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void c(View view, ViewGroup.LayoutParams layoutParams) {
        M();
        ((ViewGroup) this.f392t.findViewById(16908290)).addView(view, layoutParams);
        this.f372f.f9554a.onContentChanged();
    }

    /* JADX WARNING: Removed duplicated region for block: B:105:0x017a  */
    @Override // androidx.appcompat.app.AppCompatDelegate
    @androidx.annotation.NonNull
    @androidx.annotation.CallSuper
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.content.Context d(@androidx.annotation.NonNull android.content.Context r9) {
        /*
        // Method dump skipped, instructions count: 462
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.d(android.content.Context):android.content.Context");
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    @Nullable
    public <T extends View> T e(@IdRes int i10) {
        M();
        return (T) this.f371e.findViewById(i10);
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public final a f() {
        return new b(this);
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public int g() {
        return this.B0;
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public MenuInflater h() {
        if (this.f374i == null) {
            S();
            ActionBar actionBar = this.h;
            this.f374i = new p.g(actionBar != null ? actionBar.e() : this.f370d);
        }
        return this.f374i;
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public ActionBar i() {
        S();
        return this.h;
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void j() {
        LayoutInflater from = LayoutInflater.from(this.f370d);
        if (from.getFactory() == null) {
            from.setFactory2(this);
        } else if (!(from.getFactory2() instanceof AppCompatDelegateImpl)) {
            Log.i("AppCompatDelegate", "The Activity's LayoutInflater already has a Factory installed so we can not install AppCompat's");
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void k() {
        S();
        ActionBar actionBar = this.h;
        if (actionBar == null || !actionBar.g()) {
            T(0);
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void l(Configuration configuration) {
        if (this.f383o0 && this.f390s) {
            S();
            ActionBar actionBar = this.h;
            if (actionBar != null) {
                actionBar.h(configuration);
            }
        }
        androidx.appcompat.widget.g a10 = androidx.appcompat.widget.g.a();
        Context context = this.f370d;
        synchronized (a10) {
            b0 b0Var = a10.f1093a;
            synchronized (b0Var) {
                u.d<WeakReference<Drawable.ConstantState>> dVar = b0Var.f1063d.get(context);
                if (dVar != null) {
                    dVar.a();
                }
            }
        }
        D(false);
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void m(Bundle bundle) {
        this.f398x0 = true;
        D(false);
        N();
        Object obj = this.f369c;
        if (obj instanceof Activity) {
            String str = null;
            try {
                Activity activity = (Activity) obj;
                try {
                    str = y.d.c(activity, activity.getComponentName());
                } catch (PackageManager.NameNotFoundException e10) {
                    throw new IllegalArgumentException(e10);
                }
            } catch (IllegalArgumentException unused) {
            }
            if (str != null) {
                ActionBar actionBar = this.h;
                if (actionBar == null) {
                    this.K0 = true;
                } else {
                    actionBar.m(true);
                }
            }
            synchronized (AppCompatDelegate.f368b) {
                AppCompatDelegate.t(this);
                AppCompatDelegate.f367a.add(new WeakReference<>(this));
            }
        }
        this.f400y0 = true;
    }

    /* JADX WARNING: Removed duplicated region for block: B:24:0x0063  */
    /* JADX WARNING: Removed duplicated region for block: B:27:0x006a  */
    /* JADX WARNING: Removed duplicated region for block: B:30:0x0071  */
    /* JADX WARNING: Removed duplicated region for block: B:32:? A[RETURN, SYNTHETIC] */
    @Override // androidx.appcompat.app.AppCompatDelegate
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void n() {
        /*
        // Method dump skipped, instructions count: 117
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.n():void");
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void o(Bundle bundle) {
        M();
    }

    /* JADX DEBUG: Failed to insert an additional move for type inference into block B:82:0x01a6 */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r2v6 */
    /* JADX WARN: Type inference failed for: r2v10, types: [androidx.appcompat.widget.AppCompatRatingBar] */
    /* JADX WARN: Type inference failed for: r2v24 */
    /* JADX INFO: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final android.view.View onCreateView(android.view.View r7, java.lang.String r8, android.content.Context r9, android.util.AttributeSet r10) {
        /*
        // Method dump skipped, instructions count: 646
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.app.AppCompatDelegateImpl.onCreateView(android.view.View, java.lang.String, android.content.Context, android.util.AttributeSet):android.view.View");
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void p() {
        S();
        ActionBar actionBar = this.h;
        if (actionBar != null) {
            actionBar.n(true);
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void q(Bundle bundle) {
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void r() {
        this.f401z0 = true;
        C();
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void s() {
        this.f401z0 = false;
        S();
        ActionBar actionBar = this.h;
        if (actionBar != null) {
            actionBar.n(false);
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public boolean u(int i10) {
        if (i10 == 8) {
            Log.i("AppCompatDelegate", "You should now use the AppCompatDelegate.FEATURE_SUPPORT_ACTION_BAR id when requesting this feature.");
            i10 = 108;
        } else if (i10 == 9) {
            Log.i("AppCompatDelegate", "You should now use the AppCompatDelegate.FEATURE_SUPPORT_ACTION_BAR_OVERLAY id when requesting this feature.");
            i10 = 109;
        }
        if (this.f391s0 && i10 == 108) {
            return false;
        }
        if (this.f383o0 && i10 == 1) {
            this.f383o0 = false;
        }
        if (i10 == 1) {
            Z();
            this.f391s0 = true;
            return true;
        } else if (i10 == 2) {
            Z();
            this.f379m0 = true;
            return true;
        } else if (i10 == 5) {
            Z();
            this.f381n0 = true;
            return true;
        } else if (i10 == 10) {
            Z();
            this.f387q0 = true;
            return true;
        } else if (i10 == 108) {
            Z();
            this.f383o0 = true;
            return true;
        } else if (i10 != 109) {
            return this.f371e.requestFeature(i10);
        } else {
            Z();
            this.f385p0 = true;
            return true;
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void v(int i10) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.f392t.findViewById(16908290);
        viewGroup.removeAllViews();
        LayoutInflater.from(this.f370d).inflate(i10, viewGroup);
        this.f372f.f9554a.onContentChanged();
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void w(View view) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.f392t.findViewById(16908290);
        viewGroup.removeAllViews();
        viewGroup.addView(view);
        this.f372f.f9554a.onContentChanged();
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void x(View view, ViewGroup.LayoutParams layoutParams) {
        M();
        ViewGroup viewGroup = (ViewGroup) this.f392t.findViewById(16908290);
        viewGroup.removeAllViews();
        viewGroup.addView(view, layoutParams);
        this.f372f.f9554a.onContentChanged();
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void y(Toolbar toolbar) {
        CharSequence charSequence;
        if (this.f369c instanceof Activity) {
            S();
            ActionBar actionBar = this.h;
            if (!(actionBar instanceof p)) {
                this.f374i = null;
                if (actionBar != null) {
                    actionBar.i();
                }
                if (toolbar != null) {
                    Object obj = this.f369c;
                    if (obj instanceof Activity) {
                        charSequence = ((Activity) obj).getTitle();
                    } else {
                        charSequence = this.f375j;
                    }
                    m mVar = new m(toolbar, charSequence, this.f372f);
                    this.h = mVar;
                    this.f371e.setCallback(mVar.f461c);
                } else {
                    this.h = null;
                    this.f371e.setCallback(this.f372f);
                }
                k();
                return;
            }
            throw new IllegalStateException("This Activity already has an action bar supplied by the window decor. Do not request Window.FEATURE_SUPPORT_ACTION_BAR and set windowActionBar to false in your theme to use a Toolbar instead.");
        }
    }

    @Override // androidx.appcompat.app.AppCompatDelegate
    public void z(@StyleRes int i10) {
        this.C0 = i10;
    }

    public View onCreateView(String str, Context context, AttributeSet attributeSet) {
        return onCreateView(null, str, context, attributeSet);
    }
}
