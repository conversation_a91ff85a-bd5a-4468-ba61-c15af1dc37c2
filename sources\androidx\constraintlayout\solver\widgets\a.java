package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;
import com.duokan.airkan.common.Constant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import w.c;

/* compiled from: Analyzer */
public class a {
    public static void a(e eVar) {
        if ((eVar.f1358w0 & 32) != 32) {
            eVar.f1352q0.clear();
            eVar.f1352q0.add(0, new f(eVar.f10636i0));
            return;
        }
        eVar.f1359x0 = true;
        eVar.f1353r0 = false;
        eVar.f1354s0 = false;
        eVar.f1355t0 = false;
        ArrayList<ConstraintWidget> arrayList = eVar.f10636i0;
        List<f> list = eVar.f1352q0;
        ConstraintWidget.DimensionBehaviour i10 = eVar.i();
        ConstraintWidget.DimensionBehaviour dimensionBehaviour = ConstraintWidget.DimensionBehaviour.WRAP_CONTENT;
        boolean z10 = i10 == dimensionBehaviour;
        boolean z11 = eVar.m() == dimensionBehaviour;
        boolean z12 = z10 || z11;
        list.clear();
        for (ConstraintWidget constraintWidget : arrayList) {
            constraintWidget.f1311p = null;
            constraintWidget.f1294c0 = false;
            constraintWidget.s();
        }
        for (ConstraintWidget constraintWidget2 : arrayList) {
            if (constraintWidget2.f1311p == null) {
                f fVar = new f(new ArrayList(), true);
                list.add(fVar);
                if (!g(constraintWidget2, fVar, list, z12)) {
                    eVar.f1352q0.clear();
                    eVar.f1352q0.add(0, new f(eVar.f10636i0));
                    eVar.f1359x0 = false;
                    return;
                }
            }
        }
        int i11 = 0;
        int i12 = 0;
        for (f fVar2 : list) {
            i11 = Math.max(i11, b(fVar2, 0));
            i12 = Math.max(i12, b(fVar2, 1));
        }
        if (z10) {
            eVar.y(ConstraintWidget.DimensionBehaviour.FIXED);
            eVar.C(i11);
            eVar.f1353r0 = true;
            eVar.f1354s0 = true;
            eVar.f1356u0 = i11;
        }
        if (z11) {
            eVar.B(ConstraintWidget.DimensionBehaviour.FIXED);
            eVar.w(i12);
            eVar.f1353r0 = true;
            eVar.f1355t0 = true;
            eVar.f1357v0 = i12;
        }
        f(list, 0, eVar.n());
        f(list, 1, eVar.h());
    }

    public static int b(f fVar, int i10) {
        List<ConstraintWidget> list;
        int i11 = i10 * 2;
        Objects.requireNonNull(fVar);
        if (i10 == 0) {
            list = fVar.f1365d;
        } else {
            list = i10 == 1 ? fVar.f1366e : null;
        }
        int size = list.size();
        int i12 = 0;
        for (int i13 = 0; i13 < size; i13++) {
            ConstraintWidget constraintWidget = list.get(i13);
            ConstraintAnchor[] constraintAnchorArr = constraintWidget.A;
            int i14 = i11 + 1;
            i12 = Math.max(i12, c(constraintWidget, i10, constraintAnchorArr[i14].f1279d == null || !(constraintAnchorArr[i11].f1279d == null || constraintAnchorArr[i14].f1279d == null), 0));
        }
        fVar.f1364c[i10] = i12;
        return i12;
    }

    public static int c(ConstraintWidget constraintWidget, int i10, boolean z10, int i11) {
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        int n10;
        int i17;
        int i18;
        ConstraintWidget constraintWidget2;
        int i19;
        int i20 = 0;
        if (!constraintWidget.f1290a0) {
            return 0;
        }
        boolean z11 = constraintWidget.f1318w.f1279d != null && i10 == 1;
        if (z10) {
            i15 = constraintWidget.Q;
            i14 = constraintWidget.h() - constraintWidget.Q;
            i13 = i10 * 2;
            i12 = i13 + 1;
        } else {
            int h = constraintWidget.h();
            i14 = constraintWidget.Q;
            i15 = h - i14;
            i12 = i10 * 2;
            i13 = i12 + 1;
        }
        ConstraintAnchor[] constraintAnchorArr = constraintWidget.A;
        if (constraintAnchorArr[i12].f1279d == null || constraintAnchorArr[i13].f1279d != null) {
            i16 = 1;
        } else {
            i16 = -1;
            i12 = i13;
            i13 = i12;
        }
        int i21 = z11 ? i11 - i15 : i11;
        int d10 = d(constraintWidget, i10) + (constraintAnchorArr[i13].b() * i16);
        int i22 = i21 + d10;
        int n11 = (i10 == 0 ? constraintWidget.n() : constraintWidget.h()) * i16;
        Iterator<c> it = constraintWidget.A[i13].f1276a.f10634a.iterator();
        while (it.hasNext()) {
            i20 = Math.max(i20, c(((i) it.next()).f1377c.f1277b, i10, z10, i22));
        }
        int i23 = 0;
        for (Iterator<c> it2 = constraintWidget.A[i12].f1276a.f10634a.iterator(); it2.hasNext(); it2 = it2) {
            i23 = Math.max(i23, c(((i) it2.next()).f1377c.f1277b, i10, z10, n11 + i22));
        }
        if (z11) {
            i20 -= i15;
            n10 = i23 + i14;
        } else {
            n10 = i23 + ((i10 == 0 ? constraintWidget.n() : constraintWidget.h()) * i16);
        }
        if (i10 == 1) {
            Iterator<c> it3 = constraintWidget.f1318w.f1276a.f10634a.iterator();
            int i24 = 0;
            while (it3.hasNext()) {
                i iVar = (i) it3.next();
                if (i16 == 1) {
                    i19 = Math.max(i24, c(iVar.f1377c.f1277b, i10, z10, i15 + i22));
                } else {
                    i19 = Math.max(i24, c(iVar.f1377c.f1277b, i10, z10, (i14 * i16) + i22));
                }
                i24 = i19;
                it3 = it3;
                i12 = i12;
            }
            i17 = i12;
            i18 = (constraintWidget.f1318w.f1276a.f10634a.size() <= 0 || z11) ? i24 : i16 == 1 ? i24 + i15 : i24 - i14;
        } else {
            i17 = i12;
            i18 = 0;
        }
        int max = Math.max(i20, Math.max(n10, i18)) + d10;
        int i25 = n11 + i22;
        if (i16 == -1) {
            i22 = i25;
            i25 = i22;
        }
        if (z10) {
            h.b(constraintWidget, i10, i22);
            constraintWidget.v(i22, i25, i10);
        } else {
            constraintWidget.f1311p.a(constraintWidget, i10);
            if (i10 == 0) {
                constraintWidget.K = i22;
            } else if (i10 == 1) {
                constraintWidget.L = i22;
            }
        }
        if (constraintWidget.g(i10) == ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT && constraintWidget.G != Constant.VOLUME_FLOAT_MIN) {
            constraintWidget.f1311p.a(constraintWidget, i10);
        }
        ConstraintAnchor[] constraintAnchorArr2 = constraintWidget.A;
        if (constraintAnchorArr2[i13].f1279d != null && constraintAnchorArr2[i17].f1279d != null && constraintAnchorArr2[i13].f1279d.f1277b == (constraintWidget2 = constraintWidget.D) && constraintAnchorArr2[i17].f1279d.f1277b == constraintWidget2) {
            constraintWidget.f1311p.a(constraintWidget, i10);
        }
        return max;
    }

    public static int d(ConstraintWidget constraintWidget, int i10) {
        ConstraintWidget constraintWidget2;
        ConstraintAnchor constraintAnchor;
        int i11 = i10 * 2;
        ConstraintAnchor[] constraintAnchorArr = constraintWidget.A;
        ConstraintAnchor constraintAnchor2 = constraintAnchorArr[i11];
        ConstraintAnchor constraintAnchor3 = constraintAnchorArr[i11 + 1];
        ConstraintAnchor constraintAnchor4 = constraintAnchor2.f1279d;
        if (constraintAnchor4 == null || constraintAnchor4.f1277b != (constraintWidget2 = constraintWidget.D) || (constraintAnchor = constraintAnchor3.f1279d) == null || constraintAnchor.f1277b != constraintWidget2) {
            return 0;
        }
        return (int) (((float) (((constraintWidget2.j(i10) - constraintAnchor2.b()) - constraintAnchor3.b()) - constraintWidget.j(i10))) * (i10 == 0 ? constraintWidget.V : constraintWidget.W));
    }

    public static int e(ConstraintWidget constraintWidget) {
        float f10;
        float f11;
        ConstraintWidget.DimensionBehaviour i10 = constraintWidget.i();
        ConstraintWidget.DimensionBehaviour dimensionBehaviour = ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT;
        if (i10 == dimensionBehaviour) {
            if (constraintWidget.H == 0) {
                f11 = ((float) constraintWidget.h()) * constraintWidget.G;
            } else {
                f11 = ((float) constraintWidget.h()) / constraintWidget.G;
            }
            int i11 = (int) f11;
            constraintWidget.C(i11);
            return i11;
        } else if (constraintWidget.m() != dimensionBehaviour) {
            return -1;
        } else {
            if (constraintWidget.H == 1) {
                f10 = ((float) constraintWidget.n()) * constraintWidget.G;
            } else {
                f10 = ((float) constraintWidget.n()) / constraintWidget.G;
            }
            int i12 = (int) f10;
            constraintWidget.w(i12);
            return i12;
        }
    }

    public static void f(List<f> list, int i10, int i11) {
        HashSet<ConstraintWidget> hashSet;
        int i12;
        int size = list.size();
        for (int i13 = 0; i13 < size; i13++) {
            f fVar = list.get(i13);
            Objects.requireNonNull(fVar);
            if (i10 == 0) {
                hashSet = fVar.f1367f;
            } else {
                hashSet = i10 == 1 ? fVar.f1368g : null;
            }
            for (ConstraintWidget constraintWidget : hashSet) {
                if (constraintWidget.f1290a0) {
                    int i14 = i10 * 2;
                    ConstraintAnchor[] constraintAnchorArr = constraintWidget.A;
                    ConstraintAnchor constraintAnchor = constraintAnchorArr[i14];
                    ConstraintAnchor constraintAnchor2 = constraintAnchorArr[i14 + 1];
                    if ((constraintAnchor.f1279d == null || constraintAnchor2.f1279d == null) ? false : true) {
                        h.b(constraintWidget, i10, constraintAnchor.b() + d(constraintWidget, i10));
                    } else if (constraintWidget.G == Constant.VOLUME_FLOAT_MIN || constraintWidget.g(i10) != ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT) {
                        if (i10 == 0) {
                            i12 = constraintWidget.K;
                        } else {
                            i12 = i10 == 1 ? constraintWidget.L : 0;
                        }
                        int i15 = i11 - i12;
                        int j10 = i15 - constraintWidget.j(i10);
                        constraintWidget.v(j10, i15, i10);
                        h.b(constraintWidget, i10, j10);
                    } else {
                        int e10 = e(constraintWidget);
                        int i16 = (int) constraintWidget.A[i14].f1276a.f1381g;
                        i iVar = constraintAnchor2.f1276a;
                        iVar.f1380f = constraintAnchor.f1276a;
                        iVar.f1381g = (float) e10;
                        iVar.f10635b = 1;
                        constraintWidget.v(i16, i16 + e10, i10);
                    }
                }
            }
        }
    }

    public static boolean g(ConstraintWidget constraintWidget, f fVar, List<f> list, boolean z10) {
        ConstraintAnchor constraintAnchor;
        ConstraintWidget constraintWidget2;
        ConstraintAnchor constraintAnchor2;
        ConstraintWidget constraintWidget3;
        if (constraintWidget == null) {
            return true;
        }
        constraintWidget.f1292b0 = false;
        e eVar = (e) constraintWidget.D;
        f fVar2 = constraintWidget.f1311p;
        if (fVar2 == null) {
            constraintWidget.f1290a0 = true;
            fVar.f1362a.add(constraintWidget);
            constraintWidget.f1311p = fVar;
            if (constraintWidget.f1314s.f1279d == null && constraintWidget.f1316u.f1279d == null && constraintWidget.f1315t.f1279d == null && constraintWidget.f1317v.f1279d == null && constraintWidget.f1318w.f1279d == null && constraintWidget.f1321z.f1279d == null) {
                fVar.f1363b = false;
                eVar.f1359x0 = false;
                constraintWidget.f1290a0 = false;
                if (z10) {
                    return false;
                }
            }
            if (!(constraintWidget.f1315t.f1279d == null || constraintWidget.f1317v.f1279d == null)) {
                eVar.m();
                if (z10) {
                    fVar.f1363b = false;
                    eVar.f1359x0 = false;
                    constraintWidget.f1290a0 = false;
                    return false;
                }
                ConstraintWidget constraintWidget4 = constraintWidget.f1315t.f1279d.f1277b;
                ConstraintWidget constraintWidget5 = constraintWidget.D;
                if (!(constraintWidget4 == constraintWidget5 && constraintWidget.f1317v.f1279d.f1277b == constraintWidget5)) {
                    fVar.f1363b = false;
                    eVar.f1359x0 = false;
                    constraintWidget.f1290a0 = false;
                }
            }
            if (!(constraintWidget.f1314s.f1279d == null || constraintWidget.f1316u.f1279d == null)) {
                eVar.i();
                if (z10) {
                    fVar.f1363b = false;
                    eVar.f1359x0 = false;
                    constraintWidget.f1290a0 = false;
                    return false;
                }
                ConstraintWidget constraintWidget6 = constraintWidget.f1314s.f1279d.f1277b;
                ConstraintWidget constraintWidget7 = constraintWidget.D;
                if (!(constraintWidget6 == constraintWidget7 && constraintWidget.f1316u.f1279d.f1277b == constraintWidget7)) {
                    fVar.f1363b = false;
                    eVar.f1359x0 = false;
                    constraintWidget.f1290a0 = false;
                }
            }
            ConstraintWidget.DimensionBehaviour i10 = constraintWidget.i();
            ConstraintWidget.DimensionBehaviour dimensionBehaviour = ConstraintWidget.DimensionBehaviour.MATCH_CONSTRAINT;
            if (((i10 == dimensionBehaviour) ^ (constraintWidget.m() == dimensionBehaviour)) && constraintWidget.G != Constant.VOLUME_FLOAT_MIN) {
                e(constraintWidget);
            } else if (constraintWidget.i() == dimensionBehaviour || constraintWidget.m() == dimensionBehaviour) {
                fVar.f1363b = false;
                eVar.f1359x0 = false;
                constraintWidget.f1290a0 = false;
                if (z10) {
                    return false;
                }
            }
            ConstraintAnchor constraintAnchor3 = constraintWidget.f1314s.f1279d;
            if (((constraintAnchor3 == null && constraintWidget.f1316u.f1279d == null) || ((constraintAnchor3 != null && constraintAnchor3.f1277b == constraintWidget.D && constraintWidget.f1316u.f1279d == null) || (((constraintAnchor2 = constraintWidget.f1316u.f1279d) != null && constraintAnchor2.f1277b == constraintWidget.D && constraintAnchor3 == null) || (constraintAnchor3 != null && constraintAnchor3.f1277b == (constraintWidget3 = constraintWidget.D) && constraintAnchor2 != null && constraintAnchor2.f1277b == constraintWidget3)))) && constraintWidget.f1321z.f1279d == null && !(constraintWidget instanceof g) && !(constraintWidget instanceof w.a)) {
                fVar.f1365d.add(constraintWidget);
            }
            ConstraintAnchor constraintAnchor4 = constraintWidget.f1315t.f1279d;
            if (((constraintAnchor4 == null && constraintWidget.f1317v.f1279d == null) || ((constraintAnchor4 != null && constraintAnchor4.f1277b == constraintWidget.D && constraintWidget.f1317v.f1279d == null) || (((constraintAnchor = constraintWidget.f1317v.f1279d) != null && constraintAnchor.f1277b == constraintWidget.D && constraintAnchor4 == null) || (constraintAnchor4 != null && constraintAnchor4.f1277b == (constraintWidget2 = constraintWidget.D) && constraintAnchor != null && constraintAnchor.f1277b == constraintWidget2)))) && constraintWidget.f1321z.f1279d == null && constraintWidget.f1318w.f1279d == null && !(constraintWidget instanceof g) && !(constraintWidget instanceof w.a)) {
                fVar.f1366e.add(constraintWidget);
            }
            if (constraintWidget instanceof w.a) {
                fVar.f1363b = false;
                eVar.f1359x0 = false;
                constraintWidget.f1290a0 = false;
                if (z10) {
                    return false;
                }
                w.a aVar = (w.a) constraintWidget;
                for (int i11 = 0; i11 < aVar.f10632j0; i11++) {
                    if (!g(aVar.f10631i0[i11], fVar, list, z10)) {
                        return false;
                    }
                }
            }
            int length = constraintWidget.A.length;
            for (int i12 = 0; i12 < length; i12++) {
                ConstraintAnchor constraintAnchor5 = constraintWidget.A[i12];
                ConstraintAnchor constraintAnchor6 = constraintAnchor5.f1279d;
                if (!(constraintAnchor6 == null || constraintAnchor6.f1277b == constraintWidget.D)) {
                    if (constraintAnchor5.f1278c == ConstraintAnchor.Type.CENTER) {
                        fVar.f1363b = false;
                        eVar.f1359x0 = false;
                        constraintWidget.f1290a0 = false;
                        if (z10) {
                            return false;
                        }
                    } else {
                        i iVar = constraintAnchor5.f1276a;
                        if (constraintAnchor6.f1279d != constraintAnchor5) {
                            constraintAnchor6.f1276a.f10634a.add(iVar);
                        }
                    }
                    if (!g(constraintAnchor5.f1279d.f1277b, fVar, list, z10)) {
                        return false;
                    }
                }
            }
            return true;
        }
        if (fVar2 != fVar) {
            fVar.f1362a.addAll(fVar2.f1362a);
            fVar.f1365d.addAll(constraintWidget.f1311p.f1365d);
            fVar.f1366e.addAll(constraintWidget.f1311p.f1366e);
            f fVar3 = constraintWidget.f1311p;
            if (!fVar3.f1363b) {
                fVar.f1363b = false;
            }
            list.remove(fVar3);
            for (ConstraintWidget constraintWidget8 : constraintWidget.f1311p.f1362a) {
                constraintWidget8.f1311p = fVar;
            }
        }
        return true;
    }
}
