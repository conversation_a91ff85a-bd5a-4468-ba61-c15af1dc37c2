package androidx.appcompat.view.menu;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;
import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleRes;
import androidx.appcompat.R$dimen;
import androidx.appcompat.view.menu.h;
import androidx.core.view.ViewCompat;
import j0.m;
import java.util.WeakHashMap;
import q.d;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: MenuPopupHelper */
public class g {

    /* renamed from: a  reason: collision with root package name */
    public final Context f656a;

    /* renamed from: b  reason: collision with root package name */
    public final d f657b;

    /* renamed from: c  reason: collision with root package name */
    public final boolean f658c;

    /* renamed from: d  reason: collision with root package name */
    public final int f659d;

    /* renamed from: e  reason: collision with root package name */
    public final int f660e;

    /* renamed from: f  reason: collision with root package name */
    public View f661f;

    /* renamed from: g  reason: collision with root package name */
    public int f662g = 8388611;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public h.a f663i;

    /* renamed from: j  reason: collision with root package name */
    public d f664j;

    /* renamed from: k  reason: collision with root package name */
    public PopupWindow.OnDismissListener f665k;

    /* renamed from: l  reason: collision with root package name */
    public final PopupWindow.OnDismissListener f666l = new a();

    /* compiled from: MenuPopupHelper */
    public class a implements PopupWindow.OnDismissListener {
        public a() {
        }

        public void onDismiss() {
            g.this.c();
        }
    }

    public g(@NonNull Context context, @NonNull d dVar, @NonNull View view, boolean z10, @AttrRes int i10, @StyleRes int i11) {
        this.f656a = context;
        this.f657b = dVar;
        this.f661f = view;
        this.f658c = z10;
        this.f659d = i10;
        this.f660e = i11;
    }

    @NonNull
    public d a() {
        d dVar;
        if (this.f664j == null) {
            Display defaultDisplay = ((WindowManager) this.f656a.getSystemService("window")).getDefaultDisplay();
            Point point = new Point();
            defaultDisplay.getRealSize(point);
            if (Math.min(point.x, point.y) >= this.f656a.getResources().getDimensionPixelSize(R$dimen.abc_cascading_menus_min_smallest_width)) {
                dVar = new CascadingMenuPopup(this.f656a, this.f661f, this.f659d, this.f660e, this.f658c);
            } else {
                dVar = new j(this.f656a, this.f657b, this.f661f, this.f659d, this.f660e, this.f658c);
            }
            dVar.m(this.f657b);
            dVar.s(this.f666l);
            dVar.o(this.f661f);
            dVar.k(this.f663i);
            dVar.p(this.h);
            dVar.q(this.f662g);
            this.f664j = dVar;
        }
        return this.f664j;
    }

    public boolean b() {
        d dVar = this.f664j;
        return dVar != null && dVar.isShowing();
    }

    public void c() {
        this.f664j = null;
        PopupWindow.OnDismissListener onDismissListener = this.f665k;
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

    public void d(@Nullable h.a aVar) {
        this.f663i = aVar;
        d dVar = this.f664j;
        if (dVar != null) {
            dVar.k(aVar);
        }
    }

    public final void e(int i10, int i11, boolean z10, boolean z11) {
        d a10 = a();
        a10.t(z11);
        if (z10) {
            int i12 = this.f662g;
            View view = this.f661f;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if ((Gravity.getAbsoluteGravity(i12, view.getLayoutDirection()) & 7) == 5) {
                i10 -= this.f661f.getWidth();
            }
            a10.r(i10);
            a10.u(i11);
            int i13 = (int) ((this.f656a.getResources().getDisplayMetrics().density * 48.0f) / 2.0f);
            a10.f9729a = new Rect(i10 - i13, i11 - i13, i10 + i13, i11 + i13);
        }
        a10.j();
    }

    public boolean f() {
        if (b()) {
            return true;
        }
        if (this.f661f == null) {
            return false;
        }
        e(0, 0, false, false);
        return true;
    }
}
