package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.SolverVariable;

public class ConstraintAnchor {

    /* renamed from: a  reason: collision with root package name */
    public i f1276a = new i(this);

    /* renamed from: b  reason: collision with root package name */
    public final ConstraintWidget f1277b;

    /* renamed from: c  reason: collision with root package name */
    public final Type f1278c;

    /* renamed from: d  reason: collision with root package name */
    public ConstraintAnchor f1279d;

    /* renamed from: e  reason: collision with root package name */
    public int f1280e = 0;

    /* renamed from: f  reason: collision with root package name */
    public int f1281f = -1;

    /* renamed from: g  reason: collision with root package name */
    public Strength f1282g = Strength.NONE;
    public int h = 0;

    /* renamed from: i  reason: collision with root package name */
    public SolverVariable f1283i;

    public enum ConnectionType {
        RELAXED,
        STRICT
    }

    public enum Strength {
        NONE,
        STRONG,
        WEAK
    }

    public enum Type {
        NONE,
        LEFT,
        TOP,
        RIGHT,
        BOTTOM,
        BASELINE,
        CENTER,
        CENTER_X,
        CENTER_Y
    }

    public static /* synthetic */ class a {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int[] f1287a;

        /* JADX WARNING: Can't wrap try/catch for region: R(18:0|1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|(3:17|18|20)) */
        /* JADX WARNING: Failed to process nested try/catch */
        /* JADX WARNING: Missing exception handler attribute for start block: B:11:0x003e */
        /* JADX WARNING: Missing exception handler attribute for start block: B:13:0x0049 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:15:0x0054 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:17:0x0060 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:3:0x0012 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:5:0x001d */
        /* JADX WARNING: Missing exception handler attribute for start block: B:7:0x0028 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:9:0x0033 */
        static {
            /*
            // Method dump skipped, instructions count: 109
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.ConstraintAnchor.a.<clinit>():void");
        }
    }

    public ConstraintAnchor(ConstraintWidget constraintWidget, Type type) {
        this.f1277b = constraintWidget;
        this.f1278c = type;
    }

    /* JADX INFO: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARNING: Code restructure failed: missing block: B:18:0x0034, code lost:
        if ((r4.f1277b.Q > 0) == false) goto L_0x0067;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:33:0x0064, code lost:
        if (r10 != androidx.constraintlayout.solver.widgets.ConstraintAnchor.Type.CENTER_Y) goto L_0x0067;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:55:0x0093, code lost:
        if (r10 != androidx.constraintlayout.solver.widgets.ConstraintAnchor.Type.CENTER_Y) goto L_0x0069;
     */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x0098 A[RETURN] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean a(androidx.constraintlayout.solver.widgets.ConstraintAnchor r5, int r6, int r7, androidx.constraintlayout.solver.widgets.ConstraintAnchor.Strength r8, int r9, boolean r10) {
        /*
        // Method dump skipped, instructions count: 192
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.ConstraintAnchor.a(androidx.constraintlayout.solver.widgets.ConstraintAnchor, int, int, androidx.constraintlayout.solver.widgets.ConstraintAnchor$Strength, int, boolean):boolean");
    }

    public int b() {
        ConstraintAnchor constraintAnchor;
        if (this.f1277b.Y == 8) {
            return 0;
        }
        int i10 = this.f1281f;
        if (i10 <= -1 || (constraintAnchor = this.f1279d) == null || constraintAnchor.f1277b.Y != 8) {
            return this.f1280e;
        }
        return i10;
    }

    public boolean c() {
        return this.f1279d != null;
    }

    public void d() {
        this.f1279d = null;
        this.f1280e = 0;
        this.f1281f = -1;
        this.f1282g = Strength.STRONG;
        this.h = 0;
        this.f1276a.i();
    }

    public void e() {
        SolverVariable solverVariable = this.f1283i;
        if (solverVariable == null) {
            this.f1283i = new SolverVariable(SolverVariable.Type.UNRESTRICTED);
        } else {
            solverVariable.c();
        }
    }

    public String toString() {
        return this.f1277b.Z + ":" + this.f1278c.toString();
    }
}
