package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$dimen;
import androidx.appcompat.R$id;
import androidx.appcompat.R$layout;
import androidx.appcompat.R$styleable;
import androidx.core.view.ViewCompat;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ActivityChooserView extends ViewGroup {

    /* renamed from: a  reason: collision with root package name */
    public final f f785a;

    /* renamed from: b  reason: collision with root package name */
    public final g f786b;

    /* renamed from: c  reason: collision with root package name */
    public final View f787c;

    /* renamed from: d  reason: collision with root package name */
    public final Drawable f788d;

    /* renamed from: e  reason: collision with root package name */
    public final FrameLayout f789e;

    /* renamed from: f  reason: collision with root package name */
    public final ImageView f790f;

    /* renamed from: g  reason: collision with root package name */
    public final FrameLayout f791g;
    public j0.a h;

    /* renamed from: i  reason: collision with root package name */
    public final ViewTreeObserver.OnGlobalLayoutListener f792i;

    /* renamed from: j  reason: collision with root package name */
    public ListPopupWindow f793j;

    /* renamed from: k  reason: collision with root package name */
    public PopupWindow.OnDismissListener f794k;

    /* renamed from: l  reason: collision with root package name */
    public int f795l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f796m;

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public static class InnerLayout extends LinearLayout {

        /* renamed from: a  reason: collision with root package name */
        public static final int[] f797a = {16842964};

        public InnerLayout(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
            Drawable drawable;
            int resourceId;
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f797a);
            if (!obtainStyledAttributes.hasValue(0) || (resourceId = obtainStyledAttributes.getResourceId(0, 0)) == 0) {
                drawable = obtainStyledAttributes.getDrawable(0);
            } else {
                drawable = m.a.a(context, resourceId);
            }
            setBackgroundDrawable(drawable);
            obtainStyledAttributes.recycle();
        }
    }

    public class a extends DataSetObserver {
        public a() {
        }

        public void onChanged() {
            super.onChanged();
            ActivityChooserView.this.f785a.notifyDataSetChanged();
        }

        public void onInvalidated() {
            super.onInvalidated();
            ActivityChooserView.this.f785a.notifyDataSetInvalidated();
        }
    }

    public class b implements ViewTreeObserver.OnGlobalLayoutListener {
        public b() {
        }

        public void onGlobalLayout() {
            if (!ActivityChooserView.this.b()) {
                return;
            }
            if (!ActivityChooserView.this.isShown()) {
                ActivityChooserView.this.getListPopupWindow().dismiss();
                return;
            }
            ActivityChooserView.this.getListPopupWindow().j();
            j0.a aVar = ActivityChooserView.this.h;
            if (aVar != null) {
                aVar.i(true);
            }
        }
    }

    public class c extends View.AccessibilityDelegate {
        public c(ActivityChooserView activityChooserView) {
        }

        public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfo accessibilityNodeInfo) {
            super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfo);
            accessibilityNodeInfo.setCanOpenPopup(true);
        }
    }

    public class d extends x {
        public d(View view) {
            super(view);
        }

        @Override // androidx.appcompat.widget.x
        public q.f b() {
            return ActivityChooserView.this.getListPopupWindow();
        }

        @Override // androidx.appcompat.widget.x
        public boolean c() {
            ActivityChooserView.this.c();
            return true;
        }

        @Override // androidx.appcompat.widget.x
        public boolean d() {
            ActivityChooserView.this.a();
            return true;
        }
    }

    public class e extends DataSetObserver {
        public e() {
        }

        public void onChanged() {
            super.onChanged();
            Objects.requireNonNull(ActivityChooserView.this.f785a);
            throw null;
        }
    }

    public class f extends BaseAdapter {

        /* renamed from: a  reason: collision with root package name */
        public int f802a = 4;

        /* renamed from: b  reason: collision with root package name */
        public boolean f803b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f804c;

        public f() {
        }

        public int getCount() {
            throw null;
        }

        public Object getItem(int i10) {
            if (this.f804c) {
                throw null;
            } else if (!this.f803b) {
                throw null;
            } else {
                throw null;
            }
        }

        public long getItemId(int i10) {
            return (long) i10;
        }

        public int getItemViewType(int i10) {
            if (!this.f804c) {
                return 0;
            }
            throw null;
        }

        public View getView(int i10, View view, ViewGroup viewGroup) {
            if (!this.f804c) {
                if (view == null || view.getId() != R$id.list_item) {
                    view = LayoutInflater.from(ActivityChooserView.this.getContext()).inflate(R$layout.abc_activity_chooser_view_list_item, viewGroup, false);
                }
                ActivityChooserView.this.getContext().getPackageManager();
                ImageView imageView = (ImageView) view.findViewById(R$id.icon);
                getItem(i10);
                throw null;
            }
            throw null;
        }

        public int getViewTypeCount() {
            return 3;
        }
    }

    public class g implements AdapterView.OnItemClickListener, View.OnClickListener, View.OnLongClickListener, PopupWindow.OnDismissListener {
        public g() {
        }

        public void onClick(View view) {
            ActivityChooserView activityChooserView = ActivityChooserView.this;
            if (view == activityChooserView.f791g) {
                activityChooserView.a();
                Objects.requireNonNull(ActivityChooserView.this.f785a);
                throw null;
            } else if (view == activityChooserView.f789e) {
                activityChooserView.d(activityChooserView.f795l);
                throw null;
            } else {
                throw new IllegalArgumentException();
            }
        }

        public void onDismiss() {
            PopupWindow.OnDismissListener onDismissListener = ActivityChooserView.this.f794k;
            if (onDismissListener != null) {
                onDismissListener.onDismiss();
            }
            j0.a aVar = ActivityChooserView.this.h;
            if (aVar != null) {
                aVar.i(false);
            }
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
            if (!((f) adapterView.getAdapter()).f804c) {
                ActivityChooserView.this.a();
                ActivityChooserView activityChooserView = ActivityChooserView.this;
                Objects.requireNonNull(activityChooserView);
                boolean z10 = activityChooserView.f785a.f803b;
                throw null;
            }
            throw null;
        }

        public boolean onLongClick(View view) {
            ActivityChooserView activityChooserView = ActivityChooserView.this;
            if (view == activityChooserView.f791g) {
                Objects.requireNonNull(activityChooserView.f785a);
                throw null;
            }
            throw new IllegalArgumentException();
        }
    }

    public ActivityChooserView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public boolean a() {
        if (!b()) {
            return true;
        }
        getListPopupWindow().dismiss();
        ViewTreeObserver viewTreeObserver = getViewTreeObserver();
        if (!viewTreeObserver.isAlive()) {
            return true;
        }
        viewTreeObserver.removeGlobalOnLayoutListener(this.f792i);
        return true;
    }

    public boolean b() {
        return getListPopupWindow().isShowing();
    }

    public boolean c() {
        if (b() || !this.f796m) {
            return false;
        }
        d(this.f795l);
        throw null;
    }

    public void d(int i10) {
        Objects.requireNonNull(this.f785a);
        throw new IllegalStateException("No data model. Did you call #setDataModel?");
    }

    public d getDataModel() {
        Objects.requireNonNull(this.f785a);
        return null;
    }

    public ListPopupWindow getListPopupWindow() {
        if (this.f793j == null) {
            ListPopupWindow listPopupWindow = new ListPopupWindow(getContext(), null, R$attr.listPopupWindowStyle);
            this.f793j = listPopupWindow;
            listPopupWindow.h(this.f785a);
            ListPopupWindow listPopupWindow2 = this.f793j;
            listPopupWindow2.f898o = this;
            listPopupWindow2.p(true);
            ListPopupWindow listPopupWindow3 = this.f793j;
            g gVar = this.f786b;
            listPopupWindow3.f900p = gVar;
            listPopupWindow3.f899o0.setOnDismissListener(gVar);
        }
        return this.f793j;
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        Objects.requireNonNull(this.f785a);
        this.f796m = true;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Objects.requireNonNull(this.f785a);
        ViewTreeObserver viewTreeObserver = getViewTreeObserver();
        if (viewTreeObserver.isAlive()) {
            viewTreeObserver.removeGlobalOnLayoutListener(this.f792i);
        }
        if (b()) {
            a();
        }
        this.f796m = false;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        this.f787c.layout(0, 0, i12 - i10, i13 - i11);
        if (!b()) {
            a();
        }
    }

    public void onMeasure(int i10, int i11) {
        View view = this.f787c;
        if (this.f791g.getVisibility() != 0) {
            i11 = View.MeasureSpec.makeMeasureSpec(View.MeasureSpec.getSize(i11), 1073741824);
        }
        measureChild(view, i10, i11);
        setMeasuredDimension(view.getMeasuredWidth(), view.getMeasuredHeight());
    }

    public void setActivityChooserModel(d dVar) {
        f fVar = this.f785a;
        Objects.requireNonNull(ActivityChooserView.this.f785a);
        fVar.notifyDataSetChanged();
        if (b()) {
            a();
            c();
        }
    }

    public void setDefaultActionButtonContentDescription(int i10) {
    }

    public void setExpandActivityOverflowButtonContentDescription(int i10) {
        this.f790f.setContentDescription(getContext().getString(i10));
    }

    public void setExpandActivityOverflowButtonDrawable(Drawable drawable) {
        this.f790f.setImageDrawable(drawable);
    }

    public void setInitialActivityCount(int i10) {
        this.f795l = i10;
    }

    public void setOnDismissListener(PopupWindow.OnDismissListener onDismissListener) {
        this.f794k = onDismissListener;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public void setProvider(j0.a aVar) {
        this.h = aVar;
    }

    public ActivityChooserView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        new a();
        this.f792i = new b();
        this.f795l = 4;
        int[] iArr = R$styleable.ActivityChooserView;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i10, 0);
        ViewCompat.j(this, context, iArr, attributeSet, obtainStyledAttributes, i10, 0);
        this.f795l = obtainStyledAttributes.getInt(R$styleable.ActivityChooserView_initialActivityCount, 4);
        Drawable drawable = obtainStyledAttributes.getDrawable(R$styleable.ActivityChooserView_expandActivityOverflowButtonDrawable);
        obtainStyledAttributes.recycle();
        LayoutInflater.from(getContext()).inflate(R$layout.abc_activity_chooser_view, (ViewGroup) this, true);
        g gVar = new g();
        this.f786b = gVar;
        View findViewById = findViewById(R$id.activity_chooser_view_content);
        this.f787c = findViewById;
        this.f788d = findViewById.getBackground();
        FrameLayout frameLayout = (FrameLayout) findViewById(R$id.default_activity_button);
        this.f791g = frameLayout;
        frameLayout.setOnClickListener(gVar);
        frameLayout.setOnLongClickListener(gVar);
        int i11 = R$id.image;
        ImageView imageView = (ImageView) frameLayout.findViewById(i11);
        FrameLayout frameLayout2 = (FrameLayout) findViewById(R$id.expand_activities_button);
        frameLayout2.setOnClickListener(gVar);
        frameLayout2.setAccessibilityDelegate(new c(this));
        frameLayout2.setOnTouchListener(new d(frameLayout2));
        this.f789e = frameLayout2;
        ImageView imageView2 = (ImageView) frameLayout2.findViewById(i11);
        this.f790f = imageView2;
        imageView2.setImageDrawable(drawable);
        f fVar = new f();
        this.f785a = fVar;
        fVar.registerDataSetObserver(new e());
        Resources resources = context.getResources();
        Math.max(resources.getDisplayMetrics().widthPixels / 2, resources.getDimensionPixelSize(R$dimen.abc_config_prefDialogWidth));
    }
}
