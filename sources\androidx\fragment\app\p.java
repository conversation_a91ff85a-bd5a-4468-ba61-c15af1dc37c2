package androidx.fragment.app;

import android.view.ViewGroup;
import android.view.animation.Animation;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.k0;

/* compiled from: FragmentAnim */
public class p implements Animation.AnimationListener {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ViewGroup f1975a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ Fragment f1976b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ k0.a f1977c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ f0.a f1978d;

    /* compiled from: FragmentAnim */
    public class a implements Runnable {
        public a() {
        }

        public void run() {
            if (p.this.f1976b.d() != null) {
                p.this.f1976b.P(null);
                p pVar = p.this;
                ((FragmentManager.d) pVar.f1977c).a(pVar.f1976b, pVar.f1978d);
            }
        }
    }

    public p(ViewGroup viewGroup, Fragment fragment, k0.a aVar, f0.a aVar2) {
        this.f1975a = viewGroup;
        this.f1976b = fragment;
        this.f1977c = aVar;
        this.f1978d = aVar2;
    }

    public void onAnimationEnd(Animation animation) {
        this.f1975a.post(new a());
    }

    public void onAnimationRepeat(Animation animation) {
    }

    public void onAnimationStart(Animation animation) {
    }
}
