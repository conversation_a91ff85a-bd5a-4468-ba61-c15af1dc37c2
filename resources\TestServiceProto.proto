syntax = "proto3";

package com.xiaomi.idm.service.test.proto;

option java_package = "com.xiaomi.idm.service.test.proto";
option java_outer_classname = "TestServiceProto";

  message GetSomeString {
  int32 aid = 1;
  string param1 = 2;
  string param2 = 3;
  string param3 = 4;
}
  message GetAPlusB {
  int32 aid = 1;
  int32 a = 2;
  int32 b = 3;
}
  message GetTimestamp {
  int32 aid = 1;
}
  message TriggerClick {
  int32 aid = 1;
}
  message GetSomeStringRes {
  string some1 = 1;
  string some2 = 2;
  string some3 = 3;
}
  message MyTestEvent {
  int32 param = 1;
  string paramStr = 2;
}