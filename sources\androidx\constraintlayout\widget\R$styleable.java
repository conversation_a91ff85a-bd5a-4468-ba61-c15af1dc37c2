package androidx.constraintlayout.widget;

public final class R$styleable {
    public static final int[] ConstraintLayout_Layout = {16842948, 16843039, 16843040, 16843071, 16843072, 2130968713, 2130968714, 2130968778, 2130968877, 2130968878, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142, 2130969143, 2130969144, 2130969145, 2130969146, 2130969147, 2130969148, 2130969149, 2130969150, 2130969151, 2130969152, 2130969153, 2130969154, 2130969155, 2130969156, 2130969157, 2130969158, 2130969159, 2130969160, 2130969161, 2130969162, 2130969163, 2130969164, 2130969165, 2130969166, 2130969167, 2130969168, 2130969169, 2130969170, 2130969171, 2130969172, 2130969173, 2130969174, 2130969175, 2130969176, 2130969178, 2130969179, 2130969180, 2130969181, 2130969182, 2130969183, 2130969184, 2130969185, 2130969188};
    public static final int ConstraintLayout_Layout_android_maxHeight = 2;
    public static final int ConstraintLayout_Layout_android_maxWidth = 1;
    public static final int ConstraintLayout_Layout_android_minHeight = 4;
    public static final int ConstraintLayout_Layout_android_minWidth = 3;
    public static final int ConstraintLayout_Layout_android_orientation = 0;
    public static final int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 5;
    public static final int ConstraintLayout_Layout_barrierDirection = 6;
    public static final int ConstraintLayout_Layout_chainUseRtl = 7;
    public static final int ConstraintLayout_Layout_constraintSet = 8;
    public static final int ConstraintLayout_Layout_constraint_referenced_ids = 9;
    public static final int ConstraintLayout_Layout_layout_constrainedHeight = 10;
    public static final int ConstraintLayout_Layout_layout_constrainedWidth = 11;
    public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 12;
    public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 13;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 14;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 15;
    public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 16;
    public static final int ConstraintLayout_Layout_layout_constraintCircle = 17;
    public static final int ConstraintLayout_Layout_layout_constraintCircleAngle = 18;
    public static final int ConstraintLayout_Layout_layout_constraintCircleRadius = 19;
    public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 20;
    public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 21;
    public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 22;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 23;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 24;
    public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 25;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 26;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 27;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 28;
    public static final int ConstraintLayout_Layout_layout_constraintHeight_percent = 29;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 30;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 31;
    public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 32;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 33;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 34;
    public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 35;
    public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 36;
    public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 37;
    public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 38;
    public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 39;
    public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 40;
    public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 41;
    public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 42;
    public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 43;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 44;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 45;
    public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 46;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 47;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 48;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 49;
    public static final int ConstraintLayout_Layout_layout_constraintWidth_percent = 50;
    public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 51;
    public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 52;
    public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 53;
    public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 54;
    public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 55;
    public static final int ConstraintLayout_Layout_layout_goneMarginRight = 56;
    public static final int ConstraintLayout_Layout_layout_goneMarginStart = 57;
    public static final int ConstraintLayout_Layout_layout_goneMarginTop = 58;
    public static final int ConstraintLayout_Layout_layout_optimizationLevel = 59;
    public static final int[] ConstraintLayout_placeholder = {2130968879, 2130968977};
    public static final int ConstraintLayout_placeholder_content = 0;
    public static final int ConstraintLayout_placeholder_emptyVisibility = 1;
    public static final int[] ConstraintSet = {16842948, 16842960, 16842972, 16842996, 16842997, 16842999, 16843000, 16843001, 16843002, 16843039, 16843040, 16843071, 16843072, 16843551, 16843552, 16843553, 16843554, 16843555, 16843556, 16843557, 16843558, 16843559, 16843560, 16843701, 16843702, 16843770, 16843840, 2130968713, 2130968714, 2130968778, 2130968878, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142, 2130969143, 2130969144, 2130969145, 2130969146, 2130969147, 2130969148, 2130969149, 2130969150, 2130969151, 2130969152, 2130969153, 2130969154, 2130969155, 2130969156, 2130969157, 2130969158, 2130969159, 2130969160, 2130969161, 2130969162, 2130969163, 2130969164, 2130969165, 2130969166, 2130969167, 2130969168, 2130969169, 2130969170, 2130969171, 2130969172, 2130969173, 2130969174, 2130969175, 2130969176, 2130969178, 2130969179, 2130969180, 2130969181, 2130969182, 2130969183, 2130969184, 2130969185};
    public static final int ConstraintSet_android_alpha = 13;
    public static final int ConstraintSet_android_elevation = 26;
    public static final int ConstraintSet_android_id = 1;
    public static final int ConstraintSet_android_layout_height = 4;
    public static final int ConstraintSet_android_layout_marginBottom = 8;
    public static final int ConstraintSet_android_layout_marginEnd = 24;
    public static final int ConstraintSet_android_layout_marginLeft = 5;
    public static final int ConstraintSet_android_layout_marginRight = 7;
    public static final int ConstraintSet_android_layout_marginStart = 23;
    public static final int ConstraintSet_android_layout_marginTop = 6;
    public static final int ConstraintSet_android_layout_width = 3;
    public static final int ConstraintSet_android_maxHeight = 10;
    public static final int ConstraintSet_android_maxWidth = 9;
    public static final int ConstraintSet_android_minHeight = 12;
    public static final int ConstraintSet_android_minWidth = 11;
    public static final int ConstraintSet_android_orientation = 0;
    public static final int ConstraintSet_android_rotation = 20;
    public static final int ConstraintSet_android_rotationX = 21;
    public static final int ConstraintSet_android_rotationY = 22;
    public static final int ConstraintSet_android_scaleX = 18;
    public static final int ConstraintSet_android_scaleY = 19;
    public static final int ConstraintSet_android_transformPivotX = 14;
    public static final int ConstraintSet_android_transformPivotY = 15;
    public static final int ConstraintSet_android_translationX = 16;
    public static final int ConstraintSet_android_translationY = 17;
    public static final int ConstraintSet_android_translationZ = 25;
    public static final int ConstraintSet_android_visibility = 2;
    public static final int ConstraintSet_barrierAllowsGoneWidgets = 27;
    public static final int ConstraintSet_barrierDirection = 28;
    public static final int ConstraintSet_chainUseRtl = 29;
    public static final int ConstraintSet_constraint_referenced_ids = 30;
    public static final int ConstraintSet_layout_constrainedHeight = 31;
    public static final int ConstraintSet_layout_constrainedWidth = 32;
    public static final int ConstraintSet_layout_constraintBaseline_creator = 33;
    public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 34;
    public static final int ConstraintSet_layout_constraintBottom_creator = 35;
    public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 36;
    public static final int ConstraintSet_layout_constraintBottom_toTopOf = 37;
    public static final int ConstraintSet_layout_constraintCircle = 38;
    public static final int ConstraintSet_layout_constraintCircleAngle = 39;
    public static final int ConstraintSet_layout_constraintCircleRadius = 40;
    public static final int ConstraintSet_layout_constraintDimensionRatio = 41;
    public static final int ConstraintSet_layout_constraintEnd_toEndOf = 42;
    public static final int ConstraintSet_layout_constraintEnd_toStartOf = 43;
    public static final int ConstraintSet_layout_constraintGuide_begin = 44;
    public static final int ConstraintSet_layout_constraintGuide_end = 45;
    public static final int ConstraintSet_layout_constraintGuide_percent = 46;
    public static final int ConstraintSet_layout_constraintHeight_default = 47;
    public static final int ConstraintSet_layout_constraintHeight_max = 48;
    public static final int ConstraintSet_layout_constraintHeight_min = 49;
    public static final int ConstraintSet_layout_constraintHeight_percent = 50;
    public static final int ConstraintSet_layout_constraintHorizontal_bias = 51;
    public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 52;
    public static final int ConstraintSet_layout_constraintHorizontal_weight = 53;
    public static final int ConstraintSet_layout_constraintLeft_creator = 54;
    public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 55;
    public static final int ConstraintSet_layout_constraintLeft_toRightOf = 56;
    public static final int ConstraintSet_layout_constraintRight_creator = 57;
    public static final int ConstraintSet_layout_constraintRight_toLeftOf = 58;
    public static final int ConstraintSet_layout_constraintRight_toRightOf = 59;
    public static final int ConstraintSet_layout_constraintStart_toEndOf = 60;
    public static final int ConstraintSet_layout_constraintStart_toStartOf = 61;
    public static final int ConstraintSet_layout_constraintTop_creator = 62;
    public static final int ConstraintSet_layout_constraintTop_toBottomOf = 63;
    public static final int ConstraintSet_layout_constraintTop_toTopOf = 64;
    public static final int ConstraintSet_layout_constraintVertical_bias = 65;
    public static final int ConstraintSet_layout_constraintVertical_chainStyle = 66;
    public static final int ConstraintSet_layout_constraintVertical_weight = 67;
    public static final int ConstraintSet_layout_constraintWidth_default = 68;
    public static final int ConstraintSet_layout_constraintWidth_max = 69;
    public static final int ConstraintSet_layout_constraintWidth_min = 70;
    public static final int ConstraintSet_layout_constraintWidth_percent = 71;
    public static final int ConstraintSet_layout_editor_absoluteX = 72;
    public static final int ConstraintSet_layout_editor_absoluteY = 73;
    public static final int ConstraintSet_layout_goneMarginBottom = 74;
    public static final int ConstraintSet_layout_goneMarginEnd = 75;
    public static final int ConstraintSet_layout_goneMarginLeft = 76;
    public static final int ConstraintSet_layout_goneMarginRight = 77;
    public static final int ConstraintSet_layout_goneMarginStart = 78;
    public static final int ConstraintSet_layout_goneMarginTop = 79;
    public static final int[] LinearConstraintLayout = {16842948};
    public static final int LinearConstraintLayout_android_orientation = 0;

    private R$styleable() {
    }
}
