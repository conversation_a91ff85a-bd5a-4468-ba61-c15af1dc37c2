package androidx.coordinatorlayout;

public final class R$attr {
    public static final int alpha = **********;
    public static final int coordinatorLayoutStyle = **********;
    public static final int font = **********;
    public static final int fontProviderAuthority = **********;
    public static final int fontProviderCerts = **********;
    public static final int fontProviderFetchStrategy = **********;
    public static final int fontProviderFetchTimeout = **********;
    public static final int fontProviderPackage = **********;
    public static final int fontProviderQuery = **********;
    public static final int fontStyle = **********;
    public static final int fontVariationSettings = **********;
    public static final int fontWeight = **********;
    public static final int keylines = **********;
    public static final int layout_anchor = **********;
    public static final int layout_anchorGravity = **********;
    public static final int layout_behavior = **********;
    public static final int layout_dodgeInsetEdges = **********;
    public static final int layout_insetEdge = **********;
    public static final int layout_keyline = **********;
    public static final int statusBarBackground = **********;
    public static final int ttcIndex = **********;

    private R$attr() {
    }
}
