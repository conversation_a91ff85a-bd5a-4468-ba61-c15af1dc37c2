
syntax = "proto3";

package com.xiaomi.idm.uwb.proto;

option java_package = "com.xiaomi.idm.uwb.proto";
option java_outer_classname = "UwbData";

message TagInfo {
  string version = 1;
  string did = 2;
  string mod = 3;
}

message TagConnections {
  repeated string uwbAddress = 1;
}

message MeasurementData {
  string uwbAddress = 1;
  int64 distance = 2;
  int32 altitudeAoa = 3;
  int32 azimuthAoa = 4;
  int32 rssi = 5;
  int32 deviceType = 6;
  int64 pid = 7;
  TagState tagState = 8;
  bytes idmIdHash = 9;
  bytes didInfo = 10;
  bytes accountInfo = 11;
  bytes macAddress = 12;
  DeviceState deviceState = 13;
  string bleAddress = 14;
  bytes tvMacInfo = 15;
}

message TagState {
  int32 power = 1;
  int32 usb = 2;
  int32 hid = 3;
  int32 idmSupport = 4;
  int32 tvMode = 5;
  int32 miLinkSupport = 6;
  int32 shutDown = 7;
  int32 tvAuthorization = 8;
  int32 associationStatus = 9;
  int32 tvScreenOff = 10;
  bytes tvAccountInfo = 11;
}

message DeviceState {
  int32 binding = 1;
  int32 authorization = 2;
  int32 idmSupport = 3;
  int32 tvPlazaMode = 4;
  int32 miLinkSupport = 5;
  int32 reset = 6;
  int32 occupied = 7;
  int32 tvScreenOff = 8;
}

message FrameData {
  bytes data = 1;
  int32 taskId = 2;
  string address = 3;
}
