package aa;

import com.duokan.airkan.server.f;

/* compiled from: ASN1TaggedObject */
public abstract class x extends q implements q1 {

    /* renamed from: a  reason: collision with root package name */
    public int f242a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f243b = true;

    /* renamed from: c  reason: collision with root package name */
    public e f244c = null;

    public x(boolean z10, int i10, e eVar) {
        if (eVar instanceof d) {
            this.f243b = true;
        } else {
            this.f243b = z10;
        }
        this.f242a = i10;
        if (this.f243b) {
            this.f244c = eVar;
            return;
        }
        boolean z11 = eVar.c() instanceof t;
        this.f244c = eVar;
    }

    @Override // aa.q1
    public q d() {
        return this;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof x)) {
            return false;
        }
        x xVar = (x) qVar;
        if (this.f242a != xVar.f242a || this.f243b != xVar.f243b) {
            return false;
        }
        e eVar = this.f244c;
        if (eVar == null) {
            if (xVar.f244c != null) {
                return false;
            }
            return true;
        } else if (!eVar.c().equals(xVar.f244c.c())) {
            return false;
        } else {
            return true;
        }
    }

    @Override // aa.l
    public int hashCode() {
        int i10 = this.f242a;
        e eVar = this.f244c;
        return eVar != null ? i10 ^ eVar.hashCode() : i10;
    }

    @Override // aa.q
    public q l() {
        return new f1(this.f243b, this.f242a, this.f244c);
    }

    @Override // aa.q
    public q m() {
        return new o1(this.f243b, this.f242a, this.f244c);
    }

    public q n() {
        e eVar = this.f244c;
        if (eVar != null) {
            return eVar.c();
        }
        return null;
    }

    public String toString() {
        StringBuilder a10 = f.a("[");
        a10.append(this.f242a);
        a10.append("]");
        a10.append(this.f244c);
        return a10.toString();
    }
}
