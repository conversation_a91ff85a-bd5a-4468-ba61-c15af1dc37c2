package androidx.core.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import android.view.FocusFinder;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.animation.AnimationUtils;
import android.widget.EdgeEffect;
import android.widget.FrameLayout;
import android.widget.OverScroller;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.appcompat.app.AlertController;
import androidx.core.view.ViewCompat;
import com.duokan.airkan.common.Constant;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import j0.d;
import j0.f;
import j0.h;
import j0.i;
import j0.m;
import java.util.ArrayList;
import java.util.WeakHashMap;
import k0.b;

public class NestedScrollView extends FrameLayout implements h, d {

    /* renamed from: q0  reason: collision with root package name */
    public static final a f1605q0 = new a();

    /* renamed from: r0  reason: collision with root package name */
    public static final int[] f1606r0 = {16843130};

    /* renamed from: a  reason: collision with root package name */
    public long f1607a;

    /* renamed from: b  reason: collision with root package name */
    public final Rect f1608b;

    /* renamed from: c  reason: collision with root package name */
    public OverScroller f1609c;

    /* renamed from: d  reason: collision with root package name */
    public EdgeEffect f1610d;

    /* renamed from: e  reason: collision with root package name */
    public EdgeEffect f1611e;

    /* renamed from: f  reason: collision with root package name */
    public int f1612f;

    /* renamed from: g  reason: collision with root package name */
    public boolean f1613g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public View f1614i;

    /* renamed from: j  reason: collision with root package name */
    public boolean f1615j;

    /* renamed from: k  reason: collision with root package name */
    public VelocityTracker f1616k;

    /* renamed from: l  reason: collision with root package name */
    public boolean f1617l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1618m;

    /* renamed from: m0  reason: collision with root package name */
    public final i f1619m0;

    /* renamed from: n  reason: collision with root package name */
    public int f1620n;

    /* renamed from: n0  reason: collision with root package name */
    public final f f1621n0;

    /* renamed from: o  reason: collision with root package name */
    public int f1622o;

    /* renamed from: o0  reason: collision with root package name */
    public float f1623o0;

    /* renamed from: p  reason: collision with root package name */
    public int f1624p;

    /* renamed from: p0  reason: collision with root package name */
    public b f1625p0;

    /* renamed from: q  reason: collision with root package name */
    public int f1626q;

    /* renamed from: r  reason: collision with root package name */
    public final int[] f1627r;

    /* renamed from: s  reason: collision with root package name */
    public final int[] f1628s;

    /* renamed from: t  reason: collision with root package name */
    public int f1629t;

    /* renamed from: x  reason: collision with root package name */
    public int f1630x;

    /* renamed from: y  reason: collision with root package name */
    public SavedState f1631y;

    public static class SavedState extends View.BaseSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public int f1632a;

        public class a implements Parcelable.Creator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState createFromParcel(Parcel parcel) {
                return new SavedState(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public SavedState[] newArray(int i10) {
                return new SavedState[i10];
            }
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }

        @NonNull
        public String toString() {
            StringBuilder a10 = com.duokan.airkan.server.f.a("HorizontalScrollView.SavedState{");
            a10.append(Integer.toHexString(System.identityHashCode(this)));
            a10.append(" scrollPosition=");
            return bb.f.b(a10, this.f1632a, "}");
        }

        public void writeToParcel(Parcel parcel, int i10) {
            super.writeToParcel(parcel, i10);
            parcel.writeInt(this.f1632a);
        }

        public SavedState(Parcel parcel) {
            super(parcel);
            this.f1632a = parcel.readInt();
        }
    }

    public static class a extends androidx.core.view.a {
        @Override // androidx.core.view.a
        public void c(View view, AccessibilityEvent accessibilityEvent) {
            this.f1598a.onInitializeAccessibilityEvent(view, accessibilityEvent);
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            accessibilityEvent.setClassName(ScrollView.class.getName());
            accessibilityEvent.setScrollable(nestedScrollView.getScrollRange() > 0);
            accessibilityEvent.setScrollX(nestedScrollView.getScrollX());
            accessibilityEvent.setScrollY(nestedScrollView.getScrollY());
            accessibilityEvent.setMaxScrollX(nestedScrollView.getScrollX());
            accessibilityEvent.setMaxScrollY(nestedScrollView.getScrollRange());
        }

        @Override // androidx.core.view.a
        public void d(View view, k0.b bVar) {
            int scrollRange;
            this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            bVar.f7267a.setClassName(ScrollView.class.getName());
            if (nestedScrollView.isEnabled() && (scrollRange = nestedScrollView.getScrollRange()) > 0) {
                bVar.f7267a.setScrollable(true);
                if (nestedScrollView.getScrollY() > 0) {
                    bVar.a(b.a.f7273i);
                    bVar.a(b.a.f7277m);
                }
                if (nestedScrollView.getScrollY() < scrollRange) {
                    bVar.a(b.a.h);
                    bVar.a(b.a.f7278n);
                }
            }
        }

        @Override // androidx.core.view.a
        public boolean g(View view, int i10, Bundle bundle) {
            if (super.g(view, i10, bundle)) {
                return true;
            }
            NestedScrollView nestedScrollView = (NestedScrollView) view;
            if (!nestedScrollView.isEnabled()) {
                return false;
            }
            if (i10 != 4096) {
                if (i10 == 8192 || i10 == 16908344) {
                    int max = Math.max(nestedScrollView.getScrollY() - ((nestedScrollView.getHeight() - nestedScrollView.getPaddingBottom()) - nestedScrollView.getPaddingTop()), 0);
                    if (max == nestedScrollView.getScrollY()) {
                        return false;
                    }
                    nestedScrollView.B(0 - nestedScrollView.getScrollX(), max - nestedScrollView.getScrollY(), 250, true);
                    return true;
                } else if (i10 != 16908346) {
                    return false;
                }
            }
            int min = Math.min(nestedScrollView.getScrollY() + ((nestedScrollView.getHeight() - nestedScrollView.getPaddingBottom()) - nestedScrollView.getPaddingTop()), nestedScrollView.getScrollRange());
            if (min == nestedScrollView.getScrollY()) {
                return false;
            }
            nestedScrollView.B(0 - nestedScrollView.getScrollX(), min - nestedScrollView.getScrollY(), 250, true);
            return true;
        }
    }

    public interface b {
    }

    public NestedScrollView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public static int c(int i10, int i11, int i12) {
        if (i11 >= i12 || i10 < 0) {
            return 0;
        }
        return i11 + i10 > i12 ? i12 - i11 : i10;
    }

    private float getVerticalScrollFactorCompat() {
        if (this.f1623o0 == Constant.VOLUME_FLOAT_MIN) {
            TypedValue typedValue = new TypedValue();
            Context context = getContext();
            if (context.getTheme().resolveAttribute(16842829, typedValue, true)) {
                this.f1623o0 = typedValue.getDimension(context.getResources().getDisplayMetrics());
            } else {
                throw new IllegalStateException("Expected theme to define listPreferredItemHeight.");
            }
        }
        return this.f1623o0;
    }

    public static boolean s(View view, View view2) {
        if (view == view2) {
            return true;
        }
        ViewParent parent = view.getParent();
        if (!(parent instanceof ViewGroup) || !s((View) parent, view2)) {
            return false;
        }
        return true;
    }

    public final void A(View view) {
        view.getDrawingRect(this.f1608b);
        offsetDescendantRectToMyCoords(view, this.f1608b);
        int d10 = d(this.f1608b);
        if (d10 != 0) {
            scrollBy(0, d10);
        }
    }

    public final void B(int i10, int i11, int i12, boolean z10) {
        if (getChildCount() != 0) {
            if (AnimationUtils.currentAnimationTimeMillis() - this.f1607a > 250) {
                View childAt = getChildAt(0);
                FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
                int scrollY = getScrollY();
                OverScroller overScroller = this.f1609c;
                int scrollX = getScrollX();
                overScroller.startScroll(scrollX, scrollY, 0, Math.max(0, Math.min(i11 + scrollY, Math.max(0, ((childAt.getHeight() + layoutParams.topMargin) + layoutParams.bottomMargin) - ((getHeight() - getPaddingTop()) - getPaddingBottom())))) - scrollY, i12);
                y(z10);
            } else {
                if (!this.f1609c.isFinished()) {
                    a();
                }
                scrollBy(i10, i11);
            }
            this.f1607a = AnimationUtils.currentAnimationTimeMillis();
        }
    }

    public boolean C(int i10, int i11) {
        return this.f1621n0.n(i10, i11);
    }

    public void D(int i10) {
        this.f1621n0.p(i10);
    }

    public final void a() {
        this.f1609c.abortAnimation();
        this.f1621n0.p(1);
    }

    public void addView(View view) {
        if (getChildCount() <= 0) {
            super.addView(view);
            return;
        }
        throw new IllegalStateException("ScrollView can host only one direct child");
    }

    public boolean b(int i10) {
        View findFocus = findFocus();
        if (findFocus == this) {
            findFocus = null;
        }
        View findNextFocus = FocusFinder.getInstance().findNextFocus(this, findFocus, i10);
        int maxScrollAmount = getMaxScrollAmount();
        if (findNextFocus == null || !t(findNextFocus, maxScrollAmount, getHeight())) {
            if (i10 == 33 && getScrollY() < maxScrollAmount) {
                maxScrollAmount = getScrollY();
            } else if (i10 == 130 && getChildCount() > 0) {
                View childAt = getChildAt(0);
                maxScrollAmount = Math.min((childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin) - ((getHeight() + getScrollY()) - getPaddingBottom()), maxScrollAmount);
            }
            if (maxScrollAmount == 0) {
                return false;
            }
            if (i10 != 130) {
                maxScrollAmount = -maxScrollAmount;
            }
            f(maxScrollAmount);
        } else {
            findNextFocus.getDrawingRect(this.f1608b);
            offsetDescendantRectToMyCoords(findNextFocus, this.f1608b);
            f(d(this.f1608b));
            findNextFocus.requestFocus(i10);
        }
        if (findFocus != null && findFocus.isFocused() && (!t(findFocus, 0, getHeight()))) {
            int descendantFocusability = getDescendantFocusability();
            setDescendantFocusability(131072);
            requestFocus();
            setDescendantFocusability(descendantFocusability);
        }
        return true;
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeHorizontalScrollExtent() {
        return super.computeHorizontalScrollExtent();
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeHorizontalScrollOffset() {
        return super.computeHorizontalScrollOffset();
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeHorizontalScrollRange() {
        return super.computeHorizontalScrollRange();
    }

    public void computeScroll() {
        if (!this.f1609c.isFinished()) {
            this.f1609c.computeScrollOffset();
            int currY = this.f1609c.getCurrY();
            int i10 = currY - this.f1630x;
            this.f1630x = currY;
            int[] iArr = this.f1628s;
            boolean z10 = false;
            iArr[1] = 0;
            e(0, i10, iArr, null, 1);
            int i11 = i10 - this.f1628s[1];
            int scrollRange = getScrollRange();
            if (i11 != 0) {
                int scrollY = getScrollY();
                w(0, i11, getScrollX(), scrollY, 0, scrollRange, 0, 0);
                int scrollY2 = getScrollY() - scrollY;
                int i12 = i11 - scrollY2;
                int[] iArr2 = this.f1628s;
                iArr2[1] = 0;
                this.f1621n0.e(0, scrollY2, 0, i12, this.f1627r, 1, iArr2);
                i11 = i12 - this.f1628s[1];
            }
            if (i11 != 0) {
                int overScrollMode = getOverScrollMode();
                if (overScrollMode == 0 || (overScrollMode == 1 && scrollRange > 0)) {
                    z10 = true;
                }
                if (z10) {
                    k();
                    if (i11 < 0) {
                        if (this.f1610d.isFinished()) {
                            this.f1610d.onAbsorb((int) this.f1609c.getCurrVelocity());
                        }
                    } else if (this.f1611e.isFinished()) {
                        this.f1611e.onAbsorb((int) this.f1609c.getCurrVelocity());
                    }
                }
                a();
            }
            if (!this.f1609c.isFinished()) {
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                postInvalidateOnAnimation();
                return;
            }
            this.f1621n0.p(1);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeVerticalScrollExtent() {
        return super.computeVerticalScrollExtent();
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeVerticalScrollOffset() {
        return Math.max(0, super.computeVerticalScrollOffset());
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public int computeVerticalScrollRange() {
        int childCount = getChildCount();
        int height = (getHeight() - getPaddingBottom()) - getPaddingTop();
        if (childCount == 0) {
            return height;
        }
        View childAt = getChildAt(0);
        int bottom = childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin;
        int scrollY = getScrollY();
        int max = Math.max(0, bottom - height);
        if (scrollY < 0) {
            return bottom - scrollY;
        }
        return scrollY > max ? bottom + (scrollY - max) : bottom;
    }

    public int d(Rect rect) {
        int i10;
        int i11;
        if (getChildCount() == 0) {
            return 0;
        }
        int height = getHeight();
        int scrollY = getScrollY();
        int i12 = scrollY + height;
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        if (rect.top > 0) {
            scrollY += verticalFadingEdgeLength;
        }
        View childAt = getChildAt(0);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        int i13 = rect.bottom < (childAt.getHeight() + layoutParams.topMargin) + layoutParams.bottomMargin ? i12 - verticalFadingEdgeLength : i12;
        int i14 = rect.bottom;
        if (i14 > i13 && rect.top > scrollY) {
            if (rect.height() > height) {
                i11 = rect.top - scrollY;
            } else {
                i11 = rect.bottom - i13;
            }
            return Math.min(i11 + 0, (childAt.getBottom() + layoutParams.bottomMargin) - i12);
        } else if (rect.top >= scrollY || i14 >= i13) {
            return 0;
        } else {
            if (rect.height() > height) {
                i10 = 0 - (i13 - rect.bottom);
            } else {
                i10 = 0 - (scrollY - rect.top);
            }
            return Math.max(i10, -getScrollY());
        }
    }

    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        return super.dispatchKeyEvent(keyEvent) || l(keyEvent);
    }

    public boolean dispatchNestedFling(float f10, float f11, boolean z10) {
        return this.f1621n0.a(f10, f11, z10);
    }

    public boolean dispatchNestedPreFling(float f10, float f11) {
        return this.f1621n0.b(f10, f11);
    }

    public boolean dispatchNestedPreScroll(int i10, int i11, int[] iArr, int[] iArr2) {
        return e(i10, i11, iArr, iArr2, 0);
    }

    public boolean dispatchNestedScroll(int i10, int i11, int i12, int i13, int[] iArr) {
        return this.f1621n0.f(i10, i11, i12, i13, iArr);
    }

    public void draw(Canvas canvas) {
        int i10;
        super.draw(canvas);
        if (this.f1610d != null) {
            int scrollY = getScrollY();
            int i11 = 0;
            if (!this.f1610d.isFinished()) {
                int save = canvas.save();
                int width = getWidth();
                int height = getHeight();
                int min = Math.min(0, scrollY);
                if (getClipToPadding()) {
                    width -= getPaddingRight() + getPaddingLeft();
                    i10 = getPaddingLeft() + 0;
                } else {
                    i10 = 0;
                }
                if (getClipToPadding()) {
                    height -= getPaddingBottom() + getPaddingTop();
                    min += getPaddingTop();
                }
                canvas.translate((float) i10, (float) min);
                this.f1610d.setSize(width, height);
                if (this.f1610d.draw(canvas)) {
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    postInvalidateOnAnimation();
                }
                canvas.restoreToCount(save);
            }
            if (!this.f1611e.isFinished()) {
                int save2 = canvas.save();
                int width2 = getWidth();
                int height2 = getHeight();
                int max = Math.max(getScrollRange(), scrollY) + height2;
                if (getClipToPadding()) {
                    width2 -= getPaddingRight() + getPaddingLeft();
                    i11 = 0 + getPaddingLeft();
                }
                if (getClipToPadding()) {
                    height2 -= getPaddingBottom() + getPaddingTop();
                    max -= getPaddingBottom();
                }
                canvas.translate((float) (i11 - width2), (float) max);
                canvas.rotate(180.0f, (float) width2, Constant.VOLUME_FLOAT_MIN);
                this.f1611e.setSize(width2, height2);
                if (this.f1611e.draw(canvas)) {
                    WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
                    postInvalidateOnAnimation();
                }
                canvas.restoreToCount(save2);
            }
        }
    }

    public boolean e(int i10, int i11, int[] iArr, int[] iArr2, int i12) {
        return this.f1621n0.d(i10, i11, iArr, iArr2, i12);
    }

    public final void f(int i10) {
        if (i10 == 0) {
            return;
        }
        if (this.f1618m) {
            B(0, i10, 250, false);
        } else {
            scrollBy(0, i10);
        }
    }

    public final void g() {
        this.f1615j = false;
        x();
        this.f1621n0.p(0);
        EdgeEffect edgeEffect = this.f1610d;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            this.f1611e.onRelease();
        }
    }

    public float getBottomFadingEdgeStrength() {
        if (getChildCount() == 0) {
            return Constant.VOLUME_FLOAT_MIN;
        }
        View childAt = getChildAt(0);
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        int bottom = ((childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin) - getScrollY()) - (getHeight() - getPaddingBottom());
        if (bottom < verticalFadingEdgeLength) {
            return ((float) bottom) / ((float) verticalFadingEdgeLength);
        }
        return 1.0f;
    }

    public int getMaxScrollAmount() {
        return (int) (((float) getHeight()) * 0.5f);
    }

    public int getNestedScrollAxes() {
        return this.f1619m0.a();
    }

    public int getScrollRange() {
        if (getChildCount() <= 0) {
            return 0;
        }
        View childAt = getChildAt(0);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
        return Math.max(0, ((childAt.getHeight() + layoutParams.topMargin) + layoutParams.bottomMargin) - ((getHeight() - getPaddingTop()) - getPaddingBottom()));
    }

    public float getTopFadingEdgeStrength() {
        if (getChildCount() == 0) {
            return Constant.VOLUME_FLOAT_MIN;
        }
        int verticalFadingEdgeLength = getVerticalFadingEdgeLength();
        int scrollY = getScrollY();
        if (scrollY < verticalFadingEdgeLength) {
            return ((float) scrollY) / ((float) verticalFadingEdgeLength);
        }
        return 1.0f;
    }

    @Override // j0.g
    public void h(@NonNull View view, @NonNull View view2, int i10, int i11) {
        i iVar = this.f1619m0;
        if (i11 == 1) {
            iVar.f6908b = i10;
        } else {
            iVar.f6907a = i10;
        }
        C(2, i11);
    }

    public boolean hasNestedScrollingParent() {
        return r(0);
    }

    @Override // j0.g
    public void i(@NonNull View view, int i10) {
        i iVar = this.f1619m0;
        if (i10 == 1) {
            iVar.f6908b = 0;
        } else {
            iVar.f6907a = 0;
        }
        D(i10);
    }

    public boolean isNestedScrollingEnabled() {
        return this.f1621n0.k();
    }

    @Override // j0.g
    public void j(@NonNull View view, int i10, int i11, @NonNull int[] iArr, int i12) {
        e(i10, i11, iArr, null, i12);
    }

    public final void k() {
        if (getOverScrollMode() == 2) {
            this.f1610d = null;
            this.f1611e = null;
        } else if (this.f1610d == null) {
            Context context = getContext();
            this.f1610d = new EdgeEffect(context);
            this.f1611e = new EdgeEffect(context);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:22:0x0062  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0038  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean l(@androidx.annotation.NonNull android.view.KeyEvent r7) {
        /*
        // Method dump skipped, instructions count: 252
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.NestedScrollView.l(android.view.KeyEvent):boolean");
    }

    @Override // j0.h
    public void m(@NonNull View view, int i10, int i11, int i12, int i13, int i14, @NonNull int[] iArr) {
        u(i13, i14, iArr);
    }

    public void measureChild(View view, int i10, int i11) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        view.measure(FrameLayout.getChildMeasureSpec(i10, getPaddingRight() + getPaddingLeft(), layoutParams.width), View.MeasureSpec.makeMeasureSpec(0, 0));
    }

    public void measureChildWithMargins(View view, int i10, int i11, int i12, int i13) {
        ViewGroup.MarginLayoutParams marginLayoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        view.measure(FrameLayout.getChildMeasureSpec(i10, getPaddingRight() + getPaddingLeft() + marginLayoutParams.leftMargin + marginLayoutParams.rightMargin + i11, marginLayoutParams.width), View.MeasureSpec.makeMeasureSpec(marginLayoutParams.topMargin + marginLayoutParams.bottomMargin, 0));
    }

    @Override // j0.g
    public void n(@NonNull View view, int i10, int i11, int i12, int i13, int i14) {
        u(i13, i14, null);
    }

    @Override // j0.g
    public boolean o(@NonNull View view, @NonNull View view2, int i10, int i11) {
        return (i10 & 2) != 0;
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.h = false;
    }

    public boolean onGenericMotionEvent(MotionEvent motionEvent) {
        if ((motionEvent.getSource() & 2) != 0 && motionEvent.getAction() == 8 && !this.f1615j) {
            float axisValue = motionEvent.getAxisValue(9);
            if (axisValue != Constant.VOLUME_FLOAT_MIN) {
                int scrollRange = getScrollRange();
                int scrollY = getScrollY();
                int verticalScrollFactorCompat = scrollY - ((int) (axisValue * getVerticalScrollFactorCompat()));
                if (verticalScrollFactorCompat < 0) {
                    scrollRange = 0;
                } else if (verticalScrollFactorCompat <= scrollRange) {
                    scrollRange = verticalScrollFactorCompat;
                }
                if (scrollRange != scrollY) {
                    super.scrollTo(getScrollX(), scrollRange);
                    return true;
                }
            }
        }
        return false;
    }

    /* JADX WARNING: Removed duplicated region for block: B:47:0x00e5  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x00eb  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onInterceptTouchEvent(android.view.MotionEvent r12) {
        /*
        // Method dump skipped, instructions count: 282
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.core.widget.NestedScrollView.onInterceptTouchEvent(android.view.MotionEvent):boolean");
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        super.onLayout(z10, i10, i11, i12, i13);
        int i14 = 0;
        this.f1613g = false;
        View view = this.f1614i;
        if (view != null && s(view, this)) {
            A(this.f1614i);
        }
        this.f1614i = null;
        if (!this.h) {
            if (this.f1631y != null) {
                scrollTo(getScrollX(), this.f1631y.f1632a);
                this.f1631y = null;
            }
            if (getChildCount() > 0) {
                View childAt = getChildAt(0);
                FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
                i14 = childAt.getMeasuredHeight() + layoutParams.topMargin + layoutParams.bottomMargin;
            }
            int paddingTop = ((i13 - i11) - getPaddingTop()) - getPaddingBottom();
            int scrollY = getScrollY();
            int c10 = c(scrollY, paddingTop, i14);
            if (c10 != scrollY) {
                scrollTo(getScrollX(), c10);
            }
        }
        scrollTo(getScrollX(), getScrollY());
        this.h = true;
    }

    public void onMeasure(int i10, int i11) {
        super.onMeasure(i10, i11);
        if (this.f1617l && View.MeasureSpec.getMode(i11) != 0 && getChildCount() > 0) {
            View childAt = getChildAt(0);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
            int measuredHeight = childAt.getMeasuredHeight();
            int measuredHeight2 = (((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom()) - layoutParams.topMargin) - layoutParams.bottomMargin;
            if (measuredHeight < measuredHeight2) {
                childAt.measure(FrameLayout.getChildMeasureSpec(i10, getPaddingRight() + getPaddingLeft() + layoutParams.leftMargin + layoutParams.rightMargin, layoutParams.width), View.MeasureSpec.makeMeasureSpec(measuredHeight2, 1073741824));
            }
        }
    }

    public boolean onNestedFling(@NonNull View view, float f10, float f11, boolean z10) {
        if (z10) {
            return false;
        }
        dispatchNestedFling(Constant.VOLUME_FLOAT_MIN, f11, true);
        p((int) f11);
        return true;
    }

    public boolean onNestedPreFling(@NonNull View view, float f10, float f11) {
        return dispatchNestedPreFling(f10, f11);
    }

    public void onNestedPreScroll(@NonNull View view, int i10, int i11, @NonNull int[] iArr) {
        j(view, i10, i11, iArr, 0);
    }

    public void onNestedScroll(@NonNull View view, int i10, int i11, int i12, int i13) {
        u(i13, 0, null);
    }

    public void onNestedScrollAccepted(@NonNull View view, @NonNull View view2, int i10) {
        h(view, view2, i10, 0);
    }

    public void onOverScrolled(int i10, int i11, boolean z10, boolean z11) {
        super.scrollTo(i10, i11);
    }

    public boolean onRequestFocusInDescendants(int i10, Rect rect) {
        View view;
        if (i10 == 2) {
            i10 = Constant.UDT_AIRPLAY_ID;
        } else if (i10 == 1) {
            i10 = 33;
        }
        if (rect == null) {
            view = FocusFinder.getInstance().findNextFocus(this, null, i10);
        } else {
            view = FocusFinder.getInstance().findNextFocusFromRect(this, rect, i10);
        }
        if (view != null && !(true ^ t(view, 0, getHeight()))) {
            return view.requestFocus(i10, rect);
        }
        return false;
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.getSuperState());
        this.f1631y = savedState;
        requestLayout();
    }

    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        savedState.f1632a = getScrollY();
        return savedState;
    }

    public void onScrollChanged(int i10, int i11, int i12, int i13) {
        super.onScrollChanged(i10, i11, i12, i13);
        b bVar = this.f1625p0;
        if (bVar != null) {
            androidx.appcompat.app.b bVar2 = (androidx.appcompat.app.b) bVar;
            AlertController.c(this, bVar2.f435a, bVar2.f436b);
        }
    }

    public void onSizeChanged(int i10, int i11, int i12, int i13) {
        super.onSizeChanged(i10, i11, i12, i13);
        View findFocus = findFocus();
        if (findFocus != null && this != findFocus && t(findFocus, 0, i13)) {
            findFocus.getDrawingRect(this.f1608b);
            offsetDescendantRectToMyCoords(findFocus, this.f1608b);
            f(d(this.f1608b));
        }
    }

    public boolean onStartNestedScroll(@NonNull View view, @NonNull View view2, int i10) {
        return o(view, view2, i10, 0);
    }

    public void onStopNestedScroll(@NonNull View view) {
        i(view, 0);
    }

    public boolean onTouchEvent(MotionEvent motionEvent) {
        ViewParent parent;
        if (this.f1616k == null) {
            this.f1616k = VelocityTracker.obtain();
        }
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            this.f1629t = 0;
        }
        MotionEvent obtain = MotionEvent.obtain(motionEvent);
        obtain.offsetLocation(Constant.VOLUME_FLOAT_MIN, (float) this.f1629t);
        if (actionMasked != 0) {
            if (actionMasked == 1) {
                VelocityTracker velocityTracker = this.f1616k;
                velocityTracker.computeCurrentVelocity(1000, (float) this.f1624p);
                int yVelocity = (int) velocityTracker.getYVelocity(this.f1626q);
                if (Math.abs(yVelocity) >= this.f1622o) {
                    int i10 = -yVelocity;
                    float f10 = (float) i10;
                    if (!dispatchNestedPreFling(Constant.VOLUME_FLOAT_MIN, f10)) {
                        dispatchNestedFling(Constant.VOLUME_FLOAT_MIN, f10, true);
                        p(i10);
                    }
                } else if (this.f1609c.springBack(getScrollX(), getScrollY(), 0, 0, 0, getScrollRange())) {
                    WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                    postInvalidateOnAnimation();
                }
                this.f1626q = -1;
                g();
            } else if (actionMasked == 2) {
                int findPointerIndex = motionEvent.findPointerIndex(this.f1626q);
                if (findPointerIndex == -1) {
                    StringBuilder a10 = com.duokan.airkan.server.f.a("Invalid pointerId=");
                    a10.append(this.f1626q);
                    a10.append(" in onTouchEvent");
                    Log.e("NestedScrollView", a10.toString());
                } else {
                    int y10 = (int) motionEvent.getY(findPointerIndex);
                    int i11 = this.f1612f - y10;
                    if (!this.f1615j && Math.abs(i11) > this.f1620n) {
                        ViewParent parent2 = getParent();
                        if (parent2 != null) {
                            parent2.requestDisallowInterceptTouchEvent(true);
                        }
                        this.f1615j = true;
                        i11 = i11 > 0 ? i11 - this.f1620n : i11 + this.f1620n;
                    }
                    int i12 = i11;
                    if (this.f1615j) {
                        if (e(0, i12, this.f1628s, this.f1627r, 0)) {
                            i12 -= this.f1628s[1];
                            this.f1629t += this.f1627r[1];
                        }
                        this.f1612f = y10 - this.f1627r[1];
                        int scrollY = getScrollY();
                        int scrollRange = getScrollRange();
                        int overScrollMode = getOverScrollMode();
                        boolean z10 = overScrollMode == 0 || (overScrollMode == 1 && scrollRange > 0);
                        if (w(0, i12, 0, getScrollY(), 0, scrollRange, 0, 0) && !r(0)) {
                            this.f1616k.clear();
                        }
                        int scrollY2 = getScrollY() - scrollY;
                        int[] iArr = this.f1628s;
                        iArr[1] = 0;
                        this.f1621n0.e(0, scrollY2, 0, i12 - scrollY2, this.f1627r, 0, iArr);
                        int i13 = this.f1612f;
                        int[] iArr2 = this.f1627r;
                        this.f1612f = i13 - iArr2[1];
                        this.f1629t += iArr2[1];
                        if (z10) {
                            int i14 = i12 - this.f1628s[1];
                            k();
                            int i15 = scrollY + i14;
                            if (i15 < 0) {
                                this.f1610d.onPull(((float) i14) / ((float) getHeight()), motionEvent.getX(findPointerIndex) / ((float) getWidth()));
                                if (!this.f1611e.isFinished()) {
                                    this.f1611e.onRelease();
                                }
                            } else if (i15 > scrollRange) {
                                this.f1611e.onPull(((float) i14) / ((float) getHeight()), 1.0f - (motionEvent.getX(findPointerIndex) / ((float) getWidth())));
                                if (!this.f1610d.isFinished()) {
                                    this.f1610d.onRelease();
                                }
                            }
                            EdgeEffect edgeEffect = this.f1610d;
                            if (edgeEffect != null && (!edgeEffect.isFinished() || !this.f1611e.isFinished())) {
                                WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
                                postInvalidateOnAnimation();
                            }
                        }
                    }
                }
            } else if (actionMasked == 3) {
                if (this.f1615j && getChildCount() > 0 && this.f1609c.springBack(getScrollX(), getScrollY(), 0, 0, 0, getScrollRange())) {
                    WeakHashMap<View, m> weakHashMap3 = ViewCompat.f1593a;
                    postInvalidateOnAnimation();
                }
                this.f1626q = -1;
                g();
            } else if (actionMasked == 5) {
                int actionIndex = motionEvent.getActionIndex();
                this.f1612f = (int) motionEvent.getY(actionIndex);
                this.f1626q = motionEvent.getPointerId(actionIndex);
            } else if (actionMasked == 6) {
                v(motionEvent);
                this.f1612f = (int) motionEvent.getY(motionEvent.findPointerIndex(this.f1626q));
            }
        } else if (getChildCount() == 0) {
            return false;
        } else {
            boolean z11 = !this.f1609c.isFinished();
            this.f1615j = z11;
            if (z11 && (parent = getParent()) != null) {
                parent.requestDisallowInterceptTouchEvent(true);
            }
            if (!this.f1609c.isFinished()) {
                a();
            }
            this.f1612f = (int) motionEvent.getY();
            this.f1626q = motionEvent.getPointerId(0);
            C(2, 0);
        }
        VelocityTracker velocityTracker2 = this.f1616k;
        if (velocityTracker2 != null) {
            velocityTracker2.addMovement(obtain);
        }
        obtain.recycle();
        return true;
    }

    public void p(int i10) {
        if (getChildCount() > 0) {
            this.f1609c.fling(getScrollX(), getScrollY(), 0, i10, 0, 0, EventResultPersister.GENERATE_NEW_ID, Reader.READ_DONE, 0, 0);
            y(true);
        }
    }

    public boolean q(int i10) {
        int childCount;
        boolean z10 = i10 == 130;
        int height = getHeight();
        Rect rect = this.f1608b;
        rect.top = 0;
        rect.bottom = height;
        if (z10 && (childCount = getChildCount()) > 0) {
            View childAt = getChildAt(childCount - 1);
            this.f1608b.bottom = getPaddingBottom() + childAt.getBottom() + ((FrameLayout.LayoutParams) childAt.getLayoutParams()).bottomMargin;
            Rect rect2 = this.f1608b;
            rect2.top = rect2.bottom - height;
        }
        Rect rect3 = this.f1608b;
        return z(i10, rect3.top, rect3.bottom);
    }

    public boolean r(int i10) {
        return this.f1621n0.j(i10);
    }

    public void requestChildFocus(View view, View view2) {
        if (!this.f1613g) {
            A(view2);
        } else {
            this.f1614i = view2;
        }
        super.requestChildFocus(view, view2);
    }

    public boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z10) {
        rect.offset(view.getLeft() - view.getScrollX(), view.getTop() - view.getScrollY());
        int d10 = d(rect);
        boolean z11 = d10 != 0;
        if (z11) {
            if (z10) {
                scrollBy(0, d10);
            } else {
                B(0, d10, 250, false);
            }
        }
        return z11;
    }

    public void requestDisallowInterceptTouchEvent(boolean z10) {
        if (z10) {
            x();
        }
        super.requestDisallowInterceptTouchEvent(z10);
    }

    public void requestLayout() {
        this.f1613g = true;
        super.requestLayout();
    }

    public void scrollTo(int i10, int i11) {
        if (getChildCount() > 0) {
            View childAt = getChildAt(0);
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) childAt.getLayoutParams();
            int c10 = c(i10, (getWidth() - getPaddingLeft()) - getPaddingRight(), childAt.getWidth() + layoutParams.leftMargin + layoutParams.rightMargin);
            int c11 = c(i11, (getHeight() - getPaddingTop()) - getPaddingBottom(), childAt.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin);
            if (c10 != getScrollX() || c11 != getScrollY()) {
                super.scrollTo(c10, c11);
            }
        }
    }

    public void setFillViewport(boolean z10) {
        if (z10 != this.f1617l) {
            this.f1617l = z10;
            requestLayout();
        }
    }

    public void setNestedScrollingEnabled(boolean z10) {
        this.f1621n0.l(z10);
    }

    public void setOnScrollChangeListener(@Nullable b bVar) {
        this.f1625p0 = bVar;
    }

    public void setSmoothScrollingEnabled(boolean z10) {
        this.f1618m = z10;
    }

    public boolean shouldDelayChildPressedState() {
        return true;
    }

    public boolean startNestedScroll(int i10) {
        return this.f1621n0.n(i10, 0);
    }

    public void stopNestedScroll() {
        this.f1621n0.p(0);
    }

    public final boolean t(View view, int i10, int i11) {
        view.getDrawingRect(this.f1608b);
        offsetDescendantRectToMyCoords(view, this.f1608b);
        return this.f1608b.bottom + i10 >= getScrollY() && this.f1608b.top - i10 <= getScrollY() + i11;
    }

    public final void u(int i10, int i11, @Nullable int[] iArr) {
        int scrollY = getScrollY();
        scrollBy(0, i10);
        int scrollY2 = getScrollY() - scrollY;
        if (iArr != null) {
            iArr[1] = iArr[1] + scrollY2;
        }
        this.f1621n0.e(0, scrollY2, 0, i10 - scrollY2, null, i11, iArr);
    }

    public final void v(MotionEvent motionEvent) {
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.f1626q) {
            int i10 = actionIndex == 0 ? 1 : 0;
            this.f1612f = (int) motionEvent.getY(i10);
            this.f1626q = motionEvent.getPointerId(i10);
            VelocityTracker velocityTracker = this.f1616k;
            if (velocityTracker != null) {
                velocityTracker.clear();
            }
        }
    }

    public boolean w(int i10, int i11, int i12, int i13, int i14, int i15, int i16, int i17) {
        boolean z10;
        boolean z11;
        int overScrollMode = getOverScrollMode();
        boolean z12 = computeHorizontalScrollRange() > computeHorizontalScrollExtent();
        boolean z13 = computeVerticalScrollRange() > computeVerticalScrollExtent();
        boolean z14 = overScrollMode == 0 || (overScrollMode == 1 && z12);
        boolean z15 = overScrollMode == 0 || (overScrollMode == 1 && z13);
        int i18 = i12 + i10;
        int i19 = !z14 ? 0 : i16;
        int i20 = i13 + i11;
        int i21 = !z15 ? 0 : i17;
        int i22 = -i19;
        int i23 = i19 + i14;
        int i24 = -i21;
        int i25 = i21 + i15;
        if (i18 > i23) {
            i18 = i23;
            z10 = true;
        } else if (i18 < i22) {
            z10 = true;
            i18 = i22;
        } else {
            z10 = false;
        }
        if (i20 > i25) {
            i20 = i25;
            z11 = true;
        } else if (i20 < i24) {
            z11 = true;
            i20 = i24;
        } else {
            z11 = false;
        }
        if (z11 && !r(1)) {
            this.f1609c.springBack(i18, i20, 0, 0, 0, getScrollRange());
        }
        onOverScrolled(i18, i20, z10, z11);
        return z10 || z11;
    }

    public final void x() {
        VelocityTracker velocityTracker = this.f1616k;
        if (velocityTracker != null) {
            velocityTracker.recycle();
            this.f1616k = null;
        }
    }

    public final void y(boolean z10) {
        if (z10) {
            C(2, 1);
        } else {
            this.f1621n0.p(1);
        }
        this.f1630x = getScrollY();
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        postInvalidateOnAnimation();
    }

    public final boolean z(int i10, int i11, int i12) {
        boolean z10;
        int height = getHeight();
        int scrollY = getScrollY();
        int i13 = height + scrollY;
        boolean z11 = i10 == 33;
        ArrayList focusables = getFocusables(2);
        int size = focusables.size();
        View view = null;
        boolean z12 = false;
        for (int i14 = 0; i14 < size; i14++) {
            View view2 = (View) focusables.get(i14);
            int top = view2.getTop();
            int bottom = view2.getBottom();
            if (i11 < bottom && top < i12) {
                boolean z13 = i11 < top && bottom < i12;
                if (view == null) {
                    view = view2;
                    z12 = z13;
                } else {
                    boolean z14 = (z11 && top < view.getTop()) || (!z11 && bottom > view.getBottom());
                    if (z12) {
                        if (z13) {
                            if (!z14) {
                            }
                        }
                    } else if (z13) {
                        view = view2;
                        z12 = true;
                    } else if (!z14) {
                    }
                    view = view2;
                }
            }
        }
        if (view == null) {
            view = this;
        }
        if (i11 < scrollY || i12 > i13) {
            f(z11 ? i11 - scrollY : i12 - i13);
            z10 = true;
        } else {
            z10 = false;
        }
        if (view != findFocus()) {
            view.requestFocus(i10);
        }
        return z10;
    }

    public NestedScrollView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f1608b = new Rect();
        this.f1613g = true;
        this.h = false;
        this.f1614i = null;
        this.f1615j = false;
        this.f1618m = true;
        this.f1626q = -1;
        this.f1627r = new int[2];
        this.f1628s = new int[2];
        this.f1609c = new OverScroller(getContext());
        setFocusable(true);
        setDescendantFocusability(262144);
        setWillNotDraw(false);
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        this.f1620n = viewConfiguration.getScaledTouchSlop();
        this.f1622o = viewConfiguration.getScaledMinimumFlingVelocity();
        this.f1624p = viewConfiguration.getScaledMaximumFlingVelocity();
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, f1606r0, i10, 0);
        setFillViewport(obtainStyledAttributes.getBoolean(0, false));
        obtainStyledAttributes.recycle();
        this.f1619m0 = new i();
        this.f1621n0 = new f(this);
        setNestedScrollingEnabled(true);
        ViewCompat.k(this, f1605q0);
    }

    @Override // android.view.ViewGroup
    public void addView(View view, int i10) {
        if (getChildCount() <= 0) {
            super.addView(view, i10);
            return;
        }
        throw new IllegalStateException("ScrollView can host only one direct child");
    }

    @Override // android.view.ViewGroup
    public void addView(View view, ViewGroup.LayoutParams layoutParams) {
        if (getChildCount() <= 0) {
            super.addView(view, layoutParams);
            return;
        }
        throw new IllegalStateException("ScrollView can host only one direct child");
    }

    @Override // android.view.ViewGroup
    public void addView(View view, int i10, ViewGroup.LayoutParams layoutParams) {
        if (getChildCount() <= 0) {
            super.addView(view, i10, layoutParams);
            return;
        }
        throw new IllegalStateException("ScrollView can host only one direct child");
    }
}
