package aa;

import com.duokan.airkan.server.f;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Vector;
import mb.a;

/* compiled from: ASN1Set */
public abstract class t extends q implements Iterable {

    /* renamed from: a  reason: collision with root package name */
    public Vector f229a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f230b;

    public t() {
        this.f229a = new Vector();
        this.f230b = false;
    }

    public static t n(Object obj) {
        if (obj == null || (obj instanceof t)) {
            return (t) obj;
        }
        if (obj instanceof u) {
            return n(((u) obj).c());
        }
        if (obj instanceof byte[]) {
            try {
                return n(q.j((byte[]) obj));
            } catch (IOException e10) {
                StringBuilder a10 = f.a("failed to construct set from byte[]: ");
                a10.append(e10.getMessage());
                throw new IllegalArgumentException(a10.toString());
            }
        } else {
            if (obj instanceof e) {
                q c10 = ((e) obj).c();
                if (c10 instanceof t) {
                    return (t) c10;
                }
            }
            StringBuilder a11 = f.a("unknown object in getInstance: ");
            a11.append(obj.getClass().getName());
            throw new IllegalArgumentException(a11.toString());
        }
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof t)) {
            return false;
        }
        t tVar = (t) qVar;
        if (size() != tVar.size()) {
            return false;
        }
        Enumeration q10 = q();
        Enumeration q11 = tVar.q();
        while (q10.hasMoreElements()) {
            e o3 = o(q10);
            e o10 = o(q11);
            q c10 = o3.c();
            q c11 = o10.c();
            if (c10 != c11 && !c10.equals(c11)) {
                return false;
            }
        }
        return true;
    }

    @Override // aa.l
    public int hashCode() {
        Enumeration q10 = q();
        int size = size();
        while (q10.hasMoreElements()) {
            size = (size * 17) ^ o(q10).hashCode();
        }
        return size;
    }

    @Override // java.lang.Iterable
    public Iterator<e> iterator() {
        e[] eVarArr = new e[size()];
        for (int i10 = 0; i10 != size(); i10++) {
            eVarArr[i10] = p(i10);
        }
        return new a.C0123a(eVarArr);
    }

    @Override // aa.q
    public boolean k() {
        return true;
    }

    @Override // aa.q
    public q l() {
        if (this.f230b) {
            c1 c1Var = new c1();
            c1Var.f229a = this.f229a;
            return c1Var;
        }
        Vector vector = new Vector();
        for (int i10 = 0; i10 != this.f229a.size(); i10++) {
            vector.addElement(this.f229a.elementAt(i10));
        }
        c1 c1Var2 = new c1();
        c1Var2.f229a = vector;
        c1Var2.r();
        return c1Var2;
    }

    @Override // aa.q
    public q m() {
        n1 n1Var = new n1();
        n1Var.f229a = this.f229a;
        return n1Var;
    }

    public final e o(Enumeration enumeration) {
        e eVar = (e) enumeration.nextElement();
        return eVar == null ? u0.f233a : eVar;
    }

    public e p(int i10) {
        return (e) this.f229a.elementAt(i10);
    }

    public Enumeration q() {
        return this.f229a.elements();
    }

    /* JADX WARNING: Code restructure failed: missing block: B:19:0x005d, code lost:
        if ((r7[r12] & com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP) < (r9[r12] & com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP)) goto L_0x0066;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:22:0x0064, code lost:
        if (r11 == r7.length) goto L_0x0066;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:23:0x0066, code lost:
        r11 = true;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:24:0x0068, code lost:
        r11 = false;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x0069, code lost:
        if (r11 == false) goto L_0x006d;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:26:0x006b, code lost:
        r7 = r9;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:27:0x006d, code lost:
        r4 = r15.f229a.elementAt(r8);
        r6 = r15.f229a;
        r6.setElementAt(r6.elementAt(r10), r8);
        r15.f229a.setElementAt(r4, r10);
        r6 = true;
        r4 = r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:28:0x0083, code lost:
        r8 = r10;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void r() {
        /*
        // Method dump skipped, instructions count: 149
        */
        throw new UnsupportedOperationException("Method not decompiled: aa.t.r():void");
    }

    public int size() {
        return this.f229a.size();
    }

    public String toString() {
        return this.f229a.toString();
    }

    public t(e eVar) {
        Vector vector = new Vector();
        this.f229a = vector;
        this.f230b = false;
        vector.addElement(eVar);
    }

    public t(f fVar, boolean z10) {
        this.f229a = new Vector();
        this.f230b = false;
        for (int i10 = 0; i10 != fVar.b(); i10++) {
            this.f229a.addElement(fVar.a(i10));
        }
        if (z10) {
            r();
        }
    }

    public t(e[] eVarArr, boolean z10) {
        this.f229a = new Vector();
        this.f230b = false;
        for (int i10 = 0; i10 != eVarArr.length; i10++) {
            this.f229a.addElement(eVarArr[i10]);
        }
        if (z10) {
            r();
        }
    }
}
