package androidx.fragment.app;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.h;
import androidx.savedstate.a;
import androidx.savedstate.b;
import androidx.savedstate.c;

/* compiled from: FragmentViewLifecycleOwner */
public class p0 implements c {

    /* renamed from: a  reason: collision with root package name */
    public h f1980a = null;

    /* renamed from: b  reason: collision with root package name */
    public b f1981b = null;

    public void a(@NonNull Lifecycle.Event event) {
        h hVar = this.f1980a;
        hVar.d("handleLifecycleEvent");
        hVar.g(event.getTargetState());
    }

    @Override // androidx.lifecycle.g
    @NonNull
    public Lifecycle getLifecycle() {
        if (this.f1980a == null) {
            this.f1980a = new h(this);
            this.f1981b = new b(this);
        }
        return this.f1980a;
    }

    @Override // androidx.savedstate.c
    @NonNull
    public a getSavedStateRegistry() {
        return this.f1981b.f2505b;
    }
}
