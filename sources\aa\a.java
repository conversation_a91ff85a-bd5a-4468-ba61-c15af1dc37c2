package aa;

import java.io.IOException;

/* compiled from: ASN1ApplicationSpecific */
public abstract class a extends q {

    /* renamed from: a  reason: collision with root package name */
    public final boolean f154a;

    /* renamed from: b  reason: collision with root package name */
    public final int f155b;

    /* renamed from: c  reason: collision with root package name */
    public final byte[] f156c;

    public a(boolean z10, int i10, byte[] bArr) {
        this.f154a = z10;
        this.f155b = i10;
        this.f156c = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof a)) {
            return false;
        }
        a aVar = (a) qVar;
        if (this.f154a == aVar.f154a && this.f155b == aVar.f155b && mb.a.a(this.f156c, aVar.f156c)) {
            return true;
        }
        return false;
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.d(this.f154a ? 96 : 64, this.f155b, this.f156c);
    }

    @Override // aa.l
    public int hashCode() {
        boolean z10 = this.f154a;
        return ((z10 ? 1 : 0) ^ this.f155b) ^ mb.a.d(this.f156c);
    }

    @Override // aa.q
    public int i() throws IOException {
        return v1.a(this.f156c.length) + v1.b(this.f155b) + this.f156c.length;
    }

    @Override // aa.q
    public boolean k() {
        return this.f154a;
    }
}
