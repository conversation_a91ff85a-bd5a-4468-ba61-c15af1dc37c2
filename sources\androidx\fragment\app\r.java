package androidx.fragment.app;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.content.Context;
import android.content.res.Resources;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.AnimationUtils;
import android.view.animation.Transformation;
import androidx.annotation.NonNull;
import androidx.fragment.R$animator;
import androidx.fragment.R$id;
import androidx.fragment.app.Fragment;
import j0.k;

/* compiled from: FragmentAnim */
public class r {
    public static a a(@NonNull Context context, @NonNull Fragment fragment, boolean z10) {
        int i10;
        int i11;
        Fragment.b bVar = fragment.f1751x0;
        boolean z11 = false;
        if (bVar == null) {
            i10 = 0;
        } else {
            i10 = bVar.f1761d;
        }
        int l10 = fragment.l();
        fragment.U(0);
        ViewGroup viewGroup = fragment.f1746t0;
        if (viewGroup != null) {
            int i12 = R$id.visible_removing_fragment_view_tag;
            if (viewGroup.getTag(i12) != null) {
                fragment.f1746t0.setTag(i12, null);
            }
        }
        ViewGroup viewGroup2 = fragment.f1746t0;
        if (viewGroup2 != null && viewGroup2.getLayoutTransition() != null) {
            return null;
        }
        if (l10 == 0 && i10 != 0) {
            if (i10 != 4097) {
                i11 = i10 != 4099 ? i10 != 8194 ? -1 : z10 ? R$animator.fragment_close_enter : R$animator.fragment_close_exit : z10 ? R$animator.fragment_fade_enter : R$animator.fragment_fade_exit;
            } else {
                i11 = z10 ? R$animator.fragment_open_enter : R$animator.fragment_open_exit;
            }
            l10 = i11;
        }
        if (l10 != 0) {
            boolean equals = "anim".equals(context.getResources().getResourceTypeName(l10));
            if (equals) {
                try {
                    Animation loadAnimation = AnimationUtils.loadAnimation(context, l10);
                    if (loadAnimation != null) {
                        return new a(loadAnimation);
                    }
                    z11 = true;
                } catch (Resources.NotFoundException e10) {
                    throw e10;
                } catch (RuntimeException unused) {
                }
            }
            if (!z11) {
                try {
                    Animator loadAnimator = AnimatorInflater.loadAnimator(context, l10);
                    if (loadAnimator != null) {
                        return new a(loadAnimator);
                    }
                } catch (RuntimeException e11) {
                    if (!equals) {
                        Animation loadAnimation2 = AnimationUtils.loadAnimation(context, l10);
                        if (loadAnimation2 != null) {
                            return new a(loadAnimation2);
                        }
                    } else {
                        throw e11;
                    }
                }
            }
        }
        return null;
    }

    /* compiled from: FragmentAnim */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public final Animation f1989a;

        /* renamed from: b  reason: collision with root package name */
        public final Animator f1990b;

        public a(Animation animation) {
            this.f1989a = animation;
            this.f1990b = null;
        }

        public a(Animator animator) {
            this.f1989a = null;
            this.f1990b = animator;
        }
    }

    /* compiled from: FragmentAnim */
    public static class b extends AnimationSet implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final ViewGroup f1991a;

        /* renamed from: b  reason: collision with root package name */
        public final View f1992b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f1993c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f1994d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f1995e = true;

        public b(@NonNull Animation animation, @NonNull ViewGroup viewGroup, @NonNull View view) {
            super(false);
            this.f1991a = viewGroup;
            this.f1992b = view;
            addAnimation(animation);
            viewGroup.post(this);
        }

        public boolean getTransformation(long j10, @NonNull Transformation transformation) {
            this.f1995e = true;
            if (this.f1993c) {
                return !this.f1994d;
            }
            if (!super.getTransformation(j10, transformation)) {
                this.f1993c = true;
                k.a(this.f1991a, this);
            }
            return true;
        }

        public void run() {
            if (this.f1993c || !this.f1995e) {
                this.f1991a.endViewTransition(this.f1992b);
                this.f1994d = true;
                return;
            }
            this.f1995e = false;
            this.f1991a.post(this);
        }

        public boolean getTransformation(long j10, @NonNull Transformation transformation, float f10) {
            this.f1995e = true;
            if (this.f1993c) {
                return !this.f1994d;
            }
            if (!super.getTransformation(j10, transformation, f10)) {
                this.f1993c = true;
                k.a(this.f1991a, this);
            }
            return true;
        }
    }
}
