package a9;

import java.io.File;
import java.util.Random;
import java.util.Stack;
import java.util.StringTokenizer;
import org.apache.tools.ant.BuildException;
import org.apache.tools.ant.launch.Locator;
import z8.a;

public class b {

    /* renamed from: d  reason: collision with root package name */
    public static final b f148d = new b();

    /* renamed from: e  reason: collision with root package name */
    public static final boolean f149e = a.a("netware");

    /* renamed from: f  reason: collision with root package name */
    public static final boolean f150f = a.a("dos");

    /* renamed from: a  reason: collision with root package name */
    public Object f151a = new Object();

    /* renamed from: b  reason: collision with root package name */
    public String f152b = null;

    /* renamed from: c  reason: collision with root package name */
    public String f153c = null;

    static {
        new Random(Runtime.getRuntime().freeMemory() + System.currentTimeMillis());
        a.a("win9x");
        a.a("windows");
    }

    public static boolean b(String str) {
        int indexOf;
        int length = str.length();
        if (length == 0) {
            return false;
        }
        char c10 = File.separatorChar;
        String replace = str.replace('/', c10).replace('\\', c10);
        char charAt = replace.charAt(0);
        boolean z10 = f150f;
        if (!z10 && !f149e) {
            return charAt == c10;
        }
        if (charAt == c10) {
            return z10 && length > 4 && replace.charAt(1) == c10 && (indexOf = replace.indexOf(c10, 2)) > 2 && indexOf + 1 < length;
        }
        int indexOf2 = replace.indexOf(58);
        return (Character.isLetter(charAt) && indexOf2 == 1 && replace.length() > 2 && replace.charAt(2) == c10) || (f149e && indexOf2 > 0);
    }

    public String a(String str) {
        synchronized (this.f151a) {
            if (str.equals(this.f152b)) {
                return this.f153c;
            }
            String fromURI = Locator.fromURI(str);
            if (b(fromURI)) {
                fromURI = c(fromURI).getAbsolutePath();
            }
            this.f152b = str;
            this.f153c = fromURI;
            return fromURI;
        }
    }

    public File c(String str) {
        String str2;
        String str3;
        Stack stack = new Stack();
        char c10 = File.separatorChar;
        String replace = str.replace('/', c10).replace('\\', c10);
        if (b(replace)) {
            int indexOf = replace.indexOf(58);
            if (indexOf > 0 && (f150f || f149e)) {
                int i10 = indexOf + 1;
                String substring = replace.substring(0, i10);
                char[] charArray = replace.toCharArray();
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(substring);
                stringBuffer.append(c10);
                str2 = stringBuffer.toString();
                if (charArray[i10] == c10) {
                    i10++;
                }
                StringBuffer stringBuffer2 = new StringBuffer();
                while (i10 < charArray.length) {
                    if (charArray[i10] != c10 || charArray[i10 - 1] != c10) {
                        stringBuffer2.append(charArray[i10]);
                    }
                    i10++;
                }
                str3 = stringBuffer2.toString();
            } else if (replace.length() <= 1 || replace.charAt(1) != c10) {
                str2 = File.separator;
                str3 = replace.substring(1);
            } else {
                int indexOf2 = replace.indexOf(c10, replace.indexOf(c10, 2) + 1);
                str2 = indexOf2 > 2 ? replace.substring(0, indexOf2 + 1) : replace;
                str3 = replace.substring(str2.length());
            }
            String[] strArr = {str2, str3};
            stack.push(strArr[0]);
            StringTokenizer stringTokenizer = new StringTokenizer(strArr[1], File.separator);
            while (stringTokenizer.hasMoreTokens()) {
                String nextToken = stringTokenizer.nextToken();
                if (!".".equals(nextToken)) {
                    if (!"..".equals(nextToken)) {
                        stack.push(nextToken);
                    } else if (stack.size() < 2) {
                        return new File(str);
                    } else {
                        stack.pop();
                    }
                }
            }
            StringBuffer stringBuffer3 = new StringBuffer();
            for (int i11 = 0; i11 < stack.size(); i11++) {
                if (i11 > 1) {
                    stringBuffer3.append(File.separatorChar);
                }
                stringBuffer3.append(stack.elementAt(i11));
            }
            return new File(stringBuffer3.toString());
        }
        StringBuffer stringBuffer4 = new StringBuffer();
        stringBuffer4.append(replace);
        stringBuffer4.append(" is not an absolute path");
        throw new BuildException(stringBuffer4.toString());
    }
}
