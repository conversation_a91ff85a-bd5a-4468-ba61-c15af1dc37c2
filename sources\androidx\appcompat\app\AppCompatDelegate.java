package androidx.appcompat.app;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.MenuInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.CallSuper;
import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleRes;
import androidx.appcompat.widget.Toolbar;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.util.Iterator;
import p.a;
import u.c;

public abstract class AppCompatDelegate {

    /* renamed from: a  reason: collision with root package name */
    public static final c<WeakReference<AppCompatDelegate>> f367a = new c<>();

    /* renamed from: b  reason: collision with root package name */
    public static final Object f368b = new Object();

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface NightMode {
    }

    public static void t(@NonNull AppCompatDelegate appCompatDelegate) {
        synchronized (f368b) {
            Iterator<WeakReference<AppCompatDelegate>> it = f367a.iterator();
            while (it.hasNext()) {
                AppCompatDelegate appCompatDelegate2 = it.next().get();
                if (appCompatDelegate2 == appCompatDelegate || appCompatDelegate2 == null) {
                    it.remove();
                }
            }
        }
    }

    public abstract void A(@Nullable CharSequence charSequence);

    @Nullable
    public abstract a B(@NonNull a.AbstractC0146a aVar);

    public abstract void c(View view, ViewGroup.LayoutParams layoutParams);

    @NonNull
    @CallSuper
    public Context d(@NonNull Context context) {
        return context;
    }

    @Nullable
    public abstract <T extends View> T e(@IdRes int i10);

    @Nullable
    public abstract a f();

    public int g() {
        return -100;
    }

    public abstract MenuInflater h();

    @Nullable
    public abstract ActionBar i();

    public abstract void j();

    public abstract void k();

    public abstract void l(Configuration configuration);

    public abstract void m(Bundle bundle);

    public abstract void n();

    public abstract void o(Bundle bundle);

    public abstract void p();

    public abstract void q(Bundle bundle);

    public abstract void r();

    public abstract void s();

    public abstract boolean u(int i10);

    public abstract void v(@LayoutRes int i10);

    public abstract void w(View view);

    public abstract void x(View view, ViewGroup.LayoutParams layoutParams);

    public abstract void y(@Nullable Toolbar toolbar);

    public void z(@StyleRes int i10) {
    }
}
