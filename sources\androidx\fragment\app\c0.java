package androidx.fragment.app;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/* compiled from: FragmentStore */
public class c0 {

    /* renamed from: a  reason: collision with root package name */
    public final ArrayList<Fragment> f1862a = new ArrayList<>();

    /* renamed from: b  reason: collision with root package name */
    public final HashMap<String, b0> f1863b = new HashMap<>();

    /* renamed from: c  reason: collision with root package name */
    public z f1864c;

    public void a(@NonNull Fragment fragment) {
        if (!this.f1862a.contains(fragment)) {
            synchronized (this.f1862a) {
                this.f1862a.add(fragment);
            }
            fragment.f1729k = true;
            return;
        }
        throw new IllegalStateException("Fragment already added: " + fragment);
    }

    public void b() {
        this.f1863b.values().removeAll(Collections.singleton(null));
    }

    public boolean c(@NonNull String str) {
        return this.f1863b.get(str) != null;
    }

    @Nullable
    public Fragment d(@NonNull String str) {
        b0 b0Var = this.f1863b.get(str);
        if (b0Var != null) {
            return b0Var.f1852c;
        }
        return null;
    }

    @Nullable
    public Fragment e(@NonNull String str) {
        for (b0 b0Var : this.f1863b.values()) {
            if (b0Var != null) {
                Fragment fragment = b0Var.f1852c;
                if (!str.equals(fragment.f1724e)) {
                    fragment = fragment.f1745t.f1775c.e(str);
                }
                if (fragment != null) {
                    return fragment;
                }
            }
        }
        return null;
    }

    @NonNull
    public List<b0> f() {
        ArrayList arrayList = new ArrayList();
        for (b0 b0Var : this.f1863b.values()) {
            if (b0Var != null) {
                arrayList.add(b0Var);
            }
        }
        return arrayList;
    }

    @NonNull
    public List<Fragment> g() {
        ArrayList arrayList = new ArrayList();
        for (b0 b0Var : this.f1863b.values()) {
            if (b0Var != null) {
                arrayList.add(b0Var.f1852c);
            } else {
                arrayList.add(null);
            }
        }
        return arrayList;
    }

    @Nullable
    public b0 h(@NonNull String str) {
        return this.f1863b.get(str);
    }

    @NonNull
    public List<Fragment> i() {
        ArrayList arrayList;
        if (this.f1862a.isEmpty()) {
            return Collections.emptyList();
        }
        synchronized (this.f1862a) {
            arrayList = new ArrayList(this.f1862a);
        }
        return arrayList;
    }

    public void j(@NonNull b0 b0Var) {
        Fragment fragment = b0Var.f1852c;
        if (!c(fragment.f1724e)) {
            this.f1863b.put(fragment.f1724e, b0Var);
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "Added fragment to active set " + fragment);
            }
        }
    }

    public void k(@NonNull b0 b0Var) {
        Fragment fragment = b0Var.f1852c;
        if (fragment.f1740q0) {
            this.f1864c.c(fragment);
        }
        if (this.f1863b.put(fragment.f1724e, null) != null && FragmentManager.O(2)) {
            Log.v("FragmentManager", "Removed fragment from active set " + fragment);
        }
    }

    public void l(@NonNull Fragment fragment) {
        synchronized (this.f1862a) {
            this.f1862a.remove(fragment);
        }
        fragment.f1729k = false;
    }
}
