package androidx.constraintlayout.solver;

import androidx.constraintlayout.solver.SolverVariable;
import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import com.duokan.airkan.common.Constant;
import java.util.Arrays;
import java.util.Objects;
import v.b;

/* compiled from: LinearSystem */
public class c {

    /* renamed from: o  reason: collision with root package name */
    public static int f1262o = 1000;

    /* renamed from: a  reason: collision with root package name */
    public int f1263a;

    /* renamed from: b  reason: collision with root package name */
    public a f1264b;

    /* renamed from: c  reason: collision with root package name */
    public int f1265c;

    /* renamed from: d  reason: collision with root package name */
    public int f1266d;

    /* renamed from: e  reason: collision with root package name */
    public b[] f1267e;

    /* renamed from: f  reason: collision with root package name */
    public boolean f1268f;

    /* renamed from: g  reason: collision with root package name */
    public boolean[] f1269g;
    public int h;

    /* renamed from: i  reason: collision with root package name */
    public int f1270i;

    /* renamed from: j  reason: collision with root package name */
    public int f1271j;

    /* renamed from: k  reason: collision with root package name */
    public final v.a f1272k;

    /* renamed from: l  reason: collision with root package name */
    public SolverVariable[] f1273l;

    /* renamed from: m  reason: collision with root package name */
    public int f1274m;

    /* renamed from: n  reason: collision with root package name */
    public final a f1275n;

    /* compiled from: LinearSystem */
    public interface a {
        void a(SolverVariable solverVariable);
    }

    public c() {
        this.f1263a = 0;
        this.f1265c = 32;
        this.f1266d = 32;
        this.f1267e = null;
        this.f1268f = false;
        this.f1269g = new boolean[32];
        this.h = 1;
        this.f1270i = 0;
        this.f1271j = 32;
        this.f1273l = new SolverVariable[f1262o];
        this.f1274m = 0;
        this.f1267e = new b[32];
        s();
        v.a aVar = new v.a();
        this.f1272k = aVar;
        this.f1264b = new b(aVar);
        this.f1275n = new b(aVar);
    }

    public final SolverVariable a(SolverVariable.Type type, String str) {
        SolverVariable solverVariable = (SolverVariable) this.f1272k.f10541b.a();
        if (solverVariable == null) {
            solverVariable = new SolverVariable(type);
            solverVariable.f1245f = type;
        } else {
            solverVariable.c();
            solverVariable.f1245f = type;
        }
        int i10 = this.f1274m;
        int i11 = f1262o;
        if (i10 >= i11) {
            int i12 = i11 * 2;
            f1262o = i12;
            this.f1273l = (SolverVariable[]) Arrays.copyOf(this.f1273l, i12);
        }
        SolverVariable[] solverVariableArr = this.f1273l;
        int i13 = this.f1274m;
        this.f1274m = i13 + 1;
        solverVariableArr[i13] = solverVariable;
        return solverVariable;
    }

    public void b(SolverVariable solverVariable, SolverVariable solverVariable2, int i10, float f10, SolverVariable solverVariable3, SolverVariable solverVariable4, int i11, int i12) {
        b m10 = m();
        if (solverVariable2 == solverVariable3) {
            m10.f1260c.h(solverVariable, 1.0f);
            m10.f1260c.h(solverVariable4, 1.0f);
            m10.f1260c.h(solverVariable2, -2.0f);
        } else if (f10 == 0.5f) {
            m10.f1260c.h(solverVariable, 1.0f);
            m10.f1260c.h(solverVariable2, -1.0f);
            m10.f1260c.h(solverVariable3, -1.0f);
            m10.f1260c.h(solverVariable4, 1.0f);
            if (i10 > 0 || i11 > 0) {
                m10.f1259b = (float) ((-i10) + i11);
            }
        } else if (f10 <= Constant.VOLUME_FLOAT_MIN) {
            m10.f1260c.h(solverVariable, -1.0f);
            m10.f1260c.h(solverVariable2, 1.0f);
            m10.f1259b = (float) i10;
        } else if (f10 >= 1.0f) {
            m10.f1260c.h(solverVariable3, -1.0f);
            m10.f1260c.h(solverVariable4, 1.0f);
            m10.f1259b = (float) i11;
        } else {
            float f11 = 1.0f - f10;
            m10.f1260c.h(solverVariable, f11 * 1.0f);
            m10.f1260c.h(solverVariable2, f11 * -1.0f);
            m10.f1260c.h(solverVariable3, -1.0f * f10);
            m10.f1260c.h(solverVariable4, 1.0f * f10);
            if (i10 > 0 || i11 > 0) {
                m10.f1259b = (((float) i11) * f10) + (((float) (-i10)) * f11);
            }
        }
        if (i12 != 6) {
            m10.b(this, i12);
        }
        c(m10);
    }

    /* JADX WARNING: Removed duplicated region for block: B:114:0x00e6 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x009f  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void c(androidx.constraintlayout.solver.b r19) {
        /*
        // Method dump skipped, instructions count: 413
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.c.c(androidx.constraintlayout.solver.b):void");
    }

    public b d(SolverVariable solverVariable, SolverVariable solverVariable2, int i10, int i11) {
        b m10 = m();
        boolean z10 = false;
        if (i10 != 0) {
            if (i10 < 0) {
                i10 *= -1;
                z10 = true;
            }
            m10.f1259b = (float) i10;
        }
        if (!z10) {
            m10.f1260c.h(solverVariable, -1.0f);
            m10.f1260c.h(solverVariable2, 1.0f);
        } else {
            m10.f1260c.h(solverVariable, 1.0f);
            m10.f1260c.h(solverVariable2, -1.0f);
        }
        if (i11 != 6) {
            m10.b(this, i11);
        }
        c(m10);
        return m10;
    }

    public void e(SolverVariable solverVariable, int i10) {
        int i11 = solverVariable.f1241b;
        if (i11 != -1) {
            b bVar = this.f1267e[i11];
            if (bVar.f1261d) {
                bVar.f1259b = (float) i10;
            } else if (bVar.f1260c.f1249a == 0) {
                bVar.f1261d = true;
                bVar.f1259b = (float) i10;
            } else {
                b m10 = m();
                if (i10 < 0) {
                    m10.f1259b = (float) (i10 * -1);
                    m10.f1260c.h(solverVariable, 1.0f);
                } else {
                    m10.f1259b = (float) i10;
                    m10.f1260c.h(solverVariable, -1.0f);
                }
                c(m10);
            }
        } else {
            b m11 = m();
            m11.f1258a = solverVariable;
            float f10 = (float) i10;
            solverVariable.f1243d = f10;
            m11.f1259b = f10;
            m11.f1261d = true;
            c(m11);
        }
    }

    public void f(SolverVariable solverVariable, SolverVariable solverVariable2, int i10, int i11) {
        b m10 = m();
        SolverVariable n10 = n();
        n10.f1242c = 0;
        m10.d(solverVariable, solverVariable2, n10, i10);
        if (i11 != 6) {
            m10.f1260c.h(k(i11, null), (float) ((int) (m10.f1260c.c(n10) * -1.0f)));
        }
        c(m10);
    }

    public void g(SolverVariable solverVariable, SolverVariable solverVariable2, int i10, int i11) {
        b m10 = m();
        SolverVariable n10 = n();
        n10.f1242c = 0;
        m10.e(solverVariable, solverVariable2, n10, i10);
        if (i11 != 6) {
            m10.f1260c.h(k(i11, null), (float) ((int) (m10.f1260c.c(n10) * -1.0f)));
        }
        c(m10);
    }

    public void h(SolverVariable solverVariable, SolverVariable solverVariable2, SolverVariable solverVariable3, SolverVariable solverVariable4, float f10, int i10) {
        b m10 = m();
        m10.c(solverVariable, solverVariable2, solverVariable3, solverVariable4, f10);
        if (i10 != 6) {
            m10.b(this, i10);
        }
        c(m10);
    }

    public final void i(b bVar) {
        b[] bVarArr = this.f1267e;
        int i10 = this.f1270i;
        if (bVarArr[i10] != null) {
            this.f1272k.f10540a.b(bVarArr[i10]);
        }
        b[] bVarArr2 = this.f1267e;
        int i11 = this.f1270i;
        bVarArr2[i11] = bVar;
        SolverVariable solverVariable = bVar.f1258a;
        solverVariable.f1241b = i11;
        this.f1270i = i11 + 1;
        solverVariable.d(bVar);
    }

    public final void j() {
        for (int i10 = 0; i10 < this.f1270i; i10++) {
            b bVar = this.f1267e[i10];
            bVar.f1258a.f1243d = bVar.f1259b;
        }
    }

    public SolverVariable k(int i10, String str) {
        if (this.h + 1 >= this.f1266d) {
            p();
        }
        SolverVariable a10 = a(SolverVariable.Type.ERROR, str);
        int i11 = this.f1263a + 1;
        this.f1263a = i11;
        this.h++;
        a10.f1240a = i11;
        a10.f1242c = i10;
        this.f1272k.f10542c[i11] = a10;
        this.f1264b.a(a10);
        return a10;
    }

    public SolverVariable l(Object obj) {
        SolverVariable solverVariable = null;
        if (obj == null) {
            return null;
        }
        if (this.h + 1 >= this.f1266d) {
            p();
        }
        if (obj instanceof ConstraintAnchor) {
            ConstraintAnchor constraintAnchor = (ConstraintAnchor) obj;
            solverVariable = constraintAnchor.f1283i;
            if (solverVariable == null) {
                constraintAnchor.e();
                solverVariable = constraintAnchor.f1283i;
            }
            int i10 = solverVariable.f1240a;
            if (i10 == -1 || i10 > this.f1263a || this.f1272k.f10542c[i10] == null) {
                if (i10 != -1) {
                    solverVariable.c();
                }
                int i11 = this.f1263a + 1;
                this.f1263a = i11;
                this.h++;
                solverVariable.f1240a = i11;
                solverVariable.f1245f = SolverVariable.Type.UNRESTRICTED;
                this.f1272k.f10542c[i11] = solverVariable;
            }
        }
        return solverVariable;
    }

    public b m() {
        b bVar = (b) this.f1272k.f10540a.a();
        if (bVar == null) {
            bVar = new b(this.f1272k);
        } else {
            bVar.f1258a = null;
            bVar.f1260c.b();
            bVar.f1259b = Constant.VOLUME_FLOAT_MIN;
            bVar.f1261d = false;
        }
        SolverVariable.f1239j++;
        return bVar;
    }

    public SolverVariable n() {
        if (this.h + 1 >= this.f1266d) {
            p();
        }
        SolverVariable a10 = a(SolverVariable.Type.SLACK, null);
        int i10 = this.f1263a + 1;
        this.f1263a = i10;
        this.h++;
        a10.f1240a = i10;
        this.f1272k.f10542c[i10] = a10;
        return a10;
    }

    public int o(Object obj) {
        SolverVariable solverVariable = ((ConstraintAnchor) obj).f1283i;
        if (solverVariable != null) {
            return (int) (solverVariable.f1243d + 0.5f);
        }
        return 0;
    }

    public final void p() {
        int i10 = this.f1265c * 2;
        this.f1265c = i10;
        this.f1267e = (b[]) Arrays.copyOf(this.f1267e, i10);
        v.a aVar = this.f1272k;
        aVar.f10542c = (SolverVariable[]) Arrays.copyOf(aVar.f10542c, this.f1265c);
        int i11 = this.f1265c;
        this.f1269g = new boolean[i11];
        this.f1266d = i11;
        this.f1271j = i11;
    }

    public void q(a aVar) throws Exception {
        float f10;
        boolean z10;
        u((b) aVar);
        int i10 = 0;
        while (true) {
            int i11 = this.f1270i;
            f10 = Constant.VOLUME_FLOAT_MIN;
            if (i10 >= i11) {
                z10 = false;
                break;
            }
            b[] bVarArr = this.f1267e;
            if (bVarArr[i10].f1258a.f1245f != SolverVariable.Type.UNRESTRICTED && bVarArr[i10].f1259b < Constant.VOLUME_FLOAT_MIN) {
                z10 = true;
                break;
            }
            i10++;
        }
        if (z10) {
            boolean z11 = false;
            int i12 = 0;
            while (!z11) {
                i12++;
                float f11 = Float.MAX_VALUE;
                int i13 = -1;
                int i14 = -1;
                int i15 = 0;
                int i16 = 0;
                while (i15 < this.f1270i) {
                    b bVar = this.f1267e[i15];
                    if (bVar.f1258a.f1245f != SolverVariable.Type.UNRESTRICTED && !bVar.f1261d && bVar.f1259b < f10) {
                        int i17 = 1;
                        while (i17 < this.h) {
                            SolverVariable solverVariable = this.f1272k.f10542c[i17];
                            float c10 = bVar.f1260c.c(solverVariable);
                            if (c10 > f10) {
                                for (int i18 = 0; i18 < 7; i18++) {
                                    float f12 = solverVariable.f1244e[i18] / c10;
                                    if ((f12 < f11 && i18 == i16) || i18 > i16) {
                                        i16 = i18;
                                        f11 = f12;
                                        i13 = i15;
                                        i14 = i17;
                                    }
                                }
                            }
                            i17++;
                            f10 = Constant.VOLUME_FLOAT_MIN;
                        }
                    }
                    i15++;
                    f10 = Constant.VOLUME_FLOAT_MIN;
                }
                if (i13 != -1) {
                    b bVar2 = this.f1267e[i13];
                    bVar2.f1258a.f1241b = -1;
                    bVar2.g(this.f1272k.f10542c[i14]);
                    SolverVariable solverVariable2 = bVar2.f1258a;
                    solverVariable2.f1241b = i13;
                    solverVariable2.d(bVar2);
                } else {
                    z11 = true;
                }
                if (i12 > this.h / 2) {
                    z11 = true;
                }
                f10 = Constant.VOLUME_FLOAT_MIN;
            }
        }
        r(aVar);
        j();
    }

    public final int r(a aVar) {
        boolean z10;
        for (int i10 = 0; i10 < this.h; i10++) {
            this.f1269g[i10] = false;
        }
        boolean z11 = false;
        int i11 = 0;
        while (!z11) {
            i11++;
            if (i11 >= this.h * 2) {
                return i11;
            }
            b bVar = (b) aVar;
            SolverVariable solverVariable = bVar.f1258a;
            if (solverVariable != null) {
                this.f1269g[solverVariable.f1240a] = true;
            }
            SolverVariable d10 = bVar.f1260c.d(this.f1269g, null);
            if (d10 != null) {
                boolean[] zArr = this.f1269g;
                int i12 = d10.f1240a;
                if (zArr[i12]) {
                    return i11;
                }
                zArr[i12] = true;
            }
            if (d10 != null) {
                float f10 = Float.MAX_VALUE;
                int i13 = -1;
                for (int i14 = 0; i14 < this.f1270i; i14++) {
                    b bVar2 = this.f1267e[i14];
                    if (bVar2.f1258a.f1245f != SolverVariable.Type.UNRESTRICTED && !bVar2.f1261d) {
                        a aVar2 = bVar2.f1260c;
                        int i15 = aVar2.h;
                        if (i15 != -1) {
                            int i16 = 0;
                            while (true) {
                                if (i15 == -1 || i16 >= aVar2.f1249a) {
                                    break;
                                } else if (aVar2.f1253e[i15] == d10.f1240a) {
                                    z10 = true;
                                    break;
                                } else {
                                    i15 = aVar2.f1254f[i15];
                                    i16++;
                                }
                            }
                        }
                        z10 = false;
                        if (z10) {
                            float c10 = bVar2.f1260c.c(d10);
                            if (c10 < Constant.VOLUME_FLOAT_MIN) {
                                float f11 = (-bVar2.f1259b) / c10;
                                if (f11 < f10) {
                                    i13 = i14;
                                    f10 = f11;
                                }
                            }
                        }
                    }
                }
                if (i13 > -1) {
                    b bVar3 = this.f1267e[i13];
                    bVar3.f1258a.f1241b = -1;
                    bVar3.g(d10);
                    SolverVariable solverVariable2 = bVar3.f1258a;
                    solverVariable2.f1241b = i13;
                    solverVariable2.d(bVar3);
                }
            }
            z11 = true;
        }
        return i11;
    }

    public final void s() {
        int i10 = 0;
        while (true) {
            b[] bVarArr = this.f1267e;
            if (i10 < bVarArr.length) {
                b bVar = bVarArr[i10];
                if (bVar != null) {
                    this.f1272k.f10540a.b(bVar);
                }
                this.f1267e[i10] = null;
                i10++;
            } else {
                return;
            }
        }
    }

    public void t() {
        v.a aVar;
        int i10 = 0;
        while (true) {
            aVar = this.f1272k;
            SolverVariable[] solverVariableArr = aVar.f10542c;
            if (i10 >= solverVariableArr.length) {
                break;
            }
            SolverVariable solverVariable = solverVariableArr[i10];
            if (solverVariable != null) {
                solverVariable.c();
            }
            i10++;
        }
        v.c cVar = aVar.f10541b;
        SolverVariable[] solverVariableArr2 = this.f1273l;
        int i11 = this.f1274m;
        Objects.requireNonNull(cVar);
        if (i11 > solverVariableArr2.length) {
            i11 = solverVariableArr2.length;
        }
        for (int i12 = 0; i12 < i11; i12++) {
            SolverVariable solverVariable2 = solverVariableArr2[i12];
            int i13 = cVar.f10544b;
            Object[] objArr = cVar.f10543a;
            if (i13 < objArr.length) {
                objArr[i13] = solverVariable2;
                cVar.f10544b = i13 + 1;
            }
        }
        this.f1274m = 0;
        Arrays.fill(this.f1272k.f10542c, (Object) null);
        this.f1263a = 0;
        b bVar = (b) this.f1264b;
        bVar.f1260c.b();
        bVar.f1258a = null;
        bVar.f1259b = Constant.VOLUME_FLOAT_MIN;
        this.h = 1;
        for (int i14 = 0; i14 < this.f1270i; i14++) {
            Objects.requireNonNull(this.f1267e[i14]);
        }
        s();
        this.f1270i = 0;
    }

    public final void u(b bVar) {
        if (this.f1270i > 0) {
            a aVar = bVar.f1260c;
            b[] bVarArr = this.f1267e;
            int i10 = aVar.h;
            loop0:
            while (true) {
                int i11 = 0;
                while (i10 != -1 && i11 < aVar.f1249a) {
                    SolverVariable solverVariable = aVar.f1251c.f10542c[aVar.f1253e[i10]];
                    if (solverVariable.f1241b != -1) {
                        float f10 = aVar.f1255g[i10];
                        aVar.i(solverVariable, true);
                        b bVar2 = bVarArr[solverVariable.f1241b];
                        if (!bVar2.f1261d) {
                            a aVar2 = bVar2.f1260c;
                            int i12 = aVar2.h;
                            int i13 = 0;
                            while (i12 != -1 && i13 < aVar2.f1249a) {
                                aVar.a(aVar.f1251c.f10542c[aVar2.f1253e[i12]], aVar2.f1255g[i12] * f10, true);
                                i12 = aVar2.f1254f[i12];
                                i13++;
                            }
                        }
                        bVar.f1259b = (bVar2.f1259b * f10) + bVar.f1259b;
                        bVar2.f1258a.b(bVar);
                        i10 = aVar.h;
                    } else {
                        i10 = aVar.f1254f[i10];
                        i11++;
                    }
                }
            }
            if (bVar.f1260c.f1249a == 0) {
                bVar.f1261d = true;
            }
        }
    }
}
