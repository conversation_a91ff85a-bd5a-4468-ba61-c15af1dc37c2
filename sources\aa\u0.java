package aa;

import java.io.IOException;

/* compiled from: DERNull */
public class u0 extends k {

    /* renamed from: a  reason: collision with root package name */
    public static final u0 f233a = new u0();

    /* renamed from: b  reason: collision with root package name */
    public static final byte[] f234b = new byte[0];

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(5, f234b);
    }

    @Override // aa.q
    public int i() {
        return 2;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
