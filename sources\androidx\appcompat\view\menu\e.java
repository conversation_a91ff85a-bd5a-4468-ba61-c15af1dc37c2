package androidx.appcompat.view.menu;

import android.content.DialogInterface;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import androidx.annotation.NonNull;
import androidx.appcompat.app.d;
import androidx.appcompat.view.menu.b;
import androidx.appcompat.view.menu.h;

/* compiled from: MenuDialogHelper */
public class e implements DialogInterface.OnKeyListener, DialogInterface.OnClickListener, DialogInterface.OnDismissListener, h.a {

    /* renamed from: a  reason: collision with root package name */
    public d f627a;

    /* renamed from: b  reason: collision with root package name */
    public d f628b;

    /* renamed from: c  reason: collision with root package name */
    public b f629c;

    public e(d dVar) {
        this.f627a = dVar;
    }

    @Override // androidx.appcompat.view.menu.h.a
    public void a(@NonNull d dVar, boolean z10) {
        d dVar2;
        if ((z10 || dVar == this.f627a) && (dVar2 = this.f628b) != null) {
            dVar2.dismiss();
        }
    }

    @Override // androidx.appcompat.view.menu.h.a
    public boolean b(@NonNull d dVar) {
        return false;
    }

    public void onClick(DialogInterface dialogInterface, int i10) {
        this.f627a.q(((b.a) this.f629c.j()).getItem(i10), 0);
    }

    public void onDismiss(DialogInterface dialogInterface) {
        b bVar = this.f629c;
        d dVar = this.f627a;
        h.a aVar = bVar.f593f;
        if (aVar != null) {
            aVar.a(dVar, true);
        }
    }

    public boolean onKey(DialogInterface dialogInterface, int i10, KeyEvent keyEvent) {
        Window window;
        View decorView;
        KeyEvent.DispatcherState keyDispatcherState;
        View decorView2;
        KeyEvent.DispatcherState keyDispatcherState2;
        if (i10 == 82 || i10 == 4) {
            if (keyEvent.getAction() == 0 && keyEvent.getRepeatCount() == 0) {
                Window window2 = this.f628b.getWindow();
                if (!(window2 == null || (decorView2 = window2.getDecorView()) == null || (keyDispatcherState2 = decorView2.getKeyDispatcherState()) == null)) {
                    keyDispatcherState2.startTracking(keyEvent, this);
                    return true;
                }
            } else if (keyEvent.getAction() == 1 && !keyEvent.isCanceled() && (window = this.f628b.getWindow()) != null && (decorView = window.getDecorView()) != null && (keyDispatcherState = decorView.getKeyDispatcherState()) != null && keyDispatcherState.isTracking(keyEvent)) {
                this.f627a.c(true);
                dialogInterface.dismiss();
                return true;
            }
        }
        return this.f627a.performShortcut(i10, keyEvent, 0);
    }
}
