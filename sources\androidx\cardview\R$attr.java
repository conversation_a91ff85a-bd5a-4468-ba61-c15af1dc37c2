package androidx.cardview;

public final class R$attr {
    public static final int cardBackgroundColor = 2130968770;
    public static final int cardCornerRadius = 2130968771;
    public static final int cardElevation = 2130968772;
    public static final int cardMaxElevation = 2130968774;
    public static final int cardPreventCornerOverlap = 2130968775;
    public static final int cardUseCompatPadding = 2130968776;
    public static final int cardViewStyle = 2130968777;
    public static final int contentPadding = 2130968890;
    public static final int contentPaddingBottom = 2130968891;
    public static final int contentPaddingLeft = 2130968892;
    public static final int contentPaddingRight = 2130968893;
    public static final int contentPaddingTop = 2130968894;

    private R$attr() {
    }
}
