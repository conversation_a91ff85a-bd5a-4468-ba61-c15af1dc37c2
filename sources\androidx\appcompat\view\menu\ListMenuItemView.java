package androidx.appcompat.view.menu;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$attr;
import androidx.appcompat.R$id;
import androidx.appcompat.R$layout;
import androidx.appcompat.R$string;
import androidx.appcompat.R$styleable;
import androidx.appcompat.view.menu.i;
import androidx.appcompat.widget.m0;
import androidx.core.view.ViewCompat;
import com.xiaomi.mitv.socialtv.common.net.media.VideoConstants;
import j0.m;
import java.util.Objects;
import java.util.WeakHashMap;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class ListMenuItemView extends LinearLayout implements i.a, AbsListView.SelectionBoundsAdjuster {

    /* renamed from: a  reason: collision with root package name */
    public f f564a;

    /* renamed from: b  reason: collision with root package name */
    public ImageView f565b;

    /* renamed from: c  reason: collision with root package name */
    public RadioButton f566c;

    /* renamed from: d  reason: collision with root package name */
    public TextView f567d;

    /* renamed from: e  reason: collision with root package name */
    public CheckBox f568e;

    /* renamed from: f  reason: collision with root package name */
    public TextView f569f;

    /* renamed from: g  reason: collision with root package name */
    public ImageView f570g;
    public ImageView h;

    /* renamed from: i  reason: collision with root package name */
    public LinearLayout f571i;

    /* renamed from: j  reason: collision with root package name */
    public Drawable f572j;

    /* renamed from: k  reason: collision with root package name */
    public int f573k;

    /* renamed from: l  reason: collision with root package name */
    public Context f574l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f575m;

    /* renamed from: n  reason: collision with root package name */
    public Drawable f576n;

    /* renamed from: o  reason: collision with root package name */
    public boolean f577o;

    /* renamed from: p  reason: collision with root package name */
    public LayoutInflater f578p;

    /* renamed from: q  reason: collision with root package name */
    public boolean f579q;

    public ListMenuItemView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.listMenuViewStyle);
    }

    private LayoutInflater getInflater() {
        if (this.f578p == null) {
            this.f578p = LayoutInflater.from(getContext());
        }
        return this.f578p;
    }

    private void setSubMenuArrowVisible(boolean z10) {
        ImageView imageView = this.f570g;
        if (imageView != null) {
            imageView.setVisibility(z10 ? 0 : 8);
        }
    }

    public final void a() {
        CheckBox checkBox = (CheckBox) getInflater().inflate(R$layout.abc_list_menu_item_checkbox, (ViewGroup) this, false);
        this.f568e = checkBox;
        LinearLayout linearLayout = this.f571i;
        if (linearLayout != null) {
            linearLayout.addView(checkBox, -1);
        } else {
            addView(checkBox, -1);
        }
    }

    public void adjustListItemSelectionBounds(Rect rect) {
        ImageView imageView = this.h;
        if (imageView != null && imageView.getVisibility() == 0) {
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) this.h.getLayoutParams();
            rect.top = this.h.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin + rect.top;
        }
    }

    public final void b() {
        RadioButton radioButton = (RadioButton) getInflater().inflate(R$layout.abc_list_menu_item_radio, (ViewGroup) this, false);
        this.f566c = radioButton;
        LinearLayout linearLayout = this.f571i;
        if (linearLayout != null) {
            linearLayout.addView(radioButton, -1);
        } else {
            addView(radioButton, -1);
        }
    }

    @Override // androidx.appcompat.view.menu.i.a
    public void d(f fVar, int i10) {
        String str;
        this.f564a = fVar;
        int i11 = 0;
        setVisibility(fVar.isVisible() ? 0 : 8);
        setTitle(fVar.f634e);
        setCheckable(fVar.isCheckable());
        boolean n10 = fVar.n();
        fVar.e();
        if (!n10 || !this.f564a.n()) {
            i11 = 8;
        }
        if (i11 == 0) {
            TextView textView = this.f569f;
            f fVar2 = this.f564a;
            char e10 = fVar2.e();
            if (e10 == 0) {
                str = "";
            } else {
                Resources resources = fVar2.f642n.f604a.getResources();
                StringBuilder sb = new StringBuilder();
                if (ViewConfiguration.get(fVar2.f642n.f604a).hasPermanentMenuKey()) {
                    sb.append(resources.getString(R$string.abc_prepend_shortcut_label));
                }
                int i12 = fVar2.f642n.n() ? fVar2.f639k : fVar2.f637i;
                f.c(sb, i12, VideoConstants.SEARCH_MASK_EDUCATION, resources.getString(R$string.abc_menu_meta_shortcut_label));
                f.c(sb, i12, 4096, resources.getString(R$string.abc_menu_ctrl_shortcut_label));
                f.c(sb, i12, 2, resources.getString(R$string.abc_menu_alt_shortcut_label));
                f.c(sb, i12, 1, resources.getString(R$string.abc_menu_shift_shortcut_label));
                f.c(sb, i12, 4, resources.getString(R$string.abc_menu_sym_shortcut_label));
                f.c(sb, i12, 8, resources.getString(R$string.abc_menu_function_shortcut_label));
                if (e10 == '\b') {
                    sb.append(resources.getString(R$string.abc_menu_delete_shortcut_label));
                } else if (e10 == '\n') {
                    sb.append(resources.getString(R$string.abc_menu_enter_shortcut_label));
                } else if (e10 != ' ') {
                    sb.append(e10);
                } else {
                    sb.append(resources.getString(R$string.abc_menu_space_shortcut_label));
                }
                str = sb.toString();
            }
            textView.setText(str);
        }
        if (this.f569f.getVisibility() != i11) {
            this.f569f.setVisibility(i11);
        }
        setIcon(fVar.getIcon());
        setEnabled(fVar.isEnabled());
        setSubMenuArrowVisible(fVar.hasSubMenu());
        setContentDescription(fVar.f645q);
    }

    @Override // androidx.appcompat.view.menu.i.a
    public f getItemData() {
        return this.f564a;
    }

    public void onFinishInflate() {
        super.onFinishInflate();
        Drawable drawable = this.f572j;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        setBackground(drawable);
        TextView textView = (TextView) findViewById(R$id.title);
        this.f567d = textView;
        int i10 = this.f573k;
        if (i10 != -1) {
            textView.setTextAppearance(this.f574l, i10);
        }
        this.f569f = (TextView) findViewById(R$id.shortcut);
        ImageView imageView = (ImageView) findViewById(R$id.submenuarrow);
        this.f570g = imageView;
        if (imageView != null) {
            imageView.setImageDrawable(this.f576n);
        }
        this.h = (ImageView) findViewById(R$id.group_divider);
        this.f571i = (LinearLayout) findViewById(R$id.content);
    }

    public void onMeasure(int i10, int i11) {
        if (this.f565b != null && this.f575m) {
            ViewGroup.LayoutParams layoutParams = getLayoutParams();
            LinearLayout.LayoutParams layoutParams2 = (LinearLayout.LayoutParams) this.f565b.getLayoutParams();
            int i12 = layoutParams.height;
            if (i12 > 0 && layoutParams2.width <= 0) {
                layoutParams2.width = i12;
            }
        }
        super.onMeasure(i10, i11);
    }

    public void setCheckable(boolean z10) {
        CompoundButton compoundButton;
        CompoundButton compoundButton2;
        if (z10 || this.f566c != null || this.f568e != null) {
            if (this.f564a.h()) {
                if (this.f566c == null) {
                    b();
                }
                compoundButton2 = this.f566c;
                compoundButton = this.f568e;
            } else {
                if (this.f568e == null) {
                    a();
                }
                compoundButton2 = this.f568e;
                compoundButton = this.f566c;
            }
            if (z10) {
                compoundButton2.setChecked(this.f564a.isChecked());
                if (compoundButton2.getVisibility() != 0) {
                    compoundButton2.setVisibility(0);
                }
                if (compoundButton != null && compoundButton.getVisibility() != 8) {
                    compoundButton.setVisibility(8);
                    return;
                }
                return;
            }
            CheckBox checkBox = this.f568e;
            if (checkBox != null) {
                checkBox.setVisibility(8);
            }
            RadioButton radioButton = this.f566c;
            if (radioButton != null) {
                radioButton.setVisibility(8);
            }
        }
    }

    public void setChecked(boolean z10) {
        CompoundButton compoundButton;
        if (this.f564a.h()) {
            if (this.f566c == null) {
                b();
            }
            compoundButton = this.f566c;
        } else {
            if (this.f568e == null) {
                a();
            }
            compoundButton = this.f568e;
        }
        compoundButton.setChecked(z10);
    }

    public void setForceShowIcon(boolean z10) {
        this.f579q = z10;
        this.f575m = z10;
    }

    public void setGroupDividerEnabled(boolean z10) {
        ImageView imageView = this.h;
        if (imageView != null) {
            imageView.setVisibility((this.f577o || !z10) ? 8 : 0);
        }
    }

    public void setIcon(Drawable drawable) {
        Objects.requireNonNull(this.f564a.f642n);
        boolean z10 = this.f579q;
        if (z10 || this.f575m) {
            ImageView imageView = this.f565b;
            if (imageView != null || drawable != null || this.f575m) {
                if (imageView == null) {
                    ImageView imageView2 = (ImageView) getInflater().inflate(R$layout.abc_list_menu_item_icon, (ViewGroup) this, false);
                    this.f565b = imageView2;
                    LinearLayout linearLayout = this.f571i;
                    if (linearLayout != null) {
                        linearLayout.addView(imageView2, 0);
                    } else {
                        addView(imageView2, 0);
                    }
                }
                if (drawable != null || this.f575m) {
                    ImageView imageView3 = this.f565b;
                    if (!z10) {
                        drawable = null;
                    }
                    imageView3.setImageDrawable(drawable);
                    if (this.f565b.getVisibility() != 0) {
                        this.f565b.setVisibility(0);
                        return;
                    }
                    return;
                }
                this.f565b.setVisibility(8);
            }
        }
    }

    public void setTitle(CharSequence charSequence) {
        if (charSequence != null) {
            this.f567d.setText(charSequence);
            if (this.f567d.getVisibility() != 0) {
                this.f567d.setVisibility(0);
            }
        } else if (this.f567d.getVisibility() != 8) {
            this.f567d.setVisibility(8);
        }
    }

    public ListMenuItemView(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet);
        m0 r10 = m0.r(getContext(), attributeSet, R$styleable.MenuView, i10, 0);
        this.f572j = r10.g(R$styleable.MenuView_android_itemBackground);
        this.f573k = r10.m(R$styleable.MenuView_android_itemTextAppearance, -1);
        this.f575m = r10.a(R$styleable.MenuView_preserveIconSpacing, false);
        this.f574l = context;
        this.f576n = r10.g(R$styleable.MenuView_subMenuArrow);
        TypedArray obtainStyledAttributes = context.getTheme().obtainStyledAttributes(null, new int[]{16843049}, R$attr.dropDownListViewStyle, 0);
        this.f577o = obtainStyledAttributes.hasValue(0);
        r10.f1145b.recycle();
        obtainStyledAttributes.recycle();
    }
}
