package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.annotation.StyleableRes;
import androidx.core.content.res.a;
import m.a;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: TintTypedArray */
public class m0 {

    /* renamed from: a  reason: collision with root package name */
    public final Context f1144a;

    /* renamed from: b  reason: collision with root package name */
    public final TypedArray f1145b;

    /* renamed from: c  reason: collision with root package name */
    public TypedValue f1146c;

    public m0(Context context, TypedArray typedArray) {
        this.f1144a = context;
        this.f1145b = typedArray;
    }

    public static m0 q(Context context, AttributeSet attributeSet, int[] iArr) {
        return new m0(context, context.obtainStyledAttributes(attributeSet, iArr));
    }

    public static m0 r(Context context, AttributeSet attributeSet, int[] iArr, int i10, int i11) {
        return new m0(context, context.obtainStyledAttributes(attributeSet, iArr, i10, i11));
    }

    public boolean a(int i10, boolean z10) {
        return this.f1145b.getBoolean(i10, z10);
    }

    public int b(int i10, int i11) {
        return this.f1145b.getColor(i10, i11);
    }

    public ColorStateList c(int i10) {
        int resourceId;
        if (this.f1145b.hasValue(i10) && (resourceId = this.f1145b.getResourceId(i10, 0)) != 0) {
            Context context = this.f1144a;
            ThreadLocal<TypedValue> threadLocal = a.f7597a;
            ColorStateList colorStateList = context.getColorStateList(resourceId);
            if (colorStateList != null) {
                return colorStateList;
            }
        }
        return this.f1145b.getColorStateList(i10);
    }

    public float d(int i10, float f10) {
        return this.f1145b.getDimension(i10, f10);
    }

    public int e(int i10, int i11) {
        return this.f1145b.getDimensionPixelOffset(i10, i11);
    }

    public int f(int i10, int i11) {
        return this.f1145b.getDimensionPixelSize(i10, i11);
    }

    public Drawable g(int i10) {
        int resourceId;
        if (!this.f1145b.hasValue(i10) || (resourceId = this.f1145b.getResourceId(i10, 0)) == 0) {
            return this.f1145b.getDrawable(i10);
        }
        return a.a(this.f1144a, resourceId);
    }

    public Drawable h(int i10) {
        int resourceId;
        Drawable f10;
        if (!this.f1145b.hasValue(i10) || (resourceId = this.f1145b.getResourceId(i10, 0)) == 0) {
            return null;
        }
        g a10 = g.a();
        Context context = this.f1144a;
        synchronized (a10) {
            f10 = a10.f1093a.f(context, resourceId, true);
        }
        return f10;
    }

    @Nullable
    public Typeface i(@StyleableRes int i10, int i11, @Nullable a.AbstractC0009a aVar) {
        int resourceId = this.f1145b.getResourceId(i10, 0);
        if (resourceId == 0) {
            return null;
        }
        if (this.f1146c == null) {
            this.f1146c = new TypedValue();
        }
        Context context = this.f1144a;
        TypedValue typedValue = this.f1146c;
        if (context.isRestricted()) {
            return null;
        }
        return androidx.core.content.res.a.a(context, resourceId, typedValue, i11, aVar, null, true);
    }

    public int j(int i10, int i11) {
        return this.f1145b.getInt(i10, i11);
    }

    public int k(int i10, int i11) {
        return this.f1145b.getInteger(i10, i11);
    }

    public int l(int i10, int i11) {
        return this.f1145b.getLayoutDimension(i10, i11);
    }

    public int m(int i10, int i11) {
        return this.f1145b.getResourceId(i10, i11);
    }

    public String n(int i10) {
        return this.f1145b.getString(i10);
    }

    public CharSequence o(int i10) {
        return this.f1145b.getText(i10);
    }

    public boolean p(int i10) {
        return this.f1145b.hasValue(i10);
    }
}
