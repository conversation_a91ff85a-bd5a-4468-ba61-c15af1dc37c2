package androidx.recyclerview;

public final class R$attr {
    public static final int alpha = **********;
    public static final int fastScrollEnabled = **********;
    public static final int fastScrollHorizontalThumbDrawable = **********;
    public static final int fastScrollHorizontalTrackDrawable = **********;
    public static final int fastScrollVerticalThumbDrawable = **********;
    public static final int fastScrollVerticalTrackDrawable = **********;
    public static final int font = **********;
    public static final int fontProviderAuthority = **********;
    public static final int fontProviderCerts = **********;
    public static final int fontProviderFetchStrategy = **********;
    public static final int fontProviderFetchTimeout = **********;
    public static final int fontProviderPackage = **********;
    public static final int fontProviderQuery = **********;
    public static final int fontStyle = **********;
    public static final int fontVariationSettings = **********;
    public static final int fontWeight = **********;
    public static final int layoutManager = **********;
    public static final int recyclerViewStyle = **********;
    public static final int reverseLayout = **********;
    public static final int spanCount = **********;
    public static final int stackFromEnd = **********;
    public static final int ttcIndex = **********;

    private R$attr() {
    }
}
