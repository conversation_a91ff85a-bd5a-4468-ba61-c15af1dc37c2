package aa;

import com.xiaomi.mitv.socialtv.common.udt.protocol.UDTProtocol;
import java.io.IOException;
import mb.a;

/* compiled from: ASN1Boolean */
public class c extends q {

    /* renamed from: b  reason: collision with root package name */
    public static final byte[] f165b = {-1};

    /* renamed from: c  reason: collision with root package name */
    public static final byte[] f166c = {0};

    /* renamed from: d  reason: collision with root package name */
    public static final c f167d = new c(false);

    /* renamed from: e  reason: collision with root package name */
    public static final c f168e = new c(true);

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f169a;

    public c(byte[] bArr) {
        if (bArr.length != 1) {
            throw new IllegalArgumentException("byte value should have 1 byte in it");
        } else if (bArr[0] == 0) {
            this.f169a = f166c;
        } else if ((bArr[0] & UDTProtocol.KeyEventUDTProtocol.KEYEVENT_ACTION_DOWN_AND_UP) == 255) {
            this.f169a = f165b;
        } else {
            this.f169a = a.c(bArr);
        }
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof c) || this.f169a[0] != ((c) qVar).f169a[0]) {
            return false;
        }
        return true;
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(1, this.f169a);
    }

    @Override // aa.l
    public int hashCode() {
        return this.f169a[0];
    }

    @Override // aa.q
    public int i() {
        return 3;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return this.f169a[0] != 0 ? "TRUE" : "FALSE";
    }

    public c(boolean z10) {
        this.f169a = z10 ? f165b : f166c;
    }
}
