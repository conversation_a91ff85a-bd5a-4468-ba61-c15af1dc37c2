package a7;

import android.util.Log;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.PopupWindow;
import k7.d;
import miuix.appcompat.app.b;
import miuix.appcompat.app.j;

/* compiled from: ImmersionMenuPopupWindowImpl */
public class c extends d implements b {

    /* renamed from: m0  reason: collision with root package name */
    public b f140m0;

    /* renamed from: n0  reason: collision with root package name */
    public a f141n0;

    /* renamed from: o0  reason: collision with root package name */
    public View f142o0;

    /* renamed from: p0  reason: collision with root package name */
    public ViewGroup f143p0;

    /* compiled from: ImmersionMenuPopupWindowImpl */
    public class a implements AdapterView.OnItemClickListener {

        /* renamed from: a7.c$a$a  reason: collision with other inner class name */
        /* compiled from: ImmersionMenuPopupWindowImpl */
        public class C0004a implements PopupWindow.OnDismissListener {

            /* renamed from: a  reason: collision with root package name */
            public final /* synthetic */ SubMenu f145a;

            public C0004a(SubMenu subMenu) {
                this.f145a = subMenu;
            }

            public void onDismiss() {
                c cVar = c.this;
                cVar.f7341t = null;
                SubMenu subMenu = this.f145a;
                a aVar = cVar.f141n0;
                aVar.a(subMenu, aVar.f137b);
                aVar.notifyDataSetChanged();
                c cVar2 = c.this;
                View view = cVar2.f142o0;
                cVar2.setWidth(cVar2.k());
                cVar2.q(view);
            }
        }

        public a() {
        }

        @Override // android.widget.AdapterView.OnItemClickListener
        public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
            MenuItem menuItem = c.this.f141n0.f137b.get(i10);
            if (menuItem.hasSubMenu()) {
                SubMenu subMenu = menuItem.getSubMenu();
                c.this.f7341t = new C0004a(subMenu);
            } else {
                ((j) c.this.f140m0).k(0, menuItem);
            }
            c.this.dismiss();
        }
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public c(miuix.appcompat.app.b r3, android.view.Menu r4) {
        /*
            r2 = this;
            r0 = r3
            miuix.appcompat.app.j r0 = (miuix.appcompat.app.j) r0
            miuix.appcompat.app.i r1 = r0.f8025a
            r2.<init>(r1)
            miuix.appcompat.app.i r0 = r0.f8025a
            r2.f140m0 = r3
            a7.a r3 = new a7.a
            r3.<init>(r0, r4)
            r2.f141n0 = r3
            r2.h(r3)
            a7.c$a r3 = new a7.c$a
            r3.<init>()
            r2.f7332k = r3
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: a7.c.<init>(miuix.appcompat.app.b, android.view.Menu):void");
    }

    @Override // k7.d
    public void p(View view, ViewGroup viewGroup) {
        int i10;
        this.f142o0 = view;
        this.f143p0 = viewGroup;
        if (viewGroup == null) {
            Log.w("ImmersionMenu", "ImmersionMenuPopupWindow offset can't be adjusted without parent");
        } else {
            int[] iArr = new int[2];
            viewGroup.getLocationInWindow(iArr);
            int[] iArr2 = new int[2];
            view.getLocationInWindow(iArr2);
            boolean z10 = true;
            c(-(view.getHeight() + ((iArr2[1] - iArr[1]) - this.f7338q)));
            if (viewGroup.getLayoutDirection() != 1) {
                z10 = false;
            }
            if (z10) {
                i10 = this.f7337p;
            } else {
                i10 = (viewGroup.getWidth() - (view.getWidth() + (iArr2[0] - iArr[0]))) - this.f7337p;
            }
            e(i10);
        }
        if (m(view)) {
            q(view);
        }
    }
}
