syntax = "proto3";

package com.xiaomi.idm.service.iot.proto;

option java_package = "com.xiaomi.idm.service.iot.proto";
option java_outer_classname = "PropertyServiceProto";

  message SetProperty {
  int32 aid = 1;
  string paramJson = 2;
  bool isSort = 3;
}
  message GetProperty {
  int32 aid = 1;
  string paramJson = 2;
}
  message SetPropertyCommands {
  int32 aid = 1;
  map<string, string> propertyMap = 2;
}
  message GetPropertyCommands {
  int32 aid = 1;
  map<string, string> propertyMap = 2;
}
  message PropertyResponse {
  int32 code = 1;
  string message = 2;
  string response = 3;
}
  message PropertyEvent {
  int32 param = 1;
  string paramStr = 2;
}