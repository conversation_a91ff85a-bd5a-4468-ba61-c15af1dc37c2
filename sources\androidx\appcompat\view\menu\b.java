package androidx.appcompat.view.menu;

import android.content.Context;
import android.os.Bundle;
import android.os.Parcelable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListAdapter;
import androidx.annotation.RestrictTo;
import androidx.appcompat.R$layout;
import androidx.appcompat.app.AlertController;
import androidx.appcompat.app.d;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.view.menu.i;
import com.xiaomi.mitv.socialtv.common.net.media.VideoConstants;
import java.util.ArrayList;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: ListMenuPresenter */
public class b implements h, AdapterView.OnItemClickListener {

    /* renamed from: a  reason: collision with root package name */
    public Context f588a;

    /* renamed from: b  reason: collision with root package name */
    public LayoutInflater f589b;

    /* renamed from: c  reason: collision with root package name */
    public d f590c;

    /* renamed from: d  reason: collision with root package name */
    public ExpandedMenuView f591d;

    /* renamed from: e  reason: collision with root package name */
    public int f592e;

    /* renamed from: f  reason: collision with root package name */
    public h.a f593f;

    /* renamed from: g  reason: collision with root package name */
    public a f594g;

    /* compiled from: ListMenuPresenter */
    public class a extends BaseAdapter {

        /* renamed from: a  reason: collision with root package name */
        public int f595a = -1;

        public a() {
            a();
        }

        public void a() {
            d dVar = b.this.f590c;
            f fVar = dVar.f624v;
            if (fVar != null) {
                dVar.i();
                ArrayList<f> arrayList = dVar.f612j;
                int size = arrayList.size();
                for (int i10 = 0; i10 < size; i10++) {
                    if (arrayList.get(i10) == fVar) {
                        this.f595a = i10;
                        return;
                    }
                }
            }
            this.f595a = -1;
        }

        /* renamed from: b */
        public f getItem(int i10) {
            d dVar = b.this.f590c;
            dVar.i();
            ArrayList<f> arrayList = dVar.f612j;
            Objects.requireNonNull(b.this);
            int i11 = i10 + 0;
            int i12 = this.f595a;
            if (i12 >= 0 && i11 >= i12) {
                i11++;
            }
            return arrayList.get(i11);
        }

        public int getCount() {
            d dVar = b.this.f590c;
            dVar.i();
            int size = dVar.f612j.size();
            Objects.requireNonNull(b.this);
            int i10 = size + 0;
            return this.f595a < 0 ? i10 : i10 - 1;
        }

        public long getItemId(int i10) {
            return (long) i10;
        }

        public View getView(int i10, View view, ViewGroup viewGroup) {
            if (view == null) {
                b bVar = b.this;
                view = bVar.f589b.inflate(bVar.f592e, viewGroup, false);
            }
            ((i.a) view).d(getItem(i10), 0);
            return view;
        }

        public void notifyDataSetChanged() {
            a();
            super.notifyDataSetChanged();
        }
    }

    public b(Context context, int i10) {
        this.f592e = i10;
        this.f588a = context;
        this.f589b = LayoutInflater.from(context);
    }

    @Override // androidx.appcompat.view.menu.h
    public void a(d dVar, boolean z10) {
        h.a aVar = this.f593f;
        if (aVar != null) {
            aVar.a(dVar, z10);
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void b(boolean z10) {
        a aVar = this.f594g;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean c() {
        return false;
    }

    @Override // androidx.appcompat.view.menu.h
    public void d(Context context, d dVar) {
        if (this.f588a != null) {
            this.f588a = context;
            if (this.f589b == null) {
                this.f589b = LayoutInflater.from(context);
            }
        }
        this.f590c = dVar;
        a aVar = this.f594g;
        if (aVar != null) {
            aVar.notifyDataSetChanged();
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public void e(Parcelable parcelable) {
        SparseArray<Parcelable> sparseParcelableArray = ((Bundle) parcelable).getSparseParcelableArray("android:menu:list");
        if (sparseParcelableArray != null) {
            this.f591d.restoreHierarchyState(sparseParcelableArray);
        }
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean f(k kVar) {
        if (!kVar.hasVisibleItems()) {
            return false;
        }
        e eVar = new e(kVar);
        d.a aVar = new d.a(kVar.f604a);
        b bVar = new b(aVar.f440a.f357a, R$layout.abc_list_menu_item_layout);
        eVar.f629c = bVar;
        bVar.f593f = eVar;
        d dVar = eVar.f627a;
        dVar.b(bVar, dVar.f604a);
        ListAdapter j10 = eVar.f629c.j();
        AlertController.b bVar2 = aVar.f440a;
        bVar2.f363g = j10;
        bVar2.h = eVar;
        View view = kVar.f617o;
        if (view != null) {
            bVar2.f361e = view;
        } else {
            bVar2.f359c = kVar.f616n;
            bVar2.f360d = kVar.f615m;
        }
        bVar2.f362f = eVar;
        d a10 = aVar.a();
        eVar.f628b = a10;
        a10.setOnDismissListener(eVar);
        WindowManager.LayoutParams attributes = eVar.f628b.getWindow().getAttributes();
        attributes.type = VideoConstants.SEARCH_CINEASTE_BY_KEYWORD;
        attributes.flags |= 131072;
        eVar.f628b.show();
        h.a aVar2 = this.f593f;
        if (aVar2 == null) {
            return true;
        }
        aVar2.b(kVar);
        return true;
    }

    @Override // androidx.appcompat.view.menu.h
    public Parcelable g() {
        if (this.f591d == null) {
            return null;
        }
        Bundle bundle = new Bundle();
        SparseArray<Parcelable> sparseArray = new SparseArray<>();
        ExpandedMenuView expandedMenuView = this.f591d;
        if (expandedMenuView != null) {
            expandedMenuView.saveHierarchyState(sparseArray);
        }
        bundle.putSparseParcelableArray("android:menu:list", sparseArray);
        return bundle;
    }

    @Override // androidx.appcompat.view.menu.h
    public int getId() {
        return 0;
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean h(d dVar, f fVar) {
        return false;
    }

    @Override // androidx.appcompat.view.menu.h
    public boolean i(d dVar, f fVar) {
        return false;
    }

    public ListAdapter j() {
        if (this.f594g == null) {
            this.f594g = new a();
        }
        return this.f594g;
    }

    @Override // androidx.appcompat.view.menu.h
    public void k(h.a aVar) {
        this.f593f = aVar;
    }

    @Override // android.widget.AdapterView.OnItemClickListener
    public void onItemClick(AdapterView<?> adapterView, View view, int i10, long j10) {
        this.f590c.r(this.f594g.getItem(i10), this, 0);
    }
}
