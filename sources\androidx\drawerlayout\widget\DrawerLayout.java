package androidx.drawerlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.accessibility.AccessibilityEvent;
import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import com.duokan.airkan.common.Constant;
import j0.m;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import k0.b;
import m0.c;

public class DrawerLayout extends ViewGroup {

    /* renamed from: r0  reason: collision with root package name */
    public static final int[] f1663r0 = {16843828};

    /* renamed from: s0  reason: collision with root package name */
    public static final int[] f1664s0 = {16842931};

    /* renamed from: a  reason: collision with root package name */
    public float f1665a;

    /* renamed from: b  reason: collision with root package name */
    public int f1666b;

    /* renamed from: c  reason: collision with root package name */
    public int f1667c;

    /* renamed from: d  reason: collision with root package name */
    public float f1668d;

    /* renamed from: e  reason: collision with root package name */
    public Paint f1669e;

    /* renamed from: f  reason: collision with root package name */
    public final m0.c f1670f;

    /* renamed from: g  reason: collision with root package name */
    public final m0.c f1671g;
    public final e h;

    /* renamed from: i  reason: collision with root package name */
    public final e f1672i;

    /* renamed from: j  reason: collision with root package name */
    public int f1673j;

    /* renamed from: k  reason: collision with root package name */
    public boolean f1674k;

    /* renamed from: l  reason: collision with root package name */
    public boolean f1675l;

    /* renamed from: m  reason: collision with root package name */
    public int f1676m;

    /* renamed from: m0  reason: collision with root package name */
    public Object f1677m0;

    /* renamed from: n  reason: collision with root package name */
    public int f1678n;

    /* renamed from: n0  reason: collision with root package name */
    public boolean f1679n0;

    /* renamed from: o  reason: collision with root package name */
    public int f1680o;

    /* renamed from: o0  reason: collision with root package name */
    public final ArrayList<View> f1681o0;

    /* renamed from: p  reason: collision with root package name */
    public int f1682p;

    /* renamed from: p0  reason: collision with root package name */
    public Rect f1683p0;

    /* renamed from: q  reason: collision with root package name */
    public boolean f1684q;

    /* renamed from: q0  reason: collision with root package name */
    public Matrix f1685q0;
    @Nullable

    /* renamed from: r  reason: collision with root package name */
    public d f1686r;

    /* renamed from: s  reason: collision with root package name */
    public List<d> f1687s;

    /* renamed from: t  reason: collision with root package name */
    public float f1688t;

    /* renamed from: x  reason: collision with root package name */
    public float f1689x;

    /* renamed from: y  reason: collision with root package name */
    public Drawable f1690y;

    public class a implements View.OnApplyWindowInsetsListener {
        public a(DrawerLayout drawerLayout) {
        }

        public WindowInsets onApplyWindowInsets(View view, WindowInsets windowInsets) {
            DrawerLayout drawerLayout = (DrawerLayout) view;
            boolean z10 = true;
            boolean z11 = windowInsets.getSystemWindowInsetTop() > 0;
            drawerLayout.f1677m0 = windowInsets;
            drawerLayout.f1679n0 = z11;
            if (z11 || drawerLayout.getBackground() != null) {
                z10 = false;
            }
            drawerLayout.setWillNotDraw(z10);
            drawerLayout.requestLayout();
            return windowInsets.consumeSystemWindowInsets();
        }
    }

    public class b extends androidx.core.view.a {

        /* renamed from: d  reason: collision with root package name */
        public final Rect f1700d = new Rect();

        public b() {
        }

        @Override // androidx.core.view.a
        public boolean a(View view, AccessibilityEvent accessibilityEvent) {
            if (accessibilityEvent.getEventType() != 32) {
                return this.f1598a.dispatchPopulateAccessibilityEvent(view, accessibilityEvent);
            }
            accessibilityEvent.getText();
            View f10 = DrawerLayout.this.f();
            if (f10 == null) {
                return true;
            }
            int h = DrawerLayout.this.h(f10);
            DrawerLayout drawerLayout = DrawerLayout.this;
            Objects.requireNonNull(drawerLayout);
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            Gravity.getAbsoluteGravity(h, drawerLayout.getLayoutDirection());
            return true;
        }

        @Override // androidx.core.view.a
        public void c(View view, AccessibilityEvent accessibilityEvent) {
            this.f1598a.onInitializeAccessibilityEvent(view, accessibilityEvent);
            accessibilityEvent.setClassName(DrawerLayout.class.getName());
        }

        @Override // androidx.core.view.a
        public void d(View view, k0.b bVar) {
            int[] iArr = DrawerLayout.f1663r0;
            this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
            bVar.f7267a.setClassName(DrawerLayout.class.getName());
            bVar.f7267a.setFocusable(false);
            bVar.f7267a.setFocused(false);
            bVar.l(b.a.f7270e);
            bVar.l(b.a.f7271f);
        }

        @Override // androidx.core.view.a
        public boolean f(ViewGroup viewGroup, View view, AccessibilityEvent accessibilityEvent) {
            int[] iArr = DrawerLayout.f1663r0;
            return this.f1598a.onRequestSendAccessibilityEvent(viewGroup, view, accessibilityEvent);
        }
    }

    public static final class c extends androidx.core.view.a {
        @Override // androidx.core.view.a
        public void d(View view, k0.b bVar) {
            this.f1598a.onInitializeAccessibilityNodeInfo(view, bVar.f7267a);
            if (!DrawerLayout.i(view)) {
                bVar.o(null);
            }
        }
    }

    public interface d {
        void a(int i10);

        void b(@NonNull View view, float f10);

        void c(@NonNull View view);

        void d(@NonNull View view);
    }

    public class e extends c.AbstractC0117c {

        /* renamed from: a  reason: collision with root package name */
        public final int f1702a;

        /* renamed from: b  reason: collision with root package name */
        public m0.c f1703b;

        /* renamed from: c  reason: collision with root package name */
        public final Runnable f1704c = new a();

        public class a implements Runnable {
            public a() {
            }

            public void run() {
                int i10;
                View view;
                e eVar = e.this;
                int i11 = eVar.f1703b.f7631o;
                boolean z10 = eVar.f1702a == 3;
                if (z10) {
                    view = DrawerLayout.this.d(3);
                    i10 = (view != null ? -view.getWidth() : 0) + i11;
                } else {
                    view = DrawerLayout.this.d(5);
                    i10 = DrawerLayout.this.getWidth() - i11;
                }
                if (view == null) {
                    return;
                }
                if (((z10 && view.getLeft() < i10) || (!z10 && view.getLeft() > i10)) && DrawerLayout.this.g(view) == 0) {
                    eVar.f1703b.x(view, i10, view.getTop());
                    ((LayoutParams) view.getLayoutParams()).f1693c = true;
                    DrawerLayout.this.invalidate();
                    eVar.l();
                    DrawerLayout drawerLayout = DrawerLayout.this;
                    if (!drawerLayout.f1684q) {
                        long uptimeMillis = SystemClock.uptimeMillis();
                        MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0);
                        int childCount = drawerLayout.getChildCount();
                        for (int i12 = 0; i12 < childCount; i12++) {
                            drawerLayout.getChildAt(i12).dispatchTouchEvent(obtain);
                        }
                        obtain.recycle();
                        drawerLayout.f1684q = true;
                    }
                }
            }
        }

        public e(int i10) {
            this.f1702a = i10;
        }

        @Override // m0.c.AbstractC0117c
        public int a(View view, int i10, int i11) {
            if (DrawerLayout.this.a(view, 3)) {
                return Math.max(-view.getWidth(), Math.min(i10, 0));
            }
            int width = DrawerLayout.this.getWidth();
            return Math.max(width - view.getWidth(), Math.min(i10, width));
        }

        @Override // m0.c.AbstractC0117c
        public int b(View view, int i10, int i11) {
            return view.getTop();
        }

        @Override // m0.c.AbstractC0117c
        public int c(View view) {
            if (DrawerLayout.this.k(view)) {
                return view.getWidth();
            }
            return 0;
        }

        @Override // m0.c.AbstractC0117c
        public void e(int i10, int i11) {
            View view;
            if ((i10 & 1) == 1) {
                view = DrawerLayout.this.d(3);
            } else {
                view = DrawerLayout.this.d(5);
            }
            if (view != null && DrawerLayout.this.g(view) == 0) {
                this.f1703b.b(view, i11);
            }
        }

        @Override // m0.c.AbstractC0117c
        public void f(int i10, int i11) {
            DrawerLayout.this.postDelayed(this.f1704c, 160);
        }

        @Override // m0.c.AbstractC0117c
        public void g(View view, int i10) {
            ((LayoutParams) view.getLayoutParams()).f1693c = false;
            l();
        }

        @Override // m0.c.AbstractC0117c
        public void h(int i10) {
            DrawerLayout.this.p(i10, this.f1703b.f7635s);
        }

        @Override // m0.c.AbstractC0117c
        public void i(View view, int i10, int i11, int i12, int i13) {
            float f10;
            int width = view.getWidth();
            if (DrawerLayout.this.a(view, 3)) {
                f10 = (float) (i10 + width);
            } else {
                f10 = (float) (DrawerLayout.this.getWidth() - i10);
            }
            float f11 = f10 / ((float) width);
            DrawerLayout.this.n(view, f11);
            view.setVisibility(f11 == Constant.VOLUME_FLOAT_MIN ? 4 : 0);
            DrawerLayout.this.invalidate();
        }

        @Override // m0.c.AbstractC0117c
        public void j(View view, float f10, float f11) {
            int i10;
            Objects.requireNonNull(DrawerLayout.this);
            float f12 = ((LayoutParams) view.getLayoutParams()).f1692b;
            int width = view.getWidth();
            if (DrawerLayout.this.a(view, 3)) {
                int i11 = (f10 > Constant.VOLUME_FLOAT_MIN ? 1 : (f10 == Constant.VOLUME_FLOAT_MIN ? 0 : -1));
                i10 = (i11 > 0 || (i11 == 0 && f12 > 0.5f)) ? 0 : -width;
            } else {
                int width2 = DrawerLayout.this.getWidth();
                if (f10 < Constant.VOLUME_FLOAT_MIN || (f10 == Constant.VOLUME_FLOAT_MIN && f12 > 0.5f)) {
                    width2 -= width;
                }
                i10 = width2;
            }
            this.f1703b.v(i10, view.getTop());
            DrawerLayout.this.invalidate();
        }

        @Override // m0.c.AbstractC0117c
        public boolean k(View view, int i10) {
            return DrawerLayout.this.k(view) && DrawerLayout.this.a(view, this.f1702a) && DrawerLayout.this.g(view) == 0;
        }

        public final void l() {
            int i10 = 3;
            if (this.f1702a == 3) {
                i10 = 5;
            }
            View d10 = DrawerLayout.this.d(i10);
            if (d10 != null) {
                DrawerLayout.this.b(d10);
            }
        }

        public void m() {
            DrawerLayout.this.removeCallbacks(this.f1704c);
        }
    }

    public DrawerLayout(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public static boolean i(View view) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        return (view.getImportantForAccessibility() == 4 || view.getImportantForAccessibility() == 2) ? false : true;
    }

    public boolean a(View view, int i10) {
        return (h(view) & i10) == i10;
    }

    @Override // android.view.View, android.view.ViewGroup
    public void addFocusables(ArrayList<View> arrayList, int i10, int i11) {
        if (getDescendantFocusability() != 393216) {
            int childCount = getChildCount();
            boolean z10 = false;
            for (int i12 = 0; i12 < childCount; i12++) {
                View childAt = getChildAt(i12);
                if (!k(childAt)) {
                    this.f1681o0.add(childAt);
                } else if (!k(childAt)) {
                    throw new IllegalArgumentException("View " + childAt + " is not a drawer");
                } else if ((((LayoutParams) childAt.getLayoutParams()).f1694d & 1) == 1) {
                    childAt.addFocusables(arrayList, i10, i11);
                    z10 = true;
                }
            }
            if (!z10) {
                int size = this.f1681o0.size();
                for (int i13 = 0; i13 < size; i13++) {
                    View view = this.f1681o0.get(i13);
                    if (view.getVisibility() == 0) {
                        view.addFocusables(arrayList, i10, i11);
                    }
                }
            }
            this.f1681o0.clear();
        }
    }

    @Override // android.view.ViewGroup
    public void addView(View view, int i10, ViewGroup.LayoutParams layoutParams) {
        super.addView(view, i10, layoutParams);
        if (e() != null || k(view)) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            view.setImportantForAccessibility(4);
            return;
        }
        WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
        view.setImportantForAccessibility(1);
    }

    public void b(@NonNull View view) {
        if (k(view)) {
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            if (this.f1675l) {
                layoutParams.f1692b = Constant.VOLUME_FLOAT_MIN;
                layoutParams.f1694d = 0;
            } else {
                layoutParams.f1694d |= 4;
                if (a(view, 3)) {
                    this.f1670f.x(view, -view.getWidth(), view.getTop());
                } else {
                    this.f1671g.x(view, getWidth(), view.getTop());
                }
            }
            invalidate();
            return;
        }
        throw new IllegalArgumentException("View " + view + " is not a sliding drawer");
    }

    public void c(boolean z10) {
        boolean z11;
        int childCount = getChildCount();
        boolean z12 = false;
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
            if (k(childAt) && (!z10 || layoutParams.f1693c)) {
                int width = childAt.getWidth();
                if (a(childAt, 3)) {
                    z11 = this.f1670f.x(childAt, -width, childAt.getTop());
                } else {
                    z11 = this.f1671g.x(childAt, getWidth(), childAt.getTop());
                }
                z12 |= z11;
                layoutParams.f1693c = false;
            }
        }
        this.h.m();
        this.f1672i.m();
        if (z12) {
            invalidate();
        }
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof LayoutParams) && super.checkLayoutParams(layoutParams);
    }

    public void computeScroll() {
        int childCount = getChildCount();
        float f10 = Constant.VOLUME_FLOAT_MIN;
        for (int i10 = 0; i10 < childCount; i10++) {
            f10 = Math.max(f10, ((LayoutParams) getChildAt(i10).getLayoutParams()).f1692b);
        }
        this.f1668d = f10;
        boolean i11 = this.f1670f.i(true);
        boolean i12 = this.f1671g.i(true);
        if (i11 || i12) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            postInvalidateOnAnimation();
        }
    }

    public View d(int i10) {
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int absoluteGravity = Gravity.getAbsoluteGravity(i10, getLayoutDirection()) & 7;
        int childCount = getChildCount();
        for (int i11 = 0; i11 < childCount; i11++) {
            View childAt = getChildAt(i11);
            if ((h(childAt) & 7) == absoluteGravity) {
                return childAt;
            }
        }
        return null;
    }

    public boolean dispatchGenericMotionEvent(MotionEvent motionEvent) {
        boolean z10;
        if ((motionEvent.getSource() & 2) == 0 || motionEvent.getAction() == 10 || this.f1668d <= Constant.VOLUME_FLOAT_MIN) {
            return super.dispatchGenericMotionEvent(motionEvent);
        }
        int childCount = getChildCount();
        if (childCount == 0) {
            return false;
        }
        float x8 = motionEvent.getX();
        float y10 = motionEvent.getY();
        for (int i10 = childCount - 1; i10 >= 0; i10--) {
            View childAt = getChildAt(i10);
            if (this.f1683p0 == null) {
                this.f1683p0 = new Rect();
            }
            childAt.getHitRect(this.f1683p0);
            if (this.f1683p0.contains((int) x8, (int) y10) && !j(childAt)) {
                if (!childAt.getMatrix().isIdentity()) {
                    MotionEvent obtain = MotionEvent.obtain(motionEvent);
                    obtain.offsetLocation((float) (getScrollX() - childAt.getLeft()), (float) (getScrollY() - childAt.getTop()));
                    Matrix matrix = childAt.getMatrix();
                    if (!matrix.isIdentity()) {
                        if (this.f1685q0 == null) {
                            this.f1685q0 = new Matrix();
                        }
                        matrix.invert(this.f1685q0);
                        obtain.transform(this.f1685q0);
                    }
                    z10 = childAt.dispatchGenericMotionEvent(obtain);
                    obtain.recycle();
                } else {
                    float scrollX = (float) (getScrollX() - childAt.getLeft());
                    float scrollY = (float) (getScrollY() - childAt.getTop());
                    motionEvent.offsetLocation(scrollX, scrollY);
                    z10 = childAt.dispatchGenericMotionEvent(motionEvent);
                    motionEvent.offsetLocation(-scrollX, -scrollY);
                }
                if (z10) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean drawChild(Canvas canvas, View view, long j10) {
        int height = getHeight();
        boolean j11 = j(view);
        int width = getWidth();
        int save = canvas.save();
        int i10 = 0;
        if (j11) {
            int childCount = getChildCount();
            int i11 = 0;
            for (int i12 = 0; i12 < childCount; i12++) {
                View childAt = getChildAt(i12);
                if (childAt != view && childAt.getVisibility() == 0) {
                    Drawable background = childAt.getBackground();
                    if ((background != null && background.getOpacity() == -1) && k(childAt) && childAt.getHeight() >= height) {
                        if (a(childAt, 3)) {
                            int right = childAt.getRight();
                            if (right > i11) {
                                i11 = right;
                            }
                        } else {
                            int left = childAt.getLeft();
                            if (left < width) {
                                width = left;
                            }
                        }
                    }
                }
            }
            canvas.clipRect(i11, 0, width, getHeight());
            i10 = i11;
        }
        boolean drawChild = super.drawChild(canvas, view, j10);
        canvas.restoreToCount(save);
        float f10 = this.f1668d;
        if (f10 > Constant.VOLUME_FLOAT_MIN && j11) {
            int i13 = this.f1667c;
            this.f1669e.setColor((((int) (((float) ((-16777216 & i13) >>> 24)) * f10)) << 24) | (i13 & 16777215));
            canvas.drawRect((float) i10, Constant.VOLUME_FLOAT_MIN, (float) width, (float) getHeight(), this.f1669e);
        }
        return drawChild;
    }

    public View e() {
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if ((((LayoutParams) childAt.getLayoutParams()).f1694d & 1) == 1) {
                return childAt;
            }
        }
        return null;
    }

    public View f() {
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if (k(childAt)) {
                if (k(childAt)) {
                    if (((LayoutParams) childAt.getLayoutParams()).f1692b > Constant.VOLUME_FLOAT_MIN) {
                        return childAt;
                    }
                } else {
                    throw new IllegalArgumentException("View " + childAt + " is not a drawer");
                }
            }
        }
        return null;
    }

    public int g(@NonNull View view) {
        if (k(view)) {
            int i10 = ((LayoutParams) view.getLayoutParams()).f1691a;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            int layoutDirection = getLayoutDirection();
            if (i10 == 3) {
                int i11 = this.f1676m;
                if (i11 != 3) {
                    return i11;
                }
                int i12 = layoutDirection == 0 ? this.f1680o : this.f1682p;
                if (i12 != 3) {
                    return i12;
                }
            } else if (i10 == 5) {
                int i13 = this.f1678n;
                if (i13 != 3) {
                    return i13;
                }
                int i14 = layoutDirection == 0 ? this.f1682p : this.f1680o;
                if (i14 != 3) {
                    return i14;
                }
            } else if (i10 == 8388611) {
                int i15 = this.f1680o;
                if (i15 != 3) {
                    return i15;
                }
                int i16 = layoutDirection == 0 ? this.f1676m : this.f1678n;
                if (i16 != 3) {
                    return i16;
                }
            } else if (i10 == 8388613) {
                int i17 = this.f1682p;
                if (i17 != 3) {
                    return i17;
                }
                int i18 = layoutDirection == 0 ? this.f1678n : this.f1676m;
                if (i18 != 3) {
                    return i18;
                }
            }
            return 0;
        }
        throw new IllegalArgumentException("View " + view + " is not a drawer");
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(-1, -1);
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof LayoutParams) {
            return new LayoutParams((LayoutParams) layoutParams);
        }
        return layoutParams instanceof ViewGroup.MarginLayoutParams ? new LayoutParams((ViewGroup.MarginLayoutParams) layoutParams) : new LayoutParams(layoutParams);
    }

    public float getDrawerElevation() {
        return this.f1665a;
    }

    @Nullable
    public Drawable getStatusBarBackgroundDrawable() {
        return this.f1690y;
    }

    public int h(View view) {
        int i10 = ((LayoutParams) view.getLayoutParams()).f1691a;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        return Gravity.getAbsoluteGravity(i10, getLayoutDirection());
    }

    public boolean j(View view) {
        return ((LayoutParams) view.getLayoutParams()).f1691a == 0;
    }

    public boolean k(View view) {
        int i10 = ((LayoutParams) view.getLayoutParams()).f1691a;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int absoluteGravity = Gravity.getAbsoluteGravity(i10, view.getLayoutDirection());
        return ((absoluteGravity & 3) == 0 && (absoluteGravity & 5) == 0) ? false : true;
    }

    public void l(@NonNull View view) {
        if (k(view)) {
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            if (this.f1675l) {
                layoutParams.f1692b = 1.0f;
                layoutParams.f1694d = 1;
                o(view, true);
            } else {
                layoutParams.f1694d |= 2;
                if (a(view, 3)) {
                    this.f1670f.x(view, 0, view.getTop());
                } else {
                    this.f1671g.x(view, getWidth() - view.getWidth(), view.getTop());
                }
            }
            invalidate();
            return;
        }
        throw new IllegalArgumentException("View " + view + " is not a sliding drawer");
    }

    public void m(int i10, int i11) {
        View d10;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        int absoluteGravity = Gravity.getAbsoluteGravity(i11, getLayoutDirection());
        if (i11 == 3) {
            this.f1676m = i10;
        } else if (i11 == 5) {
            this.f1678n = i10;
        } else if (i11 == 8388611) {
            this.f1680o = i10;
        } else if (i11 == 8388613) {
            this.f1682p = i10;
        }
        if (i10 != 0) {
            (absoluteGravity == 3 ? this.f1670f : this.f1671g).a();
        }
        if (i10 == 1) {
            View d11 = d(absoluteGravity);
            if (d11 != null) {
                b(d11);
            }
        } else if (i10 == 2 && (d10 = d(absoluteGravity)) != null) {
            l(d10);
        }
    }

    public void n(View view, float f10) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        if (f10 != layoutParams.f1692b) {
            layoutParams.f1692b = f10;
            List<d> list = this.f1687s;
            if (list != null) {
                int size = list.size();
                while (true) {
                    size--;
                    if (size >= 0) {
                        this.f1687s.get(size).b(view, f10);
                    } else {
                        return;
                    }
                }
            }
        }
    }

    public final void o(View view, boolean z10) {
        int childCount = getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            View childAt = getChildAt(i10);
            if ((z10 || k(childAt)) && (!z10 || childAt != view)) {
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                childAt.setImportantForAccessibility(4);
            } else {
                WeakHashMap<View, m> weakHashMap2 = ViewCompat.f1593a;
                childAt.setImportantForAccessibility(1);
            }
        }
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.f1675l = true;
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.f1675l = true;
    }

    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (this.f1679n0 && this.f1690y != null) {
            Object obj = this.f1677m0;
            int systemWindowInsetTop = obj != null ? ((WindowInsets) obj).getSystemWindowInsetTop() : 0;
            if (systemWindowInsetTop > 0) {
                this.f1690y.setBounds(0, 0, getWidth(), systemWindowInsetTop);
                this.f1690y.draw(canvas);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:6:0x001b, code lost:
        if (r0 != 3) goto L_0x0067;
     */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x0051 A[LOOP:0: B:8:0x0024->B:17:0x0051, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:46:0x004f A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onInterceptTouchEvent(android.view.MotionEvent r9) {
        /*
        // Method dump skipped, instructions count: 184
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.drawerlayout.widget.DrawerLayout.onInterceptTouchEvent(android.view.MotionEvent):boolean");
    }

    public boolean onKeyDown(int i10, KeyEvent keyEvent) {
        if (i10 == 4) {
            if (f() != null) {
                keyEvent.startTracking();
                return true;
            }
        }
        return super.onKeyDown(i10, keyEvent);
    }

    public boolean onKeyUp(int i10, KeyEvent keyEvent) {
        if (i10 != 4) {
            return super.onKeyUp(i10, keyEvent);
        }
        View f10 = f();
        if (f10 != null && g(f10) == 0) {
            c(false);
        }
        if (f10 != null) {
            return true;
        }
        return false;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        float f10;
        int i14;
        boolean z11 = true;
        this.f1674k = true;
        int i15 = i12 - i10;
        int childCount = getChildCount();
        int i16 = 0;
        while (i16 < childCount) {
            View childAt = getChildAt(i16);
            if (childAt.getVisibility() != 8) {
                LayoutParams layoutParams = (LayoutParams) childAt.getLayoutParams();
                if (j(childAt)) {
                    int i17 = ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin;
                    childAt.layout(i17, ((ViewGroup.MarginLayoutParams) layoutParams).topMargin, childAt.getMeasuredWidth() + i17, childAt.getMeasuredHeight() + ((ViewGroup.MarginLayoutParams) layoutParams).topMargin);
                } else {
                    int measuredWidth = childAt.getMeasuredWidth();
                    int measuredHeight = childAt.getMeasuredHeight();
                    if (a(childAt, 3)) {
                        float f11 = (float) measuredWidth;
                        i14 = (-measuredWidth) + ((int) (layoutParams.f1692b * f11));
                        f10 = ((float) (measuredWidth + i14)) / f11;
                    } else {
                        float f12 = (float) measuredWidth;
                        int i18 = i15 - ((int) (layoutParams.f1692b * f12));
                        f10 = ((float) (i15 - i18)) / f12;
                        i14 = i18;
                    }
                    boolean z12 = f10 != layoutParams.f1692b ? z11 : false;
                    int i19 = layoutParams.f1691a & 112;
                    if (i19 == 16) {
                        int i20 = i13 - i11;
                        int i21 = (i20 - measuredHeight) / 2;
                        int i22 = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin;
                        if (i21 < i22) {
                            i21 = i22;
                        } else {
                            int i23 = i21 + measuredHeight;
                            int i24 = ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin;
                            if (i23 > i20 - i24) {
                                i21 = (i20 - i24) - measuredHeight;
                            }
                        }
                        childAt.layout(i14, i21, measuredWidth + i14, measuredHeight + i21);
                    } else if (i19 != 80) {
                        int i25 = ((ViewGroup.MarginLayoutParams) layoutParams).topMargin;
                        childAt.layout(i14, i25, measuredWidth + i14, measuredHeight + i25);
                    } else {
                        int i26 = i13 - i11;
                        childAt.layout(i14, (i26 - ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin) - childAt.getMeasuredHeight(), measuredWidth + i14, i26 - ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin);
                    }
                    if (z12) {
                        n(childAt, f10);
                    }
                    int i27 = layoutParams.f1692b > Constant.VOLUME_FLOAT_MIN ? 0 : 4;
                    if (childAt.getVisibility() != i27) {
                        childAt.setVisibility(i27);
                    }
                }
            }
            i16++;
            z11 = true;
        }
        this.f1674k = false;
        this.f1675l = false;
    }

    /* JADX WARNING: Removed duplicated region for block: B:20:0x0050  */
    @android.annotation.SuppressLint({"WrongConstant"})
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void onMeasure(int r17, int r18) {
        /*
        // Method dump skipped, instructions count: 455
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.drawerlayout.widget.DrawerLayout.onMeasure(int, int):void");
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        View d10;
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        super.onRestoreInstanceState(savedState.f1662a);
        int i10 = savedState.f1695c;
        if (!(i10 == 0 || (d10 = d(i10)) == null)) {
            l(d10);
        }
        int i11 = savedState.f1696d;
        if (i11 != 3) {
            m(i11, 3);
        }
        int i12 = savedState.f1697e;
        if (i12 != 3) {
            m(i12, 5);
        }
        int i13 = savedState.f1698f;
        if (i13 != 3) {
            m(i13, 8388611);
        }
        int i14 = savedState.f1699g;
        if (i14 != 3) {
            m(i14, 8388613);
        }
    }

    public void onRtlPropertiesChanged(int i10) {
    }

    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        int childCount = getChildCount();
        int i10 = 0;
        while (true) {
            if (i10 >= childCount) {
                break;
            }
            LayoutParams layoutParams = (LayoutParams) getChildAt(i10).getLayoutParams();
            int i11 = layoutParams.f1694d;
            boolean z10 = true;
            boolean z11 = i11 == 1;
            if (i11 != 2) {
                z10 = false;
            }
            if (z11 || z10) {
                savedState.f1695c = layoutParams.f1691a;
            } else {
                i10++;
            }
        }
        savedState.f1696d = this.f1676m;
        savedState.f1697e = this.f1678n;
        savedState.f1698f = this.f1680o;
        savedState.f1699g = this.f1682p;
        return savedState;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:15:0x0056, code lost:
        if (g(r7) != 2) goto L_0x0059;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouchEvent(android.view.MotionEvent r7) {
        /*
        // Method dump skipped, instructions count: 108
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.drawerlayout.widget.DrawerLayout.onTouchEvent(android.view.MotionEvent):boolean");
    }

    public void p(int i10, View view) {
        View rootView;
        int i11 = this.f1670f.f7618a;
        int i12 = this.f1671g.f7618a;
        int i13 = 2;
        if (i11 == 1 || i12 == 1) {
            i13 = 1;
        } else if (!(i11 == 2 || i12 == 2)) {
            i13 = 0;
        }
        if (view != null && i10 == 0) {
            float f10 = ((LayoutParams) view.getLayoutParams()).f1692b;
            if (f10 == Constant.VOLUME_FLOAT_MIN) {
                LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
                if ((layoutParams.f1694d & 1) == 1) {
                    layoutParams.f1694d = 0;
                    List<d> list = this.f1687s;
                    if (list != null) {
                        for (int size = list.size() - 1; size >= 0; size--) {
                            this.f1687s.get(size).d(view);
                        }
                    }
                    o(view, false);
                    if (hasWindowFocus() && (rootView = getRootView()) != null) {
                        rootView.sendAccessibilityEvent(32);
                    }
                }
            } else if (f10 == 1.0f) {
                LayoutParams layoutParams2 = (LayoutParams) view.getLayoutParams();
                if ((layoutParams2.f1694d & 1) == 0) {
                    layoutParams2.f1694d = 1;
                    List<d> list2 = this.f1687s;
                    if (list2 != null) {
                        for (int size2 = list2.size() - 1; size2 >= 0; size2--) {
                            this.f1687s.get(size2).c(view);
                        }
                    }
                    o(view, true);
                    if (hasWindowFocus()) {
                        sendAccessibilityEvent(32);
                    }
                }
            }
        }
        if (i13 != this.f1673j) {
            this.f1673j = i13;
            List<d> list3 = this.f1687s;
            if (list3 != null) {
                for (int size3 = list3.size() - 1; size3 >= 0; size3--) {
                    this.f1687s.get(size3).a(i13);
                }
            }
        }
    }

    public void requestDisallowInterceptTouchEvent(boolean z10) {
        super.requestDisallowInterceptTouchEvent(z10);
        if (z10) {
            c(true);
        }
    }

    public void requestLayout() {
        if (!this.f1674k) {
            super.requestLayout();
        }
    }

    public void setDrawerElevation(float f10) {
        this.f1665a = f10;
        for (int i10 = 0; i10 < getChildCount(); i10++) {
            View childAt = getChildAt(i10);
            if (k(childAt)) {
                float f11 = this.f1665a;
                WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
                childAt.setElevation(f11);
            }
        }
    }

    @Deprecated
    public void setDrawerListener(d dVar) {
        List<d> list;
        d dVar2 = this.f1686r;
        if (!(dVar2 == null || dVar2 == null || (list = this.f1687s) == null)) {
            list.remove(dVar2);
        }
        if (dVar != null) {
            if (this.f1687s == null) {
                this.f1687s = new ArrayList();
            }
            this.f1687s.add(dVar);
        }
        this.f1686r = dVar;
    }

    public void setDrawerLockMode(int i10) {
        m(i10, 3);
        m(i10, 5);
    }

    public void setScrimColor(@ColorInt int i10) {
        this.f1667c = i10;
        invalidate();
    }

    public void setStatusBarBackground(@Nullable Drawable drawable) {
        this.f1690y = drawable;
        invalidate();
    }

    public void setStatusBarBackgroundColor(@ColorInt int i10) {
        this.f1690y = new ColorDrawable(i10);
        invalidate();
    }

    public DrawerLayout(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        new c();
        this.f1667c = -1728053248;
        this.f1669e = new Paint();
        this.f1675l = true;
        this.f1676m = 3;
        this.f1678n = 3;
        this.f1680o = 3;
        this.f1682p = 3;
        setDescendantFocusability(262144);
        float f10 = getResources().getDisplayMetrics().density;
        this.f1666b = (int) ((64.0f * f10) + 0.5f);
        float f11 = 400.0f * f10;
        e eVar = new e(3);
        this.h = eVar;
        e eVar2 = new e(5);
        this.f1672i = eVar2;
        m0.c j10 = m0.c.j(this, 1.0f, eVar);
        this.f1670f = j10;
        j10.f7632p = 1;
        j10.f7630n = f11;
        eVar.f1703b = j10;
        m0.c j11 = m0.c.j(this, 1.0f, eVar2);
        this.f1671g = j11;
        j11.f7632p = 2;
        j11.f7630n = f11;
        eVar2.f1703b = j11;
        setFocusableInTouchMode(true);
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        setImportantForAccessibility(1);
        ViewCompat.k(this, new b());
        setMotionEventSplittingEnabled(false);
        if (getFitsSystemWindows()) {
            setOnApplyWindowInsetsListener(new a(this));
            setSystemUiVisibility(1280);
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(f1663r0);
            try {
                this.f1690y = obtainStyledAttributes.getDrawable(0);
            } finally {
                obtainStyledAttributes.recycle();
            }
        }
        this.f1665a = f10 * 10.0f;
        this.f1681o0 = new ArrayList<>();
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new LayoutParams(getContext(), attributeSet);
    }

    public void setStatusBarBackground(int i10) {
        Drawable drawable;
        if (i10 != 0) {
            Context context = getContext();
            Object obj = z.a.f11008a;
            drawable = context.getDrawable(i10);
        } else {
            drawable = null;
        }
        this.f1690y = drawable;
        invalidate();
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {

        /* renamed from: a  reason: collision with root package name */
        public int f1691a = 0;

        /* renamed from: b  reason: collision with root package name */
        public float f1692b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f1693c;

        /* renamed from: d  reason: collision with root package name */
        public int f1694d;

        public LayoutParams(@NonNull Context context, @Nullable AttributeSet attributeSet) {
            super(context, attributeSet);
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, DrawerLayout.f1664s0);
            this.f1691a = obtainStyledAttributes.getInt(0, 0);
            obtainStyledAttributes.recycle();
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(@NonNull LayoutParams layoutParams) {
            super((ViewGroup.MarginLayoutParams) layoutParams);
            this.f1691a = layoutParams.f1691a;
        }

        public LayoutParams(@NonNull ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public LayoutParams(@NonNull ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }
    }

    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: c  reason: collision with root package name */
        public int f1695c = 0;

        /* renamed from: d  reason: collision with root package name */
        public int f1696d;

        /* renamed from: e  reason: collision with root package name */
        public int f1697e;

        /* renamed from: f  reason: collision with root package name */
        public int f1698f;

        /* renamed from: g  reason: collision with root package name */
        public int f1699g;

        public static class a implements Parcelable.ClassLoaderCreator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }
        }

        public SavedState(@NonNull Parcel parcel, @Nullable ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f1695c = parcel.readInt();
            this.f1696d = parcel.readInt();
            this.f1697e = parcel.readInt();
            this.f1698f = parcel.readInt();
            this.f1699g = parcel.readInt();
        }

        @Override // androidx.customview.view.AbsSavedState
        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeParcelable(this.f1662a, i10);
            parcel.writeInt(this.f1695c);
            parcel.writeInt(this.f1696d);
            parcel.writeInt(this.f1697e);
            parcel.writeInt(this.f1698f);
            parcel.writeInt(this.f1699g);
        }

        public SavedState(@NonNull Parcelable parcelable) {
            super(parcelable);
        }
    }
}
