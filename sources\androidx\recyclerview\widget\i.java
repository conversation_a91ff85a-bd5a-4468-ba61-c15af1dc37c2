package androidx.recyclerview.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewPropertyAnimator;
import androidx.recyclerview.widget.RecyclerView;
import com.duokan.airkan.common.Constant;
import java.util.Objects;

/* compiled from: DefaultItemAnimator */
public class i extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ RecyclerView.w f2373a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ int f2374b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ View f2375c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ int f2376d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ ViewPropertyAnimator f2377e;

    /* renamed from: f  reason: collision with root package name */
    public final /* synthetic */ l f2378f;

    public i(l lVar, RecyclerView.w wVar, int i10, View view, int i11, ViewPropertyAnimator viewPropertyAnimator) {
        this.f2378f = lVar;
        this.f2373a = wVar;
        this.f2374b = i10;
        this.f2375c = view;
        this.f2376d = i11;
        this.f2377e = viewPropertyAnimator;
    }

    public void onAnimationCancel(Animator animator) {
        if (this.f2374b != 0) {
            this.f2375c.setTranslationX(Constant.VOLUME_FLOAT_MIN);
        }
        if (this.f2376d != 0) {
            this.f2375c.setTranslationY(Constant.VOLUME_FLOAT_MIN);
        }
    }

    public void onAnimationEnd(Animator animator) {
        this.f2377e.setListener(null);
        this.f2378f.c(this.f2373a);
        this.f2378f.f2395p.remove(this.f2373a);
        this.f2378f.k();
    }

    public void onAnimationStart(Animator animator) {
        Objects.requireNonNull(this.f2378f);
    }
}
