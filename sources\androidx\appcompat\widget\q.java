package androidx.appcompat.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.R$styleable;
import androidx.core.content.res.a;
import com.duokan.airkan.common.Constant;
import java.lang.ref.WeakReference;

/* compiled from: AppCompatTextHelper */
public class q {
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final TextView f1171a;

    /* renamed from: b  reason: collision with root package name */
    public k0 f1172b;

    /* renamed from: c  reason: collision with root package name */
    public k0 f1173c;

    /* renamed from: d  reason: collision with root package name */
    public k0 f1174d;

    /* renamed from: e  reason: collision with root package name */
    public k0 f1175e;

    /* renamed from: f  reason: collision with root package name */
    public k0 f1176f;

    /* renamed from: g  reason: collision with root package name */
    public k0 f1177g;
    public k0 h;
    @NonNull

    /* renamed from: i  reason: collision with root package name */
    public final r f1178i;

    /* renamed from: j  reason: collision with root package name */
    public int f1179j = 0;

    /* renamed from: k  reason: collision with root package name */
    public int f1180k = -1;

    /* renamed from: l  reason: collision with root package name */
    public Typeface f1181l;

    /* renamed from: m  reason: collision with root package name */
    public boolean f1182m;

    /* compiled from: AppCompatTextHelper */
    public class a extends a.AbstractC0009a {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ int f1183a;

        /* renamed from: b  reason: collision with root package name */
        public final /* synthetic */ int f1184b;

        /* renamed from: c  reason: collision with root package name */
        public final /* synthetic */ WeakReference f1185c;

        public a(int i10, int i11, WeakReference weakReference) {
            this.f1183a = i10;
            this.f1184b = i11;
            this.f1185c = weakReference;
        }

        @Override // androidx.core.content.res.a.AbstractC0009a
        public void c(int i10) {
        }

        @Override // androidx.core.content.res.a.AbstractC0009a
        public void d(@NonNull Typeface typeface) {
            int i10 = this.f1183a;
            if (i10 != -1) {
                typeface = Typeface.create(typeface, i10, (this.f1184b & 2) != 0);
            }
            q qVar = q.this;
            WeakReference weakReference = this.f1185c;
            if (qVar.f1182m) {
                qVar.f1181l = typeface;
                TextView textView = (TextView) weakReference.get();
                if (textView != null) {
                    textView.setTypeface(typeface, qVar.f1179j);
                }
            }
        }
    }

    public q(@NonNull TextView textView) {
        this.f1171a = textView;
        this.f1178i = new r(textView);
    }

    public static k0 c(Context context, g gVar, int i10) {
        ColorStateList d10 = gVar.d(context, i10);
        if (d10 == null) {
            return null;
        }
        k0 k0Var = new k0();
        k0Var.f1135d = true;
        k0Var.f1132a = d10;
        return k0Var;
    }

    public final void a(Drawable drawable, k0 k0Var) {
        if (drawable != null && k0Var != null) {
            g.f(drawable, k0Var, this.f1171a.getDrawableState());
        }
    }

    public void b() {
        if (!(this.f1172b == null && this.f1173c == null && this.f1174d == null && this.f1175e == null)) {
            Drawable[] compoundDrawables = this.f1171a.getCompoundDrawables();
            a(compoundDrawables[0], this.f1172b);
            a(compoundDrawables[1], this.f1173c);
            a(compoundDrawables[2], this.f1174d);
            a(compoundDrawables[3], this.f1175e);
        }
        if (this.f1176f != null || this.f1177g != null) {
            Drawable[] compoundDrawablesRelative = this.f1171a.getCompoundDrawablesRelative();
            a(compoundDrawablesRelative[0], this.f1176f);
            a(compoundDrawablesRelative[2], this.f1177g);
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:187:0x0352, code lost:
        if (r3 != null) goto L_0x0359;
     */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x00d1  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x00d6  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x00df  */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x00e4  */
    @android.annotation.SuppressLint({"NewApi"})
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void d(@androidx.annotation.Nullable android.util.AttributeSet r20, int r21) {
        /*
        // Method dump skipped, instructions count: 936
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.appcompat.widget.q.d(android.util.AttributeSet, int):void");
    }

    public void e(Context context, int i10) {
        String n10;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(i10, R$styleable.TextAppearance);
        m0 m0Var = new m0(context, obtainStyledAttributes);
        int i11 = R$styleable.TextAppearance_textAllCaps;
        if (m0Var.p(i11)) {
            this.f1171a.setAllCaps(m0Var.a(i11, false));
        }
        int i12 = R$styleable.TextAppearance_android_textSize;
        if (m0Var.p(i12) && m0Var.f(i12, -1) == 0) {
            this.f1171a.setTextSize(0, Constant.VOLUME_FLOAT_MIN);
        }
        h(context, m0Var);
        int i13 = R$styleable.TextAppearance_fontVariationSettings;
        if (m0Var.p(i13) && (n10 = m0Var.n(i13)) != null) {
            this.f1171a.setFontVariationSettings(n10);
        }
        obtainStyledAttributes.recycle();
        Typeface typeface = this.f1181l;
        if (typeface != null) {
            this.f1171a.setTypeface(typeface, this.f1179j);
        }
    }

    public void f(@Nullable ColorStateList colorStateList) {
        if (this.h == null) {
            this.h = new k0();
        }
        k0 k0Var = this.h;
        k0Var.f1132a = colorStateList;
        k0Var.f1135d = colorStateList != null;
        this.f1172b = k0Var;
        this.f1173c = k0Var;
        this.f1174d = k0Var;
        this.f1175e = k0Var;
        this.f1176f = k0Var;
        this.f1177g = k0Var;
    }

    public void g(@Nullable PorterDuff.Mode mode) {
        if (this.h == null) {
            this.h = new k0();
        }
        k0 k0Var = this.h;
        k0Var.f1133b = mode;
        k0Var.f1134c = mode != null;
        this.f1172b = k0Var;
        this.f1173c = k0Var;
        this.f1174d = k0Var;
        this.f1175e = k0Var;
        this.f1176f = k0Var;
        this.f1177g = k0Var;
    }

    public final void h(Context context, m0 m0Var) {
        String n10;
        this.f1179j = m0Var.j(R$styleable.TextAppearance_android_textStyle, this.f1179j);
        int j10 = m0Var.j(R$styleable.TextAppearance_android_textFontWeight, -1);
        this.f1180k = j10;
        boolean z10 = false;
        if (j10 != -1) {
            this.f1179j = (this.f1179j & 2) | 0;
        }
        int i10 = R$styleable.TextAppearance_android_fontFamily;
        if (m0Var.p(i10) || m0Var.p(R$styleable.TextAppearance_fontFamily)) {
            this.f1181l = null;
            int i11 = R$styleable.TextAppearance_fontFamily;
            if (m0Var.p(i11)) {
                i10 = i11;
            }
            int i12 = this.f1180k;
            int i13 = this.f1179j;
            if (!context.isRestricted()) {
                try {
                    Typeface i14 = m0Var.i(i10, this.f1179j, new a(i12, i13, new WeakReference(this.f1171a)));
                    if (i14 != null) {
                        if (this.f1180k != -1) {
                            this.f1181l = Typeface.create(Typeface.create(i14, 0), this.f1180k, (this.f1179j & 2) != 0);
                        } else {
                            this.f1181l = i14;
                        }
                    }
                    this.f1182m = this.f1181l == null;
                } catch (Resources.NotFoundException | UnsupportedOperationException unused) {
                }
            }
            if (this.f1181l == null && (n10 = m0Var.n(i10)) != null) {
                if (this.f1180k != -1) {
                    Typeface create = Typeface.create(n10, 0);
                    int i15 = this.f1180k;
                    if ((this.f1179j & 2) != 0) {
                        z10 = true;
                    }
                    this.f1181l = Typeface.create(create, i15, z10);
                    return;
                }
                this.f1181l = Typeface.create(n10, this.f1179j);
                return;
            }
            return;
        }
        int i16 = R$styleable.TextAppearance_android_typeface;
        if (m0Var.p(i16)) {
            this.f1182m = false;
            int j11 = m0Var.j(i16, 1);
            if (j11 == 1) {
                this.f1181l = Typeface.SANS_SERIF;
            } else if (j11 == 2) {
                this.f1181l = Typeface.SERIF;
            } else if (j11 == 3) {
                this.f1181l = Typeface.MONOSPACE;
            }
        }
    }
}
