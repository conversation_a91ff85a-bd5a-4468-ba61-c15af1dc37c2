package androidx.core.content.res;

import android.content.res.Resources;
import android.content.res.TypedArray;
import android.util.Base64;
import android.util.Xml;
import androidx.annotation.ArrayRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;
import androidx.core.R$styleable;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
public class FontResourcesParserCompat {

    @Retention(RetentionPolicy.SOURCE)
    public @interface FetchStrategy {
    }

    public interface a {
    }

    public static final class b implements a {
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public final c[] f1569a;

        public b(@NonNull c[] cVarArr) {
            this.f1569a = cVarArr;
        }
    }

    public static final class c {
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public final String f1570a;

        /* renamed from: b  reason: collision with root package name */
        public int f1571b;

        /* renamed from: c  reason: collision with root package name */
        public boolean f1572c;

        /* renamed from: d  reason: collision with root package name */
        public String f1573d;

        /* renamed from: e  reason: collision with root package name */
        public int f1574e;

        /* renamed from: f  reason: collision with root package name */
        public int f1575f;

        public c(@NonNull String str, int i10, boolean z10, @Nullable String str2, int i11, int i12) {
            this.f1570a = str;
            this.f1571b = i10;
            this.f1572c = z10;
            this.f1573d = str2;
            this.f1574e = i11;
            this.f1575f = i12;
        }
    }

    public static final class d implements a {
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public final g0.a f1576a;

        /* renamed from: b  reason: collision with root package name */
        public final int f1577b;

        /* renamed from: c  reason: collision with root package name */
        public final int f1578c;

        public d(@NonNull g0.a aVar, int i10, int i11) {
            this.f1576a = aVar;
            this.f1578c = i10;
            this.f1577b = i11;
        }
    }

    @Nullable
    public static a a(XmlPullParser xmlPullParser, Resources resources) throws XmlPullParserException, IOException {
        int next;
        do {
            next = xmlPullParser.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next == 2) {
            xmlPullParser.require(2, null, "font-family");
            if (xmlPullParser.getName().equals("font-family")) {
                TypedArray obtainAttributes = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), R$styleable.FontFamily);
                String string = obtainAttributes.getString(R$styleable.FontFamily_fontProviderAuthority);
                String string2 = obtainAttributes.getString(R$styleable.FontFamily_fontProviderPackage);
                String string3 = obtainAttributes.getString(R$styleable.FontFamily_fontProviderQuery);
                int resourceId = obtainAttributes.getResourceId(R$styleable.FontFamily_fontProviderCerts, 0);
                int integer = obtainAttributes.getInteger(R$styleable.FontFamily_fontProviderFetchStrategy, 1);
                int integer2 = obtainAttributes.getInteger(R$styleable.FontFamily_fontProviderFetchTimeout, 500);
                obtainAttributes.recycle();
                if (string == null || string2 == null || string3 == null) {
                    ArrayList arrayList = new ArrayList();
                    while (xmlPullParser.next() != 3) {
                        if (xmlPullParser.getEventType() == 2) {
                            if (xmlPullParser.getName().equals("font")) {
                                TypedArray obtainAttributes2 = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), R$styleable.FontFamilyFont);
                                int i10 = R$styleable.FontFamilyFont_fontWeight;
                                if (!obtainAttributes2.hasValue(i10)) {
                                    i10 = R$styleable.FontFamilyFont_android_fontWeight;
                                }
                                int i11 = obtainAttributes2.getInt(i10, 400);
                                int i12 = R$styleable.FontFamilyFont_fontStyle;
                                if (!obtainAttributes2.hasValue(i12)) {
                                    i12 = R$styleable.FontFamilyFont_android_fontStyle;
                                }
                                boolean z10 = 1 == obtainAttributes2.getInt(i12, 0);
                                int i13 = R$styleable.FontFamilyFont_ttcIndex;
                                if (!obtainAttributes2.hasValue(i13)) {
                                    i13 = R$styleable.FontFamilyFont_android_ttcIndex;
                                }
                                int i14 = R$styleable.FontFamilyFont_fontVariationSettings;
                                if (!obtainAttributes2.hasValue(i14)) {
                                    i14 = R$styleable.FontFamilyFont_android_fontVariationSettings;
                                }
                                String string4 = obtainAttributes2.getString(i14);
                                int i15 = obtainAttributes2.getInt(i13, 0);
                                int i16 = R$styleable.FontFamilyFont_font;
                                if (!obtainAttributes2.hasValue(i16)) {
                                    i16 = R$styleable.FontFamilyFont_android_font;
                                }
                                int resourceId2 = obtainAttributes2.getResourceId(i16, 0);
                                String string5 = obtainAttributes2.getString(i16);
                                obtainAttributes2.recycle();
                                while (xmlPullParser.next() != 3) {
                                    c(xmlPullParser);
                                }
                                arrayList.add(new c(string5, i11, z10, string4, i15, resourceId2));
                            } else {
                                c(xmlPullParser);
                            }
                        }
                    }
                    if (arrayList.isEmpty()) {
                        return null;
                    }
                    return new b((c[]) arrayList.toArray(new c[arrayList.size()]));
                }
                while (xmlPullParser.next() != 3) {
                    c(xmlPullParser);
                }
                return new d(new g0.a(string, string2, string3, b(resources, resourceId)), integer, integer2);
            }
            c(xmlPullParser);
            return null;
        }
        throw new XmlPullParserException("No start tag found");
    }

    public static List<List<byte[]>> b(Resources resources, @ArrayRes int i10) {
        if (i10 == 0) {
            return Collections.emptyList();
        }
        TypedArray obtainTypedArray = resources.obtainTypedArray(i10);
        try {
            if (obtainTypedArray.length() == 0) {
                return Collections.emptyList();
            }
            ArrayList arrayList = new ArrayList();
            if (obtainTypedArray.getType(0) == 1) {
                for (int i11 = 0; i11 < obtainTypedArray.length(); i11++) {
                    int resourceId = obtainTypedArray.getResourceId(i11, 0);
                    if (resourceId != 0) {
                        arrayList.add(d(resources.getStringArray(resourceId)));
                    }
                }
            } else {
                arrayList.add(d(resources.getStringArray(i10)));
            }
            obtainTypedArray.recycle();
            return arrayList;
        } finally {
            obtainTypedArray.recycle();
        }
    }

    public static void c(XmlPullParser xmlPullParser) throws XmlPullParserException, IOException {
        int i10 = 1;
        while (i10 > 0) {
            int next = xmlPullParser.next();
            if (next == 2) {
                i10++;
            } else if (next == 3) {
                i10--;
            }
        }
    }

    public static List<byte[]> d(String[] strArr) {
        ArrayList arrayList = new ArrayList();
        for (String str : strArr) {
            arrayList.add(Base64.decode(str, 0));
        }
        return arrayList;
    }
}
