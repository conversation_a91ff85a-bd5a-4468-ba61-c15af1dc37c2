package aa;

import java.io.IOException;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: BERSetParser */
public class h0 implements u {

    /* renamed from: a  reason: collision with root package name */
    public v f183a;

    public h0(v vVar) {
        this.f183a = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            throw new ASN1ParsingException(e10.getMessage(), e10);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new g0(this.f183a.c());
    }
}
