package aa;

import java.io.IOException;
import java.util.Enumeration;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: LazyConstructionEnumeration */
public class s1 implements Enumeration {

    /* renamed from: a  reason: collision with root package name */
    public i f227a;

    /* renamed from: b  reason: collision with root package name */
    public Object f228b = a();

    public s1(byte[] bArr) {
        this.f227a = new i(bArr, true);
    }

    public final Object a() {
        try {
            return this.f227a.h();
        } catch (IOException e10) {
            throw new ASN1ParsingException("malformed DER construction: " + e10, e10);
        }
    }

    public boolean hasMoreElements() {
        return this.f228b != null;
    }

    @Override // java.util.Enumeration
    public Object nextElement() {
        Object obj = this.f228b;
        this.f228b = a();
        return obj;
    }
}
