package androidx.recyclerview.widget;

import android.graphics.Rect;
import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import com.xiaomi.mitv.pie.EventResultPersister;

/* compiled from: OrientationHelper */
public abstract class v {

    /* renamed from: a  reason: collision with root package name */
    public final RecyclerView.j f2473a;

    /* renamed from: b  reason: collision with root package name */
    public int f2474b = EventResultPersister.GENERATE_NEW_ID;

    /* renamed from: c  reason: collision with root package name */
    public final Rect f2475c = new Rect();

    public v(RecyclerView.j jVar, t tVar) {
        this.f2473a = jVar;
    }

    public static v a(RecyclerView.j jVar, int i10) {
        if (i10 == 0) {
            return new t(jVar);
        }
        if (i10 == 1) {
            return new u(jVar);
        }
        throw new IllegalArgumentException("invalid orientation");
    }

    public abstract int b(View view);

    public abstract int c(View view);

    public abstract int d(View view);

    public abstract int e(View view);

    public abstract int f();

    public abstract int g();

    public abstract int h();

    public abstract int i();

    public abstract int j();

    public abstract int k();

    public abstract int l();

    public int m() {
        if (Integer.MIN_VALUE == this.f2474b) {
            return 0;
        }
        return l() - this.f2474b;
    }

    public abstract int n(View view);

    public abstract int o(View view);

    public abstract void p(int i10);
}
