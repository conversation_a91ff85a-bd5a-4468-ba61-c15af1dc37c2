package androidx.constraintlayout.solver;

import androidx.constraintlayout.solver.SolverVariable;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import java.util.Arrays;

/* compiled from: ArrayLinkedVariables */
public class a {

    /* renamed from: a  reason: collision with root package name */
    public int f1249a = 0;

    /* renamed from: b  reason: collision with root package name */
    public final b f1250b;

    /* renamed from: c  reason: collision with root package name */
    public final v.a f1251c;

    /* renamed from: d  reason: collision with root package name */
    public int f1252d = 8;

    /* renamed from: e  reason: collision with root package name */
    public int[] f1253e = new int[8];

    /* renamed from: f  reason: collision with root package name */
    public int[] f1254f = new int[8];

    /* renamed from: g  reason: collision with root package name */
    public float[] f1255g = new float[8];
    public int h = -1;

    /* renamed from: i  reason: collision with root package name */
    public int f1256i = -1;

    /* renamed from: j  reason: collision with root package name */
    public boolean f1257j = false;

    public a(b bVar, v.a aVar) {
        this.f1250b = bVar;
        this.f1251c = aVar;
    }

    public final void a(SolverVariable solverVariable, float f10, boolean z10) {
        if (f10 != Constant.VOLUME_FLOAT_MIN) {
            int i10 = this.h;
            if (i10 == -1) {
                this.h = 0;
                this.f1255g[0] = f10;
                this.f1253e[0] = solverVariable.f1240a;
                this.f1254f[0] = -1;
                solverVariable.f1247i++;
                solverVariable.a(this.f1250b);
                this.f1249a++;
                if (!this.f1257j) {
                    int i11 = this.f1256i + 1;
                    this.f1256i = i11;
                    int[] iArr = this.f1253e;
                    if (i11 >= iArr.length) {
                        this.f1257j = true;
                        this.f1256i = iArr.length - 1;
                        return;
                    }
                    return;
                }
                return;
            }
            int i12 = 0;
            int i13 = -1;
            while (i10 != -1 && i12 < this.f1249a) {
                int[] iArr2 = this.f1253e;
                int i14 = iArr2[i10];
                int i15 = solverVariable.f1240a;
                if (i14 == i15) {
                    float[] fArr = this.f1255g;
                    fArr[i10] = fArr[i10] + f10;
                    if (fArr[i10] == Constant.VOLUME_FLOAT_MIN) {
                        if (i10 == this.h) {
                            this.h = this.f1254f[i10];
                        } else {
                            int[] iArr3 = this.f1254f;
                            iArr3[i13] = iArr3[i10];
                        }
                        if (z10) {
                            solverVariable.b(this.f1250b);
                        }
                        if (this.f1257j) {
                            this.f1256i = i10;
                        }
                        solverVariable.f1247i--;
                        this.f1249a--;
                        return;
                    }
                    return;
                }
                if (iArr2[i10] < i15) {
                    i13 = i10;
                }
                i10 = this.f1254f[i10];
                i12++;
            }
            int i16 = this.f1256i;
            int i17 = i16 + 1;
            if (this.f1257j) {
                int[] iArr4 = this.f1253e;
                if (iArr4[i16] != -1) {
                    i16 = iArr4.length;
                }
            } else {
                i16 = i17;
            }
            int[] iArr5 = this.f1253e;
            if (i16 >= iArr5.length && this.f1249a < iArr5.length) {
                int i18 = 0;
                while (true) {
                    int[] iArr6 = this.f1253e;
                    if (i18 >= iArr6.length) {
                        break;
                    } else if (iArr6[i18] == -1) {
                        i16 = i18;
                        break;
                    } else {
                        i18++;
                    }
                }
            }
            int[] iArr7 = this.f1253e;
            if (i16 >= iArr7.length) {
                i16 = iArr7.length;
                int i19 = this.f1252d * 2;
                this.f1252d = i19;
                this.f1257j = false;
                this.f1256i = i16 - 1;
                this.f1255g = Arrays.copyOf(this.f1255g, i19);
                this.f1253e = Arrays.copyOf(this.f1253e, this.f1252d);
                this.f1254f = Arrays.copyOf(this.f1254f, this.f1252d);
            }
            this.f1253e[i16] = solverVariable.f1240a;
            this.f1255g[i16] = f10;
            if (i13 != -1) {
                int[] iArr8 = this.f1254f;
                iArr8[i16] = iArr8[i13];
                iArr8[i13] = i16;
            } else {
                this.f1254f[i16] = this.h;
                this.h = i16;
            }
            solverVariable.f1247i++;
            solverVariable.a(this.f1250b);
            this.f1249a++;
            if (!this.f1257j) {
                this.f1256i++;
            }
            int i20 = this.f1256i;
            int[] iArr9 = this.f1253e;
            if (i20 >= iArr9.length) {
                this.f1257j = true;
                this.f1256i = iArr9.length - 1;
            }
        }
    }

    public final void b() {
        int i10 = this.h;
        int i11 = 0;
        while (i10 != -1 && i11 < this.f1249a) {
            SolverVariable solverVariable = this.f1251c.f10542c[this.f1253e[i10]];
            if (solverVariable != null) {
                solverVariable.b(this.f1250b);
            }
            i10 = this.f1254f[i10];
            i11++;
        }
        this.h = -1;
        this.f1256i = -1;
        this.f1257j = false;
        this.f1249a = 0;
    }

    public final float c(SolverVariable solverVariable) {
        int i10 = this.h;
        int i11 = 0;
        while (i10 != -1 && i11 < this.f1249a) {
            if (this.f1253e[i10] == solverVariable.f1240a) {
                return this.f1255g[i10];
            }
            i10 = this.f1254f[i10];
            i11++;
        }
        return Constant.VOLUME_FLOAT_MIN;
    }

    public SolverVariable d(boolean[] zArr, SolverVariable solverVariable) {
        SolverVariable.Type type;
        int i10 = this.h;
        int i11 = 0;
        SolverVariable solverVariable2 = null;
        float f10 = 0.0f;
        while (i10 != -1 && i11 < this.f1249a) {
            float[] fArr = this.f1255g;
            if (fArr[i10] < Constant.VOLUME_FLOAT_MIN) {
                SolverVariable solverVariable3 = this.f1251c.f10542c[this.f1253e[i10]];
                if ((zArr == null || !zArr[solverVariable3.f1240a]) && solverVariable3 != solverVariable && ((type = solverVariable3.f1245f) == SolverVariable.Type.SLACK || type == SolverVariable.Type.ERROR)) {
                    float f11 = fArr[i10];
                    if (f11 < f10) {
                        f10 = f11;
                        solverVariable2 = solverVariable3;
                    }
                }
            }
            i10 = this.f1254f[i10];
            i11++;
        }
        return solverVariable2;
    }

    public final SolverVariable e(int i10) {
        int i11 = this.h;
        int i12 = 0;
        while (i11 != -1 && i12 < this.f1249a) {
            if (i12 == i10) {
                return this.f1251c.f10542c[this.f1253e[i11]];
            }
            i11 = this.f1254f[i11];
            i12++;
        }
        return null;
    }

    public final float f(int i10) {
        int i11 = this.h;
        int i12 = 0;
        while (i11 != -1 && i12 < this.f1249a) {
            if (i12 == i10) {
                return this.f1255g[i11];
            }
            i11 = this.f1254f[i11];
            i12++;
        }
        return Constant.VOLUME_FLOAT_MIN;
    }

    public final boolean g(SolverVariable solverVariable) {
        return solverVariable.f1247i <= 1;
    }

    public final void h(SolverVariable solverVariable, float f10) {
        if (f10 == Constant.VOLUME_FLOAT_MIN) {
            i(solverVariable, true);
            return;
        }
        int i10 = this.h;
        if (i10 == -1) {
            this.h = 0;
            this.f1255g[0] = f10;
            this.f1253e[0] = solverVariable.f1240a;
            this.f1254f[0] = -1;
            solverVariable.f1247i++;
            solverVariable.a(this.f1250b);
            this.f1249a++;
            if (!this.f1257j) {
                int i11 = this.f1256i + 1;
                this.f1256i = i11;
                int[] iArr = this.f1253e;
                if (i11 >= iArr.length) {
                    this.f1257j = true;
                    this.f1256i = iArr.length - 1;
                    return;
                }
                return;
            }
            return;
        }
        int i12 = 0;
        int i13 = -1;
        while (i10 != -1 && i12 < this.f1249a) {
            int[] iArr2 = this.f1253e;
            int i14 = iArr2[i10];
            int i15 = solverVariable.f1240a;
            if (i14 == i15) {
                this.f1255g[i10] = f10;
                return;
            }
            if (iArr2[i10] < i15) {
                i13 = i10;
            }
            i10 = this.f1254f[i10];
            i12++;
        }
        int i16 = this.f1256i;
        int i17 = i16 + 1;
        if (this.f1257j) {
            int[] iArr3 = this.f1253e;
            if (iArr3[i16] != -1) {
                i16 = iArr3.length;
            }
        } else {
            i16 = i17;
        }
        int[] iArr4 = this.f1253e;
        if (i16 >= iArr4.length && this.f1249a < iArr4.length) {
            int i18 = 0;
            while (true) {
                int[] iArr5 = this.f1253e;
                if (i18 >= iArr5.length) {
                    break;
                } else if (iArr5[i18] == -1) {
                    i16 = i18;
                    break;
                } else {
                    i18++;
                }
            }
        }
        int[] iArr6 = this.f1253e;
        if (i16 >= iArr6.length) {
            i16 = iArr6.length;
            int i19 = this.f1252d * 2;
            this.f1252d = i19;
            this.f1257j = false;
            this.f1256i = i16 - 1;
            this.f1255g = Arrays.copyOf(this.f1255g, i19);
            this.f1253e = Arrays.copyOf(this.f1253e, this.f1252d);
            this.f1254f = Arrays.copyOf(this.f1254f, this.f1252d);
        }
        this.f1253e[i16] = solverVariable.f1240a;
        this.f1255g[i16] = f10;
        if (i13 != -1) {
            int[] iArr7 = this.f1254f;
            iArr7[i16] = iArr7[i13];
            iArr7[i13] = i16;
        } else {
            this.f1254f[i16] = this.h;
            this.h = i16;
        }
        solverVariable.f1247i++;
        solverVariable.a(this.f1250b);
        int i20 = this.f1249a + 1;
        this.f1249a = i20;
        if (!this.f1257j) {
            this.f1256i++;
        }
        int[] iArr8 = this.f1253e;
        if (i20 >= iArr8.length) {
            this.f1257j = true;
        }
        if (this.f1256i >= iArr8.length) {
            this.f1257j = true;
            this.f1256i = iArr8.length - 1;
        }
    }

    public final float i(SolverVariable solverVariable, boolean z10) {
        int i10 = this.h;
        if (i10 == -1) {
            return Constant.VOLUME_FLOAT_MIN;
        }
        int i11 = 0;
        int i12 = -1;
        while (i10 != -1 && i11 < this.f1249a) {
            if (this.f1253e[i10] == solverVariable.f1240a) {
                if (i10 == this.h) {
                    this.h = this.f1254f[i10];
                } else {
                    int[] iArr = this.f1254f;
                    iArr[i12] = iArr[i10];
                }
                if (z10) {
                    solverVariable.b(this.f1250b);
                }
                solverVariable.f1247i--;
                this.f1249a--;
                this.f1253e[i10] = -1;
                if (this.f1257j) {
                    this.f1256i = i10;
                }
                return this.f1255g[i10];
            }
            i11++;
            i12 = i10;
            i10 = this.f1254f[i10];
        }
        return Constant.VOLUME_FLOAT_MIN;
    }

    public String toString() {
        int i10 = this.h;
        String str = "";
        int i11 = 0;
        while (i10 != -1 && i11 < this.f1249a) {
            StringBuilder a10 = f.a(p.f.a(str, " -> "));
            a10.append(this.f1255g[i10]);
            a10.append(" : ");
            StringBuilder a11 = f.a(a10.toString());
            a11.append(this.f1251c.f10542c[this.f1253e[i10]]);
            str = a11.toString();
            i10 = this.f1254f[i10];
            i11++;
        }
        return str;
    }
}
