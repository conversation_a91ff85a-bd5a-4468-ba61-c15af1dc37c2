package a4;

import android.os.Handler;
import android.os.Looper;
import com.duokan.airkan.common.Log;

public class a implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ f f16a;

    public a(f fVar) {
        this.f16a = fVar;
    }

    public void run() {
        Looper.prepare();
        this.f16a.f31l = new Handler();
        f fVar = this.f16a;
        Handler handler = fVar.f31l;
        if (handler != null) {
            handler.post(new c(fVar));
        } else {
            Log.e("UDTDiscoverManager", "Handler not ready, start JmDNS service failed!");
        }
        Looper.loop();
    }
}
