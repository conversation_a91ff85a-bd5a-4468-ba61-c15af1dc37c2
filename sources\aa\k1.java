package aa;

import java.io.IOException;

/* compiled from: DLBitString */
public class k1 extends b {
    public k1(byte[] bArr, int i10) {
        super(bArr, i10);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        byte[] bArr = this.f161a;
        int length = bArr.length + 1;
        byte[] bArr2 = new byte[length];
        bArr2[0] = (byte) this.f162b;
        System.arraycopy(bArr, 0, bArr2, 1, length - 1);
        pVar.e(3, bArr2);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f161a.length + 1) + 1 + this.f161a.length + 1;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
