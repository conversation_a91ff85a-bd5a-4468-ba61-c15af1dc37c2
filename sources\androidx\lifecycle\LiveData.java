package androidx.lifecycle;

import android.util.Log;
import android.view.View;
import androidx.activity.result.c;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.l;
import androidx.lifecycle.Lifecycle;
import java.util.Map;
import java.util.Objects;

public abstract class LiveData<T> {

    /* renamed from: j  reason: collision with root package name */
    public static final Object f2036j = new Object();

    /* renamed from: a  reason: collision with root package name */
    public final Object f2037a = new Object();

    /* renamed from: b  reason: collision with root package name */
    public s.b<m<? super T>, LiveData<T>.b> f2038b = new s.b<>();

    /* renamed from: c  reason: collision with root package name */
    public int f2039c = 0;

    /* renamed from: d  reason: collision with root package name */
    public boolean f2040d;

    /* renamed from: e  reason: collision with root package name */
    public volatile Object f2041e;

    /* renamed from: f  reason: collision with root package name */
    public volatile Object f2042f;

    /* renamed from: g  reason: collision with root package name */
    public int f2043g;
    public boolean h;

    /* renamed from: i  reason: collision with root package name */
    public boolean f2044i;

    public class LifecycleBoundObserver extends LiveData<T>.b implements e {
        @NonNull

        /* renamed from: e  reason: collision with root package name */
        public final g f2045e;

        /* renamed from: f  reason: collision with root package name */
        public final /* synthetic */ LiveData f2046f;

        @Override // androidx.lifecycle.e
        public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
            Lifecycle.State state = ((h) this.f2045e.getLifecycle()).f2067b;
            if (state == Lifecycle.State.DESTROYED) {
                this.f2046f.g(this.f2047a);
                return;
            }
            Lifecycle.State state2 = null;
            while (state2 != state) {
                h(((h) this.f2045e.getLifecycle()).f2067b.isAtLeast(Lifecycle.State.STARTED));
                state2 = state;
                state = ((h) this.f2045e.getLifecycle()).f2067b;
            }
        }

        @Override // androidx.lifecycle.LiveData.b
        public void i() {
            h hVar = (h) this.f2045e.getLifecycle();
            hVar.d("removeObserver");
            hVar.f2066a.f(this);
        }

        @Override // androidx.lifecycle.LiveData.b
        public boolean j() {
            return ((h) this.f2045e.getLifecycle()).f2067b.isAtLeast(Lifecycle.State.STARTED);
        }
    }

    public class a extends LiveData<T>.b {
        public a(LiveData liveData, m<? super T> mVar) {
            super(mVar);
        }

        @Override // androidx.lifecycle.LiveData.b
        public boolean j() {
            return true;
        }
    }

    public abstract class b {

        /* renamed from: a  reason: collision with root package name */
        public final m<? super T> f2047a;

        /* renamed from: b  reason: collision with root package name */
        public boolean f2048b;

        /* renamed from: c  reason: collision with root package name */
        public int f2049c = -1;

        public b(m<? super T> mVar) {
            this.f2047a = mVar;
        }

        public void h(boolean z10) {
            if (z10 != this.f2048b) {
                this.f2048b = z10;
                LiveData liveData = LiveData.this;
                int i10 = z10 ? 1 : -1;
                int i11 = liveData.f2039c;
                liveData.f2039c = i10 + i11;
                if (!liveData.f2040d) {
                    liveData.f2040d = true;
                    while (true) {
                        try {
                            int i12 = liveData.f2039c;
                            if (i11 == i12) {
                                break;
                            }
                            boolean z11 = i11 == 0 && i12 > 0;
                            boolean z12 = i11 > 0 && i12 == 0;
                            if (z11) {
                                liveData.e();
                            } else if (z12) {
                                liveData.f();
                            }
                            i11 = i12;
                        } finally {
                            liveData.f2040d = false;
                        }
                    }
                }
                if (this.f2048b) {
                    LiveData.this.c(this);
                }
            }
        }

        public void i() {
        }

        public abstract boolean j();
    }

    public LiveData() {
        Object obj = f2036j;
        this.f2042f = obj;
        this.f2041e = obj;
        this.f2043g = -1;
    }

    public static void a(String str) {
        if (!r.a.b().a()) {
            throw new IllegalStateException(c.a("Cannot invoke ", str, " on a background thread"));
        }
    }

    public final void b(LiveData<T>.b bVar) {
        if (bVar.f2048b) {
            if (!bVar.j()) {
                bVar.h(false);
                return;
            }
            int i10 = bVar.f2049c;
            int i11 = this.f2043g;
            if (i10 < i11) {
                bVar.f2049c = i11;
                m<? super T> mVar = bVar.f2047a;
                Object obj = this.f2041e;
                l.d dVar = (l.d) mVar;
                Objects.requireNonNull(dVar);
                if (((g) obj) != null) {
                    l lVar = l.this;
                    if (lVar.P0) {
                        View O = lVar.O();
                        if (O.getParent() != null) {
                            throw new IllegalStateException("DialogFragment can not be attached to a container view");
                        } else if (l.this.T0 != null) {
                            if (FragmentManager.O(3)) {
                                Log.d("FragmentManager", "DialogFragment " + dVar + " setting the content view on " + l.this.T0);
                            }
                            l.this.T0.setContentView(O);
                        }
                    }
                }
            }
        }
    }

    public void c(@Nullable LiveData<T>.b bVar) {
        if (this.h) {
            this.f2044i = true;
            return;
        }
        this.h = true;
        do {
            this.f2044i = false;
            if (bVar == null) {
                s.b<K, V>.d c10 = this.f2038b.c();
                while (c10.hasNext()) {
                    b((b) ((Map.Entry) c10.next()).getValue());
                    if (this.f2044i) {
                        break;
                    }
                }
            } else {
                b(bVar);
                bVar = null;
            }
        } while (this.f2044i);
        this.h = false;
    }

    @MainThread
    public void d(@NonNull m<? super T> mVar) {
        a("observeForever");
        a aVar = new a(this, mVar);
        LiveData<T>.b e10 = this.f2038b.e(mVar, aVar);
        if (e10 instanceof LifecycleBoundObserver) {
            throw new IllegalArgumentException("Cannot add the same observer with different lifecycles");
        } else if (e10 == null) {
            aVar.h(true);
        }
    }

    public void e() {
    }

    public void f() {
    }

    @MainThread
    public void g(@NonNull m<? super T> mVar) {
        a("removeObserver");
        LiveData<T>.b f10 = this.f2038b.f(mVar);
        if (f10 != null) {
            f10.i();
            f10.h(false);
        }
    }
}
