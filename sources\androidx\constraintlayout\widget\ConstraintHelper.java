package androidx.constraintlayout.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.constraintlayout.solver.widgets.ConstraintWidget;
import androidx.constraintlayout.widget.ConstraintLayout;
import java.util.Arrays;
import w.a;

public abstract class ConstraintHelper extends View {

    /* renamed from: a  reason: collision with root package name */
    public int[] f1398a;

    /* renamed from: b  reason: collision with root package name */
    public int f1399b;

    /* renamed from: c  reason: collision with root package name */
    public Context f1400c;

    /* renamed from: d  reason: collision with root package name */
    public a f1401d;

    /* renamed from: e  reason: collision with root package name */
    public String f1402e;

    public ConstraintHelper(Context context) {
        super(context);
        this.f1398a = new int[32];
        this.f1400c = context;
        b(null);
    }

    private void setIds(String str) {
        if (str != null) {
            int i10 = 0;
            while (true) {
                int indexOf = str.indexOf(44, i10);
                if (indexOf == -1) {
                    a(str.substring(i10));
                    return;
                } else {
                    a(str.substring(i10, indexOf));
                    i10 = indexOf + 1;
                }
            }
        }
    }

    public final void a(String str) {
        int i10;
        Object b10;
        if (str != null && this.f1400c != null) {
            String trim = str.trim();
            try {
                i10 = R$id.class.getField(trim).getInt(null);
            } catch (Exception unused) {
                i10 = 0;
            }
            if (i10 == 0) {
                i10 = this.f1400c.getResources().getIdentifier(trim, "id", this.f1400c.getPackageName());
            }
            if (i10 == 0 && isInEditMode() && (getParent() instanceof ConstraintLayout) && (b10 = ((ConstraintLayout) getParent()).b(0, trim)) != null && (b10 instanceof Integer)) {
                i10 = ((Integer) b10).intValue();
            }
            if (i10 != 0) {
                setTag(i10, null);
                return;
            }
            Log.w("ConstraintHelper", "Could not find id of \"" + trim + "\"");
        }
    }

    public void b(AttributeSet attributeSet) {
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attributeSet, R$styleable.ConstraintLayout_Layout);
            int indexCount = obtainStyledAttributes.getIndexCount();
            for (int i10 = 0; i10 < indexCount; i10++) {
                int index = obtainStyledAttributes.getIndex(i10);
                if (index == R$styleable.ConstraintLayout_Layout_constraint_referenced_ids) {
                    String string = obtainStyledAttributes.getString(index);
                    this.f1402e = string;
                    setIds(string);
                }
            }
        }
    }

    public void c(ConstraintLayout constraintLayout) {
    }

    public void d(ConstraintLayout constraintLayout) {
        if (isInEditMode()) {
            setIds(this.f1402e);
        }
        a aVar = this.f1401d;
        if (aVar != null) {
            aVar.f10632j0 = 0;
            for (int i10 = 0; i10 < this.f1399b; i10++) {
                View view = constraintLayout.f1403a.get(this.f1398a[i10]);
                if (view != null) {
                    a aVar2 = this.f1401d;
                    ConstraintWidget d10 = constraintLayout.d(view);
                    int i11 = aVar2.f10632j0 + 1;
                    ConstraintWidget[] constraintWidgetArr = aVar2.f10631i0;
                    if (i11 > constraintWidgetArr.length) {
                        aVar2.f10631i0 = (ConstraintWidget[]) Arrays.copyOf(constraintWidgetArr, constraintWidgetArr.length * 2);
                    }
                    ConstraintWidget[] constraintWidgetArr2 = aVar2.f10631i0;
                    int i12 = aVar2.f10632j0;
                    constraintWidgetArr2[i12] = d10;
                    aVar2.f10632j0 = i12 + 1;
                }
            }
        }
    }

    public void e() {
        if (this.f1401d != null) {
            ViewGroup.LayoutParams layoutParams = getLayoutParams();
            if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                ((ConstraintLayout.LayoutParams) layoutParams).f1437k0 = this.f1401d;
            }
        }
    }

    public int[] getReferencedIds() {
        return Arrays.copyOf(this.f1398a, this.f1399b);
    }

    public void onDraw(Canvas canvas) {
    }

    public void onMeasure(int i10, int i11) {
        setMeasuredDimension(0, 0);
    }

    public void setReferencedIds(int[] iArr) {
        this.f1399b = 0;
        for (int i10 : iArr) {
            setTag(i10, null);
        }
    }

    public void setTag(int i10, Object obj) {
        int i11 = this.f1399b + 1;
        int[] iArr = this.f1398a;
        if (i11 > iArr.length) {
            this.f1398a = Arrays.copyOf(iArr, iArr.length * 2);
        }
        int[] iArr2 = this.f1398a;
        int i12 = this.f1399b;
        iArr2[i12] = i10;
        this.f1399b = i12 + 1;
    }

    public ConstraintHelper(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.f1398a = new int[32];
        this.f1400c = context;
        b(attributeSet);
    }

    public ConstraintHelper(Context context, AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        this.f1398a = new int[32];
        this.f1400c = context;
        b(attributeSet);
    }
}
