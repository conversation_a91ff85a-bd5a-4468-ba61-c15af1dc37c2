package aa;

import java.io.IOException;

/* compiled from: ASN1Primitive */
public abstract class q extends l {
    public static q j(byte[] bArr) throws IOException {
        i iVar = new i(bArr);
        try {
            q h = iVar.h();
            if (iVar.available() == 0) {
                return h;
            }
            throw new IOException("Extra data detected in stream");
        } catch (ClassCastException unused) {
            throw new IOException("cannot recognise object in stream");
        }
    }

    @Override // aa.l, aa.e
    public q c() {
        return this;
    }

    @Override // aa.l
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        return (obj instanceof e) && g(((e) obj).c());
    }

    public abstract boolean g(q qVar);

    public abstract void h(p pVar) throws IOException;

    public abstract int i() throws IOException;

    public abstract boolean k();

    public q l() {
        return this;
    }

    public q m() {
        return this;
    }
}
