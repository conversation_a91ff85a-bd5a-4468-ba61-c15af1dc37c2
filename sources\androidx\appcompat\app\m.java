package androidx.appcompat.app;

import android.content.Context;
import android.content.res.Configuration;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.h;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.o0;
import androidx.appcompat.widget.t;
import androidx.core.view.ViewCompat;
import java.util.ArrayList;
import java.util.WeakHashMap;
import p.i;

/* compiled from: ToolbarActionBar */
public class m extends ActionBar {

    /* renamed from: a  reason: collision with root package name */
    public t f459a;

    /* renamed from: b  reason: collision with root package name */
    public boolean f460b;

    /* renamed from: c  reason: collision with root package name */
    public Window.Callback f461c;

    /* renamed from: d  reason: collision with root package name */
    public boolean f462d;

    /* renamed from: e  reason: collision with root package name */
    public boolean f463e;

    /* renamed from: f  reason: collision with root package name */
    public ArrayList<ActionBar.a> f464f = new ArrayList<>();

    /* renamed from: g  reason: collision with root package name */
    public final Runnable f465g = new a();

    /* compiled from: ToolbarActionBar */
    public class a implements Runnable {
        public a() {
        }

        public void run() {
            m mVar = m.this;
            Menu q10 = mVar.q();
            androidx.appcompat.view.menu.d dVar = q10 instanceof androidx.appcompat.view.menu.d ? (androidx.appcompat.view.menu.d) q10 : null;
            if (dVar != null) {
                dVar.A();
            }
            try {
                q10.clear();
                if (!mVar.f461c.onCreatePanelMenu(0, q10) || !mVar.f461c.onPreparePanel(0, null, q10)) {
                    q10.clear();
                }
            } finally {
                if (dVar != null) {
                    dVar.z();
                }
            }
        }
    }

    /* compiled from: ToolbarActionBar */
    public class b implements Toolbar.e {
        public b() {
        }
    }

    /* compiled from: ToolbarActionBar */
    public final class c implements h.a {

        /* renamed from: a  reason: collision with root package name */
        public boolean f468a;

        public c() {
        }

        @Override // androidx.appcompat.view.menu.h.a
        public void a(@NonNull androidx.appcompat.view.menu.d dVar, boolean z10) {
            if (!this.f468a) {
                this.f468a = true;
                m.this.f459a.h();
                Window.Callback callback = m.this.f461c;
                if (callback != null) {
                    callback.onPanelClosed(108, dVar);
                }
                this.f468a = false;
            }
        }

        @Override // androidx.appcompat.view.menu.h.a
        public boolean b(@NonNull androidx.appcompat.view.menu.d dVar) {
            Window.Callback callback = m.this.f461c;
            if (callback == null) {
                return false;
            }
            callback.onMenuOpened(108, dVar);
            return true;
        }
    }

    /* compiled from: ToolbarActionBar */
    public final class d implements d.a {
        public d() {
        }

        @Override // androidx.appcompat.view.menu.d.a
        public boolean a(@NonNull androidx.appcompat.view.menu.d dVar, @NonNull MenuItem menuItem) {
            return false;
        }

        @Override // androidx.appcompat.view.menu.d.a
        public void b(@NonNull androidx.appcompat.view.menu.d dVar) {
            m mVar = m.this;
            if (mVar.f461c == null) {
                return;
            }
            if (mVar.f459a.b()) {
                m.this.f461c.onPanelClosed(108, dVar);
            } else if (m.this.f461c.onPreparePanel(0, null, dVar)) {
                m.this.f461c.onMenuOpened(108, dVar);
            }
        }
    }

    /* compiled from: ToolbarActionBar */
    public class e extends i {
        public e(Window.Callback callback) {
            super(callback);
        }

        @Override // p.i
        public View onCreatePanelView(int i10) {
            if (i10 == 0) {
                return new View(m.this.f459a.getContext());
            }
            return this.f9554a.onCreatePanelView(i10);
        }

        @Override // p.i
        public boolean onPreparePanel(int i10, View view, Menu menu) {
            boolean onPreparePanel = this.f9554a.onPreparePanel(i10, view, menu);
            if (onPreparePanel) {
                m mVar = m.this;
                if (!mVar.f460b) {
                    mVar.f459a.c();
                    m.this.f460b = true;
                }
            }
            return onPreparePanel;
        }
    }

    public m(Toolbar toolbar, CharSequence charSequence, Window.Callback callback) {
        b bVar = new b();
        this.f459a = new o0(toolbar, false);
        e eVar = new e(callback);
        this.f461c = eVar;
        this.f459a.setWindowCallback(eVar);
        toolbar.setOnMenuItemClickListener(bVar);
        this.f459a.setWindowTitle(charSequence);
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean a() {
        return this.f459a.e();
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean b() {
        if (!this.f459a.m()) {
            return false;
        }
        this.f459a.collapseActionView();
        return true;
    }

    @Override // androidx.appcompat.app.ActionBar
    public void c(boolean z10) {
        if (z10 != this.f463e) {
            this.f463e = z10;
            int size = this.f464f.size();
            for (int i10 = 0; i10 < size; i10++) {
                this.f464f.get(i10).a(z10);
            }
        }
    }

    @Override // androidx.appcompat.app.ActionBar
    public int d() {
        return this.f459a.o();
    }

    @Override // androidx.appcompat.app.ActionBar
    public Context e() {
        return this.f459a.getContext();
    }

    @Override // androidx.appcompat.app.ActionBar
    public void f() {
        this.f459a.setVisibility(8);
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean g() {
        this.f459a.k().removeCallbacks(this.f465g);
        ViewGroup k10 = this.f459a.k();
        Runnable runnable = this.f465g;
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        k10.postOnAnimation(runnable);
        return true;
    }

    @Override // androidx.appcompat.app.ActionBar
    public void h(Configuration configuration) {
    }

    @Override // androidx.appcompat.app.ActionBar
    public void i() {
        this.f459a.k().removeCallbacks(this.f465g);
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean j(int i10, KeyEvent keyEvent) {
        Menu q10 = q();
        if (q10 == null) {
            return false;
        }
        boolean z10 = true;
        if (KeyCharacterMap.load(keyEvent != null ? keyEvent.getDeviceId() : -1).getKeyboardType() == 1) {
            z10 = false;
        }
        q10.setQwertyMode(z10);
        return q10.performShortcut(i10, keyEvent, 0);
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean k(KeyEvent keyEvent) {
        if (keyEvent.getAction() == 1) {
            this.f459a.f();
        }
        return true;
    }

    @Override // androidx.appcompat.app.ActionBar
    public boolean l() {
        return this.f459a.f();
    }

    @Override // androidx.appcompat.app.ActionBar
    public void m(boolean z10) {
    }

    @Override // androidx.appcompat.app.ActionBar
    public void n(boolean z10) {
    }

    @Override // androidx.appcompat.app.ActionBar
    public void o(CharSequence charSequence) {
        this.f459a.setWindowTitle(charSequence);
    }

    public final Menu q() {
        if (!this.f462d) {
            this.f459a.i(new c(), new d());
            this.f462d = true;
        }
        return this.f459a.p();
    }
}
