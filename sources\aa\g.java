package aa;

import java.io.IOException;
import mb.a;

/* compiled from: ASN1Enumerated */
public class g extends q {

    /* renamed from: b  reason: collision with root package name */
    public static g[] f179b = new g[12];

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f180a;

    public g(byte[] bArr) {
        this.f180a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof g)) {
            return false;
        }
        return a.a(this.f180a, ((g) qVar).f180a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(10, this.f180a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f180a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f180a.length) + 1 + this.f180a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
