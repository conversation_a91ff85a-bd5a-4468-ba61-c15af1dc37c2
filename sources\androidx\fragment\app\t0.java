package androidx.fragment.app;

import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.i;
import androidx.core.view.ViewCompat;
import androidx.fragment.R$id;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import j0.m;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;

/* compiled from: SpecialEffectsController */
public abstract class t0 {

    /* renamed from: a  reason: collision with root package name */
    public final ViewGroup f2001a;

    /* renamed from: b  reason: collision with root package name */
    public final ArrayList<b> f2002b = new ArrayList<>();

    /* renamed from: c  reason: collision with root package name */
    public final ArrayList<b> f2003c = new ArrayList<>();

    /* renamed from: d  reason: collision with root package name */
    public boolean f2004d = false;

    /* renamed from: e  reason: collision with root package name */
    public boolean f2005e = false;

    /* compiled from: SpecialEffectsController */
    public static class a extends b {
        @NonNull
        public final b0 h;

        public a(@NonNull int i10, @NonNull int i11, @NonNull b0 b0Var, @NonNull f0.a aVar) {
            super(i10, i11, b0Var.f1852c, aVar);
            this.h = b0Var;
        }

        @Override // androidx.fragment.app.t0.b
        public void b() {
            super.b();
            this.h.k();
        }

        @Override // androidx.fragment.app.t0.b
        public void d() {
            float f10;
            Fragment fragment = this.h.f1852c;
            View findFocus = fragment.f1747u0.findFocus();
            if (findFocus != null) {
                fragment.c().f1767k = findFocus;
                if (FragmentManager.O(2)) {
                    Log.v("FragmentManager", "requestFocus: Saved focused view " + findFocus + " for Fragment " + fragment);
                }
            }
            if (this.f2007b == 2) {
                View O = this.f2008c.O();
                if (O.getParent() == null) {
                    this.h.b();
                    O.setAlpha(Constant.VOLUME_FLOAT_MIN);
                }
                if (O.getAlpha() == Constant.VOLUME_FLOAT_MIN && O.getVisibility() == 0) {
                    O.setVisibility(4);
                }
                Fragment.b bVar = fragment.f1751x0;
                if (bVar == null) {
                    f10 = 1.0f;
                } else {
                    f10 = bVar.f1766j;
                }
                O.setAlpha(f10);
            }
        }
    }

    /* compiled from: SpecialEffectsController */
    public static class b {
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public int f2006a;
        @NonNull

        /* renamed from: b  reason: collision with root package name */
        public int f2007b;
        @NonNull

        /* renamed from: c  reason: collision with root package name */
        public final Fragment f2008c;
        @NonNull

        /* renamed from: d  reason: collision with root package name */
        public final List<Runnable> f2009d = new ArrayList();
        @NonNull

        /* renamed from: e  reason: collision with root package name */
        public final HashSet<f0.a> f2010e = new HashSet<>();

        /* renamed from: f  reason: collision with root package name */
        public boolean f2011f = false;

        /* renamed from: g  reason: collision with root package name */
        public boolean f2012g = false;

        public b(@NonNull int i10, @NonNull int i11, @NonNull Fragment fragment, @NonNull f0.a aVar) {
            this.f2006a = i10;
            this.f2007b = i11;
            this.f2008c = fragment;
            aVar.b(new u0(this));
        }

        public final void a() {
            if (!this.f2011f) {
                this.f2011f = true;
                if (this.f2010e.isEmpty()) {
                    b();
                    return;
                }
                Iterator it = new ArrayList(this.f2010e).iterator();
                while (it.hasNext()) {
                    ((f0.a) it.next()).a();
                }
            }
        }

        @CallSuper
        public void b() {
            if (!this.f2012g) {
                if (FragmentManager.O(2)) {
                    Log.v("FragmentManager", "SpecialEffectsController: " + this + " has called complete.");
                }
                this.f2012g = true;
                for (Runnable runnable : this.f2009d) {
                    runnable.run();
                }
            }
        }

        public final void c(@NonNull int i10, @NonNull int i11) {
            if (i11 != 0) {
                int i12 = i11 - 1;
                if (i12 != 0) {
                    if (i12 != 1) {
                        if (i12 == 2) {
                            if (FragmentManager.O(2)) {
                                StringBuilder a10 = f.a("SpecialEffectsController: For fragment ");
                                a10.append(this.f2008c);
                                a10.append(" mFinalState = ");
                                a10.append(x0.d(this.f2006a));
                                a10.append(" -> REMOVED. mLifecycleImpact  = ");
                                a10.append(v0.e(this.f2007b));
                                a10.append(" to REMOVING.");
                                Log.v("FragmentManager", a10.toString());
                            }
                            this.f2006a = 1;
                            this.f2007b = 3;
                        }
                    } else if (this.f2006a == 1) {
                        if (FragmentManager.O(2)) {
                            StringBuilder a11 = f.a("SpecialEffectsController: For fragment ");
                            a11.append(this.f2008c);
                            a11.append(" mFinalState = REMOVED -> VISIBLE. mLifecycleImpact = ");
                            a11.append(v0.e(this.f2007b));
                            a11.append(" to ADDING.");
                            Log.v("FragmentManager", a11.toString());
                        }
                        this.f2006a = 2;
                        this.f2007b = 2;
                    }
                } else if (this.f2006a != 1) {
                    if (FragmentManager.O(2)) {
                        StringBuilder a12 = f.a("SpecialEffectsController: For fragment ");
                        a12.append(this.f2008c);
                        a12.append(" mFinalState = ");
                        a12.append(x0.d(this.f2006a));
                        a12.append(" -> ");
                        a12.append(x0.d(i10));
                        a12.append(". ");
                        Log.v("FragmentManager", a12.toString());
                    }
                    this.f2006a = i10;
                }
            } else {
                throw null;
            }
        }

        public void d() {
        }

        @NonNull
        public String toString() {
            StringBuilder b10 = i.b("Operation ", "{");
            b10.append(Integer.toHexString(System.identityHashCode(this)));
            b10.append("} ");
            b10.append("{");
            b10.append("mFinalState = ");
            b10.append(x0.d(this.f2006a));
            b10.append("} ");
            b10.append("{");
            b10.append("mLifecycleImpact = ");
            b10.append(v0.e(this.f2007b));
            b10.append("} ");
            b10.append("{");
            b10.append("mFragment = ");
            b10.append(this.f2008c);
            b10.append("}");
            return b10.toString();
        }
    }

    public t0(@NonNull ViewGroup viewGroup) {
        this.f2001a = viewGroup;
    }

    @NonNull
    public static t0 f(@NonNull ViewGroup viewGroup, @NonNull y0 y0Var) {
        int i10 = R$id.special_effects_controller_view_tag;
        Object tag = viewGroup.getTag(i10);
        if (tag instanceof t0) {
            return (t0) tag;
        }
        Objects.requireNonNull((FragmentManager.f) y0Var);
        b bVar = new b(viewGroup);
        viewGroup.setTag(i10, bVar);
        return bVar;
    }

    public final void a(@NonNull int i10, @NonNull int i11, @NonNull b0 b0Var) {
        synchronized (this.f2002b) {
            f0.a aVar = new f0.a();
            b d10 = d(b0Var.f1852c);
            if (d10 != null) {
                d10.c(i10, i11);
                return;
            }
            a aVar2 = new a(i10, i11, b0Var, aVar);
            this.f2002b.add(aVar2);
            aVar2.f2009d.add(new r0(this, aVar2));
            aVar2.f2009d.add(new s0(this, aVar2));
        }
    }

    public abstract void b(@NonNull List<b> list, boolean z10);

    public void c() {
        if (!this.f2005e) {
            ViewGroup viewGroup = this.f2001a;
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            if (!viewGroup.isAttachedToWindow()) {
                e();
                this.f2004d = false;
                return;
            }
            synchronized (this.f2002b) {
                if (!this.f2002b.isEmpty()) {
                    ArrayList arrayList = new ArrayList(this.f2003c);
                    this.f2003c.clear();
                    Iterator it = arrayList.iterator();
                    while (it.hasNext()) {
                        b bVar = (b) it.next();
                        if (FragmentManager.O(2)) {
                            Log.v("FragmentManager", "SpecialEffectsController: Cancelling operation " + bVar);
                        }
                        bVar.a();
                        if (!bVar.f2012g) {
                            this.f2003c.add(bVar);
                        }
                    }
                    h();
                    ArrayList arrayList2 = new ArrayList(this.f2002b);
                    this.f2002b.clear();
                    this.f2003c.addAll(arrayList2);
                    Iterator it2 = arrayList2.iterator();
                    while (it2.hasNext()) {
                        ((b) it2.next()).d();
                    }
                    b(arrayList2, this.f2004d);
                    this.f2004d = false;
                }
            }
        }
    }

    @Nullable
    public final b d(@NonNull Fragment fragment) {
        Iterator<b> it = this.f2002b.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f2008c.equals(fragment) && !next.f2011f) {
                return next;
            }
        }
        return null;
    }

    public void e() {
        String str;
        String str2;
        ViewGroup viewGroup = this.f2001a;
        WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
        boolean isAttachedToWindow = viewGroup.isAttachedToWindow();
        synchronized (this.f2002b) {
            h();
            Iterator<b> it = this.f2002b.iterator();
            while (it.hasNext()) {
                it.next().d();
            }
            Iterator it2 = new ArrayList(this.f2003c).iterator();
            while (it2.hasNext()) {
                b bVar = (b) it2.next();
                if (FragmentManager.O(2)) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("SpecialEffectsController: ");
                    if (isAttachedToWindow) {
                        str2 = "";
                    } else {
                        str2 = "Container " + this.f2001a + " is not attached to window. ";
                    }
                    sb.append(str2);
                    sb.append("Cancelling running operation ");
                    sb.append(bVar);
                    Log.v("FragmentManager", sb.toString());
                }
                bVar.a();
            }
            Iterator it3 = new ArrayList(this.f2002b).iterator();
            while (it3.hasNext()) {
                b bVar2 = (b) it3.next();
                if (FragmentManager.O(2)) {
                    StringBuilder sb2 = new StringBuilder();
                    sb2.append("SpecialEffectsController: ");
                    if (isAttachedToWindow) {
                        str = "";
                    } else {
                        str = "Container " + this.f2001a + " is not attached to window. ";
                    }
                    sb2.append(str);
                    sb2.append("Cancelling pending operation ");
                    sb2.append(bVar2);
                    Log.v("FragmentManager", sb2.toString());
                }
                bVar2.a();
            }
        }
    }

    public void g() {
        synchronized (this.f2002b) {
            h();
            this.f2005e = false;
            int size = this.f2002b.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                }
                b bVar = this.f2002b.get(size);
                int c10 = x0.c(bVar.f2008c.f1747u0);
                if (bVar.f2006a == 2 && c10 != 2) {
                    Objects.requireNonNull(bVar.f2008c);
                    this.f2005e = false;
                    break;
                }
            }
        }
    }

    public final void h() {
        Iterator<b> it = this.f2002b.iterator();
        while (it.hasNext()) {
            b next = it.next();
            if (next.f2007b == 2) {
                next.c(x0.b(next.f2008c.O().getVisibility()), 1);
            }
        }
    }
}
