package aa;

import java.io.IOException;

/* compiled from: DEROctetString */
public class w0 extends n {
    public w0(byte[] bArr) {
        super(bArr);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(4, this.f205a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f205a.length) + 1 + this.f205a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
