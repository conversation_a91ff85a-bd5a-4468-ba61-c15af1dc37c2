package aa;

import com.duokan.airkan.server.f;
import java.io.IOException;
import java.io.InputStream;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: DEROctetStringParser */
public class x0 implements o {

    /* renamed from: a  reason: collision with root package name */
    public p1 f245a;

    public x0(p1 p1Var) {
        this.f245a = p1Var;
    }

    @Override // aa.o
    public InputStream a() {
        return this.f245a;
    }

    @Override // aa.e
    public q c() {
        try {
            return new w0(this.f245a.e());
        } catch (IOException e10) {
            StringBuilder a10 = f.a("IOException converting stream to byte array: ");
            a10.append(e10.getMessage());
            throw new ASN1ParsingException(a10.toString(), e10);
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return new w0(this.f245a.e());
    }
}
