package androidx.core.app;

import android.app.PendingIntent;
import androidx.annotation.RestrictTo;
import androidx.core.graphics.drawable.IconCompat;
import androidx.versionedparcelable.VersionedParcel;
import java.util.Objects;

@RestrictTo({RestrictTo.Scope.LIBRARY})
public class RemoteActionCompatParcelizer {
    public static RemoteActionCompat read(VersionedParcel versionedParcel) {
        RemoteActionCompat remoteActionCompat = new RemoteActionCompat();
        Object obj = remoteActionCompat.f1563a;
        if (versionedParcel.i(1)) {
            obj = versionedParcel.o();
        }
        remoteActionCompat.f1563a = (IconCompat) obj;
        CharSequence charSequence = remoteActionCompat.f1564b;
        if (versionedParcel.i(2)) {
            charSequence = versionedParcel.h();
        }
        remoteActionCompat.f1564b = charSequence;
        CharSequence charSequence2 = remoteActionCompat.f1565c;
        if (versionedParcel.i(3)) {
            charSequence2 = versionedParcel.h();
        }
        remoteActionCompat.f1565c = charSequence2;
        remoteActionCompat.f1566d = (PendingIntent) versionedParcel.m(remoteActionCompat.f1566d, 4);
        boolean z10 = remoteActionCompat.f1567e;
        if (versionedParcel.i(5)) {
            z10 = versionedParcel.f();
        }
        remoteActionCompat.f1567e = z10;
        boolean z11 = remoteActionCompat.f1568f;
        if (versionedParcel.i(6)) {
            z11 = versionedParcel.f();
        }
        remoteActionCompat.f1568f = z11;
        return remoteActionCompat;
    }

    public static void write(RemoteActionCompat remoteActionCompat, VersionedParcel versionedParcel) {
        Objects.requireNonNull(versionedParcel);
        IconCompat iconCompat = remoteActionCompat.f1563a;
        versionedParcel.p(1);
        versionedParcel.w(iconCompat);
        CharSequence charSequence = remoteActionCompat.f1564b;
        versionedParcel.p(2);
        versionedParcel.s(charSequence);
        CharSequence charSequence2 = remoteActionCompat.f1565c;
        versionedParcel.p(3);
        versionedParcel.s(charSequence2);
        PendingIntent pendingIntent = remoteActionCompat.f1566d;
        versionedParcel.p(4);
        versionedParcel.u(pendingIntent);
        boolean z10 = remoteActionCompat.f1567e;
        versionedParcel.p(5);
        versionedParcel.q(z10);
        boolean z11 = remoteActionCompat.f1568f;
        versionedParcel.p(6);
        versionedParcel.q(z11);
    }
}
