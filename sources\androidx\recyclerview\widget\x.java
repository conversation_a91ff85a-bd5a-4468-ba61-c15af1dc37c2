package androidx.recyclerview.widget;

import android.view.View;
import androidx.recyclerview.widget.c;

/* compiled from: RecyclerView */
public class x implements c.b {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ RecyclerView f2479a;

    public x(RecyclerView recyclerView) {
        this.f2479a = recyclerView;
    }

    public View a(int i10) {
        return this.f2479a.getChildAt(i10);
    }

    public int b() {
        return this.f2479a.getChildCount();
    }

    public void c(int i10) {
        View childAt = this.f2479a.getChildAt(i10);
        if (childAt != null) {
            this.f2479a.p(childAt);
            childAt.clearAnimation();
        }
        this.f2479a.removeViewAt(i10);
    }
}
