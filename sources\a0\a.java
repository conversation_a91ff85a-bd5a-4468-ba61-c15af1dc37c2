package a0;

import androidx.annotation.RequiresApi;
import java.lang.reflect.Method;

@RequiresApi(23)
/* compiled from: ResourcesCompat */
public class a {

    /* renamed from: a  reason: collision with root package name */
    public static final Object f4a = new Object();

    /* renamed from: b  reason: collision with root package name */
    public static Method f5b;

    /* renamed from: c  reason: collision with root package name */
    public static boolean f6c;
}
