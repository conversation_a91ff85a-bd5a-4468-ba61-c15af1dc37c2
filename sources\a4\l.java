package a4;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.DhcpInfo;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.Looper;
import androidx.recyclerview.widget.o;
import b1.h;
import com.duokan.airkan.common.Log;
import com.duokan.airkan.common.ServiceData;
import com.xiaomi.milink.discover.core.udt.UDTDiscoverService;
import com.xiaomi.onetrack.util.z;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.jmdns.ServiceEvent;
import javax.jmdns.ServiceInfo;
import javax.jmdns.impl.JmDNSImpl;

public class l extends Thread {

    /* renamed from: a  reason: collision with root package name */
    public volatile AtomicBoolean f42a = new AtomicBoolean(false);

    /* renamed from: b  reason: collision with root package name */
    public byte[] f43b = new byte[0];

    /* renamed from: c  reason: collision with root package name */
    public final f f44c;

    /* renamed from: d  reason: collision with root package name */
    public final Context f45d;

    /* renamed from: e  reason: collision with root package name */
    public WifiManager.MulticastLock f46e = null;

    /* renamed from: f  reason: collision with root package name */
    public e f47f = new e();

    /* renamed from: g  reason: collision with root package name */
    public InetAddress f48g = null;
    public Handler h = null;

    /* renamed from: i  reason: collision with root package name */
    public h5.a f49i = null;

    /* renamed from: j  reason: collision with root package name */
    public Map<String, ServiceData> f50j = new HashMap();

    public class a implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ServiceData f51a;

        public a(ServiceData serviceData) {
            this.f51a = serviceData;
        }

        public void run() {
            l lVar = l.this;
            ServiceData serviceData = this.f51a;
            Objects.requireNonNull(lVar);
            StringBuilder sb = new StringBuilder();
            sb.append("To REGIST: ");
            sb.append(serviceData.name);
            sb.append(".");
            sb.append(serviceData.type);
            sb.append(" port:");
            sb.append(serviceData.port);
            sb.append(" text:");
            o.b(sb, serviceData.extraText, "AKTS-UDTJmDNSThread");
            synchronized (lVar.f43b) {
                if (lVar.f49i == null) {
                    Log.w("AKTS-UDTJmDNSThread", "JmDNS not available");
                } else if (lVar.e(serviceData)) {
                    Log.i("AKTS-UDTJmDNSThread", "Service already registered");
                } else {
                    try {
                        String replaceAll = serviceData.name.replaceAll(z.f5835a, " ");
                        serviceData.name = replaceAll;
                        String replaceAll2 = replaceAll.replaceAll("\\\\", " ");
                        serviceData.name = replaceAll2;
                        ServiceInfo c10 = ServiceInfo.c(serviceData.type, replaceAll2, serviceData.port, 0, 0, false, serviceData.extraText);
                        if (c10.i() == null) {
                            Log.w("AKTS-UDTJmDNSThread", "Service info key is null");
                        } else {
                            Log.i("AKTS-UDTJmDNSThread", "Service info key: " + c10.i());
                            lVar.f49i.s(c10);
                            Log.d("AKTS-UDTJmDNSThread", "REGISTER DONE: " + serviceData.name + "." + serviceData.type + " port:" + serviceData.port + " text:" + serviceData.extraText);
                            for (ServiceInfo serviceInfo : ((JmDNSImpl) lVar.f49i).f7132i.values()) {
                                if (serviceInfo.p().equalsIgnoreCase(serviceData.type)) {
                                    serviceData.name = serviceInfo.getName();
                                    if (serviceData.ip == null) {
                                        serviceData.ip = new String[1];
                                    }
                                    serviceData.ip[0] = serviceInfo.f()[0];
                                    if (serviceData.ip.length > 0) {
                                        Log.d("AKTS-UDTJmDNSThread", "local ip: " + serviceData.ip[0]);
                                    }
                                    lVar.f50j.put(serviceData.type, serviceData);
                                }
                            }
                        }
                    } catch (Exception e10) {
                        Log.e("AKTS-UDTJmDNSThread", "Regist service error: " + e10.toString());
                    }
                }
            }
        }
    }

    public class b implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ ServiceData f53a;

        public b(ServiceData serviceData) {
            this.f53a = serviceData;
        }

        public void run() {
            l lVar = l.this;
            ServiceData serviceData = this.f53a;
            synchronized (lVar.f43b) {
                if (lVar.f49i == null) {
                    Log.w("AKTS-UDTJmDNSThread", "JmDNS not available");
                } else {
                    String replaceAll = serviceData.name.replaceAll(z.f5835a, " ");
                    serviceData.name = replaceAll;
                    serviceData.name = replaceAll.replaceAll("\\\\", " ");
                    for (ServiceInfo serviceInfo : ((JmDNSImpl) lVar.f49i).f7132i.values()) {
                        String name = serviceInfo.getName();
                        String p10 = serviceInfo.p();
                        if (name.startsWith(serviceData.name) && p10.equalsIgnoreCase(serviceData.type)) {
                            lVar.f49i.v(serviceInfo);
                            Log.d("AKTS-UDTJmDNSThread", "REMOVE DONE: " + name + "." + p10 + " port:" + serviceData.port + " text:" + serviceData.extraText);
                            lVar.f50j.remove(serviceData.type);
                        }
                    }
                }
            }
        }
    }

    public class c implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ String f55a;

        public c(String str) {
            this.f55a = str;
        }

        public void run() {
            l lVar = l.this;
            String str = this.f55a;
            h5.a aVar = lVar.f49i;
            if (aVar != null) {
                aVar.q(str, lVar.f47f);
                Log.i("AKTS-UDTJmDNSThread", "Regist subscibed service success");
                return;
            }
            Log.e("AKTS-UDTJmDNSThread", "JmDNS not available, regist subscibed service failed!");
        }
    }

    public class d implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ String f57a;

        public d(String str) {
            this.f57a = str;
        }

        public void run() {
            l lVar = l.this;
            String str = this.f57a;
            h5.a aVar = lVar.f49i;
            if (aVar != null) {
                aVar.t(str, lVar.f47f);
                Log.i("AKTS-UDTJmDNSThread", "Remove subscibed service success");
                return;
            }
            Log.e("AKTS-UDTJmDNSThread", "JmDNS not available, remove subscibed service failed!");
        }
    }

    public class e implements h5.c, h5.d {
        public e() {
        }

        @Override // h5.c
        public void a(ServiceEvent serviceEvent) {
            ServiceData d10 = d(serviceEvent);
            StringBuilder a10 = com.duokan.airkan.server.f.a("Service Resolved: ");
            a10.append(d10.name);
            a10.append(".");
            o.b(a10, d10.type, "AKTS-UDTJmDNSThread");
            l lVar = l.this;
            lVar.h.post(new m(lVar, d10));
        }

        @Override // h5.c
        public void b(ServiceEvent serviceEvent) {
            ServiceData d10 = d(serviceEvent);
            StringBuilder a10 = com.duokan.airkan.server.f.a("Service Removed: ");
            a10.append(d10.name);
            a10.append(".");
            o.b(a10, d10.type, "AKTS-UDTJmDNSThread");
            l lVar = l.this;
            lVar.h.post(new n(lVar, d10));
        }

        @Override // h5.c
        public void c(ServiceEvent serviceEvent) {
            ServiceData d10 = d(serviceEvent);
            StringBuilder a10 = com.duokan.airkan.server.f.a("Service Added: ");
            a10.append(d10.name);
            a10.append(".");
            o.b(a10, d10.type, "AKTS-UDTJmDNSThread");
            l lVar = l.this;
            lVar.h.post(new m(lVar, d10));
        }

        public final ServiceData d(ServiceEvent serviceEvent) {
            ServiceData serviceData = new ServiceData();
            serviceData.name = serviceEvent.getName();
            serviceData.type = serviceEvent.getType();
            return serviceData;
        }
    }

    public static class f {
        public static InetAddress a(Context context, int[] iArr) {
            String str;
            InetAddress inetAddress;
            String str2;
            String str3;
            Boolean bool = Boolean.FALSE;
            try {
                ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
                if (connectivityManager == null) {
                    Log.w("AKTS-UDTJmDNSThread", "connectivity manager is null");
                    return null;
                }
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                if (activeNetworkInfo == null) {
                    str = "There is no activeConnectivity";
                } else {
                    if (activeNetworkInfo.isConnected()) {
                        int type = activeNetworkInfo.getType();
                        if (type == 1) {
                            bool = Boolean.TRUE;
                            str3 = "Wifi is active connectivity";
                        } else if (type != 9) {
                            str = "Unknown active connectivity: " + activeNetworkInfo.getType();
                        } else {
                            str3 = "Ethernet is active connectivity";
                        }
                        Log.d("AKTS-UDTJmDNSThread", str3);
                    }
                    if (bool.booleanValue()) {
                        WifiManager wifiManager = (WifiManager) context.getSystemService("wifi");
                        if (wifiManager == null) {
                            str2 = "wifi manager is not ready, ignore";
                        } else if (3 != wifiManager.getWifiState()) {
                            str2 = "wifi not enabled, ignore";
                        } else {
                            DhcpInfo dhcpInfo = wifiManager.getDhcpInfo();
                            inetAddress = InetAddress.getByAddress(l.b(dhcpInfo.ipAddress));
                            iArr[0] = dhcpInfo.netmask;
                            Log.d("AKTS-UDTJmDNSThread", "get wifi IP: " + inetAddress);
                            return inetAddress;
                        }
                        Log.i("AKTS-UDTJmDNSThread", str2);
                    }
                    Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                    inetAddress = null;
                    while (networkInterfaces.hasMoreElements()) {
                        NetworkInterface nextElement = networkInterfaces.nextElement();
                        Log.d("AKTS-UDTJmDNSThread", "DisplayName:" + nextElement.getDisplayName());
                        Log.d("AKTS-UDTJmDNSThread", "Name:" + nextElement.getName());
                        if (!nextElement.isLoopback()) {
                            Enumeration<InetAddress> inetAddresses = nextElement.getInetAddresses();
                            while (true) {
                                if (!inetAddresses.hasMoreElements()) {
                                    break;
                                }
                                InetAddress nextElement2 = inetAddresses.nextElement();
                                if (nextElement2 instanceof Inet4Address) {
                                    Log.d("AKTS-UDTJmDNSThread", "IP:" + nextElement2.getHostAddress());
                                    inetAddress = nextElement2;
                                    break;
                                } else if (nextElement2 instanceof Inet6Address) {
                                    Log.d("AKTS-UDTJmDNSThread", "IPv6:" + nextElement2.getHostAddress());
                                } else {
                                    Log.w("AKTS-UDTJmDNSThread", "invalid ip");
                                }
                            }
                        }
                    }
                    Log.d("AKTS-UDTJmDNSThread", "IP: " + inetAddress);
                    iArr[0] = 0;
                    return inetAddress;
                }
                Log.e("AKTS-UDTJmDNSThread", str);
                return null;
            } catch (Exception e10) {
                e10.printStackTrace();
                return null;
            }
        }
    }

    public l(Context context, f fVar) {
        this.f45d = context;
        this.f44c = fVar;
        try {
            Class.forName("mitv.network.ethernet.EthernetManager");
        } catch (Exception e10) {
            com.duokan.airkan.server.c.b(e10, com.duokan.airkan.server.f.a("Exception: "), "UDTCommon");
        }
        h.e("TAG", "mIsSupportEthernet:false");
    }

    public static void a(l lVar) {
        synchronized (lVar.f43b) {
            if (lVar.f49i == null) {
                int[] iArr = new int[1];
                InetAddress a10 = f.a(lVar.f45d, iArr);
                lVar.f48g = a10;
                if (a10 != null) {
                    Log.i("AKTS-UDTJmDNSThread", "IP: " + lVar.f48g + " NetMask: " + String.format("%08x", Integer.valueOf(iArr[0])));
                    try {
                        lVar.f49i = new JmDNSImpl(lVar.f48g, "milink" + Integer.toString((int) System.currentTimeMillis()), iArr[0]);
                        Log.i("AKTS-UDTJmDNSThread", "Create JmDNS success");
                        ((JmDNSImpl) lVar.f49i).f7125a = UDTDiscoverService.f4654m;
                    } catch (Exception e10) {
                        Log.e("AKTS-UDTJmDNSThread", "Create JmDNS failed: " + e10.toString());
                    }
                } else {
                    Log.e("AKTS-UDTJmDNSThread", "Local address is null");
                }
            } else {
                Log.i("AKTS-UDTJmDNSThread", "JmDNS already available");
            }
        }
    }

    public static byte[] b(int i10) {
        byte[] bArr = new byte[4];
        bArr[3] = (byte) ((i10 >> 24) & 255);
        bArr[2] = (byte) ((i10 >> 16) & 255);
        bArr[1] = (byte) ((i10 >> 8) & 255);
        bArr[0] = (byte) (i10 & 255);
        return bArr;
    }

    public final void c() {
        synchronized (this.f43b) {
            h5.a aVar = this.f49i;
            if (aVar != null) {
                try {
                    aVar.u();
                } catch (Exception e10) {
                    Log.e("AKTS-UDTJmDNSThread", "unregisterAllServices error:" + e10.toString());
                }
                try {
                    ((JmDNSImpl) this.f49i).close();
                    Log.i("AKTS-UDTJmDNSThread", "Close JmDNS success");
                } catch (Exception e11) {
                    Log.e("AKTS-UDTJmDNSThread", "Close JmDNS failed: " + e11.toString());
                }
                this.f49i = null;
            } else {
                Log.i("AKTS-UDTJmDNSThread", "JmDNS not available");
            }
        }
    }

    public boolean d() {
        synchronized (this.f43b) {
            h5.a aVar = this.f49i;
            if (aVar != null) {
                if (aVar.r()) {
                    InetAddress a10 = f.a(this.f45d, new int[1]);
                    this.f48g = a10;
                    if (a10 == null) {
                        Log.e("AKTS-UDTJmDNSThread", "Local address is null");
                        return true;
                    }
                    javax.jmdns.impl.f fVar = ((JmDNSImpl) this.f49i).f7135l;
                    if (fVar == null) {
                        Log.d("AKTS-UDTJmDNSThread", "HostInfo is null");
                        return false;
                    }
                    InetAddress inetAddress = fVar.f7228b;
                    if (inetAddress == null) {
                        Log.d("AKTS-UDTJmDNSThread", "JmDNS address is null");
                        return false;
                    }
                    Log.d("AKTS-UDTJmDNSThread", "JmDNS address:" + inetAddress);
                    return this.f48g.equals(inetAddress);
                }
            }
            Log.d("AKTS-UDTJmDNSThread", "JmDNS not alive");
            return false;
        }
    }

    public final boolean e(ServiceData serviceData) {
        for (ServiceInfo serviceInfo : ((JmDNSImpl) this.f49i).f7132i.values()) {
            if (serviceInfo.getName().startsWith(serviceData.name) && serviceData.type.equalsIgnoreCase(serviceInfo.p())) {
                StringBuilder a10 = com.duokan.airkan.server.f.a("Exist: ");
                a10.append(serviceInfo.getName());
                a10.append(serviceInfo.p());
                a10.append(serviceInfo.j());
                Log.i("AKTS-UDTJmDNSThread", a10.toString());
                return true;
            }
        }
        return false;
    }

    public int f(ServiceData serviceData) {
        Handler handler = this.h;
        if (handler != null) {
            handler.post(new a(serviceData));
            return 0;
        }
        Log.e("AKTS-UDTJmDNSThread", "Handler not available, regist service failed!");
        return -1;
    }

    public int g(String str) {
        Handler handler = this.h;
        if (handler != null) {
            handler.post(new c(str));
            return 0;
        }
        Log.e("AKTS-UDTJmDNSThread", "Handler not available, regist subscribed service failed!");
        return -1;
    }

    public int h(ServiceData serviceData) {
        Handler handler = this.h;
        if (handler != null) {
            handler.post(new b(serviceData));
            return 0;
        }
        Log.e("AKTS-UDTJmDNSThread", "Handler not available, remove service failed!");
        return -1;
    }

    public int i(String str) {
        Handler handler = this.h;
        if (handler != null) {
            handler.post(new d(str));
            return 0;
        }
        Log.e("AKTS-UDTJmDNSThread", "Handler not available, remove subscribed service failed!");
        return -1;
    }

    public void run() {
        Log.i("AKTS-UDTJmDNSThread", "JmDNS thread started");
        WifiManager wifiManager = (WifiManager) this.f45d.getSystemService("wifi");
        if (this.f46e == null) {
            WifiManager.MulticastLock createMulticastLock = wifiManager.createMulticastLock("UDTDiscover");
            this.f46e = createMulticastLock;
            createMulticastLock.setReferenceCounted(true);
            Log.d("AKTS-UDTJmDNSThread", "Try To acquire WiFi lock!");
            this.f46e.acquire();
            Log.d("AKTS-UDTJmDNSThread", "Acquire WiFi lock success");
        }
        Looper.prepare();
        Handler handler = new Handler();
        this.h = handler;
        handler.post(new i(this));
        this.f42a.set(true);
        Looper.loop();
        this.f42a.set(false);
        c();
        WifiManager.MulticastLock multicastLock = this.f46e;
        if (multicastLock != null) {
            if (multicastLock.isHeld()) {
                Log.d("AKTS-UDTJmDNSThread", "mlocking, release");
                this.f46e.release();
            }
            this.f46e = null;
        } else {
            Log.d("AKTS-UDTJmDNSThread", "mlock already unlocked");
        }
        Log.i("AKTS-UDTJmDNSThread", "JmDNS thread stopped");
    }
}
