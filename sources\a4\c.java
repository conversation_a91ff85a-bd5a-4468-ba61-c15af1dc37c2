package a4;

import android.os.Handler;
import com.duokan.airkan.common.Log;

public class c implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ f f18a;

    public c(f fVar) {
        this.f18a = fVar;
    }

    public void run() {
        Log.i("UDTDiscoverManager", "Start JmDNS service");
        f fVar = this.f18a;
        synchronized (fVar.f32m) {
            l lVar = fVar.f27g;
            if (lVar == null) {
                l lVar2 = new l(fVar.h, fVar);
                fVar.f27g = lVar2;
                lVar2.start();
                if (!fVar.b()) {
                    Log.e("UDTDiscoverManager", "Timeout and JmDNS not initiated");
                } else {
                    Log.i("UDTDiscoverManager", "JmDNS service started");
                }
            } else {
                Handler handler = lVar.h;
                if (handler != null) {
                    handler.post(new j(lVar));
                } else {
                    Log.e("AKTS-UDTJmDNSThread", "Handler not available, reset JmDNS service failed!");
                }
            }
            fVar.d();
            fVar.e();
            fVar.c();
        }
        Log.i("UDTDiscoverManager", "Start JmDNS service done");
    }
}
