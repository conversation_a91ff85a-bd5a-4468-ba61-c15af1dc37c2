package aa;

import java.io.IOException;
import java.util.Enumeration;

/* compiled from: DERSequence */
public class a1 extends r {

    /* renamed from: b  reason: collision with root package name */
    public int f159b = -1;

    public a1() {
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        p a10 = pVar.a();
        int s10 = s();
        pVar.c(48);
        pVar.g(s10);
        Enumeration q10 = q();
        while (q10.hasMoreElements()) {
            a10.h((e) q10.nextElement());
        }
    }

    @Override // aa.q
    public int i() throws IOException {
        int s10 = s();
        return v1.a(s10) + 1 + s10;
    }

    public final int s() throws IOException {
        if (this.f159b < 0) {
            int i10 = 0;
            Enumeration q10 = q();
            while (q10.hasMoreElements()) {
                i10 += ((e) q10.nextElement()).c().l().i();
            }
            this.f159b = i10;
        }
        return this.f159b;
    }

    public a1(f fVar) {
        super(fVar);
    }

    public a1(e[] eVarArr) {
        super(eVarArr);
    }
}
