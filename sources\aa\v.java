package aa;

import androidx.appcompat.widget.h;
import com.duokan.airkan.server.f;
import java.io.IOException;
import java.io.InputStream;
import org.spongycastle.asn1.ASN1Exception;

/* compiled from: ASN1StreamParser */
public class v {

    /* renamed from: a  reason: collision with root package name */
    public final InputStream f237a;

    /* renamed from: b  reason: collision with root package name */
    public final int f238b;

    /* renamed from: c  reason: collision with root package name */
    public final byte[][] f239c;

    public v(InputStream inputStream) {
        int c10 = v1.c(inputStream);
        this.f237a = inputStream;
        this.f238b = c10;
        this.f239c = new byte[11][];
    }

    public e a() throws IOException {
        int read = this.f237a.read();
        if (read == -1) {
            return null;
        }
        InputStream inputStream = this.f237a;
        boolean z10 = false;
        if (inputStream instanceof r1) {
            r1 r1Var = (r1) inputStream;
            r1Var.f225f = false;
            r1Var.e();
        }
        int i10 = i.i(this.f237a, read);
        if ((read & 32) != 0) {
            z10 = true;
        }
        int g10 = i.g(this.f237a, this.f238b);
        if (g10 >= 0) {
            p1 p1Var = new p1(this.f237a, g10);
            if ((read & 64) != 0) {
                return new l0(z10, i10, p1Var.e());
            }
            if ((read & 128) != 0) {
                return new j0(z10, i10, new v(p1Var));
            }
            if (z10) {
                if (i10 == 4) {
                    return new d0(new v(p1Var));
                }
                if (i10 == 8) {
                    return new p0(new v(p1Var));
                }
                if (i10 == 16) {
                    return new b1(new v(p1Var));
                }
                if (i10 == 17) {
                    return new d1(new v(p1Var));
                }
                throw new IOException(h.a("unknown tag ", i10, " encountered"));
            } else if (i10 == 4) {
                return new x0(p1Var);
            } else {
                try {
                    return i.e(i10, p1Var, this.f239c);
                } catch (IllegalArgumentException e10) {
                    throw new ASN1Exception("corrupted stream detected", e10);
                }
            }
        } else if (z10) {
            v vVar = new v(new r1(this.f237a, this.f238b), this.f238b);
            if ((read & 64) != 0) {
                return new a0(i10, vVar);
            }
            if ((read & 128) != 0) {
                return new j0(true, i10, vVar);
            }
            if (i10 == 4) {
                return new d0(vVar);
            }
            if (i10 == 8) {
                return new p0(vVar);
            }
            if (i10 == 16) {
                return new f0(vVar);
            }
            if (i10 == 17) {
                return new h0(vVar);
            }
            StringBuilder a10 = f.a("unknown BER object encountered: 0x");
            a10.append(Integer.toHexString(i10));
            throw new ASN1Exception(a10.toString());
        } else {
            throw new IOException("indefinite-length primitive encoding encountered");
        }
    }

    public q b(boolean z10, int i10) throws IOException {
        if (!z10) {
            return new f1(false, i10, new w0(((p1) this.f237a).e()));
        }
        f c10 = c();
        if (this.f237a instanceof r1) {
            if (c10.b() == 1) {
                return new i0(true, i10, c10.a(0));
            }
            e0 e0Var = b0.f163a;
            return new i0(false, i10, c10.b() < 1 ? b0.f163a : new e0(c10));
        } else if (c10.b() == 1) {
            return new f1(true, i10, c10.a(0));
        } else {
            r rVar = q0.f218a;
            return new f1(false, i10, c10.b() < 1 ? q0.f218a : new m1(c10));
        }
    }

    public f c() throws IOException {
        f fVar = new f();
        while (true) {
            e a10 = a();
            if (a10 == null) {
                return fVar;
            }
            if (a10 instanceof q1) {
                fVar.f177a.addElement(((q1) a10).d());
            } else {
                fVar.f177a.addElement(a10.c());
            }
        }
    }

    public v(InputStream inputStream, int i10) {
        this.f237a = inputStream;
        this.f238b = i10;
        this.f239c = new byte[11][];
    }
}
