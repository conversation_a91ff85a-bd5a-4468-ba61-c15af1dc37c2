package androidx.activity.result;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

@SuppressLint({"BanParcelableUsage"})
public final class IntentSenderRequest implements Parcelable {
    @NonNull
    public static final Parcelable.Creator<IntentSenderRequest> CREATOR = new a();
    @NonNull

    /* renamed from: a  reason: collision with root package name */
    public final IntentSender f320a;
    @Nullable

    /* renamed from: b  reason: collision with root package name */
    public final Intent f321b;

    /* renamed from: c  reason: collision with root package name */
    public final int f322c;

    /* renamed from: d  reason: collision with root package name */
    public final int f323d;

    public class a implements Parcelable.Creator<IntentSenderRequest> {
        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // android.os.Parcelable.Creator
        public IntentSenderRequest createFromParcel(Parcel parcel) {
            return new IntentSenderRequest(parcel);
        }

        /* Return type fixed from 'java.lang.Object[]' to match base method */
        @Override // android.os.Parcelable.Creator
        public IntentSenderRequest[] newArray(int i10) {
            return new IntentSenderRequest[i10];
        }
    }

    public IntentSenderRequest(@NonNull IntentSender intentSender, @Nullable Intent intent, int i10, int i11) {
        this.f320a = intentSender;
        this.f321b = intent;
        this.f322c = i10;
        this.f323d = i11;
    }

    public int describeContents() {
        return 0;
    }

    public void writeToParcel(@NonNull Parcel parcel, int i10) {
        parcel.writeParcelable(this.f320a, i10);
        parcel.writeParcelable(this.f321b, i10);
        parcel.writeInt(this.f322c);
        parcel.writeInt(this.f323d);
    }

    public IntentSenderRequest(@NonNull Parcel parcel) {
        this.f320a = (IntentSender) parcel.readParcelable(IntentSender.class.getClassLoader());
        this.f321b = (Intent) parcel.readParcelable(Intent.class.getClassLoader());
        this.f322c = parcel.readInt();
        this.f323d = parcel.readInt();
    }
}
