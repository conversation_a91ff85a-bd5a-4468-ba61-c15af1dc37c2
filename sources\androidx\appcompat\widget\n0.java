package androidx.appcompat.widget;

import android.view.View;
import android.view.Window;
import q.a;

/* compiled from: ToolbarWidgetWrapper */
public class n0 implements View.OnClickListener {

    /* renamed from: a  reason: collision with root package name */
    public final a f1149a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ o0 f1150b;

    public n0(o0 o0Var) {
        this.f1150b = o0Var;
        this.f1149a = new a(o0Var.f1152a.getContext(), 0, 16908332, 0, o0Var.f1159i);
    }

    public void onClick(View view) {
        o0 o0Var = this.f1150b;
        Window.Callback callback = o0Var.f1162l;
        if (callback != null && o0Var.f1163m) {
            callback.onMenuItemSelected(0, this.f1149a);
        }
    }
}
