package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.c;
import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import bb.h;
import com.duokan.airkan.common.Constant;
import com.duokan.airkan.server.f;
import com.google.protobuf.Reader;
import java.util.ArrayList;
import w.b;

public class ConstraintWidget {
    public ConstraintAnchor[] A;
    public ArrayList<ConstraintAnchor> B;
    public DimensionBehaviour[] C;
    public ConstraintWidget D;
    public int E;
    public int F;
    public float G;
    public int H;
    public int I;
    public int J;
    public int K;
    public int L;
    public int M;
    public int N;
    public int O;
    public int P;
    public int Q;
    public int R;
    public int S;
    public int T;
    public int U;
    public float V;
    public float W;
    public Object X;
    public int Y;
    public String Z;

    /* renamed from: a  reason: collision with root package name */
    public int f1289a = -1;

    /* renamed from: a0  reason: collision with root package name */
    public boolean f1290a0;

    /* renamed from: b  reason: collision with root package name */
    public int f1291b = -1;

    /* renamed from: b0  reason: collision with root package name */
    public boolean f1292b0;

    /* renamed from: c  reason: collision with root package name */
    public b f1293c;

    /* renamed from: c0  reason: collision with root package name */
    public boolean f1294c0;

    /* renamed from: d  reason: collision with root package name */
    public b f1295d;

    /* renamed from: d0  reason: collision with root package name */
    public int f1296d0;

    /* renamed from: e  reason: collision with root package name */
    public int f1297e = 0;

    /* renamed from: e0  reason: collision with root package name */
    public int f1298e0;

    /* renamed from: f  reason: collision with root package name */
    public int f1299f = 0;

    /* renamed from: f0  reason: collision with root package name */
    public float[] f1300f0;

    /* renamed from: g  reason: collision with root package name */
    public int[] f1301g = new int[2];

    /* renamed from: g0  reason: collision with root package name */
    public ConstraintWidget[] f1302g0;
    public int h = 0;

    /* renamed from: h0  reason: collision with root package name */
    public ConstraintWidget[] f1303h0;

    /* renamed from: i  reason: collision with root package name */
    public int f1304i = 0;

    /* renamed from: j  reason: collision with root package name */
    public float f1305j = 1.0f;

    /* renamed from: k  reason: collision with root package name */
    public int f1306k = 0;

    /* renamed from: l  reason: collision with root package name */
    public int f1307l = 0;

    /* renamed from: m  reason: collision with root package name */
    public float f1308m = 1.0f;

    /* renamed from: n  reason: collision with root package name */
    public int f1309n = -1;

    /* renamed from: o  reason: collision with root package name */
    public float f1310o = 1.0f;

    /* renamed from: p  reason: collision with root package name */
    public f f1311p = null;

    /* renamed from: q  reason: collision with root package name */
    public int[] f1312q = {Reader.READ_DONE, Reader.READ_DONE};

    /* renamed from: r  reason: collision with root package name */
    public float f1313r = Constant.VOLUME_FLOAT_MIN;

    /* renamed from: s  reason: collision with root package name */
    public ConstraintAnchor f1314s = new ConstraintAnchor(this, ConstraintAnchor.Type.LEFT);

    /* renamed from: t  reason: collision with root package name */
    public ConstraintAnchor f1315t = new ConstraintAnchor(this, ConstraintAnchor.Type.TOP);

    /* renamed from: u  reason: collision with root package name */
    public ConstraintAnchor f1316u = new ConstraintAnchor(this, ConstraintAnchor.Type.RIGHT);

    /* renamed from: v  reason: collision with root package name */
    public ConstraintAnchor f1317v = new ConstraintAnchor(this, ConstraintAnchor.Type.BOTTOM);

    /* renamed from: w  reason: collision with root package name */
    public ConstraintAnchor f1318w = new ConstraintAnchor(this, ConstraintAnchor.Type.BASELINE);

    /* renamed from: x  reason: collision with root package name */
    public ConstraintAnchor f1319x = new ConstraintAnchor(this, ConstraintAnchor.Type.CENTER_X);

    /* renamed from: y  reason: collision with root package name */
    public ConstraintAnchor f1320y = new ConstraintAnchor(this, ConstraintAnchor.Type.CENTER_Y);

    /* renamed from: z  reason: collision with root package name */
    public ConstraintAnchor f1321z;

    public enum ContentAlignment {
        BEGIN,
        MIDDLE,
        END,
        TOP,
        VERTICAL_MIDDLE,
        BOTTOM,
        LEFT,
        RIGHT
    }

    public enum DimensionBehaviour {
        FIXED,
        WRAP_CONTENT,
        MATCH_CONSTRAINT,
        MATCH_PARENT
    }

    public static /* synthetic */ class a {

        /* renamed from: a  reason: collision with root package name */
        public static final /* synthetic */ int[] f1324a;

        /* renamed from: b  reason: collision with root package name */
        public static final /* synthetic */ int[] f1325b;

        /* JADX WARNING: Can't wrap try/catch for region: R(29:0|(2:1|2)|3|(2:5|6)|7|9|10|11|(2:13|14)|15|17|18|19|20|21|22|23|24|25|26|27|28|29|30|31|32|33|34|36) */
        /* JADX WARNING: Can't wrap try/catch for region: R(31:0|1|2|3|(2:5|6)|7|9|10|11|13|14|15|17|18|19|20|21|22|23|24|25|26|27|28|29|30|31|32|33|34|36) */
        /* JADX WARNING: Can't wrap try/catch for region: R(32:0|1|2|3|5|6|7|9|10|11|13|14|15|17|18|19|20|21|22|23|24|25|26|27|28|29|30|31|32|33|34|36) */
        /* JADX WARNING: Failed to process nested try/catch */
        /* JADX WARNING: Missing exception handler attribute for start block: B:19:0x0044 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:21:0x004e */
        /* JADX WARNING: Missing exception handler attribute for start block: B:23:0x0058 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:25:0x0062 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:27:0x006d */
        /* JADX WARNING: Missing exception handler attribute for start block: B:29:0x0078 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:31:0x0083 */
        /* JADX WARNING: Missing exception handler attribute for start block: B:33:0x008f */
        static {
            /*
            // Method dump skipped, instructions count: 156
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.ConstraintWidget.a.<clinit>():void");
        }
    }

    public ConstraintWidget() {
        ConstraintAnchor constraintAnchor = new ConstraintAnchor(this, ConstraintAnchor.Type.CENTER);
        this.f1321z = constraintAnchor;
        this.A = new ConstraintAnchor[]{this.f1314s, this.f1316u, this.f1315t, this.f1317v, this.f1318w, constraintAnchor};
        ArrayList<ConstraintAnchor> arrayList = new ArrayList<>();
        this.B = arrayList;
        DimensionBehaviour dimensionBehaviour = DimensionBehaviour.FIXED;
        this.C = new DimensionBehaviour[]{dimensionBehaviour, dimensionBehaviour};
        this.D = null;
        this.E = 0;
        this.F = 0;
        this.G = Constant.VOLUME_FLOAT_MIN;
        this.H = -1;
        this.I = 0;
        this.J = 0;
        this.K = 0;
        this.L = 0;
        this.M = 0;
        this.N = 0;
        this.O = 0;
        this.P = 0;
        this.Q = 0;
        this.V = 0.5f;
        this.W = 0.5f;
        this.Y = 0;
        this.Z = null;
        this.f1290a0 = false;
        this.f1292b0 = false;
        this.f1294c0 = false;
        this.f1296d0 = 0;
        this.f1298e0 = 0;
        this.f1300f0 = new float[]{-1.0f, -1.0f};
        this.f1302g0 = new ConstraintWidget[]{null, null};
        this.f1303h0 = new ConstraintWidget[]{null, null};
        arrayList.add(this.f1314s);
        this.B.add(this.f1315t);
        this.B.add(this.f1316u);
        this.B.add(this.f1317v);
        this.B.add(this.f1319x);
        this.B.add(this.f1320y);
        this.B.add(this.f1321z);
        this.B.add(this.f1318w);
    }

    public void A(int i10, int i11) {
        this.J = i10;
        int i12 = i11 - i10;
        this.F = i12;
        int i13 = this.S;
        if (i12 < i13) {
            this.F = i13;
        }
    }

    public void B(DimensionBehaviour dimensionBehaviour) {
        this.C[1] = dimensionBehaviour;
        if (dimensionBehaviour == DimensionBehaviour.WRAP_CONTENT) {
            w(this.U);
        }
    }

    public void C(int i10) {
        this.E = i10;
        int i11 = this.R;
        if (i10 < i11) {
            this.E = i11;
        }
    }

    public void D() {
        int i10 = this.I;
        int i11 = this.J;
        this.M = i10;
        this.N = i11;
    }

    public void E(c cVar) {
        int i10;
        int i11;
        int o3 = cVar.o(this.f1314s);
        int o10 = cVar.o(this.f1315t);
        int o11 = cVar.o(this.f1316u);
        int o12 = cVar.o(this.f1317v);
        int i12 = o12 - o10;
        if (o11 - o3 < 0 || i12 < 0 || o3 == Integer.MIN_VALUE || o3 == Integer.MAX_VALUE || o10 == Integer.MIN_VALUE || o10 == Integer.MAX_VALUE || o11 == Integer.MIN_VALUE || o11 == Integer.MAX_VALUE || o12 == Integer.MIN_VALUE || o12 == Integer.MAX_VALUE) {
            o12 = 0;
            o3 = 0;
            o10 = 0;
            o11 = 0;
        }
        int i13 = o11 - o3;
        int i14 = o12 - o10;
        this.I = o3;
        this.J = o10;
        if (this.Y == 8) {
            this.E = 0;
            this.F = 0;
            return;
        }
        DimensionBehaviour[] dimensionBehaviourArr = this.C;
        DimensionBehaviour dimensionBehaviour = dimensionBehaviourArr[0];
        DimensionBehaviour dimensionBehaviour2 = DimensionBehaviour.FIXED;
        if (dimensionBehaviour == dimensionBehaviour2 && i13 < (i11 = this.E)) {
            i13 = i11;
        }
        if (dimensionBehaviourArr[1] == dimensionBehaviour2 && i14 < (i10 = this.F)) {
            i14 = i10;
        }
        this.E = i13;
        this.F = i14;
        int i15 = this.S;
        if (i14 < i15) {
            this.F = i15;
        }
        int i16 = this.R;
        if (i13 < i16) {
            this.E = i16;
        }
        this.f1292b0 = true;
    }

    /* JADX WARNING: Removed duplicated region for block: B:185:0x02a0  */
    /* JADX WARNING: Removed duplicated region for block: B:189:0x02aa  */
    /* JADX WARNING: Removed duplicated region for block: B:193:0x02b6  */
    /* JADX WARNING: Removed duplicated region for block: B:199:0x02cf  */
    /* JADX WARNING: Removed duplicated region for block: B:208:0x0331  */
    /* JADX WARNING: Removed duplicated region for block: B:211:0x0342 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:212:0x0343  */
    /* JADX WARNING: Removed duplicated region for block: B:238:0x0398  */
    /* JADX WARNING: Removed duplicated region for block: B:239:0x03a1  */
    /* JADX WARNING: Removed duplicated region for block: B:242:0x03a7  */
    /* JADX WARNING: Removed duplicated region for block: B:243:0x03af  */
    /* JADX WARNING: Removed duplicated region for block: B:246:0x03e6  */
    /* JADX WARNING: Removed duplicated region for block: B:250:0x040f  */
    /* JADX WARNING: Removed duplicated region for block: B:253:0x0419  */
    /* JADX WARNING: Removed duplicated region for block: B:255:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:27:0x006f  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x0078  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void a(androidx.constraintlayout.solver.c r41) {
        /*
        // Method dump skipped, instructions count: 1194
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.ConstraintWidget.a(androidx.constraintlayout.solver.c):void");
    }

    public boolean b() {
        return this.Y != 8;
    }

    public void c(int i10) {
        for (int i11 = 0; i11 < 6; i11++) {
            i iVar = this.A[i11].f1276a;
            ConstraintAnchor constraintAnchor = iVar.f1377c;
            ConstraintAnchor constraintAnchor2 = constraintAnchor.f1279d;
            if (constraintAnchor2 != null) {
                if (constraintAnchor2.f1279d == constraintAnchor) {
                    iVar.h = 4;
                    constraintAnchor2.f1276a.h = 4;
                }
                int b10 = constraintAnchor.b();
                ConstraintAnchor.Type type = iVar.f1377c.f1278c;
                if (type == ConstraintAnchor.Type.RIGHT || type == ConstraintAnchor.Type.BOTTOM) {
                    b10 = -b10;
                }
                iVar.g(constraintAnchor2.f1276a, b10);
            }
        }
        i iVar2 = this.f1314s.f1276a;
        i iVar3 = this.f1315t.f1276a;
        i iVar4 = this.f1316u.f1276a;
        i iVar5 = this.f1317v.f1276a;
        boolean z10 = (i10 & 8) == 8;
        DimensionBehaviour dimensionBehaviour = this.C[0];
        DimensionBehaviour dimensionBehaviour2 = DimensionBehaviour.MATCH_CONSTRAINT;
        boolean z11 = dimensionBehaviour == dimensionBehaviour2 && h.a(this, 0);
        if (!(iVar2.h == 4 || iVar4.h == 4)) {
            if (this.C[0] == DimensionBehaviour.FIXED || (z11 && this.Y == 8)) {
                ConstraintAnchor constraintAnchor3 = this.f1314s.f1279d;
                if (constraintAnchor3 == null && this.f1316u.f1279d == null) {
                    iVar2.h = 1;
                    iVar4.h = 1;
                    if (z10) {
                        iVar4.h(iVar2, 1, l());
                    } else {
                        int n10 = n();
                        iVar4.f1378d = iVar2;
                        iVar4.f1379e = (float) n10;
                        iVar2.f10634a.add(iVar4);
                    }
                } else if (constraintAnchor3 != null && this.f1316u.f1279d == null) {
                    iVar2.h = 1;
                    iVar4.h = 1;
                    if (z10) {
                        iVar4.h(iVar2, 1, l());
                    } else {
                        int n11 = n();
                        iVar4.f1378d = iVar2;
                        iVar4.f1379e = (float) n11;
                        iVar2.f10634a.add(iVar4);
                    }
                } else if (constraintAnchor3 == null && this.f1316u.f1279d != null) {
                    iVar2.h = 1;
                    iVar4.h = 1;
                    iVar2.f1378d = iVar4;
                    iVar2.f1379e = (float) (-n());
                    iVar4.f10634a.add(iVar2);
                    if (z10) {
                        iVar2.h(iVar4, -1, l());
                    } else {
                        iVar2.f1378d = iVar4;
                        iVar2.f1379e = (float) (-n());
                        iVar4.f10634a.add(iVar2);
                    }
                } else if (!(constraintAnchor3 == null || this.f1316u.f1279d == null)) {
                    iVar2.h = 2;
                    iVar4.h = 2;
                    if (z10) {
                        l().f10634a.add(iVar2);
                        l().f10634a.add(iVar4);
                        b l10 = l();
                        iVar2.f1382i = iVar4;
                        iVar2.f1385l = l10;
                        b l11 = l();
                        iVar4.f1382i = iVar2;
                        iVar4.f1385l = l11;
                    } else {
                        iVar2.f1382i = iVar4;
                        iVar4.f1382i = iVar2;
                    }
                }
            } else if (z11) {
                int n12 = n();
                iVar2.h = 1;
                iVar4.h = 1;
                ConstraintAnchor constraintAnchor4 = this.f1314s.f1279d;
                if (constraintAnchor4 == null && this.f1316u.f1279d == null) {
                    if (z10) {
                        iVar4.h(iVar2, 1, l());
                    } else {
                        iVar4.f1378d = iVar2;
                        iVar4.f1379e = (float) n12;
                        iVar2.f10634a.add(iVar4);
                    }
                } else if (constraintAnchor4 == null || this.f1316u.f1279d != null) {
                    if (constraintAnchor4 != null || this.f1316u.f1279d == null) {
                        if (!(constraintAnchor4 == null || this.f1316u.f1279d == null)) {
                            if (z10) {
                                l().f10634a.add(iVar2);
                                l().f10634a.add(iVar4);
                            }
                            if (this.G == Constant.VOLUME_FLOAT_MIN) {
                                iVar2.h = 3;
                                iVar4.h = 3;
                                iVar2.f1382i = iVar4;
                                iVar4.f1382i = iVar2;
                            } else {
                                iVar2.h = 2;
                                iVar4.h = 2;
                                iVar2.f1382i = iVar4;
                                iVar4.f1382i = iVar2;
                                C(n12);
                            }
                        }
                    } else if (z10) {
                        iVar2.h(iVar4, -1, l());
                    } else {
                        iVar2.f1378d = iVar4;
                        iVar2.f1379e = (float) (-n12);
                        iVar4.f10634a.add(iVar2);
                    }
                } else if (z10) {
                    iVar4.h(iVar2, 1, l());
                } else {
                    iVar4.f1378d = iVar2;
                    iVar4.f1379e = (float) n12;
                    iVar2.f10634a.add(iVar4);
                }
            }
        }
        boolean z12 = this.C[1] == dimensionBehaviour2 && h.a(this, 1);
        if (iVar3.h != 4 && iVar5.h != 4) {
            if (this.C[1] == DimensionBehaviour.FIXED || (z12 && this.Y == 8)) {
                ConstraintAnchor constraintAnchor5 = this.f1315t.f1279d;
                if (constraintAnchor5 == null && this.f1317v.f1279d == null) {
                    iVar3.h = 1;
                    iVar5.h = 1;
                    if (z10) {
                        iVar5.h(iVar3, 1, k());
                    } else {
                        int h6 = h();
                        iVar5.f1378d = iVar3;
                        iVar5.f1379e = (float) h6;
                        iVar3.f10634a.add(iVar5);
                    }
                    ConstraintAnchor constraintAnchor6 = this.f1318w;
                    if (constraintAnchor6.f1279d != null) {
                        i iVar6 = constraintAnchor6.f1276a;
                        iVar6.h = 1;
                        iVar3.f(1, iVar6, -this.Q);
                    }
                } else if (constraintAnchor5 != null && this.f1317v.f1279d == null) {
                    iVar3.h = 1;
                    iVar5.h = 1;
                    if (z10) {
                        iVar5.h(iVar3, 1, k());
                    } else {
                        int h10 = h();
                        iVar5.f1378d = iVar3;
                        iVar5.f1379e = (float) h10;
                        iVar3.f10634a.add(iVar5);
                    }
                    int i12 = this.Q;
                    if (i12 > 0) {
                        this.f1318w.f1276a.f(1, iVar3, i12);
                    }
                } else if (constraintAnchor5 == null && this.f1317v.f1279d != null) {
                    iVar3.h = 1;
                    iVar5.h = 1;
                    if (z10) {
                        iVar3.h(iVar5, -1, k());
                    } else {
                        iVar3.f1378d = iVar5;
                        iVar3.f1379e = (float) (-h());
                        iVar5.f10634a.add(iVar3);
                    }
                    int i13 = this.Q;
                    if (i13 > 0) {
                        this.f1318w.f1276a.f(1, iVar3, i13);
                    }
                } else if (constraintAnchor5 != null && this.f1317v.f1279d != null) {
                    iVar3.h = 2;
                    iVar5.h = 2;
                    if (z10) {
                        b k10 = k();
                        iVar3.f1382i = iVar5;
                        iVar3.f1385l = k10;
                        b k11 = k();
                        iVar5.f1382i = iVar3;
                        iVar5.f1385l = k11;
                        k().f10634a.add(iVar3);
                        l().f10634a.add(iVar5);
                    } else {
                        iVar3.f1382i = iVar5;
                        iVar5.f1382i = iVar3;
                    }
                    int i14 = this.Q;
                    if (i14 > 0) {
                        this.f1318w.f1276a.f(1, iVar3, i14);
                    }
                }
            } else if (z12) {
                int h11 = h();
                iVar3.h = 1;
                iVar5.h = 1;
                ConstraintAnchor constraintAnchor7 = this.f1315t.f1279d;
                if (constraintAnchor7 == null && this.f1317v.f1279d == null) {
                    if (z10) {
                        iVar5.h(iVar3, 1, k());
                        return;
                    }
                    iVar5.f1378d = iVar3;
                    iVar5.f1379e = (float) h11;
                    iVar3.f10634a.add(iVar5);
                } else if (constraintAnchor7 == null || this.f1317v.f1279d != null) {
                    if (constraintAnchor7 != null || this.f1317v.f1279d == null) {
                        if (!(constraintAnchor7 == null || this.f1317v.f1279d == null)) {
                            if (z10) {
                                k().f10634a.add(iVar3);
                                l().f10634a.add(iVar5);
                            }
                            if (this.G == Constant.VOLUME_FLOAT_MIN) {
                                iVar3.h = 3;
                                iVar5.h = 3;
                                iVar3.f1382i = iVar5;
                                iVar5.f1382i = iVar3;
                                return;
                            }
                            iVar3.h = 2;
                            iVar5.h = 2;
                            iVar3.f1382i = iVar5;
                            iVar5.f1382i = iVar3;
                            w(h11);
                            int i15 = this.Q;
                            if (i15 > 0) {
                                this.f1318w.f1276a.f(1, iVar3, i15);
                            }
                        }
                    } else if (z10) {
                        iVar3.h(iVar5, -1, k());
                    } else {
                        iVar3.f1378d = iVar5;
                        iVar3.f1379e = (float) (-h11);
                        iVar5.f10634a.add(iVar3);
                    }
                } else if (z10) {
                    iVar5.h(iVar3, 1, k());
                } else {
                    iVar5.f1378d = iVar3;
                    iVar5.f1379e = (float) h11;
                    iVar3.f10634a.add(iVar5);
                }
            }
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:148:0x0265  */
    /* JADX WARNING: Removed duplicated region for block: B:155:0x02a4  */
    /* JADX WARNING: Removed duplicated region for block: B:159:0x02b5  */
    /* JADX WARNING: Removed duplicated region for block: B:169:0x02d4  */
    /* JADX WARNING: Removed duplicated region for block: B:170:0x02da  */
    /* JADX WARNING: Removed duplicated region for block: B:174:0x02e1  */
    /* JADX WARNING: Removed duplicated region for block: B:175:0x02e8  */
    /* JADX WARNING: Removed duplicated region for block: B:183:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:44:0x00af  */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00d9  */
    /* JADX WARNING: Removed duplicated region for block: B:84:0x0190  */
    /* JADX WARNING: Removed duplicated region for block: B:93:0x01a8  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void d(androidx.constraintlayout.solver.c r26, boolean r27, androidx.constraintlayout.solver.SolverVariable r28, androidx.constraintlayout.solver.SolverVariable r29, androidx.constraintlayout.solver.widgets.ConstraintWidget.DimensionBehaviour r30, boolean r31, androidx.constraintlayout.solver.widgets.ConstraintAnchor r32, androidx.constraintlayout.solver.widgets.ConstraintAnchor r33, int r34, int r35, int r36, int r37, float r38, boolean r39, boolean r40, int r41, int r42, int r43, float r44, boolean r45) {
        /*
        // Method dump skipped, instructions count: 762
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.constraintlayout.solver.widgets.ConstraintWidget.d(androidx.constraintlayout.solver.c, boolean, androidx.constraintlayout.solver.SolverVariable, androidx.constraintlayout.solver.SolverVariable, androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour, boolean, androidx.constraintlayout.solver.widgets.ConstraintAnchor, androidx.constraintlayout.solver.widgets.ConstraintAnchor, int, int, int, int, float, boolean, boolean, int, int, int, float, boolean):void");
    }

    public void e(c cVar) {
        cVar.l(this.f1314s);
        cVar.l(this.f1315t);
        cVar.l(this.f1316u);
        cVar.l(this.f1317v);
        if (this.Q > 0) {
            cVar.l(this.f1318w);
        }
    }

    public ConstraintAnchor f(ConstraintAnchor.Type type) {
        switch (a.f1324a[type.ordinal()]) {
            case 1:
                return this.f1314s;
            case 2:
                return this.f1315t;
            case 3:
                return this.f1316u;
            case 4:
                return this.f1317v;
            case 5:
                return this.f1318w;
            case 6:
                return this.f1321z;
            case 7:
                return this.f1319x;
            case 8:
                return this.f1320y;
            case 9:
                return null;
            default:
                throw new AssertionError(type.name());
        }
    }

    public DimensionBehaviour g(int i10) {
        if (i10 == 0) {
            return i();
        }
        if (i10 == 1) {
            return m();
        }
        return null;
    }

    public int h() {
        if (this.Y == 8) {
            return 0;
        }
        return this.F;
    }

    public DimensionBehaviour i() {
        return this.C[0];
    }

    public int j(int i10) {
        if (i10 == 0) {
            return n();
        }
        if (i10 == 1) {
            return h();
        }
        return 0;
    }

    public b k() {
        if (this.f1295d == null) {
            this.f1295d = new b();
        }
        return this.f1295d;
    }

    public b l() {
        if (this.f1293c == null) {
            this.f1293c = new b();
        }
        return this.f1293c;
    }

    public DimensionBehaviour m() {
        return this.C[1];
    }

    public int n() {
        if (this.Y == 8) {
            return 0;
        }
        return this.E;
    }

    public void o(ConstraintAnchor.Type type, ConstraintWidget constraintWidget, ConstraintAnchor.Type type2, int i10, int i11) {
        f(type).a(constraintWidget.f(type2), i10, i11, ConstraintAnchor.Strength.STRONG, 0, true);
    }

    public final boolean p(int i10) {
        int i11 = i10 * 2;
        ConstraintAnchor[] constraintAnchorArr = this.A;
        if (!(constraintAnchorArr[i11].f1279d == null || constraintAnchorArr[i11].f1279d.f1279d == constraintAnchorArr[i11])) {
            int i12 = i11 + 1;
            return constraintAnchorArr[i12].f1279d != null && constraintAnchorArr[i12].f1279d.f1279d == constraintAnchorArr[i12];
        }
    }

    public boolean q() {
        if (this.f1314s.f1276a.f10635b == 1 && this.f1316u.f1276a.f10635b == 1 && this.f1315t.f1276a.f10635b == 1 && this.f1317v.f1276a.f10635b == 1) {
            return true;
        }
        return false;
    }

    public void r() {
        this.f1314s.d();
        this.f1315t.d();
        this.f1316u.d();
        this.f1317v.d();
        this.f1318w.d();
        this.f1319x.d();
        this.f1320y.d();
        this.f1321z.d();
        this.D = null;
        this.f1313r = Constant.VOLUME_FLOAT_MIN;
        this.E = 0;
        this.F = 0;
        this.G = Constant.VOLUME_FLOAT_MIN;
        this.H = -1;
        this.I = 0;
        this.J = 0;
        this.M = 0;
        this.N = 0;
        this.O = 0;
        this.P = 0;
        this.Q = 0;
        this.R = 0;
        this.S = 0;
        this.T = 0;
        this.U = 0;
        this.V = 0.5f;
        this.W = 0.5f;
        DimensionBehaviour[] dimensionBehaviourArr = this.C;
        DimensionBehaviour dimensionBehaviour = DimensionBehaviour.FIXED;
        dimensionBehaviourArr[0] = dimensionBehaviour;
        dimensionBehaviourArr[1] = dimensionBehaviour;
        this.X = null;
        this.Y = 0;
        this.f1296d0 = 0;
        this.f1298e0 = 0;
        float[] fArr = this.f1300f0;
        fArr[0] = -1.0f;
        fArr[1] = -1.0f;
        this.f1289a = -1;
        this.f1291b = -1;
        int[] iArr = this.f1312q;
        iArr[0] = Integer.MAX_VALUE;
        iArr[1] = Integer.MAX_VALUE;
        this.f1297e = 0;
        this.f1299f = 0;
        this.f1305j = 1.0f;
        this.f1308m = 1.0f;
        this.f1304i = Reader.READ_DONE;
        this.f1307l = Reader.READ_DONE;
        this.h = 0;
        this.f1306k = 0;
        this.f1309n = -1;
        this.f1310o = 1.0f;
        b bVar = this.f1293c;
        if (bVar != null) {
            bVar.f10635b = 0;
            bVar.f10634a.clear();
            bVar.f10633c = Constant.VOLUME_FLOAT_MIN;
        }
        b bVar2 = this.f1295d;
        if (bVar2 != null) {
            bVar2.f10635b = 0;
            bVar2.f10634a.clear();
            bVar2.f10633c = Constant.VOLUME_FLOAT_MIN;
        }
        this.f1311p = null;
        this.f1290a0 = false;
        this.f1292b0 = false;
        this.f1294c0 = false;
    }

    public void s() {
        for (int i10 = 0; i10 < 6; i10++) {
            this.A[i10].f1276a.i();
        }
    }

    public void t(v.a aVar) {
        this.f1314s.e();
        this.f1315t.e();
        this.f1316u.e();
        this.f1317v.e();
        this.f1318w.e();
        this.f1321z.e();
        this.f1319x.e();
        this.f1320y.e();
    }

    public String toString() {
        String str = "";
        StringBuilder a10 = f.a(str);
        if (this.Z != null) {
            str = h.b(f.a("id: "), this.Z, " ");
        }
        a10.append(str);
        a10.append("(");
        a10.append(this.I);
        a10.append(", ");
        a10.append(this.J);
        a10.append(") - (");
        a10.append(this.E);
        a10.append(" x ");
        a10.append(this.F);
        a10.append(") wrap: (");
        a10.append(this.T);
        a10.append(" x ");
        return bb.f.b(a10, this.U, ")");
    }

    public void u() {
    }

    public void v(int i10, int i11, int i12) {
        if (i12 == 0) {
            this.I = i10;
            int i13 = i11 - i10;
            this.E = i13;
            int i14 = this.R;
            if (i13 < i14) {
                this.E = i14;
            }
        } else if (i12 == 1) {
            this.J = i10;
            int i15 = i11 - i10;
            this.F = i15;
            int i16 = this.S;
            if (i15 < i16) {
                this.F = i16;
            }
        }
        this.f1292b0 = true;
    }

    public void w(int i10) {
        this.F = i10;
        int i11 = this.S;
        if (i10 < i11) {
            this.F = i11;
        }
    }

    public void x(int i10, int i11) {
        this.I = i10;
        int i12 = i11 - i10;
        this.E = i12;
        int i13 = this.R;
        if (i12 < i13) {
            this.E = i13;
        }
    }

    public void y(DimensionBehaviour dimensionBehaviour) {
        this.C[0] = dimensionBehaviour;
        if (dimensionBehaviour == DimensionBehaviour.WRAP_CONTENT) {
            C(this.T);
        }
    }

    public void z(int i10, int i11) {
        this.O = i10;
        this.P = i11;
    }
}
