package androidx.fragment.app;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.d0;
import com.duokan.airkan.server.f;
import java.io.PrintWriter;
import java.lang.reflect.Modifier;
import java.util.ArrayList;

/* compiled from: BackStackRecord */
public final class a extends d0 implements FragmentManager.k {

    /* renamed from: p  reason: collision with root package name */
    public final FragmentManager f1837p;

    /* renamed from: q  reason: collision with root package name */
    public boolean f1838q;

    /* renamed from: r  reason: collision with root package name */
    public int f1839r;

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public a(@androidx.annotation.NonNull androidx.fragment.app.FragmentManager r3) {
        /*
            r2 = this;
            androidx.fragment.app.u r0 = r3.L()
            androidx.fragment.app.v<?> r1 = r3.f1788q
            if (r1 == 0) goto L_0x000f
            android.content.Context r1 = r1.f2016b
            java.lang.ClassLoader r1 = r1.getClassLoader()
            goto L_0x0010
        L_0x000f:
            r1 = 0
        L_0x0010:
            r2.<init>(r0, r1)
            r0 = -1
            r2.f1839r = r0
            r2.f1837p = r3
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.a.<init>(androidx.fragment.app.FragmentManager):void");
    }

    @Override // androidx.fragment.app.FragmentManager.k
    public boolean a(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2) {
        if (FragmentManager.O(2)) {
            Log.v("FragmentManager", "Run: " + this);
        }
        arrayList.add(this);
        arrayList2.add(Boolean.FALSE);
        if (!this.f1872g) {
            return true;
        }
        FragmentManager fragmentManager = this.f1837p;
        if (fragmentManager.f1776d == null) {
            fragmentManager.f1776d = new ArrayList<>();
        }
        fragmentManager.f1776d.add(this);
        return true;
    }

    @Override // androidx.fragment.app.d0
    public int c() {
        return e(false);
    }

    public void d(int i10) {
        if (this.f1872g) {
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "Bump nesting in " + this + " by " + i10);
            }
            int size = this.f1866a.size();
            for (int i11 = 0; i11 < size; i11++) {
                d0.a aVar = this.f1866a.get(i11);
                Fragment fragment = aVar.f1881b;
                if (fragment != null) {
                    fragment.f1739q += i10;
                    if (FragmentManager.O(2)) {
                        StringBuilder a10 = f.a("Bump nesting of ");
                        a10.append(aVar.f1881b);
                        a10.append(" to ");
                        a10.append(aVar.f1881b.f1739q);
                        Log.v("FragmentManager", a10.toString());
                    }
                }
            }
        }
    }

    public int e(boolean z10) {
        if (!this.f1838q) {
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "Commit: " + this);
                PrintWriter printWriter = new PrintWriter(new q0("FragmentManager"));
                g("  ", printWriter, true);
                printWriter.close();
            }
            this.f1838q = true;
            if (this.f1872g) {
                this.f1839r = this.f1837p.f1780i.getAndIncrement();
            } else {
                this.f1839r = -1;
            }
            this.f1837p.A(this, z10);
            return this.f1839r;
        }
        throw new IllegalStateException("commit already called");
    }

    public void f(int i10, Fragment fragment, @Nullable String str, int i11) {
        Class<?> cls = fragment.getClass();
        int modifiers = cls.getModifiers();
        if (cls.isAnonymousClass() || !Modifier.isPublic(modifiers) || (cls.isMemberClass() && !Modifier.isStatic(modifiers))) {
            StringBuilder a10 = f.a("Fragment ");
            a10.append(cls.getCanonicalName());
            a10.append(" must be a public static class to be  properly recreated from instance state.");
            throw new IllegalStateException(a10.toString());
        }
        if (str != null) {
            String str2 = fragment.f1734n0;
            if (str2 == null || str.equals(str2)) {
                fragment.f1734n0 = str;
            } else {
                throw new IllegalStateException("Can't change tag of fragment " + fragment + ": was " + fragment.f1734n0 + " now " + str);
            }
        }
        if (i10 != 0) {
            if (i10 != -1) {
                int i12 = fragment.f1752y;
                if (i12 == 0 || i12 == i10) {
                    fragment.f1752y = i10;
                    fragment.f1732m0 = i10;
                } else {
                    throw new IllegalStateException("Can't change container ID of fragment " + fragment + ": was " + fragment.f1752y + " now " + i10);
                }
            } else {
                throw new IllegalArgumentException("Can't add fragment " + fragment + " with tag " + str + " to container view with no id");
            }
        }
        b(new d0.a(i11, fragment));
        fragment.f1741r = this.f1837p;
    }

    public void g(String str, PrintWriter printWriter, boolean z10) {
        String str2;
        if (z10) {
            printWriter.print(str);
            printWriter.print("mName=");
            printWriter.print(this.h);
            printWriter.print(" mIndex=");
            printWriter.print(this.f1839r);
            printWriter.print(" mCommitted=");
            printWriter.println(this.f1838q);
            if (this.f1871f != 0) {
                printWriter.print(str);
                printWriter.print("mTransition=#");
                printWriter.print(Integer.toHexString(this.f1871f));
            }
            if (!(this.f1867b == 0 && this.f1868c == 0)) {
                printWriter.print(str);
                printWriter.print("mEnterAnim=#");
                printWriter.print(Integer.toHexString(this.f1867b));
                printWriter.print(" mExitAnim=#");
                printWriter.println(Integer.toHexString(this.f1868c));
            }
            if (!(this.f1869d == 0 && this.f1870e == 0)) {
                printWriter.print(str);
                printWriter.print("mPopEnterAnim=#");
                printWriter.print(Integer.toHexString(this.f1869d));
                printWriter.print(" mPopExitAnim=#");
                printWriter.println(Integer.toHexString(this.f1870e));
            }
            if (!(this.f1873i == 0 && this.f1874j == null)) {
                printWriter.print(str);
                printWriter.print("mBreadCrumbTitleRes=#");
                printWriter.print(Integer.toHexString(this.f1873i));
                printWriter.print(" mBreadCrumbTitleText=");
                printWriter.println(this.f1874j);
            }
            if (!(this.f1875k == 0 && this.f1876l == null)) {
                printWriter.print(str);
                printWriter.print("mBreadCrumbShortTitleRes=#");
                printWriter.print(Integer.toHexString(this.f1875k));
                printWriter.print(" mBreadCrumbShortTitleText=");
                printWriter.println(this.f1876l);
            }
        }
        if (!this.f1866a.isEmpty()) {
            printWriter.print(str);
            printWriter.println("Operations:");
            int size = this.f1866a.size();
            for (int i10 = 0; i10 < size; i10++) {
                d0.a aVar = this.f1866a.get(i10);
                switch (aVar.f1880a) {
                    case 0:
                        str2 = "NULL";
                        break;
                    case 1:
                        str2 = "ADD";
                        break;
                    case 2:
                        str2 = "REPLACE";
                        break;
                    case 3:
                        str2 = "REMOVE";
                        break;
                    case 4:
                        str2 = "HIDE";
                        break;
                    case 5:
                        str2 = "SHOW";
                        break;
                    case 6:
                        str2 = "DETACH";
                        break;
                    case 7:
                        str2 = "ATTACH";
                        break;
                    case 8:
                        str2 = "SET_PRIMARY_NAV";
                        break;
                    case 9:
                        str2 = "UNSET_PRIMARY_NAV";
                        break;
                    case 10:
                        str2 = "OP_SET_MAX_LIFECYCLE";
                        break;
                    default:
                        StringBuilder a10 = f.a("cmd=");
                        a10.append(aVar.f1880a);
                        str2 = a10.toString();
                        break;
                }
                printWriter.print(str);
                printWriter.print("  Op #");
                printWriter.print(i10);
                printWriter.print(": ");
                printWriter.print(str2);
                printWriter.print(" ");
                printWriter.println(aVar.f1881b);
                if (z10) {
                    if (!(aVar.f1882c == 0 && aVar.f1883d == 0)) {
                        printWriter.print(str);
                        printWriter.print("enterAnim=#");
                        printWriter.print(Integer.toHexString(aVar.f1882c));
                        printWriter.print(" exitAnim=#");
                        printWriter.println(Integer.toHexString(aVar.f1883d));
                    }
                    if (aVar.f1884e != 0 || aVar.f1885f != 0) {
                        printWriter.print(str);
                        printWriter.print("popEnterAnim=#");
                        printWriter.print(Integer.toHexString(aVar.f1884e));
                        printWriter.print(" popExitAnim=#");
                        printWriter.println(Integer.toHexString(aVar.f1885f));
                    }
                }
            }
        }
    }

    public void h() {
        int size = this.f1866a.size();
        for (int i10 = 0; i10 < size; i10++) {
            d0.a aVar = this.f1866a.get(i10);
            Fragment fragment = aVar.f1881b;
            if (fragment != null) {
                int i11 = this.f1871f;
                if (!(fragment.f1751x0 == null && i11 == 0)) {
                    fragment.c();
                    fragment.f1751x0.f1761d = i11;
                }
                ArrayList<String> arrayList = this.f1877m;
                ArrayList<String> arrayList2 = this.f1878n;
                fragment.c();
                Fragment.b bVar = fragment.f1751x0;
                bVar.f1762e = arrayList;
                bVar.f1763f = arrayList2;
            }
            switch (aVar.f1880a) {
                case 1:
                    fragment.U(aVar.f1882c);
                    this.f1837p.d0(fragment, false);
                    this.f1837p.a(fragment);
                    break;
                case 2:
                default:
                    StringBuilder a10 = f.a("Unknown cmd: ");
                    a10.append(aVar.f1880a);
                    throw new IllegalArgumentException(a10.toString());
                case 3:
                    fragment.U(aVar.f1883d);
                    this.f1837p.Y(fragment);
                    break;
                case 4:
                    fragment.U(aVar.f1883d);
                    this.f1837p.N(fragment);
                    break;
                case 5:
                    fragment.U(aVar.f1882c);
                    this.f1837p.d0(fragment, false);
                    this.f1837p.h0(fragment);
                    break;
                case 6:
                    fragment.U(aVar.f1883d);
                    this.f1837p.j(fragment);
                    break;
                case 7:
                    fragment.U(aVar.f1882c);
                    this.f1837p.d0(fragment, false);
                    this.f1837p.c(fragment);
                    break;
                case 8:
                    this.f1837p.f0(fragment);
                    break;
                case 9:
                    this.f1837p.f0(null);
                    break;
                case 10:
                    this.f1837p.e0(fragment, aVar.h);
                    break;
            }
        }
    }

    public void i(boolean z10) {
        for (int size = this.f1866a.size() - 1; size >= 0; size--) {
            d0.a aVar = this.f1866a.get(size);
            Fragment fragment = aVar.f1881b;
            if (fragment != null) {
                int i10 = this.f1871f;
                int i11 = 8194;
                if (i10 != 4097) {
                    i11 = i10 != 4099 ? i10 != 8194 ? 0 : 4097 : 4099;
                }
                if (!(fragment.f1751x0 == null && i11 == 0)) {
                    fragment.c();
                    fragment.f1751x0.f1761d = i11;
                }
                ArrayList<String> arrayList = this.f1878n;
                ArrayList<String> arrayList2 = this.f1877m;
                fragment.c();
                Fragment.b bVar = fragment.f1751x0;
                bVar.f1762e = arrayList;
                bVar.f1763f = arrayList2;
            }
            switch (aVar.f1880a) {
                case 1:
                    fragment.U(aVar.f1885f);
                    this.f1837p.d0(fragment, true);
                    this.f1837p.Y(fragment);
                    break;
                case 2:
                default:
                    StringBuilder a10 = f.a("Unknown cmd: ");
                    a10.append(aVar.f1880a);
                    throw new IllegalArgumentException(a10.toString());
                case 3:
                    fragment.U(aVar.f1884e);
                    this.f1837p.a(fragment);
                    break;
                case 4:
                    fragment.U(aVar.f1884e);
                    this.f1837p.h0(fragment);
                    break;
                case 5:
                    fragment.U(aVar.f1885f);
                    this.f1837p.d0(fragment, true);
                    this.f1837p.N(fragment);
                    break;
                case 6:
                    fragment.U(aVar.f1884e);
                    this.f1837p.c(fragment);
                    break;
                case 7:
                    fragment.U(aVar.f1885f);
                    this.f1837p.d0(fragment, true);
                    this.f1837p.j(fragment);
                    break;
                case 8:
                    this.f1837p.f0(null);
                    break;
                case 9:
                    this.f1837p.f0(fragment);
                    break;
                case 10:
                    this.f1837p.e0(fragment, aVar.f1886g);
                    break;
            }
        }
    }

    public boolean j(int i10) {
        int size = this.f1866a.size();
        for (int i11 = 0; i11 < size; i11++) {
            Fragment fragment = this.f1866a.get(i11).f1881b;
            int i12 = fragment != null ? fragment.f1732m0 : 0;
            if (i12 != 0 && i12 == i10) {
                return true;
            }
        }
        return false;
    }

    public boolean k(ArrayList<a> arrayList, int i10, int i11) {
        if (i11 == i10) {
            return false;
        }
        int size = this.f1866a.size();
        int i12 = -1;
        for (int i13 = 0; i13 < size; i13++) {
            Fragment fragment = this.f1866a.get(i13).f1881b;
            int i14 = fragment != null ? fragment.f1732m0 : 0;
            if (!(i14 == 0 || i14 == i12)) {
                for (int i15 = i10; i15 < i11; i15++) {
                    a aVar = arrayList.get(i15);
                    int size2 = aVar.f1866a.size();
                    for (int i16 = 0; i16 < size2; i16++) {
                        Fragment fragment2 = aVar.f1866a.get(i16).f1881b;
                        if ((fragment2 != null ? fragment2.f1732m0 : 0) == i14) {
                            return true;
                        }
                    }
                }
                i12 = i14;
            }
        }
        return false;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("BackStackEntry{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        if (this.f1839r >= 0) {
            sb.append(" #");
            sb.append(this.f1839r);
        }
        if (this.h != null) {
            sb.append(" ");
            sb.append(this.h);
        }
        sb.append("}");
        return sb.toString();
    }
}
