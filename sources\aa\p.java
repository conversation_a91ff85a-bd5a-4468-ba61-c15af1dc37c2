package aa;

import java.io.IOException;
import java.io.OutputStream;

/* compiled from: ASN1OutputStream */
public class p {

    /* renamed from: a  reason: collision with root package name */
    public OutputStream f212a;

    /* compiled from: ASN1OutputStream */
    public class a extends p {

        /* renamed from: b  reason: collision with root package name */
        public boolean f213b = true;

        public a(p pVar, OutputStream outputStream) {
            super(outputStream);
        }

        @Override // aa.p
        public void c(int i10) throws IOException {
            if (this.f213b) {
                this.f213b = false;
            } else {
                this.f212a.write(i10);
            }
        }
    }

    public p(OutputStream outputStream) {
        this.f212a = outputStream;
    }

    public p a() {
        return new y0(this.f212a);
    }

    public p b() {
        return new l1(this.f212a);
    }

    public void c(int i10) throws IOException {
        this.f212a.write(i10);
    }

    public void d(int i10, int i11, byte[] bArr) throws IOException {
        i(i10, i11);
        g(bArr.length);
        this.f212a.write(bArr);
    }

    public void e(int i10, byte[] bArr) throws IOException {
        c(i10);
        g(bArr.length);
        this.f212a.write(bArr);
    }

    public void f(q qVar) throws IOException {
        qVar.h(new a(this, this.f212a));
    }

    public void g(int i10) throws IOException {
        if (i10 > 127) {
            int i11 = i10;
            int i12 = 1;
            while (true) {
                i11 >>>= 8;
                if (i11 == 0) {
                    break;
                }
                i12++;
            }
            c((byte) (i12 | 128));
            for (int i13 = (i12 - 1) * 8; i13 >= 0; i13 -= 8) {
                c((byte) (i10 >> i13));
            }
            return;
        }
        c((byte) i10);
    }

    public void h(e eVar) throws IOException {
        if (eVar != null) {
            eVar.c().h(this);
            return;
        }
        throw new IOException("null object detected");
    }

    public void i(int i10, int i11) throws IOException {
        if (i11 < 31) {
            c(i10 | i11);
            return;
        }
        c(i10 | 31);
        if (i11 < 128) {
            c(i11);
            return;
        }
        byte[] bArr = new byte[5];
        int i12 = 4;
        bArr[4] = (byte) (i11 & 127);
        do {
            i11 >>= 7;
            i12--;
            bArr[i12] = (byte) ((i11 & 127) | 128);
        } while (i11 > 127);
        this.f212a.write(bArr, i12, 5 - i12);
    }
}
