package androidx.lifecycle;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;

/* access modifiers changed from: package-private */
public class CompositeGeneratedAdaptersObserver implements e {

    /* renamed from: a  reason: collision with root package name */
    public final d[] f2029a;

    public CompositeGeneratedAdaptersObserver(d[] dVarArr) {
        this.f2029a = dVarArr;
    }

    @Override // androidx.lifecycle.e
    public void d(@NonNull g gVar, @NonNull Lifecycle.Event event) {
        k kVar = new k();
        for (d dVar : this.f2029a) {
            dVar.a(gVar, event, false, kVar);
        }
        for (d dVar2 : this.f2029a) {
            dVar2.a(gVar, event, true, kVar);
        }
    }
}
