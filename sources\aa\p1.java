package aa;

import com.duokan.airkan.server.f;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import ob.a;

/* compiled from: DefiniteLengthInputStream */
public class p1 extends u1 {

    /* renamed from: e  reason: collision with root package name */
    public static final byte[] f215e = new byte[0];

    /* renamed from: c  reason: collision with root package name */
    public final int f216c;

    /* renamed from: d  reason: collision with root package name */
    public int f217d;

    public p1(InputStream inputStream, int i10) {
        super(inputStream, i10);
        if (i10 >= 0) {
            this.f216c = i10;
            this.f217d = i10;
            if (i10 == 0) {
                d(true);
                return;
            }
            return;
        }
        throw new IllegalArgumentException("negative lengths not allowed");
    }

    @Override // aa.u1
    public int a() {
        return this.f217d;
    }

    public byte[] e() throws IOException {
        int i10 = this.f217d;
        if (i10 == 0) {
            return f215e;
        }
        byte[] bArr = new byte[i10];
        int a10 = i10 - a.a(this.f235a, bArr);
        this.f217d = a10;
        if (a10 == 0) {
            d(true);
            return bArr;
        }
        StringBuilder a11 = f.a("DEF length ");
        a11.append(this.f216c);
        a11.append(" object truncated by ");
        a11.append(this.f217d);
        throw new EOFException(a11.toString());
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (this.f217d == 0) {
            return -1;
        }
        int read = this.f235a.read();
        if (read >= 0) {
            int i10 = this.f217d - 1;
            this.f217d = i10;
            if (i10 == 0) {
                d(true);
            }
            return read;
        }
        StringBuilder a10 = f.a("DEF length ");
        a10.append(this.f216c);
        a10.append(" object truncated by ");
        a10.append(this.f217d);
        throw new EOFException(a10.toString());
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i10, int i11) throws IOException {
        int i12 = this.f217d;
        if (i12 == 0) {
            return -1;
        }
        int read = this.f235a.read(bArr, i10, Math.min(i11, i12));
        if (read >= 0) {
            int i13 = this.f217d - read;
            this.f217d = i13;
            if (i13 == 0) {
                d(true);
            }
            return read;
        }
        StringBuilder a10 = f.a("DEF length ");
        a10.append(this.f216c);
        a10.append(" object truncated by ");
        a10.append(this.f217d);
        throw new EOFException(a10.toString());
    }
}
