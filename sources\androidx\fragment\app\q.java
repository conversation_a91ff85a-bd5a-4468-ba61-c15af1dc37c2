package androidx.fragment.app;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.k0;
import f0.a;

/* compiled from: FragmentAnim */
public class q extends AnimatorListenerAdapter {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ ViewGroup f1982a;

    /* renamed from: b  reason: collision with root package name */
    public final /* synthetic */ View f1983b;

    /* renamed from: c  reason: collision with root package name */
    public final /* synthetic */ Fragment f1984c;

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ k0.a f1985d;

    /* renamed from: e  reason: collision with root package name */
    public final /* synthetic */ a f1986e;

    public q(ViewGroup viewGroup, View view, Fragment fragment, k0.a aVar, a aVar2) {
        this.f1982a = viewGroup;
        this.f1983b = view;
        this.f1984c = fragment;
        this.f1985d = aVar;
        this.f1986e = aVar2;
    }

    public void onAnimationEnd(Animator animator) {
        Animator animator2;
        this.f1982a.endViewTransition(this.f1983b);
        Fragment fragment = this.f1984c;
        Fragment.b bVar = fragment.f1751x0;
        if (bVar == null) {
            animator2 = null;
        } else {
            animator2 = bVar.f1759b;
        }
        fragment.Q(null);
        if (animator2 != null && this.f1982a.indexOfChild(this.f1983b) < 0) {
            ((FragmentManager.d) this.f1985d).a(this.f1984c, this.f1986e);
        }
    }
}
