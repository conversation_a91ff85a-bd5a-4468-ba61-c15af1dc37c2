package a4;

import android.os.Handler;
import com.duokan.airkan.common.Log;

public class e implements Runnable {

    /* renamed from: a  reason: collision with root package name */
    public final /* synthetic */ f f20a;

    public e(f fVar) {
        this.f20a = fVar;
    }

    public void run() {
        String str;
        String str2;
        Log.i("UDTDiscoverManager", "Close JmDNS service");
        f fVar = this.f20a;
        synchronized (fVar.f32m) {
            l lVar = fVar.f27g;
            if (lVar != null) {
                Handler handler = lVar.h;
                if (handler != null) {
                    handler.post(new k(lVar));
                } else {
                    Log.e("AKTS-UDTJmDNSThread", "Handler not available, close JmDNS service failed!");
                }
                try {
                    fVar.f27g.join();
                } catch (Exception e10) {
                    Log.e("UDTDiscoverManager", "Exception: " + e10.toString());
                }
                fVar.f27g = null;
                str2 = "UDTDiscoverManager";
                str = "JmDNS service stopped";
            } else {
                str2 = "UDTDiscoverManager";
                str = "JmDNS service already stopped";
            }
            Log.d(str2, str);
        }
        Log.i("UDTDiscoverManager", "Close JmDNS service done");
    }
}
