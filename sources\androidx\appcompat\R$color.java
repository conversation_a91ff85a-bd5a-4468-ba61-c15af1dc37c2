package androidx.appcompat;

public final class R$color {
    public static final int abc_background_cache_hint_selector_material_dark = 2131099649;
    public static final int abc_background_cache_hint_selector_material_light = 2131099650;
    public static final int abc_btn_colored_borderless_text_material = 2131099651;
    public static final int abc_btn_colored_text_material = 2131099652;
    public static final int abc_color_highlight_material = 2131099653;
    public static final int abc_decor_view_status_guard = 2131099654;
    public static final int abc_decor_view_status_guard_light = 2131099655;
    public static final int abc_hint_foreground_material_dark = 2131099656;
    public static final int abc_hint_foreground_material_light = 2131099657;
    public static final int abc_primary_text_disable_only_material_dark = 2131099658;
    public static final int abc_primary_text_disable_only_material_light = 2131099659;
    public static final int abc_primary_text_material_dark = 2131099660;
    public static final int abc_primary_text_material_light = 2131099661;
    public static final int abc_search_url_text = 2131099662;
    public static final int abc_search_url_text_normal = 2131099663;
    public static final int abc_search_url_text_pressed = 2131099664;
    public static final int abc_search_url_text_selected = 2131099665;
    public static final int abc_secondary_text_material_dark = 2131099666;
    public static final int abc_secondary_text_material_light = 2131099667;
    public static final int abc_tint_btn_checkable = 2131099668;
    public static final int abc_tint_default = 2131099669;
    public static final int abc_tint_edittext = 2131099670;
    public static final int abc_tint_seek_thumb = 2131099671;
    public static final int abc_tint_spinner = 2131099672;
    public static final int abc_tint_switch_track = 2131099673;
    public static final int accent_material_dark = 2131099674;
    public static final int accent_material_light = 2131099675;
    public static final int androidx_core_ripple_material_light = 2131099676;
    public static final int androidx_core_secondary_text_default_material_light = 2131099677;
    public static final int background_floating_material_dark = 2131099682;
    public static final int background_floating_material_light = 2131099683;
    public static final int background_material_dark = 2131099685;
    public static final int background_material_light = 2131099686;
    public static final int bright_foreground_disabled_material_dark = 2131099692;
    public static final int bright_foreground_disabled_material_light = 2131099693;
    public static final int bright_foreground_inverse_material_dark = 2131099694;
    public static final int bright_foreground_inverse_material_light = 2131099695;
    public static final int bright_foreground_material_dark = 2131099696;
    public static final int bright_foreground_material_light = 2131099697;
    public static final int button_material_dark = 2131099698;
    public static final int button_material_light = 2131099699;
    public static final int dim_foreground_disabled_material_dark = 2131099743;
    public static final int dim_foreground_disabled_material_light = 2131099744;
    public static final int dim_foreground_material_dark = 2131099745;
    public static final int dim_foreground_material_light = 2131099746;
    public static final int error_color_material_dark = 2131099747;
    public static final int error_color_material_light = 2131099748;
    public static final int foreground_material_dark = 2131099749;
    public static final int foreground_material_light = 2131099750;
    public static final int highlighted_text_material_dark = 2131099751;
    public static final int highlighted_text_material_light = 2131099752;
    public static final int material_blue_grey_800 = 2131099753;
    public static final int material_blue_grey_900 = 2131099754;
    public static final int material_blue_grey_950 = 2131099755;
    public static final int material_deep_teal_200 = 2131099756;
    public static final int material_deep_teal_500 = 2131099757;
    public static final int material_grey_100 = 2131099758;
    public static final int material_grey_300 = 2131099759;
    public static final int material_grey_50 = 2131099760;
    public static final int material_grey_600 = 2131099761;
    public static final int material_grey_800 = 2131099762;
    public static final int material_grey_850 = 2131099763;
    public static final int material_grey_900 = 2131099764;
    public static final int notification_action_color_filter = 2131100322;
    public static final int notification_icon_bg_color = 2131100323;
    public static final int primary_dark_material_dark = 2131100325;
    public static final int primary_dark_material_light = 2131100326;
    public static final int primary_material_dark = 2131100327;
    public static final int primary_material_light = 2131100328;
    public static final int primary_text_default_material_dark = 2131100329;
    public static final int primary_text_default_material_light = 2131100330;
    public static final int primary_text_disabled_material_dark = 2131100331;
    public static final int primary_text_disabled_material_light = 2131100332;
    public static final int ripple_material_dark = 2131100338;
    public static final int ripple_material_light = 2131100339;
    public static final int secondary_text_default_material_dark = 2131100340;
    public static final int secondary_text_default_material_light = 2131100341;
    public static final int secondary_text_disabled_material_dark = 2131100342;
    public static final int secondary_text_disabled_material_light = 2131100343;
    public static final int switch_thumb_disabled_material_dark = 2131100344;
    public static final int switch_thumb_disabled_material_light = 2131100345;
    public static final int switch_thumb_material_dark = 2131100346;
    public static final int switch_thumb_material_light = 2131100347;
    public static final int switch_thumb_normal_material_dark = 2131100348;
    public static final int switch_thumb_normal_material_light = 2131100349;
    public static final int tooltip_background_dark = 2131100354;
    public static final int tooltip_background_light = 2131100355;

    private R$color() {
    }
}
