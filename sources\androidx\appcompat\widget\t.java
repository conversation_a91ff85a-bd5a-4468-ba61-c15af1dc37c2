package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.Menu;
import android.view.ViewGroup;
import android.view.Window;
import androidx.annotation.RestrictTo;
import androidx.appcompat.view.menu.d;
import androidx.appcompat.view.menu.h;
import j0.m;

@RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
/* compiled from: DecorToolbar */
public interface t {
    void a(Menu menu, h.a aVar);

    boolean b();

    void c();

    void collapseActionView();

    boolean d();

    boolean e();

    boolean f();

    boolean g();

    Context getContext();

    CharSequence getTitle();

    void h();

    void i(h.a aVar, d.a aVar2);

    void j(e0 e0Var);

    ViewGroup k();

    void l(boolean z10);

    boolean m();

    void n(int i10);

    int o();

    Menu p();

    void q(int i10);

    int r();

    m s(int i10, long j10);

    void setIcon(int i10);

    void setIcon(Drawable drawable);

    void setVisibility(int i10);

    void setWindowCallback(Window.Callback callback);

    void setWindowTitle(CharSequence charSequence);

    void t();

    void u();

    void v(boolean z10);
}
