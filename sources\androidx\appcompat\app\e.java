package androidx.appcompat.app;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.CallSuper;
import androidx.annotation.ContentView;
import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.widget.p0;
import androidx.fragment.app.n;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Objects;
import p.a;
import u.c;
import y.d;
import y.g;

/* compiled from: AppCompatActivity */
public class e extends n implements f, g.a {
    private AppCompatDelegate mDelegate;
    private Resources mResources;

    public e() {
    }

    private boolean performMenuItemShortcut(KeyEvent keyEvent) {
        return false;
    }

    @Override // androidx.activity.ComponentActivity
    public void addContentView(View view, ViewGroup.LayoutParams layoutParams) {
        getDelegate().c(view, layoutParams);
    }

    public void attachBaseContext(Context context) {
        super.attachBaseContext(getDelegate().d(context));
    }

    public void closeOptionsMenu() {
        ActionBar supportActionBar = getSupportActionBar();
        if (!getWindow().hasFeature(0)) {
            return;
        }
        if (supportActionBar == null || !supportActionBar.a()) {
            super.closeOptionsMenu();
        }
    }

    @Override // y.c
    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        int keyCode = keyEvent.getKeyCode();
        ActionBar supportActionBar = getSupportActionBar();
        if (keyCode != 82 || supportActionBar == null || !supportActionBar.k(keyEvent)) {
            return super.dispatchKeyEvent(keyEvent);
        }
        return true;
    }

    @Override // android.app.Activity
    public <T extends View> T findViewById(@IdRes int i10) {
        return (T) getDelegate().e(i10);
    }

    @NonNull
    public AppCompatDelegate getDelegate() {
        if (this.mDelegate == null) {
            c<WeakReference<AppCompatDelegate>> cVar = AppCompatDelegate.f367a;
            this.mDelegate = new AppCompatDelegateImpl(this, null, this, this);
        }
        return this.mDelegate;
    }

    @Nullable
    public a getDrawerToggleDelegate() {
        return getDelegate().f();
    }

    @NonNull
    public MenuInflater getMenuInflater() {
        return getDelegate().h();
    }

    public Resources getResources() {
        Resources resources = this.mResources;
        if (resources == null) {
            int i10 = p0.f1170a;
        }
        return resources == null ? super.getResources() : resources;
    }

    @Nullable
    public ActionBar getSupportActionBar() {
        return getDelegate().i();
    }

    @Override // y.g.a
    @Nullable
    public Intent getSupportParentActivityIntent() {
        return d.a(this);
    }

    public void invalidateOptionsMenu() {
        getDelegate().k();
    }

    @Override // androidx.fragment.app.n
    public void onConfigurationChanged(@NonNull Configuration configuration) {
        super.onConfigurationChanged(configuration);
        if (this.mResources != null) {
            this.mResources.updateConfiguration(configuration, super.getResources().getDisplayMetrics());
        }
        getDelegate().l(configuration);
    }

    public void onContentChanged() {
        onSupportContentChanged();
    }

    @Override // androidx.activity.ComponentActivity, y.c, androidx.fragment.app.n
    public void onCreate(@Nullable Bundle bundle) {
        AppCompatDelegate delegate = getDelegate();
        delegate.j();
        delegate.m(bundle);
        super.onCreate(bundle);
    }

    public void onCreateSupportNavigateUpTaskStack(@NonNull g gVar) {
        Objects.requireNonNull(gVar);
        Intent supportParentActivityIntent = getSupportParentActivityIntent();
        if (supportParentActivityIntent == null) {
            supportParentActivityIntent = d.a(this);
        }
        if (supportParentActivityIntent != null) {
            ComponentName component = supportParentActivityIntent.getComponent();
            if (component == null) {
                component = supportParentActivityIntent.resolveActivity(gVar.f10935b.getPackageManager());
            }
            int size = gVar.f10934a.size();
            try {
                Intent b10 = d.b(gVar.f10935b, component);
                while (b10 != null) {
                    gVar.f10934a.add(size, b10);
                    b10 = d.b(gVar.f10935b, b10.getComponent());
                }
                gVar.f10934a.add(supportParentActivityIntent);
            } catch (PackageManager.NameNotFoundException e10) {
                Log.e("TaskStackBuilder", "Bad ComponentName while traversing activity parent metadata");
                throw new IllegalArgumentException(e10);
            }
        }
    }

    @Override // androidx.fragment.app.n
    public void onDestroy() {
        super.onDestroy();
        getDelegate().n();
    }

    public boolean onKeyDown(int i10, KeyEvent keyEvent) {
        if (performMenuItemShortcut(keyEvent)) {
            return true;
        }
        return super.onKeyDown(i10, keyEvent);
    }

    @Override // androidx.fragment.app.n
    public final boolean onMenuItemSelected(int i10, @NonNull MenuItem menuItem) {
        if (super.onMenuItemSelected(i10, menuItem)) {
            return true;
        }
        ActionBar supportActionBar = getSupportActionBar();
        if (menuItem.getItemId() != 16908332 || supportActionBar == null || (supportActionBar.d() & 4) == 0) {
            return false;
        }
        return onSupportNavigateUp();
    }

    public boolean onMenuOpened(int i10, Menu menu) {
        return super.onMenuOpened(i10, menu);
    }

    public void onNightModeChanged(int i10) {
    }

    @Override // androidx.fragment.app.n
    public void onPanelClosed(int i10, @NonNull Menu menu) {
        super.onPanelClosed(i10, menu);
    }

    public void onPostCreate(@Nullable Bundle bundle) {
        super.onPostCreate(bundle);
        getDelegate().o(bundle);
    }

    @Override // androidx.fragment.app.n
    public void onPostResume() {
        super.onPostResume();
        getDelegate().p();
    }

    public void onPrepareSupportNavigateUpTaskStack(@NonNull g gVar) {
    }

    @Override // androidx.activity.ComponentActivity, y.c
    public void onSaveInstanceState(@NonNull Bundle bundle) {
        super.onSaveInstanceState(bundle);
        getDelegate().q(bundle);
    }

    @Override // androidx.fragment.app.n
    public void onStart() {
        super.onStart();
        getDelegate().r();
    }

    @Override // androidx.fragment.app.n
    public void onStop() {
        super.onStop();
        getDelegate().s();
    }

    @Override // androidx.appcompat.app.f
    @CallSuper
    public void onSupportActionModeFinished(@NonNull a aVar) {
    }

    @Override // androidx.appcompat.app.f
    @CallSuper
    public void onSupportActionModeStarted(@NonNull a aVar) {
    }

    @Deprecated
    public void onSupportContentChanged() {
    }

    public boolean onSupportNavigateUp() {
        Intent supportParentActivityIntent = getSupportParentActivityIntent();
        if (supportParentActivityIntent == null) {
            return false;
        }
        if (supportShouldUpRecreateTask(supportParentActivityIntent)) {
            g gVar = new g(this);
            onCreateSupportNavigateUpTaskStack(gVar);
            onPrepareSupportNavigateUpTaskStack(gVar);
            if (!gVar.f10934a.isEmpty()) {
                ArrayList<Intent> arrayList = gVar.f10934a;
                Intent[] intentArr = (Intent[]) arrayList.toArray(new Intent[arrayList.size()]);
                intentArr[0] = new Intent(intentArr[0]).addFlags(268484608);
                Context context = gVar.f10935b;
                Object obj = z.a.f11008a;
                context.startActivities(intentArr, null);
                try {
                    int i10 = y.a.f10932b;
                    finishAffinity();
                    return true;
                } catch (IllegalStateException unused) {
                    finish();
                    return true;
                }
            } else {
                throw new IllegalStateException("No intents added to TaskStackBuilder; cannot startActivities");
            }
        } else {
            supportNavigateUpTo(supportParentActivityIntent);
            return true;
        }
    }

    public void onTitleChanged(CharSequence charSequence, int i10) {
        super.onTitleChanged(charSequence, i10);
        getDelegate().A(charSequence);
    }

    @Override // androidx.appcompat.app.f
    @Nullable
    public a onWindowStartingSupportActionMode(@NonNull a.AbstractC0146a aVar) {
        return null;
    }

    public void openOptionsMenu() {
        ActionBar supportActionBar = getSupportActionBar();
        if (!getWindow().hasFeature(0)) {
            return;
        }
        if (supportActionBar == null || !supportActionBar.l()) {
            super.openOptionsMenu();
        }
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void setContentView(@LayoutRes int i10) {
        getDelegate().v(i10);
    }

    public void setSupportActionBar(@Nullable Toolbar toolbar) {
        getDelegate().y(toolbar);
    }

    @Deprecated
    public void setSupportProgress(int i10) {
    }

    @Deprecated
    public void setSupportProgressBarIndeterminate(boolean z10) {
    }

    @Deprecated
    public void setSupportProgressBarIndeterminateVisibility(boolean z10) {
    }

    @Deprecated
    public void setSupportProgressBarVisibility(boolean z10) {
    }

    @Override // android.view.ContextThemeWrapper, android.app.Activity
    public void setTheme(@StyleRes int i10) {
        super.setTheme(i10);
        getDelegate().z(i10);
    }

    @Nullable
    public a startSupportActionMode(@NonNull a.AbstractC0146a aVar) {
        return getDelegate().B(aVar);
    }

    @Override // androidx.fragment.app.n
    public void supportInvalidateOptionsMenu() {
        getDelegate().k();
    }

    public void supportNavigateUpTo(@NonNull Intent intent) {
        navigateUpTo(intent);
    }

    public boolean supportRequestWindowFeature(int i10) {
        return getDelegate().u(i10);
    }

    public boolean supportShouldUpRecreateTask(@NonNull Intent intent) {
        return shouldUpRecreateTask(intent);
    }

    @ContentView
    public e(@LayoutRes int i10) {
        super(i10);
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public void setContentView(View view) {
        getDelegate().w(view);
    }

    @Override // androidx.activity.ComponentActivity
    public void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        getDelegate().x(view, layoutParams);
    }
}
