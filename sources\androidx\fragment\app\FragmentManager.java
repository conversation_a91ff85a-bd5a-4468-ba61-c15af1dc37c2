package androidx.fragment.app;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Looper;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.OnBackPressedDispatcher;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.IntentSenderRequest;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.f0;
import androidx.fragment.R$id;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.d0;
import androidx.fragment.app.k0;
import androidx.fragment.app.z;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.q;
import androidx.lifecycle.s;
import androidx.lifecycle.t;
import androidx.lifecycle.v;
import androidx.lifecycle.w;
import androidx.lifecycle.x;
import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class FragmentManager {
    public boolean A;
    public boolean B;
    public boolean C;
    public boolean D;
    public boolean E;
    public ArrayList<a> F;
    public ArrayList<Boolean> G;
    public ArrayList<Fragment> H;
    public ArrayList<m> I;
    public z J;
    public Runnable K = new g();

    /* renamed from: a  reason: collision with root package name */
    public final ArrayList<k> f1773a = new ArrayList<>();

    /* renamed from: b  reason: collision with root package name */
    public boolean f1774b;

    /* renamed from: c  reason: collision with root package name */
    public final c0 f1775c = new c0();

    /* renamed from: d  reason: collision with root package name */
    public ArrayList<a> f1776d;

    /* renamed from: e  reason: collision with root package name */
    public ArrayList<Fragment> f1777e;

    /* renamed from: f  reason: collision with root package name */
    public final w f1778f = new w(this);

    /* renamed from: g  reason: collision with root package name */
    public OnBackPressedDispatcher f1779g;
    public final androidx.activity.b h = new c(false);

    /* renamed from: i  reason: collision with root package name */
    public final AtomicInteger f1780i = new AtomicInteger();

    /* renamed from: j  reason: collision with root package name */
    public final Map<String, Bundle> f1781j = Collections.synchronizedMap(new HashMap());

    /* renamed from: k  reason: collision with root package name */
    public final Map<String, Object> f1782k = Collections.synchronizedMap(new HashMap());

    /* renamed from: l  reason: collision with root package name */
    public Map<Fragment, HashSet<f0.a>> f1783l = Collections.synchronizedMap(new HashMap());

    /* renamed from: m  reason: collision with root package name */
    public final k0.a f1784m = new d();

    /* renamed from: n  reason: collision with root package name */
    public final x f1785n = new x(this);

    /* renamed from: o  reason: collision with root package name */
    public final CopyOnWriteArrayList<a0> f1786o = new CopyOnWriteArrayList<>();

    /* renamed from: p  reason: collision with root package name */
    public int f1787p = -1;

    /* renamed from: q  reason: collision with root package name */
    public v<?> f1788q;

    /* renamed from: r  reason: collision with root package name */
    public s f1789r;

    /* renamed from: s  reason: collision with root package name */
    public Fragment f1790s;
    @Nullable

    /* renamed from: t  reason: collision with root package name */
    public Fragment f1791t;

    /* renamed from: u  reason: collision with root package name */
    public u f1792u = new e();

    /* renamed from: v  reason: collision with root package name */
    public y0 f1793v = new f(this);

    /* renamed from: w  reason: collision with root package name */
    public androidx.activity.result.b<Intent> f1794w;

    /* renamed from: x  reason: collision with root package name */
    public androidx.activity.result.b<IntentSenderRequest> f1795x;

    /* renamed from: y  reason: collision with root package name */
    public androidx.activity.result.b<String[]> f1796y;

    /* renamed from: z  reason: collision with root package name */
    public ArrayDeque<LaunchedFragmentInfo> f1797z = new ArrayDeque<>();

    /* renamed from: androidx.fragment.app.FragmentManager$6  reason: invalid class name */
    class AnonymousClass6 implements androidx.lifecycle.e {
        @Override // androidx.lifecycle.e
        public void d(@NonNull androidx.lifecycle.g gVar, @NonNull Lifecycle.Event event) {
            if (event == Lifecycle.Event.ON_START) {
                throw null;
            } else if (event == Lifecycle.Event.ON_DESTROY) {
                throw null;
            }
        }
    }

    public class a implements androidx.activity.result.a<ActivityResult> {
        public a() {
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // androidx.activity.result.a
        public void a(ActivityResult activityResult) {
            ActivityResult activityResult2 = activityResult;
            LaunchedFragmentInfo pollFirst = FragmentManager.this.f1797z.pollFirst();
            if (pollFirst == null) {
                Log.w("FragmentManager", "No IntentSenders were started for " + this);
                return;
            }
            String str = pollFirst.f1798a;
            int i10 = pollFirst.f1799b;
            Fragment e10 = FragmentManager.this.f1775c.e(str);
            if (e10 == null) {
                Log.w("FragmentManager", "Intent Sender result delivered for unknown Fragment " + str);
                return;
            }
            e10.v(i10, activityResult2.f295a, activityResult2.f296b);
        }
    }

    public class b implements androidx.activity.result.a<Map<String, Boolean>> {
        public b() {
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // androidx.activity.result.a
        @SuppressLint({"SyntheticAccessor"})
        public void a(Map<String, Boolean> map) {
            Map<String, Boolean> map2 = map;
            String[] strArr = (String[]) map2.keySet().toArray(new String[0]);
            ArrayList arrayList = new ArrayList(map2.values());
            int[] iArr = new int[arrayList.size()];
            for (int i10 = 0; i10 < arrayList.size(); i10++) {
                iArr[i10] = ((Boolean) arrayList.get(i10)).booleanValue() ? 0 : -1;
            }
            LaunchedFragmentInfo pollFirst = FragmentManager.this.f1797z.pollFirst();
            if (pollFirst == null) {
                Log.w("FragmentManager", "No permissions were requested for " + this);
                return;
            }
            String str = pollFirst.f1798a;
            if (FragmentManager.this.f1775c.e(str) == null) {
                Log.w("FragmentManager", "Permission request result delivered for unknown Fragment " + str);
            }
        }
    }

    public class c extends androidx.activity.b {
        public c(boolean z10) {
            super(z10);
        }

        @Override // androidx.activity.b
        public void a() {
            FragmentManager fragmentManager = FragmentManager.this;
            fragmentManager.C(true);
            if (fragmentManager.h.f293a) {
                fragmentManager.W();
            } else {
                fragmentManager.f1779g.b();
            }
        }
    }

    public class d implements k0.a {
        public d() {
        }

        public void a(@NonNull Fragment fragment, @NonNull f0.a aVar) {
            boolean z10;
            synchronized (aVar) {
                z10 = aVar.f6256a;
            }
            if (!z10) {
                FragmentManager fragmentManager = FragmentManager.this;
                HashSet<f0.a> hashSet = fragmentManager.f1783l.get(fragment);
                if (hashSet != null && hashSet.remove(aVar) && hashSet.isEmpty()) {
                    fragmentManager.f1783l.remove(fragment);
                    if (fragment.f1720a < 5) {
                        fragmentManager.i(fragment);
                        fragmentManager.U(fragment, fragmentManager.f1787p);
                    }
                }
            }
        }

        public void b(@NonNull Fragment fragment, @NonNull f0.a aVar) {
            FragmentManager fragmentManager = FragmentManager.this;
            if (fragmentManager.f1783l.get(fragment) == null) {
                fragmentManager.f1783l.put(fragment, new HashSet<>());
            }
            fragmentManager.f1783l.get(fragment).add(aVar);
        }
    }

    public class e extends u {
        public e() {
        }

        @Override // androidx.fragment.app.u
        @NonNull
        public Fragment a(@NonNull ClassLoader classLoader, @NonNull String str) {
            v<?> vVar = FragmentManager.this.f1788q;
            Context context = vVar.f2016b;
            Objects.requireNonNull(vVar);
            Object obj = Fragment.H0;
            try {
                return (Fragment) u.c(context.getClassLoader(), str).getConstructor(new Class[0]).newInstance(new Object[0]);
            } catch (InstantiationException e10) {
                throw new Fragment.InstantiationException(androidx.activity.result.c.a("Unable to instantiate fragment ", str, ": make sure class name exists, is public, and has an empty constructor that is public"), e10);
            } catch (IllegalAccessException e11) {
                throw new Fragment.InstantiationException(androidx.activity.result.c.a("Unable to instantiate fragment ", str, ": make sure class name exists, is public, and has an empty constructor that is public"), e11);
            } catch (NoSuchMethodException e12) {
                throw new Fragment.InstantiationException(androidx.activity.result.c.a("Unable to instantiate fragment ", str, ": could not find Fragment constructor"), e12);
            } catch (InvocationTargetException e13) {
                throw new Fragment.InstantiationException(androidx.activity.result.c.a("Unable to instantiate fragment ", str, ": calling Fragment constructor caused an exception"), e13);
            }
        }
    }

    public class f implements y0 {
        public f(FragmentManager fragmentManager) {
        }
    }

    public class g implements Runnable {
        public g() {
        }

        public void run() {
            FragmentManager.this.C(true);
        }
    }

    public class h implements a0 {

        /* renamed from: a  reason: collision with root package name */
        public final /* synthetic */ Fragment f1806a;

        public h(FragmentManager fragmentManager, Fragment fragment) {
            this.f1806a = fragment;
        }

        @Override // androidx.fragment.app.a0
        public void a(@NonNull FragmentManager fragmentManager, @NonNull Fragment fragment) {
            Objects.requireNonNull(this.f1806a);
        }
    }

    public class i implements androidx.activity.result.a<ActivityResult> {
        public i() {
        }

        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [java.lang.Object] */
        @Override // androidx.activity.result.a
        public void a(ActivityResult activityResult) {
            ActivityResult activityResult2 = activityResult;
            LaunchedFragmentInfo pollFirst = FragmentManager.this.f1797z.pollFirst();
            if (pollFirst == null) {
                Log.w("FragmentManager", "No Activities were started for result for " + this);
                return;
            }
            String str = pollFirst.f1798a;
            int i10 = pollFirst.f1799b;
            Fragment e10 = FragmentManager.this.f1775c.e(str);
            if (e10 == null) {
                Log.w("FragmentManager", "Activity result delivered for unknown Fragment " + str);
                return;
            }
            e10.v(i10, activityResult2.f295a, activityResult2.f296b);
        }
    }

    public static class j extends l.a<IntentSenderRequest, ActivityResult> {
        /* JADX DEBUG: Method arguments types fixed to match base method, original types: [android.content.Context, java.lang.Object] */
        @Override // l.a
        @NonNull
        public Intent a(@NonNull Context context, IntentSenderRequest intentSenderRequest) {
            Bundle bundleExtra;
            IntentSenderRequest intentSenderRequest2 = intentSenderRequest;
            Intent intent = new Intent("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST");
            Intent intent2 = intentSenderRequest2.f321b;
            if (!(intent2 == null || (bundleExtra = intent2.getBundleExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE")) == null)) {
                intent.putExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE", bundleExtra);
                intent2.removeExtra("androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE");
                if (intent2.getBooleanExtra("androidx.fragment.extra.ACTIVITY_OPTIONS_BUNDLE", false)) {
                    intentSenderRequest2 = new IntentSenderRequest(intentSenderRequest2.f320a, null, intentSenderRequest2.f322c, intentSenderRequest2.f323d);
                }
            }
            intent.putExtra("androidx.activity.result.contract.extra.INTENT_SENDER_REQUEST", intentSenderRequest2);
            if (FragmentManager.O(2)) {
                Log.v("FragmentManager", "CreateIntent created the following intent: " + intent);
            }
            return intent;
        }

        /* Return type fixed from 'java.lang.Object' to match base method */
        @Override // l.a
        @NonNull
        public ActivityResult c(int i10, @Nullable Intent intent) {
            return new ActivityResult(i10, intent);
        }
    }

    public interface k {
        boolean a(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2);
    }

    public class l implements k {

        /* renamed from: a  reason: collision with root package name */
        public final int f1808a;

        /* renamed from: b  reason: collision with root package name */
        public final int f1809b;

        public l(@Nullable String str, int i10, int i11) {
            this.f1808a = i10;
            this.f1809b = i11;
        }

        @Override // androidx.fragment.app.FragmentManager.k
        public boolean a(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2) {
            Fragment fragment = FragmentManager.this.f1791t;
            if (fragment == null || this.f1808a >= 0 || !fragment.e().W()) {
                return FragmentManager.this.X(arrayList, arrayList2, null, this.f1808a, this.f1809b);
            }
            return false;
        }
    }

    public static class m implements Fragment.d {

        /* renamed from: a  reason: collision with root package name */
        public final boolean f1811a;

        /* renamed from: b  reason: collision with root package name */
        public final a f1812b;

        /* renamed from: c  reason: collision with root package name */
        public int f1813c;

        public void a() {
            boolean z10 = this.f1813c > 0;
            for (Fragment fragment : this.f1812b.f1837p.f1775c.i()) {
                fragment.V(null);
            }
            a aVar = this.f1812b;
            aVar.f1837p.g(aVar, this.f1811a, !z10, true);
        }
    }

    public static boolean O(int i10) {
        return Log.isLoggable("FragmentManager", i10);
    }

    public void A(@NonNull k kVar, boolean z10) {
        if (!z10) {
            if (this.f1788q == null) {
                if (this.D) {
                    throw new IllegalStateException("FragmentManager has been destroyed");
                }
                throw new IllegalStateException("FragmentManager has not been attached to a host.");
            } else if (S()) {
                throw new IllegalStateException("Can not perform this action after onSaveInstanceState");
            }
        }
        synchronized (this.f1773a) {
            if (this.f1788q != null) {
                this.f1773a.add(kVar);
                c0();
            } else if (!z10) {
                throw new IllegalStateException("Activity has been destroyed");
            }
        }
    }

    public final void B(boolean z10) {
        if (this.f1774b) {
            throw new IllegalStateException("FragmentManager is already executing transactions");
        } else if (this.f1788q == null) {
            if (this.D) {
                throw new IllegalStateException("FragmentManager has been destroyed");
            }
            throw new IllegalStateException("FragmentManager has not been attached to a host.");
        } else if (Looper.myLooper() != this.f1788q.f2017c.getLooper()) {
            throw new IllegalStateException("Must be called from main thread of fragment host");
        } else if (z10 || !S()) {
            if (this.F == null) {
                this.F = new ArrayList<>();
                this.G = new ArrayList<>();
            }
            this.f1774b = true;
            try {
                F(null, null);
            } finally {
                this.f1774b = false;
            }
        } else {
            throw new IllegalStateException("Can not perform this action after onSaveInstanceState");
        }
    }

    /* JADX INFO: finally extract failed */
    public boolean C(boolean z10) {
        boolean z11;
        B(z10);
        boolean z12 = false;
        while (true) {
            ArrayList<a> arrayList = this.F;
            ArrayList<Boolean> arrayList2 = this.G;
            synchronized (this.f1773a) {
                if (this.f1773a.isEmpty()) {
                    z11 = false;
                } else {
                    int size = this.f1773a.size();
                    z11 = false;
                    for (int i10 = 0; i10 < size; i10++) {
                        z11 |= this.f1773a.get(i10).a(arrayList, arrayList2);
                    }
                    this.f1773a.clear();
                    this.f1788q.f2017c.removeCallbacks(this.K);
                }
            }
            if (z11) {
                this.f1774b = true;
                try {
                    Z(this.F, this.G);
                    e();
                    z12 = true;
                } catch (Throwable th) {
                    e();
                    throw th;
                }
            } else {
                j0();
                x();
                this.f1775c.b();
                return z12;
            }
        }
    }

    /* JADX INFO: finally extract failed */
    public void D(@NonNull k kVar, boolean z10) {
        if (!z10 || (this.f1788q != null && !this.D)) {
            B(z10);
            ((a) kVar).a(this.F, this.G);
            this.f1774b = true;
            try {
                Z(this.F, this.G);
                e();
                j0();
                x();
                this.f1775c.b();
            } catch (Throwable th) {
                e();
                throw th;
            }
        }
    }

    public final void E(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2, int i10, int i11) {
        ViewGroup viewGroup;
        int i12;
        int i13;
        ArrayList<Boolean> arrayList3 = arrayList2;
        boolean z10 = arrayList.get(i10).f1879o;
        ArrayList<Fragment> arrayList4 = this.H;
        if (arrayList4 == null) {
            this.H = new ArrayList<>();
        } else {
            arrayList4.clear();
        }
        this.H.addAll(this.f1775c.i());
        Fragment fragment = this.f1791t;
        int i14 = i10;
        boolean z11 = false;
        while (true) {
            int i15 = 1;
            if (i14 < i11) {
                a aVar = arrayList.get(i14);
                int i16 = 3;
                if (!arrayList3.get(i14).booleanValue()) {
                    ArrayList<Fragment> arrayList5 = this.H;
                    int i17 = 0;
                    while (i17 < aVar.f1866a.size()) {
                        d0.a aVar2 = aVar.f1866a.get(i17);
                        int i18 = aVar2.f1880a;
                        if (i18 != i15) {
                            if (i18 == 2) {
                                Fragment fragment2 = aVar2.f1881b;
                                int i19 = fragment2.f1732m0;
                                int size = arrayList5.size() - 1;
                                boolean z12 = false;
                                while (size >= 0) {
                                    Fragment fragment3 = arrayList5.get(size);
                                    if (fragment3.f1732m0 != i19) {
                                        i13 = i19;
                                    } else if (fragment3 == fragment2) {
                                        i13 = i19;
                                        z12 = true;
                                    } else {
                                        if (fragment3 == fragment) {
                                            i13 = i19;
                                            aVar.f1866a.add(i17, new d0.a(9, fragment3));
                                            i17++;
                                            fragment = null;
                                        } else {
                                            i13 = i19;
                                        }
                                        d0.a aVar3 = new d0.a(3, fragment3);
                                        aVar3.f1882c = aVar2.f1882c;
                                        aVar3.f1884e = aVar2.f1884e;
                                        aVar3.f1883d = aVar2.f1883d;
                                        aVar3.f1885f = aVar2.f1885f;
                                        aVar.f1866a.add(i17, aVar3);
                                        arrayList5.remove(fragment3);
                                        i17++;
                                    }
                                    size--;
                                    i19 = i13;
                                }
                                if (z12) {
                                    aVar.f1866a.remove(i17);
                                    i17--;
                                } else {
                                    i12 = 1;
                                    aVar2.f1880a = 1;
                                    arrayList5.add(fragment2);
                                    i17 += i12;
                                    i15 = i12;
                                    i16 = 3;
                                }
                            } else if (i18 == i16 || i18 == 6) {
                                arrayList5.remove(aVar2.f1881b);
                                Fragment fragment4 = aVar2.f1881b;
                                if (fragment4 == fragment) {
                                    aVar.f1866a.add(i17, new d0.a(9, fragment4));
                                    i17++;
                                    i12 = 1;
                                    fragment = null;
                                    i17 += i12;
                                    i15 = i12;
                                    i16 = 3;
                                }
                            } else if (i18 == 7) {
                                i12 = 1;
                            } else if (i18 == 8) {
                                aVar.f1866a.add(i17, new d0.a(9, fragment));
                                i17++;
                                fragment = aVar2.f1881b;
                            }
                            i12 = 1;
                            i17 += i12;
                            i15 = i12;
                            i16 = 3;
                        } else {
                            i12 = i15;
                        }
                        arrayList5.add(aVar2.f1881b);
                        i17 += i12;
                        i15 = i12;
                        i16 = 3;
                    }
                } else {
                    int i20 = 1;
                    ArrayList<Fragment> arrayList6 = this.H;
                    int size2 = aVar.f1866a.size() - 1;
                    while (size2 >= 0) {
                        d0.a aVar4 = aVar.f1866a.get(size2);
                        int i21 = aVar4.f1880a;
                        if (i21 != i20) {
                            if (i21 != 3) {
                                switch (i21) {
                                    case 8:
                                        fragment = null;
                                        break;
                                    case 9:
                                        fragment = aVar4.f1881b;
                                        break;
                                    case 10:
                                        aVar4.h = aVar4.f1886g;
                                        break;
                                }
                                size2--;
                                i20 = 1;
                            }
                            arrayList6.add(aVar4.f1881b);
                            size2--;
                            i20 = 1;
                        }
                        arrayList6.remove(aVar4.f1881b);
                        size2--;
                        i20 = 1;
                    }
                }
                z11 = z11 || aVar.f1872g;
                i14++;
                arrayList3 = arrayList2;
            } else {
                this.H.clear();
                if (!z10 && this.f1787p >= 1) {
                    for (int i22 = i10; i22 < i11; i22++) {
                        Iterator<d0.a> it = arrayList.get(i22).f1866a.iterator();
                        while (it.hasNext()) {
                            Fragment fragment5 = it.next().f1881b;
                            if (!(fragment5 == null || fragment5.f1741r == null)) {
                                this.f1775c.j(h(fragment5));
                            }
                        }
                    }
                }
                int i23 = i10;
                while (i23 < i11) {
                    a aVar5 = arrayList.get(i23);
                    if (arrayList2.get(i23).booleanValue()) {
                        aVar5.d(-1);
                        aVar5.i(i23 == i11 + -1);
                    } else {
                        aVar5.d(1);
                        aVar5.h();
                    }
                    i23++;
                }
                boolean booleanValue = arrayList2.get(i11 - 1).booleanValue();
                for (int i24 = i10; i24 < i11; i24++) {
                    a aVar6 = arrayList.get(i24);
                    if (booleanValue) {
                        for (int size3 = aVar6.f1866a.size() - 1; size3 >= 0; size3--) {
                            Fragment fragment6 = aVar6.f1866a.get(size3).f1881b;
                            if (fragment6 != null) {
                                h(fragment6).k();
                            }
                        }
                    } else {
                        Iterator<d0.a> it2 = aVar6.f1866a.iterator();
                        while (it2.hasNext()) {
                            Fragment fragment7 = it2.next().f1881b;
                            if (fragment7 != null) {
                                h(fragment7).k();
                            }
                        }
                    }
                }
                T(this.f1787p, true);
                HashSet hashSet = new HashSet();
                for (int i25 = i10; i25 < i11; i25++) {
                    Iterator<d0.a> it3 = arrayList.get(i25).f1866a.iterator();
                    while (it3.hasNext()) {
                        Fragment fragment8 = it3.next().f1881b;
                        if (!(fragment8 == null || (viewGroup = fragment8.f1746t0) == null)) {
                            hashSet.add(t0.f(viewGroup, M()));
                        }
                    }
                }
                Iterator it4 = hashSet.iterator();
                while (it4.hasNext()) {
                    t0 t0Var = (t0) it4.next();
                    t0Var.f2004d = booleanValue;
                    t0Var.g();
                    t0Var.c();
                }
                for (int i26 = i10; i26 < i11; i26++) {
                    a aVar7 = arrayList.get(i26);
                    if (arrayList2.get(i26).booleanValue() && aVar7.f1839r >= 0) {
                        aVar7.f1839r = -1;
                    }
                    Objects.requireNonNull(aVar7);
                }
                return;
            }
        }
    }

    public final void F(@Nullable ArrayList<a> arrayList, @Nullable ArrayList<Boolean> arrayList2) {
        int indexOf;
        int indexOf2;
        ArrayList<m> arrayList3 = this.I;
        int size = arrayList3 == null ? 0 : arrayList3.size();
        int i10 = 0;
        while (i10 < size) {
            m mVar = this.I.get(i10);
            if (arrayList == null || mVar.f1811a || (indexOf2 = arrayList.indexOf(mVar.f1812b)) == -1 || arrayList2 == null || !arrayList2.get(indexOf2).booleanValue()) {
                if ((mVar.f1813c == 0) || (arrayList != null && mVar.f1812b.k(arrayList, 0, arrayList.size()))) {
                    this.I.remove(i10);
                    i10--;
                    size--;
                    if (arrayList == null || mVar.f1811a || (indexOf = arrayList.indexOf(mVar.f1812b)) == -1 || arrayList2 == null || !arrayList2.get(indexOf).booleanValue()) {
                        mVar.a();
                    } else {
                        a aVar = mVar.f1812b;
                        aVar.f1837p.g(aVar, mVar.f1811a, false, false);
                    }
                }
            } else {
                this.I.remove(i10);
                i10--;
                size--;
                a aVar2 = mVar.f1812b;
                aVar2.f1837p.g(aVar2, mVar.f1811a, false, false);
            }
            i10++;
        }
    }

    @Nullable
    public Fragment G(@NonNull String str) {
        return this.f1775c.d(str);
    }

    @Nullable
    public Fragment H(@IdRes int i10) {
        c0 c0Var = this.f1775c;
        int size = c0Var.f1862a.size();
        while (true) {
            size--;
            if (size >= 0) {
                Fragment fragment = c0Var.f1862a.get(size);
                if (fragment != null && fragment.f1752y == i10) {
                    return fragment;
                }
            } else {
                for (b0 b0Var : c0Var.f1863b.values()) {
                    if (b0Var != null) {
                        Fragment fragment2 = b0Var.f1852c;
                        if (fragment2.f1752y == i10) {
                            return fragment2;
                        }
                    }
                }
                return null;
            }
        }
    }

    @Nullable
    public Fragment I(@Nullable String str) {
        c0 c0Var = this.f1775c;
        Objects.requireNonNull(c0Var);
        int size = c0Var.f1862a.size();
        while (true) {
            size--;
            if (size >= 0) {
                Fragment fragment = c0Var.f1862a.get(size);
                if (fragment != null && str.equals(fragment.f1734n0)) {
                    return fragment;
                }
            } else {
                for (b0 b0Var : c0Var.f1863b.values()) {
                    if (b0Var != null) {
                        Fragment fragment2 = b0Var.f1852c;
                        if (str.equals(fragment2.f1734n0)) {
                            return fragment2;
                        }
                    }
                }
                return null;
            }
        }
    }

    public final void J() {
        Iterator it = ((HashSet) f()).iterator();
        while (it.hasNext()) {
            t0 t0Var = (t0) it.next();
            if (t0Var.f2005e) {
                t0Var.f2005e = false;
                t0Var.c();
            }
        }
    }

    public final ViewGroup K(@NonNull Fragment fragment) {
        ViewGroup viewGroup = fragment.f1746t0;
        if (viewGroup != null) {
            return viewGroup;
        }
        if (fragment.f1732m0 > 0 && this.f1789r.c()) {
            View b10 = this.f1789r.b(fragment.f1732m0);
            if (b10 instanceof ViewGroup) {
                return (ViewGroup) b10;
            }
        }
        return null;
    }

    @NonNull
    public u L() {
        Fragment fragment = this.f1790s;
        if (fragment != null) {
            return fragment.f1741r.L();
        }
        return this.f1792u;
    }

    @NonNull
    public y0 M() {
        Fragment fragment = this.f1790s;
        if (fragment != null) {
            return fragment.f1741r.M();
        }
        return this.f1793v;
    }

    public void N(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "hide: " + fragment);
        }
        if (!fragment.f1736o0) {
            fragment.f1736o0 = true;
            fragment.f1753y0 = true ^ fragment.f1753y0;
            g0(fragment);
        }
    }

    public final boolean P(@NonNull Fragment fragment) {
        FragmentManager fragmentManager = fragment.f1745t;
        Iterator it = ((ArrayList) fragmentManager.f1775c.g()).iterator();
        boolean z10 = false;
        while (it.hasNext()) {
            Fragment fragment2 = (Fragment) it.next();
            if (fragment2 != null) {
                z10 = fragmentManager.P(fragment2);
                continue;
            }
            if (z10) {
                return true;
            }
        }
        return false;
    }

    public boolean Q(@Nullable Fragment fragment) {
        FragmentManager fragmentManager;
        if (fragment == null) {
            return true;
        }
        if (!fragment.f1742r0 || ((fragmentManager = fragment.f1741r) != null && !fragmentManager.Q(fragment.f1750x))) {
            return false;
        }
        return true;
    }

    public boolean R(@Nullable Fragment fragment) {
        if (fragment == null) {
            return true;
        }
        FragmentManager fragmentManager = fragment.f1741r;
        if (!fragment.equals(fragmentManager.f1791t) || !R(fragmentManager.f1790s)) {
            return false;
        }
        return true;
    }

    public boolean S() {
        return this.B || this.C;
    }

    public void T(int i10, boolean z10) {
        v<?> vVar;
        if (this.f1788q == null && i10 != -1) {
            throw new IllegalStateException("No activity");
        } else if (z10 || i10 != this.f1787p) {
            this.f1787p = i10;
            c0 c0Var = this.f1775c;
            Iterator<Fragment> it = c0Var.f1862a.iterator();
            while (it.hasNext()) {
                b0 b0Var = c0Var.f1863b.get(it.next().f1724e);
                if (b0Var != null) {
                    b0Var.k();
                }
            }
            Iterator<b0> it2 = c0Var.f1863b.values().iterator();
            while (true) {
                boolean z11 = false;
                if (!it2.hasNext()) {
                    break;
                }
                b0 next = it2.next();
                if (next != null) {
                    next.k();
                    Fragment fragment = next.f1852c;
                    if (fragment.f1730l && !fragment.t()) {
                        z11 = true;
                    }
                    if (z11) {
                        c0Var.k(next);
                    }
                }
            }
            i0();
            if (this.A && (vVar = this.f1788q) != null && this.f1787p == 7) {
                vVar.g();
                this.A = false;
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:23:0x005c, code lost:
        if (r1 != 5) goto L_0x01b8;
     */
    /* JADX WARNING: Removed duplicated region for block: B:101:0x01b4  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x006c  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x0071  */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x0076  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x007b  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x0080  */
    /* JADX WARNING: Removed duplicated region for block: B:95:0x01a4  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void U(@androidx.annotation.NonNull androidx.fragment.app.Fragment r17, int r18) {
        /*
        // Method dump skipped, instructions count: 492
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.FragmentManager.U(androidx.fragment.app.Fragment, int):void");
    }

    public void V() {
        if (this.f1788q != null) {
            this.B = false;
            this.C = false;
            this.J.h = false;
            for (Fragment fragment : this.f1775c.i()) {
                if (fragment != null) {
                    fragment.f1745t.V();
                }
            }
        }
    }

    public boolean W() {
        C(false);
        B(true);
        Fragment fragment = this.f1791t;
        if (fragment != null && fragment.e().W()) {
            return true;
        }
        boolean X = X(this.F, this.G, null, -1, 0);
        if (X) {
            this.f1774b = true;
            try {
                Z(this.F, this.G);
            } finally {
                e();
            }
        }
        j0();
        x();
        this.f1775c.b();
        return X;
    }

    public boolean X(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2, @Nullable String str, int i10, int i11) {
        ArrayList<a> arrayList3 = this.f1776d;
        if (arrayList3 == null) {
            return false;
        }
        if (str == null && i10 < 0 && (i11 & 1) == 0) {
            int size = arrayList3.size() - 1;
            if (size < 0) {
                return false;
            }
            arrayList.add(this.f1776d.remove(size));
            arrayList2.add(Boolean.TRUE);
        } else {
            int i12 = -1;
            if (str != null || i10 >= 0) {
                int size2 = arrayList3.size() - 1;
                while (size2 >= 0) {
                    a aVar = this.f1776d.get(size2);
                    if ((str != null && str.equals(aVar.h)) || (i10 >= 0 && i10 == aVar.f1839r)) {
                        break;
                    }
                    size2--;
                }
                if (size2 < 0) {
                    return false;
                }
                if ((i11 & 1) != 0) {
                    while (true) {
                        size2--;
                        if (size2 < 0) {
                            break;
                        }
                        a aVar2 = this.f1776d.get(size2);
                        if ((str == null || !str.equals(aVar2.h)) && (i10 < 0 || i10 != aVar2.f1839r)) {
                            break;
                        }
                    }
                }
                i12 = size2;
            }
            if (i12 == this.f1776d.size() - 1) {
                return false;
            }
            for (int size3 = this.f1776d.size() - 1; size3 > i12; size3--) {
                arrayList.add(this.f1776d.remove(size3));
                arrayList2.add(Boolean.TRUE);
            }
        }
        return true;
    }

    public void Y(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "remove: " + fragment + " nesting=" + fragment.f1739q);
        }
        boolean z10 = !fragment.t();
        if (!fragment.f1738p0 || z10) {
            this.f1775c.l(fragment);
            if (P(fragment)) {
                this.A = true;
            }
            fragment.f1730l = true;
            g0(fragment);
        }
    }

    public final void Z(@NonNull ArrayList<a> arrayList, @NonNull ArrayList<Boolean> arrayList2) {
        if (!arrayList.isEmpty()) {
            if (arrayList.size() == arrayList2.size()) {
                F(arrayList, arrayList2);
                int size = arrayList.size();
                int i10 = 0;
                int i11 = 0;
                while (i10 < size) {
                    if (!arrayList.get(i10).f1879o) {
                        if (i11 != i10) {
                            E(arrayList, arrayList2, i11, i10);
                        }
                        i11 = i10 + 1;
                        if (arrayList2.get(i10).booleanValue()) {
                            while (i11 < size && arrayList2.get(i11).booleanValue() && !arrayList.get(i11).f1879o) {
                                i11++;
                            }
                        }
                        E(arrayList, arrayList2, i10, i11);
                        i10 = i11 - 1;
                    }
                    i10++;
                }
                if (i11 != size) {
                    E(arrayList, arrayList2, i11, size);
                    return;
                }
                return;
            }
            throw new IllegalStateException("Internal error with the back stack records");
        }
    }

    public void a(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "add: " + fragment);
        }
        b0 h6 = h(fragment);
        fragment.f1741r = this;
        this.f1775c.j(h6);
        if (!fragment.f1738p0) {
            this.f1775c.a(fragment);
            fragment.f1730l = false;
            if (fragment.f1747u0 == null) {
                fragment.f1753y0 = false;
            }
            if (P(fragment)) {
                this.A = true;
            }
        }
    }

    public void a0(@Nullable Parcelable parcelable) {
        b0 b0Var;
        if (parcelable != null) {
            FragmentManagerState fragmentManagerState = (FragmentManagerState) parcelable;
            if (fragmentManagerState.f1814a != null) {
                this.f1775c.f1863b.clear();
                Iterator<FragmentState> it = fragmentManagerState.f1814a.iterator();
                while (it.hasNext()) {
                    FragmentState next = it.next();
                    if (next != null) {
                        Fragment fragment = this.J.f2024c.get(next.f1822b);
                        if (fragment != null) {
                            if (O(2)) {
                                Log.v("FragmentManager", "restoreSaveState: re-attaching retained " + fragment);
                            }
                            b0Var = new b0(this.f1785n, this.f1775c, fragment, next);
                        } else {
                            b0Var = new b0(this.f1785n, this.f1775c, this.f1788q.f2016b.getClassLoader(), L(), next);
                        }
                        Fragment fragment2 = b0Var.f1852c;
                        fragment2.f1741r = this;
                        if (O(2)) {
                            StringBuilder a10 = com.duokan.airkan.server.f.a("restoreSaveState: active (");
                            a10.append(fragment2.f1724e);
                            a10.append("): ");
                            a10.append(fragment2);
                            Log.v("FragmentManager", a10.toString());
                        }
                        b0Var.m(this.f1788q.f2016b.getClassLoader());
                        this.f1775c.j(b0Var);
                        b0Var.f1854e = this.f1787p;
                    }
                }
                z zVar = this.J;
                Objects.requireNonNull(zVar);
                Iterator it2 = new ArrayList(zVar.f2024c.values()).iterator();
                while (it2.hasNext()) {
                    Fragment fragment3 = (Fragment) it2.next();
                    if (!this.f1775c.c(fragment3.f1724e)) {
                        if (O(2)) {
                            Log.v("FragmentManager", "Discarding retained Fragment " + fragment3 + " that was not found in the set of active Fragments " + fragmentManagerState.f1814a);
                        }
                        this.J.c(fragment3);
                        fragment3.f1741r = this;
                        b0 b0Var2 = new b0(this.f1785n, this.f1775c, fragment3);
                        b0Var2.f1854e = 1;
                        b0Var2.k();
                        fragment3.f1730l = true;
                        b0Var2.k();
                    }
                }
                c0 c0Var = this.f1775c;
                ArrayList<String> arrayList = fragmentManagerState.f1815b;
                c0Var.f1862a.clear();
                if (arrayList != null) {
                    for (String str : arrayList) {
                        Fragment d10 = c0Var.d(str);
                        if (d10 != null) {
                            if (O(2)) {
                                Log.v("FragmentManager", "restoreSaveState: added (" + str + "): " + d10);
                            }
                            c0Var.a(d10);
                        } else {
                            throw new IllegalStateException(androidx.activity.result.c.a("No instantiated fragment for (", str, ")"));
                        }
                    }
                }
                Fragment fragment4 = null;
                if (fragmentManagerState.f1816c != null) {
                    this.f1776d = new ArrayList<>(fragmentManagerState.f1816c.length);
                    int i10 = 0;
                    while (true) {
                        BackStackState[] backStackStateArr = fragmentManagerState.f1816c;
                        if (i10 >= backStackStateArr.length) {
                            break;
                        }
                        BackStackState backStackState = backStackStateArr[i10];
                        Objects.requireNonNull(backStackState);
                        a aVar = new a(this);
                        int i11 = 0;
                        int i12 = 0;
                        while (true) {
                            int[] iArr = backStackState.f1707a;
                            if (i11 >= iArr.length) {
                                break;
                            }
                            d0.a aVar2 = new d0.a();
                            int i13 = i11 + 1;
                            aVar2.f1880a = iArr[i11];
                            if (O(2)) {
                                Log.v("FragmentManager", "Instantiate " + aVar + " op #" + i12 + " base fragment #" + backStackState.f1707a[i13]);
                            }
                            String str2 = backStackState.f1708b.get(i12);
                            if (str2 != null) {
                                aVar2.f1881b = this.f1775c.d(str2);
                            } else {
                                aVar2.f1881b = fragment4;
                            }
                            aVar2.f1886g = Lifecycle.State.values()[backStackState.f1709c[i12]];
                            aVar2.h = Lifecycle.State.values()[backStackState.f1710d[i12]];
                            int[] iArr2 = backStackState.f1707a;
                            int i14 = i13 + 1;
                            int i15 = iArr2[i13];
                            aVar2.f1882c = i15;
                            int i16 = i14 + 1;
                            int i17 = iArr2[i14];
                            aVar2.f1883d = i17;
                            int i18 = i16 + 1;
                            int i19 = iArr2[i16];
                            aVar2.f1884e = i19;
                            int i20 = iArr2[i18];
                            aVar2.f1885f = i20;
                            aVar.f1867b = i15;
                            aVar.f1868c = i17;
                            aVar.f1869d = i19;
                            aVar.f1870e = i20;
                            aVar.b(aVar2);
                            i12++;
                            fragment4 = null;
                            i11 = i18 + 1;
                        }
                        aVar.f1871f = backStackState.f1711e;
                        aVar.h = backStackState.f1712f;
                        aVar.f1839r = backStackState.f1713g;
                        aVar.f1872g = true;
                        aVar.f1873i = backStackState.h;
                        aVar.f1874j = backStackState.f1714i;
                        aVar.f1875k = backStackState.f1715j;
                        aVar.f1876l = backStackState.f1716k;
                        aVar.f1877m = backStackState.f1717l;
                        aVar.f1878n = backStackState.f1718m;
                        aVar.f1879o = backStackState.f1719n;
                        aVar.d(1);
                        if (O(2)) {
                            StringBuilder b10 = f0.b("restoreAllState: back stack #", i10, " (index ");
                            b10.append(aVar.f1839r);
                            b10.append("): ");
                            b10.append(aVar);
                            Log.v("FragmentManager", b10.toString());
                            PrintWriter printWriter = new PrintWriter(new q0("FragmentManager"));
                            aVar.g("  ", printWriter, false);
                            printWriter.close();
                        }
                        this.f1776d.add(aVar);
                        i10++;
                        fragment4 = null;
                    }
                } else {
                    this.f1776d = null;
                }
                this.f1780i.set(fragmentManagerState.f1817d);
                String str3 = fragmentManagerState.f1818e;
                if (str3 != null) {
                    Fragment G2 = G(str3);
                    this.f1791t = G2;
                    t(G2);
                }
                ArrayList<String> arrayList2 = fragmentManagerState.f1819f;
                if (arrayList2 != null) {
                    for (int i21 = 0; i21 < arrayList2.size(); i21++) {
                        this.f1781j.put(arrayList2.get(i21), fragmentManagerState.f1820g.get(i21));
                    }
                }
                this.f1797z = new ArrayDeque<>(fragmentManagerState.h);
            }
        }
    }

    @SuppressLint({"SyntheticAccessor"})
    public void b(@NonNull v<?> vVar, @NonNull s sVar, @Nullable Fragment fragment) {
        q qVar;
        if (this.f1788q == null) {
            this.f1788q = vVar;
            this.f1789r = sVar;
            this.f1790s = fragment;
            if (fragment != null) {
                this.f1786o.add(new h(this, fragment));
            } else if (vVar instanceof a0) {
                this.f1786o.add((a0) vVar);
            }
            if (this.f1790s != null) {
                j0();
            }
            if (vVar instanceof androidx.activity.c) {
                androidx.activity.c cVar = (androidx.activity.c) vVar;
                OnBackPressedDispatcher onBackPressedDispatcher = cVar.getOnBackPressedDispatcher();
                this.f1779g = onBackPressedDispatcher;
                androidx.lifecycle.g gVar = cVar;
                if (fragment != null) {
                    gVar = fragment;
                }
                onBackPressedDispatcher.a(gVar, this.h);
            }
            if (fragment != null) {
                z zVar = fragment.f1741r.J;
                z zVar2 = zVar.f2025d.get(fragment.f1724e);
                if (zVar2 == null) {
                    zVar2 = new z(zVar.f2027f);
                    zVar.f2025d.put(fragment.f1724e, zVar2);
                }
                this.J = zVar2;
            } else if (vVar instanceof x) {
                w viewModelStore = ((x) vVar).getViewModelStore();
                s sVar2 = z.f2023i;
                String canonicalName = z.class.getCanonicalName();
                if (canonicalName != null) {
                    String a10 = p.f.a("androidx.lifecycle.ViewModelProvider.DefaultKey:", canonicalName);
                    q qVar2 = viewModelStore.f2095a.get(a10);
                    if (!z.class.isInstance(qVar2)) {
                        if (sVar2 instanceof t) {
                            qVar = ((t) sVar2).c(a10, z.class);
                        } else {
                            qVar = ((z.a) sVar2).a(z.class);
                        }
                        qVar2 = qVar;
                        q put = viewModelStore.f2095a.put(a10, qVar2);
                        if (put != null) {
                            put.a();
                        }
                    } else if (sVar2 instanceof v) {
                        ((v) sVar2).b(qVar2);
                    }
                    this.J = (z) qVar2;
                } else {
                    throw new IllegalArgumentException("Local and anonymous classes can not be ViewModels");
                }
            } else {
                this.J = new z(false);
            }
            this.J.h = S();
            this.f1775c.f1864c = this.J;
            v<?> vVar2 = this.f1788q;
            if (vVar2 instanceof androidx.activity.result.d) {
                ActivityResultRegistry activityResultRegistry = ((androidx.activity.result.d) vVar2).getActivityResultRegistry();
                String a11 = p.f.a("FragmentManager:", fragment != null ? bb.h.b(new StringBuilder(), fragment.f1724e, ":") : "");
                this.f1794w = activityResultRegistry.d(p.f.a(a11, "StartActivityForResult"), new l.c(), new i());
                this.f1795x = activityResultRegistry.d(p.f.a(a11, "StartIntentSenderForResult"), new j(), new a());
                this.f1796y = activityResultRegistry.d(p.f.a(a11, "RequestPermissions"), new l.b(), new b());
                return;
            }
            return;
        }
        throw new IllegalStateException("Already attached");
    }

    public Parcelable b0() {
        int i10;
        BackStackState[] backStackStateArr;
        ArrayList<String> arrayList;
        int size;
        J();
        z();
        C(true);
        this.B = true;
        this.J.h = true;
        c0 c0Var = this.f1775c;
        Objects.requireNonNull(c0Var);
        ArrayList<FragmentState> arrayList2 = new ArrayList<>(c0Var.f1863b.size());
        Iterator<b0> it = c0Var.f1863b.values().iterator();
        while (true) {
            backStackStateArr = null;
            Bundle bundle = null;
            backStackStateArr = null;
            if (!it.hasNext()) {
                break;
            }
            b0 next = it.next();
            if (next != null) {
                Fragment fragment = next.f1852c;
                FragmentState fragmentState = new FragmentState(fragment);
                Fragment fragment2 = next.f1852c;
                if (fragment2.f1720a <= -1 || fragmentState.f1832m != null) {
                    fragmentState.f1832m = fragment2.f1721b;
                } else {
                    Bundle bundle2 = new Bundle();
                    Fragment fragment3 = next.f1852c;
                    fragment3.D(bundle2);
                    fragment3.F0.b(bundle2);
                    Parcelable b02 = fragment3.f1745t.b0();
                    if (b02 != null) {
                        bundle2.putParcelable(n.FRAGMENTS_TAG, b02);
                    }
                    next.f1850a.j(next.f1852c, bundle2, false);
                    if (!bundle2.isEmpty()) {
                        bundle = bundle2;
                    }
                    if (next.f1852c.f1747u0 != null) {
                        next.o();
                    }
                    if (next.f1852c.f1722c != null) {
                        if (bundle == null) {
                            bundle = new Bundle();
                        }
                        bundle.putSparseParcelableArray("android:view_state", next.f1852c.f1722c);
                    }
                    if (next.f1852c.f1723d != null) {
                        if (bundle == null) {
                            bundle = new Bundle();
                        }
                        bundle.putBundle("android:view_registry_state", next.f1852c.f1723d);
                    }
                    if (!next.f1852c.f1749w0) {
                        if (bundle == null) {
                            bundle = new Bundle();
                        }
                        bundle.putBoolean("android:user_visible_hint", next.f1852c.f1749w0);
                    }
                    fragmentState.f1832m = bundle;
                    if (next.f1852c.h != null) {
                        if (bundle == null) {
                            fragmentState.f1832m = new Bundle();
                        }
                        fragmentState.f1832m.putString("android:target_state", next.f1852c.h);
                        int i11 = next.f1852c.f1727i;
                        if (i11 != 0) {
                            fragmentState.f1832m.putInt("android:target_req_state", i11);
                        }
                    }
                }
                arrayList2.add(fragmentState);
                if (O(2)) {
                    Log.v("FragmentManager", "Saved state of " + fragment + ": " + fragmentState.f1832m);
                }
            }
        }
        if (arrayList2.isEmpty()) {
            if (O(2)) {
                Log.v("FragmentManager", "saveAllState: no fragments!");
            }
            return null;
        }
        c0 c0Var2 = this.f1775c;
        synchronized (c0Var2.f1862a) {
            if (c0Var2.f1862a.isEmpty()) {
                arrayList = null;
            } else {
                arrayList = new ArrayList<>(c0Var2.f1862a.size());
                Iterator<Fragment> it2 = c0Var2.f1862a.iterator();
                while (it2.hasNext()) {
                    Fragment next2 = it2.next();
                    arrayList.add(next2.f1724e);
                    if (O(2)) {
                        Log.v("FragmentManager", "saveAllState: adding fragment (" + next2.f1724e + "): " + next2);
                    }
                }
            }
        }
        ArrayList<a> arrayList3 = this.f1776d;
        if (arrayList3 != null && (size = arrayList3.size()) > 0) {
            backStackStateArr = new BackStackState[size];
            for (i10 = 0; i10 < size; i10++) {
                backStackStateArr[i10] = new BackStackState(this.f1776d.get(i10));
                if (O(2)) {
                    StringBuilder b10 = f0.b("saveAllState: adding back stack #", i10, ": ");
                    b10.append(this.f1776d.get(i10));
                    Log.v("FragmentManager", b10.toString());
                }
            }
        }
        FragmentManagerState fragmentManagerState = new FragmentManagerState();
        fragmentManagerState.f1814a = arrayList2;
        fragmentManagerState.f1815b = arrayList;
        fragmentManagerState.f1816c = backStackStateArr;
        fragmentManagerState.f1817d = this.f1780i.get();
        Fragment fragment4 = this.f1791t;
        if (fragment4 != null) {
            fragmentManagerState.f1818e = fragment4.f1724e;
        }
        fragmentManagerState.f1819f.addAll(this.f1781j.keySet());
        fragmentManagerState.f1820g.addAll(this.f1781j.values());
        fragmentManagerState.h = new ArrayList<>(this.f1797z);
        return fragmentManagerState;
    }

    public void c(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "attach: " + fragment);
        }
        if (fragment.f1738p0) {
            fragment.f1738p0 = false;
            if (!fragment.f1729k) {
                this.f1775c.a(fragment);
                if (O(2)) {
                    Log.v("FragmentManager", "add from attach: " + fragment);
                }
                if (P(fragment)) {
                    this.A = true;
                }
            }
        }
    }

    public void c0() {
        synchronized (this.f1773a) {
            ArrayList<m> arrayList = this.I;
            boolean z10 = false;
            boolean z11 = arrayList != null && !arrayList.isEmpty();
            if (this.f1773a.size() == 1) {
                z10 = true;
            }
            if (z11 || z10) {
                this.f1788q.f2017c.removeCallbacks(this.K);
                this.f1788q.f2017c.post(this.K);
                j0();
            }
        }
    }

    public final void d(@NonNull Fragment fragment) {
        HashSet<f0.a> hashSet = this.f1783l.get(fragment);
        if (hashSet != null) {
            Iterator<f0.a> it = hashSet.iterator();
            while (it.hasNext()) {
                it.next().a();
            }
            hashSet.clear();
            i(fragment);
            this.f1783l.remove(fragment);
        }
    }

    public void d0(@NonNull Fragment fragment, boolean z10) {
        ViewGroup K2 = K(fragment);
        if (K2 != null && (K2 instanceof FragmentContainerView)) {
            ((FragmentContainerView) K2).setDrawDisappearingViewsLast(!z10);
        }
    }

    public final void e() {
        this.f1774b = false;
        this.G.clear();
        this.F.clear();
    }

    public void e0(@NonNull Fragment fragment, @NonNull Lifecycle.State state) {
        if (!fragment.equals(G(fragment.f1724e)) || !(fragment.f1743s == null || fragment.f1741r == this)) {
            throw new IllegalArgumentException("Fragment " + fragment + " is not an active fragment of FragmentManager " + this);
        }
        fragment.B0 = state;
    }

    public final Set<t0> f() {
        HashSet hashSet = new HashSet();
        Iterator it = ((ArrayList) this.f1775c.f()).iterator();
        while (it.hasNext()) {
            ViewGroup viewGroup = ((b0) it.next()).f1852c.f1746t0;
            if (viewGroup != null) {
                hashSet.add(t0.f(viewGroup, M()));
            }
        }
        return hashSet;
    }

    public void f0(@Nullable Fragment fragment) {
        if (fragment == null || (fragment.equals(G(fragment.f1724e)) && (fragment.f1743s == null || fragment.f1741r == this))) {
            Fragment fragment2 = this.f1791t;
            this.f1791t = fragment;
            t(fragment2);
            t(this.f1791t);
            return;
        }
        throw new IllegalArgumentException("Fragment " + fragment + " is not an active fragment of FragmentManager " + this);
    }

    public void g(@NonNull a aVar, boolean z10, boolean z11, boolean z12) {
        if (z10) {
            aVar.i(z12);
        } else {
            aVar.h();
        }
        ArrayList arrayList = new ArrayList(1);
        ArrayList arrayList2 = new ArrayList(1);
        arrayList.add(aVar);
        arrayList2.add(Boolean.valueOf(z10));
        if (z11 && this.f1787p >= 1) {
            k0.p(this.f1788q.f2016b, this.f1789r, arrayList, arrayList2, 0, 1, true, this.f1784m);
        }
        if (z12) {
            T(this.f1787p, true);
        }
        Iterator it = ((ArrayList) this.f1775c.g()).iterator();
        while (it.hasNext()) {
            Fragment fragment = (Fragment) it.next();
            if (fragment != null) {
                View view = fragment.f1747u0;
            }
        }
    }

    public final void g0(@NonNull Fragment fragment) {
        ViewGroup K2 = K(fragment);
        if (K2 != null && fragment.l() > 0) {
            int i10 = R$id.visible_removing_fragment_view_tag;
            if (K2.getTag(i10) == null) {
                K2.setTag(i10, fragment);
            }
            ((Fragment) K2.getTag(i10)).U(fragment.l());
        }
    }

    @NonNull
    public b0 h(@NonNull Fragment fragment) {
        b0 h6 = this.f1775c.h(fragment.f1724e);
        if (h6 != null) {
            return h6;
        }
        b0 b0Var = new b0(this.f1785n, this.f1775c, fragment);
        b0Var.m(this.f1788q.f2016b.getClassLoader());
        b0Var.f1854e = this.f1787p;
        return b0Var;
    }

    public void h0(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "show: " + fragment);
        }
        if (fragment.f1736o0) {
            fragment.f1736o0 = false;
            fragment.f1753y0 = !fragment.f1753y0;
        }
    }

    public final void i(@NonNull Fragment fragment) {
        fragment.J();
        this.f1785n.n(fragment, false);
        fragment.f1746t0 = null;
        fragment.f1747u0 = null;
        fragment.D0 = null;
        fragment.E0.h(null);
        fragment.f1733n = false;
    }

    public final void i0() {
        Iterator it = ((ArrayList) this.f1775c.f()).iterator();
        while (it.hasNext()) {
            b0 b0Var = (b0) it.next();
            Fragment fragment = b0Var.f1852c;
            if (fragment.f1748v0) {
                if (this.f1774b) {
                    this.E = true;
                } else {
                    fragment.f1748v0 = false;
                    b0Var.k();
                }
            }
        }
    }

    public void j(@NonNull Fragment fragment) {
        if (O(2)) {
            Log.v("FragmentManager", "detach: " + fragment);
        }
        if (!fragment.f1738p0) {
            fragment.f1738p0 = true;
            if (fragment.f1729k) {
                if (O(2)) {
                    Log.v("FragmentManager", "remove from detach: " + fragment);
                }
                this.f1775c.l(fragment);
                if (P(fragment)) {
                    this.A = true;
                }
                g0(fragment);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:10:0x0018, code lost:
        if (r1 == null) goto L_0x001f;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:11:0x001a, code lost:
        r1 = r1.size();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:12:0x001f, code lost:
        r1 = 0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0020, code lost:
        if (r1 <= 0) goto L_0x002b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:15:0x0028, code lost:
        if (R(r4.f1790s) == false) goto L_0x002b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:16:0x002b, code lost:
        r2 = false;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:17:0x002c, code lost:
        r0.f293a = r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:18:0x002e, code lost:
        return;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:9:0x0013, code lost:
        r0 = r4.h;
        r1 = r4.f1776d;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void j0() {
        /*
            r4 = this;
            java.util.ArrayList<androidx.fragment.app.FragmentManager$k> r0 = r4.f1773a
            monitor-enter(r0)
            java.util.ArrayList<androidx.fragment.app.FragmentManager$k> r1 = r4.f1773a     // Catch:{ all -> 0x002f }
            boolean r1 = r1.isEmpty()     // Catch:{ all -> 0x002f }
            r2 = 1
            if (r1 != 0) goto L_0x0012
            androidx.activity.b r1 = r4.h     // Catch:{ all -> 0x002f }
            r1.f293a = r2     // Catch:{ all -> 0x002f }
            monitor-exit(r0)     // Catch:{ all -> 0x002f }
            return
        L_0x0012:
            monitor-exit(r0)     // Catch:{ all -> 0x002f }
            androidx.activity.b r0 = r4.h
            java.util.ArrayList<androidx.fragment.app.a> r1 = r4.f1776d
            r3 = 0
            if (r1 == 0) goto L_0x001f
            int r1 = r1.size()
            goto L_0x0020
        L_0x001f:
            r1 = r3
        L_0x0020:
            if (r1 <= 0) goto L_0x002b
            androidx.fragment.app.Fragment r1 = r4.f1790s
            boolean r1 = r4.R(r1)
            if (r1 == 0) goto L_0x002b
            goto L_0x002c
        L_0x002b:
            r2 = r3
        L_0x002c:
            r0.f293a = r2
            return
        L_0x002f:
            r1 = move-exception
            monitor-exit(r0)
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.fragment.app.FragmentManager.j0():void");
    }

    public void k(@NonNull Configuration configuration) {
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null) {
                fragment.onConfigurationChanged(configuration);
                fragment.f1745t.k(configuration);
            }
        }
    }

    public boolean l(@NonNull MenuItem menuItem) {
        if (this.f1787p < 1) {
            return false;
        }
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null && fragment.H(menuItem)) {
                return true;
            }
        }
        return false;
    }

    public void m() {
        this.B = false;
        this.C = false;
        this.J.h = false;
        w(1);
    }

    public boolean n(@NonNull Menu menu, @NonNull MenuInflater menuInflater) {
        if (this.f1787p < 1) {
            return false;
        }
        ArrayList<Fragment> arrayList = null;
        boolean z10 = false;
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null && Q(fragment)) {
                if (!fragment.f1736o0 ? fragment.f1745t.n(menu, menuInflater) | false : false) {
                    if (arrayList == null) {
                        arrayList = new ArrayList<>();
                    }
                    arrayList.add(fragment);
                    z10 = true;
                }
            }
        }
        if (this.f1777e != null) {
            for (int i10 = 0; i10 < this.f1777e.size(); i10++) {
                Fragment fragment2 = this.f1777e.get(i10);
                if (arrayList == null || !arrayList.contains(fragment2)) {
                    Objects.requireNonNull(fragment2);
                }
            }
        }
        this.f1777e = arrayList;
        return z10;
    }

    public void o() {
        this.D = true;
        C(true);
        z();
        w(-1);
        this.f1788q = null;
        this.f1789r = null;
        this.f1790s = null;
        if (this.f1779g != null) {
            Iterator<androidx.activity.a> it = this.h.f294b.iterator();
            while (it.hasNext()) {
                it.next().cancel();
            }
            this.f1779g = null;
        }
        androidx.activity.result.b<Intent> bVar = this.f1794w;
        if (bVar != null) {
            bVar.b();
            this.f1795x.b();
            this.f1796y.b();
        }
    }

    public void p() {
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null) {
                fragment.K();
            }
        }
    }

    public void q(boolean z10) {
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null) {
                fragment.f1745t.q(z10);
            }
        }
    }

    public boolean r(@NonNull MenuItem menuItem) {
        if (this.f1787p < 1) {
            return false;
        }
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null && fragment.L(menuItem)) {
                return true;
            }
        }
        return false;
    }

    public void s(@NonNull Menu menu) {
        if (this.f1787p >= 1) {
            for (Fragment fragment : this.f1775c.i()) {
                if (fragment != null && !fragment.f1736o0) {
                    fragment.f1745t.s(menu);
                }
            }
        }
    }

    public final void t(@Nullable Fragment fragment) {
        if (fragment != null && fragment.equals(G(fragment.f1724e))) {
            boolean R = fragment.f1741r.R(fragment);
            Boolean bool = fragment.f1728j;
            if (bool == null || bool.booleanValue() != R) {
                fragment.f1728j = Boolean.valueOf(R);
                FragmentManager fragmentManager = fragment.f1745t;
                fragmentManager.j0();
                fragmentManager.t(fragmentManager.f1791t);
            }
        }
    }

    @NonNull
    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append("FragmentManager{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append(" in ");
        Fragment fragment = this.f1790s;
        if (fragment != null) {
            sb.append(fragment.getClass().getSimpleName());
            sb.append("{");
            sb.append(Integer.toHexString(System.identityHashCode(this.f1790s)));
            sb.append("}");
        } else {
            v<?> vVar = this.f1788q;
            if (vVar != null) {
                sb.append(vVar.getClass().getSimpleName());
                sb.append("{");
                sb.append(Integer.toHexString(System.identityHashCode(this.f1788q)));
                sb.append("}");
            } else {
                sb.append("null");
            }
        }
        sb.append("}}");
        return sb.toString();
    }

    public void u(boolean z10) {
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null) {
                fragment.f1745t.u(z10);
            }
        }
    }

    public boolean v(@NonNull Menu menu) {
        boolean z10 = false;
        if (this.f1787p < 1) {
            return false;
        }
        for (Fragment fragment : this.f1775c.i()) {
            if (fragment != null && Q(fragment) && fragment.M(menu)) {
                z10 = true;
            }
        }
        return z10;
    }

    /* JADX INFO: finally extract failed */
    public final void w(int i10) {
        try {
            this.f1774b = true;
            for (b0 b0Var : this.f1775c.f1863b.values()) {
                if (b0Var != null) {
                    b0Var.f1854e = i10;
                }
            }
            T(i10, false);
            Iterator it = ((HashSet) f()).iterator();
            while (it.hasNext()) {
                ((t0) it.next()).e();
            }
            this.f1774b = false;
            C(true);
        } catch (Throwable th) {
            this.f1774b = false;
            throw th;
        }
    }

    public final void x() {
        if (this.E) {
            this.E = false;
            i0();
        }
    }

    public void y(@NonNull String str, @Nullable FileDescriptor fileDescriptor, @NonNull PrintWriter printWriter, @Nullable String[] strArr) {
        int size;
        int size2;
        String a10 = p.f.a(str, "    ");
        c0 c0Var = this.f1775c;
        Objects.requireNonNull(c0Var);
        String str2 = str + "    ";
        if (!c0Var.f1863b.isEmpty()) {
            printWriter.print(str);
            printWriter.print("Active Fragments:");
            for (b0 b0Var : c0Var.f1863b.values()) {
                printWriter.print(str);
                if (b0Var != null) {
                    Fragment fragment = b0Var.f1852c;
                    printWriter.println(fragment);
                    fragment.b(str2, fileDescriptor, printWriter, strArr);
                } else {
                    printWriter.println("null");
                }
            }
        }
        int size3 = c0Var.f1862a.size();
        if (size3 > 0) {
            printWriter.print(str);
            printWriter.println("Added Fragments:");
            for (int i10 = 0; i10 < size3; i10++) {
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i10);
                printWriter.print(": ");
                printWriter.println(c0Var.f1862a.get(i10).toString());
            }
        }
        ArrayList<Fragment> arrayList = this.f1777e;
        if (arrayList != null && (size2 = arrayList.size()) > 0) {
            printWriter.print(str);
            printWriter.println("Fragments Created Menus:");
            for (int i11 = 0; i11 < size2; i11++) {
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i11);
                printWriter.print(": ");
                printWriter.println(this.f1777e.get(i11).toString());
            }
        }
        ArrayList<a> arrayList2 = this.f1776d;
        if (arrayList2 != null && (size = arrayList2.size()) > 0) {
            printWriter.print(str);
            printWriter.println("Back Stack:");
            for (int i12 = 0; i12 < size; i12++) {
                a aVar = this.f1776d.get(i12);
                printWriter.print(str);
                printWriter.print("  #");
                printWriter.print(i12);
                printWriter.print(": ");
                printWriter.println(aVar.toString());
                aVar.g(a10, printWriter, true);
            }
        }
        printWriter.print(str);
        printWriter.println("Back Stack Index: " + this.f1780i.get());
        synchronized (this.f1773a) {
            int size4 = this.f1773a.size();
            if (size4 > 0) {
                printWriter.print(str);
                printWriter.println("Pending Actions:");
                for (int i13 = 0; i13 < size4; i13++) {
                    printWriter.print(str);
                    printWriter.print("  #");
                    printWriter.print(i13);
                    printWriter.print(": ");
                    printWriter.println((k) this.f1773a.get(i13));
                }
            }
        }
        printWriter.print(str);
        printWriter.println("FragmentManager misc state:");
        printWriter.print(str);
        printWriter.print("  mHost=");
        printWriter.println(this.f1788q);
        printWriter.print(str);
        printWriter.print("  mContainer=");
        printWriter.println(this.f1789r);
        if (this.f1790s != null) {
            printWriter.print(str);
            printWriter.print("  mParent=");
            printWriter.println(this.f1790s);
        }
        printWriter.print(str);
        printWriter.print("  mCurState=");
        printWriter.print(this.f1787p);
        printWriter.print(" mStateSaved=");
        printWriter.print(this.B);
        printWriter.print(" mStopped=");
        printWriter.print(this.C);
        printWriter.print(" mDestroyed=");
        printWriter.println(this.D);
        if (this.A) {
            printWriter.print(str);
            printWriter.print("  mNeedMenuInvalidate=");
            printWriter.println(this.A);
        }
    }

    public final void z() {
        Iterator it = ((HashSet) f()).iterator();
        while (it.hasNext()) {
            ((t0) it.next()).e();
        }
    }

    @SuppressLint({"BanParcelableUsage"})
    public static class LaunchedFragmentInfo implements Parcelable {
        public static final Parcelable.Creator<LaunchedFragmentInfo> CREATOR = new a();

        /* renamed from: a  reason: collision with root package name */
        public String f1798a;

        /* renamed from: b  reason: collision with root package name */
        public int f1799b;

        public class a implements Parcelable.Creator<LaunchedFragmentInfo> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.Creator
            public LaunchedFragmentInfo createFromParcel(Parcel parcel) {
                return new LaunchedFragmentInfo(parcel);
            }

            /* Return type fixed from 'java.lang.Object[]' to match base method */
            @Override // android.os.Parcelable.Creator
            public LaunchedFragmentInfo[] newArray(int i10) {
                return new LaunchedFragmentInfo[i10];
            }
        }

        public LaunchedFragmentInfo(@NonNull String str, int i10) {
            this.f1798a = str;
            this.f1799b = i10;
        }

        public int describeContents() {
            return 0;
        }

        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeString(this.f1798a);
            parcel.writeInt(this.f1799b);
        }

        public LaunchedFragmentInfo(@NonNull Parcel parcel) {
            this.f1798a = parcel.readString();
            this.f1799b = parcel.readInt();
        }
    }
}
