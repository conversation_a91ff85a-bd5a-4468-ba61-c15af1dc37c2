package androidx.recyclerview.widget;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.database.Observable;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.os.SystemClock;
import android.os.Trace;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Display;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.ViewPropertyAnimator;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.view.animation.Interpolator;
import android.widget.EdgeEffect;
import android.widget.OverScroller;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.Px;
import androidx.annotation.RestrictTo;
import androidx.annotation.VisibleForTesting;
import androidx.appcompat.widget.f0;
import androidx.core.view.ViewCompat;
import androidx.customview.view.AbsSavedState;
import androidx.recyclerview.R$attr;
import androidx.recyclerview.R$dimen;
import androidx.recyclerview.R$styleable;
import androidx.recyclerview.widget.ViewBoundsCheck;
import androidx.recyclerview.widget.a;
import androidx.recyclerview.widget.c;
import androidx.recyclerview.widget.d0;
import androidx.recyclerview.widget.l;
import androidx.recyclerview.widget.n;
import androidx.recyclerview.widget.z;
import com.duokan.airkan.common.Constant;
import com.google.protobuf.Reader;
import com.xiaomi.mitv.pie.EventResultPersister;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.WeakHashMap;
import k0.b;

public class RecyclerView extends ViewGroup implements j0.e, j0.d {

    /* renamed from: i1  reason: collision with root package name */
    public static final int[] f2135i1 = {16843830};

    /* renamed from: j1  reason: collision with root package name */
    public static final Class<?>[] f2136j1;

    /* renamed from: k1  reason: collision with root package name */
    public static final Interpolator f2137k1 = new b();
    public int A0;
    public int B0;
    public VelocityTracker C0;
    public int D0;
    public int E0;
    public int F0;
    public int G0;
    public int H0;
    public l I0;
    public final int J0;
    public final int K0;
    public float L0;
    public float M0;
    public boolean N0;
    public final v O0;
    public n P0;
    public n.b Q0;
    public final t R0;
    public n S0;
    public List<n> T0;
    public boolean U0;
    public boolean V0;
    public ItemAnimator.b W0;
    public boolean X0;
    public z Y0;
    public g Z0;

    /* renamed from: a  reason: collision with root package name */
    public final r f2138a;

    /* renamed from: a1  reason: collision with root package name */
    public final int[] f2139a1;

    /* renamed from: b  reason: collision with root package name */
    public final p f2140b;

    /* renamed from: b1  reason: collision with root package name */
    public j0.f f2141b1;

    /* renamed from: c  reason: collision with root package name */
    public SavedState f2142c;

    /* renamed from: c1  reason: collision with root package name */
    public final int[] f2143c1;

    /* renamed from: d  reason: collision with root package name */
    public a f2144d;
    public final int[] d1;

    /* renamed from: e  reason: collision with root package name */
    public c f2145e;

    /* renamed from: e1  reason: collision with root package name */
    public final int[] f2146e1;

    /* renamed from: f  reason: collision with root package name */
    public final d0 f2147f;
    @VisibleForTesting

    /* renamed from: f1  reason: collision with root package name */
    public final List<w> f2148f1;

    /* renamed from: g  reason: collision with root package name */
    public boolean f2149g;

    /* renamed from: g1  reason: collision with root package name */
    public Runnable f2150g1;
    public final Rect h;

    /* renamed from: h1  reason: collision with root package name */
    public final d0.b f2151h1;

    /* renamed from: i  reason: collision with root package name */
    public final Rect f2152i;

    /* renamed from: j  reason: collision with root package name */
    public final RectF f2153j;

    /* renamed from: k  reason: collision with root package name */
    public d f2154k;
    @VisibleForTesting

    /* renamed from: l  reason: collision with root package name */
    public j f2155l;

    /* renamed from: m  reason: collision with root package name */
    public q f2156m;

    /* renamed from: m0  reason: collision with root package name */
    public boolean f2157m0;

    /* renamed from: n  reason: collision with root package name */
    public final ArrayList<i> f2158n;

    /* renamed from: n0  reason: collision with root package name */
    public int f2159n0;

    /* renamed from: o  reason: collision with root package name */
    public final ArrayList<m> f2160o;

    /* renamed from: o0  reason: collision with root package name */
    public final AccessibilityManager f2161o0;

    /* renamed from: p  reason: collision with root package name */
    public m f2162p;

    /* renamed from: p0  reason: collision with root package name */
    public List<k> f2163p0;

    /* renamed from: q  reason: collision with root package name */
    public boolean f2164q;

    /* renamed from: q0  reason: collision with root package name */
    public boolean f2165q0;

    /* renamed from: r  reason: collision with root package name */
    public boolean f2166r;

    /* renamed from: r0  reason: collision with root package name */
    public boolean f2167r0;
    @VisibleForTesting

    /* renamed from: s  reason: collision with root package name */
    public boolean f2168s;

    /* renamed from: s0  reason: collision with root package name */
    public int f2169s0;

    /* renamed from: t  reason: collision with root package name */
    public int f2170t;

    /* renamed from: t0  reason: collision with root package name */
    public int f2171t0;
    @NonNull

    /* renamed from: u0  reason: collision with root package name */
    public EdgeEffectFactory f2172u0;

    /* renamed from: v0  reason: collision with root package name */
    public EdgeEffect f2173v0;

    /* renamed from: w0  reason: collision with root package name */
    public EdgeEffect f2174w0;

    /* renamed from: x  reason: collision with root package name */
    public boolean f2175x;

    /* renamed from: x0  reason: collision with root package name */
    public EdgeEffect f2176x0;

    /* renamed from: y  reason: collision with root package name */
    public boolean f2177y;

    /* renamed from: y0  reason: collision with root package name */
    public EdgeEffect f2178y0;

    /* renamed from: z0  reason: collision with root package name */
    public ItemAnimator f2179z0;

    public static class EdgeEffectFactory {

        @Retention(RetentionPolicy.SOURCE)
        public @interface EdgeDirection {
        }

        @NonNull
        public EdgeEffect a(@NonNull RecyclerView recyclerView) {
            return new EdgeEffect(recyclerView.getContext());
        }
    }

    public static abstract class ItemAnimator {

        /* renamed from: a  reason: collision with root package name */
        public b f2180a = null;

        /* renamed from: b  reason: collision with root package name */
        public ArrayList<a> f2181b = new ArrayList<>();

        /* renamed from: c  reason: collision with root package name */
        public long f2182c = 120;

        /* renamed from: d  reason: collision with root package name */
        public long f2183d = 120;

        /* renamed from: e  reason: collision with root package name */
        public long f2184e = 250;

        /* renamed from: f  reason: collision with root package name */
        public long f2185f = 250;

        @Retention(RetentionPolicy.SOURCE)
        public @interface AdapterChanges {
        }

        public interface a {
            void a();
        }

        public interface b {
        }

        public static class c {

            /* renamed from: a  reason: collision with root package name */
            public int f2186a;

            /* renamed from: b  reason: collision with root package name */
            public int f2187b;
        }

        public static int b(w wVar) {
            int i10;
            int i11 = wVar.f2275j & 14;
            if (wVar.j()) {
                return 4;
            }
            if ((i11 & 4) != 0) {
                return i11;
            }
            int i12 = wVar.f2270d;
            RecyclerView recyclerView = wVar.f2283r;
            if (recyclerView == null) {
                i10 = -1;
            } else {
                i10 = recyclerView.H(wVar);
            }
            return (i12 == -1 || i10 == -1 || i12 == i10) ? i11 : i11 | 2048;
        }

        public abstract boolean a(@NonNull w wVar, @NonNull w wVar2, @NonNull c cVar, @NonNull c cVar2);

        public final void c(@NonNull w wVar) {
            b bVar = this.f2180a;
            if (bVar != null) {
                h hVar = (h) bVar;
                Objects.requireNonNull(hVar);
                boolean z10 = true;
                wVar.s(true);
                if (wVar.h != null && wVar.f2274i == null) {
                    wVar.h = null;
                }
                wVar.f2274i = null;
                if (!((wVar.f2275j & 16) != 0)) {
                    RecyclerView recyclerView = RecyclerView.this;
                    View view = wVar.f2267a;
                    recyclerView.i0();
                    c cVar = recyclerView.f2145e;
                    int indexOfChild = ((x) cVar.f2342a).f2479a.indexOfChild(view);
                    if (indexOfChild == -1) {
                        cVar.l(view);
                    } else if (cVar.f2343b.d(indexOfChild)) {
                        cVar.f2343b.f(indexOfChild);
                        cVar.l(view);
                        ((x) cVar.f2342a).c(indexOfChild);
                    } else {
                        z10 = false;
                    }
                    if (z10) {
                        w K = RecyclerView.K(view);
                        recyclerView.f2140b.k(K);
                        recyclerView.f2140b.h(K);
                    }
                    recyclerView.k0(!z10);
                    if (!z10 && wVar.n()) {
                        RecyclerView.this.removeDetachedView(wVar.f2267a, false);
                    }
                }
            }
        }

        public final void d() {
            int size = this.f2181b.size();
            for (int i10 = 0; i10 < size; i10++) {
                this.f2181b.get(i10).a();
            }
            this.f2181b.clear();
        }

        public abstract void e(@NonNull w wVar);

        public abstract void f();

        public abstract boolean g();

        /* JADX WARN: Incorrect args count in method signature: (Landroidx/recyclerview/widget/RecyclerView$t;Landroidx/recyclerview/widget/RecyclerView$w;ILjava/util/List<Ljava/lang/Object;>;)Landroidx/recyclerview/widget/RecyclerView$ItemAnimator$c; */
        @NonNull
        public c h(@NonNull w wVar) {
            c cVar = new c();
            View view = wVar.f2267a;
            cVar.f2186a = view.getLeft();
            cVar.f2187b = view.getTop();
            view.getRight();
            view.getBottom();
            return cVar;
        }
    }

    @Retention(RetentionPolicy.SOURCE)
    @RestrictTo({RestrictTo.Scope.LIBRARY_GROUP_PREFIX})
    public @interface Orientation {
    }

    public class a implements Runnable {
        public a() {
        }

        public void run() {
            ItemAnimator itemAnimator = RecyclerView.this.f2179z0;
            if (itemAnimator != null) {
                l lVar = (l) itemAnimator;
                boolean z10 = !lVar.h.isEmpty();
                boolean z11 = !lVar.f2389j.isEmpty();
                boolean z12 = !lVar.f2390k.isEmpty();
                boolean z13 = !lVar.f2388i.isEmpty();
                if (z10 || z11 || z13 || z12) {
                    Iterator<w> it = lVar.h.iterator();
                    while (it.hasNext()) {
                        w next = it.next();
                        View view = next.f2267a;
                        ViewPropertyAnimator animate = view.animate();
                        lVar.f2396q.add(next);
                        animate.setDuration(lVar.f2183d).alpha(Constant.VOLUME_FLOAT_MIN).setListener(new g(lVar, next, animate, view)).start();
                    }
                    lVar.h.clear();
                    if (z11) {
                        ArrayList<l.b> arrayList = new ArrayList<>();
                        arrayList.addAll(lVar.f2389j);
                        lVar.f2392m.add(arrayList);
                        lVar.f2389j.clear();
                        d dVar = new d(lVar, arrayList);
                        if (z10) {
                            View view2 = arrayList.get(0).f2404a.f2267a;
                            long j10 = lVar.f2183d;
                            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
                            view2.postOnAnimationDelayed(dVar, j10);
                        } else {
                            dVar.run();
                        }
                    }
                    if (z12) {
                        ArrayList<l.a> arrayList2 = new ArrayList<>();
                        arrayList2.addAll(lVar.f2390k);
                        lVar.f2393n.add(arrayList2);
                        lVar.f2390k.clear();
                        e eVar = new e(lVar, arrayList2);
                        if (z10) {
                            View view3 = arrayList2.get(0).f2398a.f2267a;
                            long j11 = lVar.f2183d;
                            WeakHashMap<View, j0.m> weakHashMap2 = ViewCompat.f1593a;
                            view3.postOnAnimationDelayed(eVar, j11);
                        } else {
                            eVar.run();
                        }
                    }
                    if (z13) {
                        ArrayList<w> arrayList3 = new ArrayList<>();
                        arrayList3.addAll(lVar.f2388i);
                        lVar.f2391l.add(arrayList3);
                        lVar.f2388i.clear();
                        f fVar = new f(lVar, arrayList3);
                        if (z10 || z11 || z12) {
                            long j12 = 0;
                            long j13 = z10 ? lVar.f2183d : 0;
                            long j14 = z11 ? lVar.f2184e : 0;
                            if (z12) {
                                j12 = lVar.f2185f;
                            }
                            View view4 = arrayList3.get(0).f2267a;
                            WeakHashMap<View, j0.m> weakHashMap3 = ViewCompat.f1593a;
                            view4.postOnAnimationDelayed(fVar, Math.max(j14, j12) + j13);
                        } else {
                            fVar.run();
                        }
                    }
                }
            }
            RecyclerView.this.X0 = false;
        }
    }

    public static class b implements Interpolator {
        public float getInterpolation(float f10) {
            float f11 = f10 - 1.0f;
            return (f11 * f11 * f11 * f11 * f11) + 1.0f;
        }
    }

    public class c implements d0.b {
        public c() {
        }

        public void a(w wVar, ItemAnimator.c cVar, ItemAnimator.c cVar2) {
            boolean z10;
            int i10;
            int i11;
            RecyclerView recyclerView = RecyclerView.this;
            Objects.requireNonNull(recyclerView);
            wVar.s(false);
            b0 b0Var = (b0) recyclerView.f2179z0;
            Objects.requireNonNull(b0Var);
            if (cVar == null || ((i10 = cVar.f2186a) == (i11 = cVar2.f2186a) && cVar.f2187b == cVar2.f2187b)) {
                l lVar = (l) b0Var;
                lVar.n(wVar);
                wVar.f2267a.setAlpha(Constant.VOLUME_FLOAT_MIN);
                lVar.f2388i.add(wVar);
                z10 = true;
            } else {
                z10 = b0Var.i(wVar, i10, cVar.f2187b, i11, cVar2.f2187b);
            }
            if (z10) {
                recyclerView.V();
            }
        }

        public void b(w wVar, @NonNull ItemAnimator.c cVar, @Nullable ItemAnimator.c cVar2) {
            boolean z10;
            RecyclerView.this.f2140b.k(wVar);
            RecyclerView recyclerView = RecyclerView.this;
            recyclerView.f(wVar);
            wVar.s(false);
            b0 b0Var = (b0) recyclerView.f2179z0;
            Objects.requireNonNull(b0Var);
            int i10 = cVar.f2186a;
            int i11 = cVar.f2187b;
            View view = wVar.f2267a;
            int left = cVar2 == null ? view.getLeft() : cVar2.f2186a;
            int top = cVar2 == null ? view.getTop() : cVar2.f2187b;
            if (wVar.l() || (i10 == left && i11 == top)) {
                l lVar = (l) b0Var;
                lVar.n(wVar);
                lVar.h.add(wVar);
                z10 = true;
            } else {
                view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                z10 = b0Var.i(wVar, i10, i11, left, top);
            }
            if (z10) {
                recyclerView.V();
            }
        }
    }

    public static abstract class d<VH extends w> {

        /* renamed from: a  reason: collision with root package name */
        public final e f2195a = new e();

        /* renamed from: b  reason: collision with root package name */
        public boolean f2196b = false;

        public abstract int a();

        public long b(int i10) {
            return -1;
        }

        public int c(int i10) {
            return 0;
        }

        public abstract void d(@NonNull VH vh, int i10);

        @NonNull
        public abstract VH e(@NonNull ViewGroup viewGroup, int i10);

        public void f(@NonNull VH vh) {
        }
    }

    public static class e extends Observable<f> {
        public boolean a() {
            return !((Observable) this).mObservers.isEmpty();
        }

        public void b() {
            for (int size = ((Observable) this).mObservers.size() - 1; size >= 0; size--) {
                ((f) ((Observable) this).mObservers.get(size)).a();
            }
        }
    }

    public static abstract class f {
        public void a() {
        }
    }

    public interface g {
        int a(int i10, int i11);
    }

    public class h implements ItemAnimator.b {
        public h() {
        }
    }

    public static abstract class i {
        public void d(@NonNull Canvas canvas, @NonNull RecyclerView recyclerView, @NonNull t tVar) {
        }

        public void e(@NonNull Canvas canvas, @NonNull RecyclerView recyclerView, @NonNull t tVar) {
        }
    }

    public static abstract class j {

        /* renamed from: a  reason: collision with root package name */
        public c f2198a;

        /* renamed from: b  reason: collision with root package name */
        public RecyclerView f2199b;

        /* renamed from: c  reason: collision with root package name */
        public ViewBoundsCheck f2200c;

        /* renamed from: d  reason: collision with root package name */
        public ViewBoundsCheck f2201d;
        @Nullable

        /* renamed from: e  reason: collision with root package name */
        public s f2202e;

        /* renamed from: f  reason: collision with root package name */
        public boolean f2203f = false;

        /* renamed from: g  reason: collision with root package name */
        public boolean f2204g = false;
        public boolean h = true;

        /* renamed from: i  reason: collision with root package name */
        public boolean f2205i = true;

        /* renamed from: j  reason: collision with root package name */
        public int f2206j;

        /* renamed from: k  reason: collision with root package name */
        public boolean f2207k;

        /* renamed from: l  reason: collision with root package name */
        public int f2208l;

        /* renamed from: m  reason: collision with root package name */
        public int f2209m;

        /* renamed from: n  reason: collision with root package name */
        public int f2210n;

        /* renamed from: o  reason: collision with root package name */
        public int f2211o;

        public class a implements ViewBoundsCheck.b {
            public a() {
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int a() {
                j jVar = j.this;
                return jVar.f2210n - jVar.O();
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int b(View view) {
                return j.this.C(view) - ((ViewGroup.MarginLayoutParams) ((LayoutParams) view.getLayoutParams())).leftMargin;
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public View c(int i10) {
                return j.this.w(i10);
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int d() {
                return j.this.N();
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int e(View view) {
                return j.this.F(view) + ((ViewGroup.MarginLayoutParams) ((LayoutParams) view.getLayoutParams())).rightMargin;
            }
        }

        public class b implements ViewBoundsCheck.b {
            public b() {
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int a() {
                j jVar = j.this;
                return jVar.f2211o - jVar.M();
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int b(View view) {
                return j.this.G(view) - ((ViewGroup.MarginLayoutParams) ((LayoutParams) view.getLayoutParams())).topMargin;
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public View c(int i10) {
                return j.this.w(i10);
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int d() {
                return j.this.P();
            }

            @Override // androidx.recyclerview.widget.ViewBoundsCheck.b
            public int e(View view) {
                return j.this.A(view) + ((ViewGroup.MarginLayoutParams) ((LayoutParams) view.getLayoutParams())).bottomMargin;
            }
        }

        public interface c {
        }

        public static class d {

            /* renamed from: a  reason: collision with root package name */
            public int f2214a;

            /* renamed from: b  reason: collision with root package name */
            public int f2215b;

            /* renamed from: c  reason: collision with root package name */
            public boolean f2216c;

            /* renamed from: d  reason: collision with root package name */
            public boolean f2217d;
        }

        public j() {
            a aVar = new a();
            b bVar = new b();
            this.f2200c = new ViewBoundsCheck(aVar);
            this.f2201d = new ViewBoundsCheck(bVar);
        }

        public static d R(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10, int i11) {
            d dVar = new d();
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, R$styleable.RecyclerView, i10, i11);
            dVar.f2214a = obtainStyledAttributes.getInt(R$styleable.RecyclerView_android_orientation, 1);
            dVar.f2215b = obtainStyledAttributes.getInt(R$styleable.RecyclerView_spanCount, 1);
            dVar.f2216c = obtainStyledAttributes.getBoolean(R$styleable.RecyclerView_reverseLayout, false);
            dVar.f2217d = obtainStyledAttributes.getBoolean(R$styleable.RecyclerView_stackFromEnd, false);
            obtainStyledAttributes.recycle();
            return dVar;
        }

        public static boolean V(int i10, int i11, int i12) {
            int mode = View.MeasureSpec.getMode(i11);
            int size = View.MeasureSpec.getSize(i11);
            if (i12 > 0 && i10 != i12) {
                return false;
            }
            if (mode == Integer.MIN_VALUE) {
                return size >= i10;
            }
            if (mode != 0) {
                return mode == 1073741824 && size == i10;
            }
            return true;
        }

        public static int h(int i10, int i11, int i12) {
            int mode = View.MeasureSpec.getMode(i10);
            int size = View.MeasureSpec.getSize(i10);
            if (mode != Integer.MIN_VALUE) {
                return mode != 1073741824 ? Math.max(i11, i12) : size;
            }
            return Math.min(size, Math.max(i11, i12));
        }

        /* JADX WARNING: Code restructure failed: missing block: B:6:0x0017, code lost:
            if (r5 == 1073741824) goto L_0x0020;
         */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public static int y(int r4, int r5, int r6, int r7, boolean r8) {
            /*
                int r4 = r4 - r6
                r6 = 0
                int r4 = java.lang.Math.max(r6, r4)
                r0 = -2
                r1 = -1
                r2 = -2147483648(0xffffffff80000000, float:-0.0)
                r3 = 1073741824(0x40000000, float:2.0)
                if (r8 == 0) goto L_0x001a
                if (r7 < 0) goto L_0x0011
                goto L_0x001c
            L_0x0011:
                if (r7 != r1) goto L_0x002f
                if (r5 == r2) goto L_0x0020
                if (r5 == 0) goto L_0x002f
                if (r5 == r3) goto L_0x0020
                goto L_0x002f
            L_0x001a:
                if (r7 < 0) goto L_0x001e
            L_0x001c:
                r5 = r3
                goto L_0x0031
            L_0x001e:
                if (r7 != r1) goto L_0x0022
            L_0x0020:
                r7 = r4
                goto L_0x0031
            L_0x0022:
                if (r7 != r0) goto L_0x002f
                if (r5 == r2) goto L_0x002c
                if (r5 != r3) goto L_0x0029
                goto L_0x002c
            L_0x0029:
                r7 = r4
                r5 = r6
                goto L_0x0031
            L_0x002c:
                r7 = r4
                r5 = r2
                goto L_0x0031
            L_0x002f:
                r5 = r6
                r7 = r5
            L_0x0031:
                int r4 = android.view.View.MeasureSpec.makeMeasureSpec(r7, r5)
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.j.y(int, int, int, int, boolean):int");
        }

        public int A(@NonNull View view) {
            return view.getBottom() + ((LayoutParams) view.getLayoutParams()).f2189b.bottom;
        }

        public void A0(RecyclerView recyclerView) {
            B0(View.MeasureSpec.makeMeasureSpec(recyclerView.getWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(recyclerView.getHeight(), 1073741824));
        }

        public void B(@NonNull View view, @NonNull Rect rect) {
            int[] iArr = RecyclerView.f2135i1;
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            Rect rect2 = layoutParams.f2189b;
            rect.set((view.getLeft() - rect2.left) - ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin, (view.getTop() - rect2.top) - ((ViewGroup.MarginLayoutParams) layoutParams).topMargin, view.getRight() + rect2.right + ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin, view.getBottom() + rect2.bottom + ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin);
        }

        public void B0(int i10, int i11) {
            this.f2210n = View.MeasureSpec.getSize(i10);
            int mode = View.MeasureSpec.getMode(i10);
            this.f2208l = mode;
            if (mode == 0) {
                int[] iArr = RecyclerView.f2135i1;
            }
            this.f2211o = View.MeasureSpec.getSize(i11);
            int mode2 = View.MeasureSpec.getMode(i11);
            this.f2209m = mode2;
            if (mode2 == 0) {
                int[] iArr2 = RecyclerView.f2135i1;
            }
        }

        public int C(@NonNull View view) {
            return view.getLeft() - ((LayoutParams) view.getLayoutParams()).f2189b.left;
        }

        public void C0(Rect rect, int i10, int i11) {
            int O = O() + N() + rect.width();
            int M = M() + P() + rect.height();
            this.f2199b.setMeasuredDimension(h(i10, O, L()), h(i11, M, K()));
        }

        public int D(@NonNull View view) {
            Rect rect = ((LayoutParams) view.getLayoutParams()).f2189b;
            return view.getMeasuredHeight() + rect.top + rect.bottom;
        }

        public void D0(int i10, int i11) {
            int x8 = x();
            if (x8 == 0) {
                this.f2199b.o(i10, i11);
                return;
            }
            int i12 = EventResultPersister.GENERATE_NEW_ID;
            int i13 = Integer.MAX_VALUE;
            int i14 = Integer.MAX_VALUE;
            int i15 = Integer.MIN_VALUE;
            for (int i16 = 0; i16 < x8; i16++) {
                View w10 = w(i16);
                Rect rect = this.f2199b.h;
                B(w10, rect);
                int i17 = rect.left;
                if (i17 < i13) {
                    i13 = i17;
                }
                int i18 = rect.right;
                if (i18 > i12) {
                    i12 = i18;
                }
                int i19 = rect.top;
                if (i19 < i14) {
                    i14 = i19;
                }
                int i20 = rect.bottom;
                if (i20 > i15) {
                    i15 = i20;
                }
            }
            this.f2199b.h.set(i13, i14, i12, i15);
            C0(this.f2199b.h, i10, i11);
        }

        public int E(@NonNull View view) {
            Rect rect = ((LayoutParams) view.getLayoutParams()).f2189b;
            return view.getMeasuredWidth() + rect.left + rect.right;
        }

        public void E0(RecyclerView recyclerView) {
            if (recyclerView == null) {
                this.f2199b = null;
                this.f2198a = null;
                this.f2210n = 0;
                this.f2211o = 0;
            } else {
                this.f2199b = recyclerView;
                this.f2198a = recyclerView.f2145e;
                this.f2210n = recyclerView.getWidth();
                this.f2211o = recyclerView.getHeight();
            }
            this.f2208l = 1073741824;
            this.f2209m = 1073741824;
        }

        public int F(@NonNull View view) {
            return view.getRight() + ((LayoutParams) view.getLayoutParams()).f2189b.right;
        }

        public boolean F0(View view, int i10, int i11, LayoutParams layoutParams) {
            return view.isLayoutRequested() || !this.h || !V(view.getWidth(), i10, ((ViewGroup.MarginLayoutParams) layoutParams).width) || !V(view.getHeight(), i11, ((ViewGroup.MarginLayoutParams) layoutParams).height);
        }

        public int G(@NonNull View view) {
            return view.getTop() - ((LayoutParams) view.getLayoutParams()).f2189b.top;
        }

        public boolean G0() {
            return false;
        }

        @Nullable
        public View H() {
            View focusedChild;
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView == null || (focusedChild = recyclerView.getFocusedChild()) == null || this.f2198a.f2344c.contains(focusedChild)) {
                return null;
            }
            return focusedChild;
        }

        public boolean H0(View view, int i10, int i11, LayoutParams layoutParams) {
            return !this.h || !V(view.getMeasuredWidth(), i10, ((ViewGroup.MarginLayoutParams) layoutParams).width) || !V(view.getMeasuredHeight(), i11, ((ViewGroup.MarginLayoutParams) layoutParams).height);
        }

        public int I() {
            RecyclerView recyclerView = this.f2199b;
            d adapter = recyclerView != null ? recyclerView.getAdapter() : null;
            if (adapter != null) {
                return adapter.a();
            }
            return 0;
        }

        public void I0(RecyclerView recyclerView, t tVar, int i10) {
            Log.e("RecyclerView", "You must override smoothScrollToPosition to support smooth scrolling");
        }

        public int J() {
            RecyclerView recyclerView = this.f2199b;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            return recyclerView.getLayoutDirection();
        }

        public void J0(s sVar) {
            s sVar2 = this.f2202e;
            if (!(sVar2 == null || sVar == sVar2 || !sVar2.f2236e)) {
                sVar2.d();
            }
            this.f2202e = sVar;
            RecyclerView recyclerView = this.f2199b;
            recyclerView.O0.c();
            if (sVar.h) {
                StringBuilder a10 = com.duokan.airkan.server.f.a("An instance of ");
                a10.append(sVar.getClass().getSimpleName());
                a10.append(" was started more than once. Each instance of");
                a10.append(sVar.getClass().getSimpleName());
                a10.append(" is intended to only be used once. You should create a new instance for each use.");
                Log.w("RecyclerView", a10.toString());
            }
            sVar.f2233b = recyclerView;
            sVar.f2234c = this;
            int i10 = sVar.f2232a;
            if (i10 != -1) {
                recyclerView.R0.f2246a = i10;
                sVar.f2236e = true;
                sVar.f2235d = true;
                sVar.f2237f = recyclerView.f2155l.s(i10);
                sVar.f2233b.O0.a();
                sVar.h = true;
                return;
            }
            throw new IllegalArgumentException("Invalid target position");
        }

        @Px
        public int K() {
            RecyclerView recyclerView = this.f2199b;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            return recyclerView.getMinimumHeight();
        }

        public boolean K0() {
            return false;
        }

        @Px
        public int L() {
            RecyclerView recyclerView = this.f2199b;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            return recyclerView.getMinimumWidth();
        }

        @Px
        public int M() {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                return recyclerView.getPaddingBottom();
            }
            return 0;
        }

        @Px
        public int N() {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                return recyclerView.getPaddingLeft();
            }
            return 0;
        }

        @Px
        public int O() {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                return recyclerView.getPaddingRight();
            }
            return 0;
        }

        @Px
        public int P() {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                return recyclerView.getPaddingTop();
            }
            return 0;
        }

        public int Q(@NonNull View view) {
            return ((LayoutParams) view.getLayoutParams()).a();
        }

        public int S(@NonNull p pVar, @NonNull t tVar) {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView == null || recyclerView.f2154k == null || !f()) {
                return 1;
            }
            return this.f2199b.f2154k.a();
        }

        public void T(@NonNull View view, boolean z10, @NonNull Rect rect) {
            Matrix matrix;
            if (z10) {
                Rect rect2 = ((LayoutParams) view.getLayoutParams()).f2189b;
                rect.set(-rect2.left, -rect2.top, view.getWidth() + rect2.right, view.getHeight() + rect2.bottom);
            } else {
                rect.set(0, 0, view.getWidth(), view.getHeight());
            }
            if (!(this.f2199b == null || (matrix = view.getMatrix()) == null || matrix.isIdentity())) {
                RectF rectF = this.f2199b.f2153j;
                rectF.set(rect);
                matrix.mapRect(rectF);
                rect.set((int) Math.floor((double) rectF.left), (int) Math.floor((double) rectF.top), (int) Math.ceil((double) rectF.right), (int) Math.ceil((double) rectF.bottom));
            }
            rect.offset(view.getLeft(), view.getTop());
        }

        public boolean U() {
            return false;
        }

        public void W(@NonNull View view, int i10, int i11, int i12, int i13) {
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            Rect rect = layoutParams.f2189b;
            view.layout(i10 + rect.left + ((ViewGroup.MarginLayoutParams) layoutParams).leftMargin, i11 + rect.top + ((ViewGroup.MarginLayoutParams) layoutParams).topMargin, (i12 - rect.right) - ((ViewGroup.MarginLayoutParams) layoutParams).rightMargin, (i13 - rect.bottom) - ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin);
        }

        public void X(@Px int i10) {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                int e10 = recyclerView.f2145e.e();
                for (int i11 = 0; i11 < e10; i11++) {
                    recyclerView.f2145e.d(i11).offsetLeftAndRight(i10);
                }
            }
        }

        public void Y(@Px int i10) {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                int e10 = recyclerView.f2145e.e();
                for (int i11 = 0; i11 < e10; i11++) {
                    recyclerView.f2145e.d(i11).offsetTopAndBottom(i10);
                }
            }
        }

        @CallSuper
        public void Z(RecyclerView recyclerView, p pVar) {
        }

        @Nullable
        public View a0(@NonNull View view, int i10, @NonNull p pVar, @NonNull t tVar) {
            return null;
        }

        public void b(View view) {
            c(view, -1, false);
        }

        public void b0(@NonNull AccessibilityEvent accessibilityEvent) {
            RecyclerView recyclerView = this.f2199b;
            p pVar = recyclerView.f2140b;
            t tVar = recyclerView.R0;
            if (recyclerView != null && accessibilityEvent != null) {
                boolean z10 = true;
                if (!recyclerView.canScrollVertically(1) && !this.f2199b.canScrollVertically(-1) && !this.f2199b.canScrollHorizontally(-1) && !this.f2199b.canScrollHorizontally(1)) {
                    z10 = false;
                }
                accessibilityEvent.setScrollable(z10);
                d dVar = this.f2199b.f2154k;
                if (dVar != null) {
                    accessibilityEvent.setItemCount(dVar.a());
                }
            }
        }

        public final void c(View view, int i10, boolean z10) {
            w K = RecyclerView.K(view);
            if (z10 || K.l()) {
                this.f2199b.f2147f.a(K);
            } else {
                this.f2199b.f2147f.f(K);
            }
            LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
            if (K.u() || K.m()) {
                if (K.m()) {
                    K.f2279n.k(K);
                } else {
                    K.d();
                }
                this.f2198a.b(view, i10, view.getLayoutParams(), false);
            } else {
                int i11 = -1;
                if (view.getParent() == this.f2199b) {
                    int j10 = this.f2198a.j(view);
                    if (i10 == -1) {
                        i10 = this.f2198a.e();
                    }
                    if (j10 == -1) {
                        StringBuilder a10 = com.duokan.airkan.server.f.a("Added View has RecyclerView as parent but view is not a real child. Unfiltered index:");
                        a10.append(this.f2199b.indexOfChild(view));
                        throw new IllegalStateException(b.a(this.f2199b, a10));
                    } else if (j10 != i10) {
                        j jVar = this.f2199b.f2155l;
                        View w10 = jVar.w(j10);
                        if (w10 != null) {
                            jVar.w(j10);
                            jVar.f2198a.c(j10);
                            LayoutParams layoutParams2 = (LayoutParams) w10.getLayoutParams();
                            w K2 = RecyclerView.K(w10);
                            if (K2.l()) {
                                jVar.f2199b.f2147f.a(K2);
                            } else {
                                jVar.f2199b.f2147f.f(K2);
                            }
                            jVar.f2198a.b(w10, i10, layoutParams2, K2.l());
                        } else {
                            throw new IllegalArgumentException("Cannot move a child from non-existing index:" + j10 + jVar.f2199b.toString());
                        }
                    }
                } else {
                    this.f2198a.a(view, i10, false);
                    layoutParams.f2190c = true;
                    s sVar = this.f2202e;
                    if (sVar != null && sVar.f2236e) {
                        Objects.requireNonNull(sVar.f2233b);
                        w K3 = RecyclerView.K(view);
                        if (K3 != null) {
                            i11 = K3.e();
                        }
                        if (i11 == sVar.f2232a) {
                            sVar.f2237f = view;
                        }
                    }
                }
            }
            if (layoutParams.f2191d) {
                K.f2267a.invalidate();
                layoutParams.f2191d = false;
            }
        }

        public void c0(@NonNull p pVar, @NonNull t tVar, @NonNull k0.b bVar) {
            if (this.f2199b.canScrollVertically(-1) || this.f2199b.canScrollHorizontally(-1)) {
                bVar.f7267a.addAction(8192);
                bVar.f7267a.setScrollable(true);
            }
            if (this.f2199b.canScrollVertically(1) || this.f2199b.canScrollHorizontally(1)) {
                bVar.f7267a.addAction(4096);
                bVar.f7267a.setScrollable(true);
            }
            bVar.m(b.C0108b.a(S(pVar, tVar), z(pVar, tVar), false, 0));
        }

        public void d(String str) {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                recyclerView.i(str);
            }
        }

        public void d0(View view, k0.b bVar) {
            w K = RecyclerView.K(view);
            if (K != null && !K.l() && !this.f2198a.k(K.f2267a)) {
                RecyclerView recyclerView = this.f2199b;
                e0(recyclerView.f2140b, recyclerView.R0, view, bVar);
            }
        }

        public boolean e() {
            return false;
        }

        public void e0(@NonNull p pVar, @NonNull t tVar, @NonNull View view, @NonNull k0.b bVar) {
            int i10 = 0;
            int Q = f() ? Q(view) : 0;
            if (e()) {
                i10 = Q(view);
            }
            bVar.n(b.c.a(Q, 1, i10, 1, false, false));
        }

        public boolean f() {
            return false;
        }

        public void f0(@NonNull RecyclerView recyclerView, int i10, int i11) {
        }

        public boolean g(LayoutParams layoutParams) {
            return layoutParams != null;
        }

        public void g0(@NonNull RecyclerView recyclerView) {
        }

        public void h0(@NonNull RecyclerView recyclerView, int i10, int i11, int i12) {
        }

        public void i(int i10, int i11, t tVar, c cVar) {
        }

        public void i0(@NonNull RecyclerView recyclerView, int i10, int i11) {
        }

        public void j(int i10, c cVar) {
        }

        public void j0(@NonNull RecyclerView recyclerView, int i10, int i11, @Nullable Object obj) {
        }

        public int k(@NonNull t tVar) {
            return 0;
        }

        public void k0(p pVar, t tVar) {
            Log.e("RecyclerView", "You must override onLayoutChildren(Recycler recycler, State state) ");
        }

        public int l(@NonNull t tVar) {
            return 0;
        }

        public void l0(t tVar) {
        }

        public int m(@NonNull t tVar) {
            return 0;
        }

        public void m0(Parcelable parcelable) {
        }

        public int n(@NonNull t tVar) {
            return 0;
        }

        @Nullable
        public Parcelable n0() {
            return null;
        }

        public int o(@NonNull t tVar) {
            return 0;
        }

        public void o0(int i10) {
        }

        public int p(@NonNull t tVar) {
            return 0;
        }

        /* JADX WARNING: Removed duplicated region for block: B:25:0x006d A[ADDED_TO_REGION] */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public boolean p0(@androidx.annotation.NonNull androidx.recyclerview.widget.RecyclerView.p r8, @androidx.annotation.NonNull androidx.recyclerview.widget.RecyclerView.t r9, int r10, @androidx.annotation.Nullable android.os.Bundle r11) {
            /*
            // Method dump skipped, instructions count: 122
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.j.p0(androidx.recyclerview.widget.RecyclerView$p, androidx.recyclerview.widget.RecyclerView$t, int, android.os.Bundle):boolean");
        }

        public void q(@NonNull p pVar) {
            int x8 = x();
            while (true) {
                x8--;
                if (x8 >= 0) {
                    View w10 = w(x8);
                    w K = RecyclerView.K(w10);
                    if (!K.t()) {
                        if (!K.j() || K.l() || this.f2199b.f2154k.f2196b) {
                            w(x8);
                            this.f2198a.c(x8);
                            pVar.i(w10);
                            this.f2199b.f2147f.f(K);
                        } else {
                            u0(x8);
                            pVar.h(K);
                        }
                    }
                } else {
                    return;
                }
            }
        }

        public void q0(@NonNull p pVar) {
            for (int x8 = x() - 1; x8 >= 0; x8--) {
                if (!RecyclerView.K(w(x8)).t()) {
                    t0(x8, pVar);
                }
            }
        }

        @Nullable
        public View r(@NonNull View view) {
            View C;
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView == null || (C = recyclerView.C(view)) == null || this.f2198a.f2344c.contains(C)) {
                return null;
            }
            return C;
        }

        public void r0(p pVar) {
            int size = pVar.f2224a.size();
            for (int i10 = size - 1; i10 >= 0; i10--) {
                View view = pVar.f2224a.get(i10).f2267a;
                w K = RecyclerView.K(view);
                if (!K.t()) {
                    K.s(false);
                    if (K.n()) {
                        this.f2199b.removeDetachedView(view, false);
                    }
                    ItemAnimator itemAnimator = this.f2199b.f2179z0;
                    if (itemAnimator != null) {
                        itemAnimator.e(K);
                    }
                    K.s(true);
                    w K2 = RecyclerView.K(view);
                    K2.f2279n = null;
                    K2.f2280o = false;
                    K2.d();
                    pVar.h(K2);
                }
            }
            pVar.f2224a.clear();
            ArrayList<w> arrayList = pVar.f2225b;
            if (arrayList != null) {
                arrayList.clear();
            }
            if (size > 0) {
                this.f2199b.invalidate();
            }
        }

        @Nullable
        public View s(int i10) {
            int x8 = x();
            for (int i11 = 0; i11 < x8; i11++) {
                View w10 = w(i11);
                w K = RecyclerView.K(w10);
                if (K != null && K.e() == i10 && !K.t() && (this.f2199b.R0.f2252g || !K.l())) {
                    return w10;
                }
            }
            return null;
        }

        public void s0(@NonNull View view, @NonNull p pVar) {
            c cVar = this.f2198a;
            int indexOfChild = ((x) cVar.f2342a).f2479a.indexOfChild(view);
            if (indexOfChild >= 0) {
                if (cVar.f2343b.f(indexOfChild)) {
                    cVar.l(view);
                }
                ((x) cVar.f2342a).c(indexOfChild);
            }
            pVar.g(view);
        }

        public abstract LayoutParams t();

        public void t0(int i10, @NonNull p pVar) {
            View w10 = w(i10);
            u0(i10);
            pVar.g(w10);
        }

        public LayoutParams u(Context context, AttributeSet attributeSet) {
            return new LayoutParams(context, attributeSet);
        }

        public void u0(int i10) {
            c cVar;
            int f10;
            View a10;
            if (w(i10) != null && (a10 = ((x) cVar.f2342a).a((f10 = (cVar = this.f2198a).f(i10)))) != null) {
                if (cVar.f2343b.f(f10)) {
                    cVar.l(a10);
                }
                ((x) cVar.f2342a).c(f10);
            }
        }

        public LayoutParams v(ViewGroup.LayoutParams layoutParams) {
            if (layoutParams instanceof LayoutParams) {
                return new LayoutParams((LayoutParams) layoutParams);
            }
            if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                return new LayoutParams((ViewGroup.MarginLayoutParams) layoutParams);
            }
            return new LayoutParams(layoutParams);
        }

        /* JADX WARNING: Code restructure failed: missing block: B:23:0x00b8, code lost:
            if (r1 == false) goto L_0x00bf;
         */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public boolean v0(@androidx.annotation.NonNull androidx.recyclerview.widget.RecyclerView r19, @androidx.annotation.NonNull android.view.View r20, @androidx.annotation.NonNull android.graphics.Rect r21, boolean r22, boolean r23) {
            /*
            // Method dump skipped, instructions count: 213
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.j.v0(androidx.recyclerview.widget.RecyclerView, android.view.View, android.graphics.Rect, boolean, boolean):boolean");
        }

        @Nullable
        public View w(int i10) {
            c cVar = this.f2198a;
            if (cVar == null) {
                return null;
            }
            return ((x) cVar.f2342a).a(cVar.f(i10));
        }

        public void w0() {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView != null) {
                recyclerView.requestLayout();
            }
        }

        public int x() {
            c cVar = this.f2198a;
            if (cVar != null) {
                return cVar.e();
            }
            return 0;
        }

        public int x0(int i10, p pVar, t tVar) {
            return 0;
        }

        public void y0(int i10) {
        }

        public int z(@NonNull p pVar, @NonNull t tVar) {
            RecyclerView recyclerView = this.f2199b;
            if (recyclerView == null || recyclerView.f2154k == null || !e()) {
                return 1;
            }
            return this.f2199b.f2154k.a();
        }

        public int z0(int i10, p pVar, t tVar) {
            return 0;
        }
    }

    public interface k {
        void a(@NonNull View view);

        void b(@NonNull View view);
    }

    public static abstract class l {
    }

    public interface m {
        boolean a(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent);

        void b(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent);

        void c(boolean z10);
    }

    public static abstract class n {
        public void a(@NonNull RecyclerView recyclerView, int i10) {
        }

        public void b(@NonNull RecyclerView recyclerView, int i10, int i11) {
        }
    }

    public static class o {

        /* renamed from: a  reason: collision with root package name */
        public SparseArray<a> f2218a = new SparseArray<>();

        /* renamed from: b  reason: collision with root package name */
        public int f2219b = 0;

        public static class a {

            /* renamed from: a  reason: collision with root package name */
            public final ArrayList<w> f2220a = new ArrayList<>();

            /* renamed from: b  reason: collision with root package name */
            public int f2221b = 5;

            /* renamed from: c  reason: collision with root package name */
            public long f2222c = 0;

            /* renamed from: d  reason: collision with root package name */
            public long f2223d = 0;
        }

        public final a a(int i10) {
            a aVar = this.f2218a.get(i10);
            if (aVar != null) {
                return aVar;
            }
            a aVar2 = new a();
            this.f2218a.put(i10, aVar2);
            return aVar2;
        }

        public long b(long j10, long j11) {
            if (j10 == 0) {
                return j11;
            }
            return (j11 / 4) + ((j10 / 4) * 3);
        }
    }

    public final class p {

        /* renamed from: a  reason: collision with root package name */
        public final ArrayList<w> f2224a;

        /* renamed from: b  reason: collision with root package name */
        public ArrayList<w> f2225b = null;

        /* renamed from: c  reason: collision with root package name */
        public final ArrayList<w> f2226c = new ArrayList<>();

        /* renamed from: d  reason: collision with root package name */
        public final List<w> f2227d;

        /* renamed from: e  reason: collision with root package name */
        public int f2228e;

        /* renamed from: f  reason: collision with root package name */
        public int f2229f;

        /* renamed from: g  reason: collision with root package name */
        public o f2230g;

        public p() {
            ArrayList<w> arrayList = new ArrayList<>();
            this.f2224a = arrayList;
            this.f2227d = Collections.unmodifiableList(arrayList);
            this.f2228e = 2;
            this.f2229f = 2;
        }

        public void a(@NonNull w wVar, boolean z10) {
            RecyclerView.k(wVar);
            View view = wVar.f2267a;
            z zVar = RecyclerView.this.Y0;
            if (zVar != null) {
                z.a aVar = zVar.f2482e;
                ViewCompat.k(view, aVar instanceof z.a ? aVar.f2484e.remove(view) : null);
            }
            if (z10) {
                q qVar = RecyclerView.this.f2156m;
                if (qVar != null) {
                    qVar.a(wVar);
                }
                d dVar = RecyclerView.this.f2154k;
                if (dVar != null) {
                    dVar.f(wVar);
                }
                RecyclerView recyclerView = RecyclerView.this;
                if (recyclerView.R0 != null) {
                    recyclerView.f2147f.g(wVar);
                }
            }
            wVar.f2283r = null;
            o d10 = d();
            Objects.requireNonNull(d10);
            int i10 = wVar.f2272f;
            ArrayList<w> arrayList = d10.a(i10).f2220a;
            if (d10.f2218a.get(i10).f2221b > arrayList.size()) {
                wVar.q();
                arrayList.add(wVar);
            }
        }

        public void b() {
            this.f2224a.clear();
            e();
        }

        public int c(int i10) {
            if (i10 < 0 || i10 >= RecyclerView.this.R0.b()) {
                StringBuilder b10 = f0.b("invalid position ", i10, ". State item count is ");
                b10.append(RecyclerView.this.R0.b());
                throw new IndexOutOfBoundsException(b.a(RecyclerView.this, b10));
            }
            RecyclerView recyclerView = RecyclerView.this;
            if (!recyclerView.R0.f2252g) {
                return i10;
            }
            return recyclerView.f2144d.f(i10, 0);
        }

        public o d() {
            if (this.f2230g == null) {
                this.f2230g = new o();
            }
            return this.f2230g;
        }

        public void e() {
            for (int size = this.f2226c.size() - 1; size >= 0; size--) {
                f(size);
            }
            this.f2226c.clear();
            int[] iArr = RecyclerView.f2135i1;
            n.b bVar = RecyclerView.this.Q0;
            int[] iArr2 = bVar.f2447c;
            if (iArr2 != null) {
                Arrays.fill(iArr2, -1);
            }
            bVar.f2448d = 0;
        }

        public void f(int i10) {
            a(this.f2226c.get(i10), true);
            this.f2226c.remove(i10);
        }

        public void g(@NonNull View view) {
            w K = RecyclerView.K(view);
            if (K.n()) {
                RecyclerView.this.removeDetachedView(view, false);
            }
            if (K.m()) {
                K.f2279n.k(K);
            } else if (K.u()) {
                K.d();
            }
            h(K);
            if (RecyclerView.this.f2179z0 != null && !K.k()) {
                RecyclerView.this.f2179z0.e(K);
            }
        }

        /* JADX WARNING: Removed duplicated region for block: B:16:0x003b  */
        /* JADX WARNING: Removed duplicated region for block: B:38:0x0092  */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public void h(androidx.recyclerview.widget.RecyclerView.w r7) {
            /*
            // Method dump skipped, instructions count: 249
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.p.h(androidx.recyclerview.widget.RecyclerView$w):void");
        }

        /* JADX WARNING: Removed duplicated region for block: B:18:0x003d  */
        /* JADX WARNING: Removed duplicated region for block: B:21:0x0043  */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public void i(android.view.View r5) {
            /*
            // Method dump skipped, instructions count: 137
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.p.i(android.view.View):void");
        }

        /* JADX WARNING: Code restructure failed: missing block: B:160:0x02ff, code lost:
            r7 = null;
         */
        /* JADX WARNING: Code restructure failed: missing block: B:226:0x043c, code lost:
            if (r7.j() == false) goto L_0x0472;
         */
        /* JADX WARNING: Code restructure failed: missing block: B:235:0x0470, code lost:
            if ((r10 == 0 || r10 + r8 < r19) == false) goto L_0x0472;
         */
        /* JADX WARNING: Removed duplicated region for block: B:124:0x024b  */
        /* JADX WARNING: Removed duplicated region for block: B:212:0x0404  */
        /* JADX WARNING: Removed duplicated region for block: B:220:0x042e  */
        /* JADX WARNING: Removed duplicated region for block: B:229:0x0459  */
        /* JADX WARNING: Removed duplicated region for block: B:239:0x0483  */
        /* JADX WARNING: Removed duplicated region for block: B:242:0x04a0  */
        /* JADX WARNING: Removed duplicated region for block: B:245:0x04b3  */
        /* JADX WARNING: Removed duplicated region for block: B:253:0x04e3  */
        /* JADX WARNING: Removed duplicated region for block: B:266:0x0513  */
        /* JADX WARNING: Removed duplicated region for block: B:269:0x051c  */
        /* JADX WARNING: Removed duplicated region for block: B:273:0x0527  */
        /* JADX WARNING: Removed duplicated region for block: B:274:0x0535  */
        /* JADX WARNING: Removed duplicated region for block: B:34:0x0089  */
        /* JADX WARNING: Removed duplicated region for block: B:39:0x0090  */
        @androidx.annotation.Nullable
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public androidx.recyclerview.widget.RecyclerView.w j(int r17, boolean r18, long r19) {
            /*
            // Method dump skipped, instructions count: 1400
            */
            throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.p.j(int, boolean, long):androidx.recyclerview.widget.RecyclerView$w");
        }

        public void k(w wVar) {
            if (wVar.f2280o) {
                this.f2225b.remove(wVar);
            } else {
                this.f2224a.remove(wVar);
            }
            wVar.f2279n = null;
            wVar.f2280o = false;
            wVar.d();
        }

        public void l() {
            j jVar = RecyclerView.this.f2155l;
            this.f2229f = this.f2228e + (jVar != null ? jVar.f2206j : 0);
            for (int size = this.f2226c.size() - 1; size >= 0 && this.f2226c.size() > this.f2229f; size--) {
                f(size);
            }
        }
    }

    public interface q {
        void a(@NonNull w wVar);
    }

    public class r extends f {
        public r() {
        }

        @Override // androidx.recyclerview.widget.RecyclerView.f
        public void a() {
            RecyclerView.this.i(null);
            RecyclerView recyclerView = RecyclerView.this;
            recyclerView.R0.f2251f = true;
            recyclerView.X(true);
            if (!RecyclerView.this.f2144d.g()) {
                RecyclerView.this.requestLayout();
            }
        }
    }

    public static abstract class s {

        /* renamed from: a  reason: collision with root package name */
        public int f2232a = -1;

        /* renamed from: b  reason: collision with root package name */
        public RecyclerView f2233b;

        /* renamed from: c  reason: collision with root package name */
        public j f2234c;

        /* renamed from: d  reason: collision with root package name */
        public boolean f2235d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f2236e;

        /* renamed from: f  reason: collision with root package name */
        public View f2237f;

        /* renamed from: g  reason: collision with root package name */
        public final a f2238g = new a(0, 0);
        public boolean h;

        public static class a {

            /* renamed from: a  reason: collision with root package name */
            public int f2239a;

            /* renamed from: b  reason: collision with root package name */
            public int f2240b;

            /* renamed from: c  reason: collision with root package name */
            public int f2241c;

            /* renamed from: d  reason: collision with root package name */
            public int f2242d = -1;

            /* renamed from: e  reason: collision with root package name */
            public Interpolator f2243e;

            /* renamed from: f  reason: collision with root package name */
            public boolean f2244f = false;

            /* renamed from: g  reason: collision with root package name */
            public int f2245g = 0;

            public a(@Px int i10, @Px int i11) {
                this.f2239a = i10;
                this.f2240b = i11;
                this.f2241c = EventResultPersister.GENERATE_NEW_ID;
                this.f2243e = null;
            }

            public void a(RecyclerView recyclerView) {
                int i10 = this.f2242d;
                if (i10 >= 0) {
                    this.f2242d = -1;
                    recyclerView.P(i10);
                    this.f2244f = false;
                } else if (this.f2244f) {
                    Interpolator interpolator = this.f2243e;
                    if (interpolator == null || this.f2241c >= 1) {
                        int i11 = this.f2241c;
                        if (i11 >= 1) {
                            recyclerView.O0.b(this.f2239a, this.f2240b, i11, interpolator);
                            int i12 = this.f2245g + 1;
                            this.f2245g = i12;
                            if (i12 > 10) {
                                Log.e("RecyclerView", "Smooth Scroll action is being updated too frequently. Make sure you are not changing it unless necessary");
                            }
                            this.f2244f = false;
                            return;
                        }
                        throw new IllegalStateException("Scroll duration must be a positive number");
                    }
                    throw new IllegalStateException("If you provide an interpolator, you must set a positive duration");
                } else {
                    this.f2245g = 0;
                }
            }

            public void b(@Px int i10, @Px int i11, int i12, @Nullable Interpolator interpolator) {
                this.f2239a = i10;
                this.f2240b = i11;
                this.f2241c = i12;
                this.f2243e = interpolator;
                this.f2244f = true;
            }
        }

        public interface b {
            @Nullable
            PointF a(int i10);
        }

        @Nullable
        public PointF a(int i10) {
            j jVar = this.f2234c;
            if (jVar instanceof b) {
                return ((b) jVar).a(i10);
            }
            StringBuilder a10 = com.duokan.airkan.server.f.a("You should override computeScrollVectorForPosition when the LayoutManager does not implement ");
            a10.append(b.class.getCanonicalName());
            Log.w("RecyclerView", a10.toString());
            return null;
        }

        public void b(int i10, int i11) {
            PointF a10;
            RecyclerView recyclerView = this.f2233b;
            int i12 = -1;
            if (this.f2232a == -1 || recyclerView == null) {
                d();
            }
            if (this.f2235d && this.f2237f == null && this.f2234c != null && (a10 = a(this.f2232a)) != null) {
                float f10 = a10.x;
                if (!(f10 == Constant.VOLUME_FLOAT_MIN && a10.y == Constant.VOLUME_FLOAT_MIN)) {
                    recyclerView.d0((int) Math.signum(f10), (int) Math.signum(a10.y), null);
                }
            }
            boolean z10 = false;
            this.f2235d = false;
            View view = this.f2237f;
            if (view != null) {
                Objects.requireNonNull(this.f2233b);
                w K = RecyclerView.K(view);
                if (K != null) {
                    i12 = K.e();
                }
                if (i12 == this.f2232a) {
                    c(this.f2237f, recyclerView.R0, this.f2238g);
                    this.f2238g.a(recyclerView);
                    d();
                } else {
                    Log.e("RecyclerView", "Passed over target position while smooth scrolling.");
                    this.f2237f = null;
                }
            }
            if (this.f2236e) {
                t tVar = recyclerView.R0;
                a aVar = this.f2238g;
                q qVar = (q) this;
                if (qVar.f2233b.f2155l.x() == 0) {
                    qVar.d();
                } else {
                    int i13 = qVar.f2468o;
                    int i14 = i13 - i10;
                    if (i13 * i14 <= 0) {
                        i14 = 0;
                    }
                    qVar.f2468o = i14;
                    int i15 = qVar.f2469p;
                    int i16 = i15 - i11;
                    if (i15 * i16 <= 0) {
                        i16 = 0;
                    }
                    qVar.f2469p = i16;
                    if (i14 == 0 && i16 == 0) {
                        PointF a11 = qVar.a(qVar.f2232a);
                        if (a11 != null) {
                            float f11 = a11.x;
                            if (!(f11 == Constant.VOLUME_FLOAT_MIN && a11.y == Constant.VOLUME_FLOAT_MIN)) {
                                float f12 = a11.y;
                                float sqrt = (float) Math.sqrt((double) ((f12 * f12) + (f11 * f11)));
                                float f13 = a11.x / sqrt;
                                a11.x = f13;
                                float f14 = a11.y / sqrt;
                                a11.y = f14;
                                qVar.f2464k = a11;
                                qVar.f2468o = (int) (f13 * 10000.0f);
                                qVar.f2469p = (int) (f14 * 10000.0f);
                                aVar.b((int) (((float) qVar.f2468o) * 1.2f), (int) (((float) qVar.f2469p) * 1.2f), (int) (((float) qVar.h(10000)) * 1.2f), qVar.f2462i);
                            }
                        }
                        aVar.f2242d = qVar.f2232a;
                        qVar.d();
                    }
                }
                a aVar2 = this.f2238g;
                if (aVar2.f2242d >= 0) {
                    z10 = true;
                }
                aVar2.a(recyclerView);
                if (z10 && this.f2236e) {
                    this.f2235d = true;
                    recyclerView.O0.a();
                }
            }
        }

        public abstract void c(@NonNull View view, @NonNull t tVar, @NonNull a aVar);

        public final void d() {
            if (this.f2236e) {
                this.f2236e = false;
                q qVar = (q) this;
                qVar.f2469p = 0;
                qVar.f2468o = 0;
                qVar.f2464k = null;
                this.f2233b.R0.f2246a = -1;
                this.f2237f = null;
                this.f2232a = -1;
                this.f2235d = false;
                j jVar = this.f2234c;
                if (jVar.f2202e == this) {
                    jVar.f2202e = null;
                }
                this.f2234c = null;
                this.f2233b = null;
            }
        }
    }

    public static class t {

        /* renamed from: a  reason: collision with root package name */
        public int f2246a = -1;

        /* renamed from: b  reason: collision with root package name */
        public int f2247b = 0;

        /* renamed from: c  reason: collision with root package name */
        public int f2248c = 0;

        /* renamed from: d  reason: collision with root package name */
        public int f2249d = 1;

        /* renamed from: e  reason: collision with root package name */
        public int f2250e = 0;

        /* renamed from: f  reason: collision with root package name */
        public boolean f2251f = false;

        /* renamed from: g  reason: collision with root package name */
        public boolean f2252g = false;
        public boolean h = false;

        /* renamed from: i  reason: collision with root package name */
        public boolean f2253i = false;

        /* renamed from: j  reason: collision with root package name */
        public boolean f2254j = false;

        /* renamed from: k  reason: collision with root package name */
        public boolean f2255k = false;

        /* renamed from: l  reason: collision with root package name */
        public int f2256l;

        /* renamed from: m  reason: collision with root package name */
        public long f2257m;

        /* renamed from: n  reason: collision with root package name */
        public int f2258n;

        public void a(int i10) {
            if ((this.f2249d & i10) == 0) {
                StringBuilder a10 = com.duokan.airkan.server.f.a("Layout state should be one of ");
                a10.append(Integer.toBinaryString(i10));
                a10.append(" but it is ");
                a10.append(Integer.toBinaryString(this.f2249d));
                throw new IllegalStateException(a10.toString());
            }
        }

        public int b() {
            return this.f2252g ? this.f2247b - this.f2248c : this.f2250e;
        }

        public String toString() {
            StringBuilder a10 = com.duokan.airkan.server.f.a("State{mTargetPosition=");
            a10.append(this.f2246a);
            a10.append(", mData=");
            a10.append((Object) null);
            a10.append(", mItemCount=");
            a10.append(this.f2250e);
            a10.append(", mIsMeasuring=");
            a10.append(this.f2253i);
            a10.append(", mPreviousLayoutItemCount=");
            a10.append(this.f2247b);
            a10.append(", mDeletedInvisibleItemCountSincePreviousLayout=");
            a10.append(this.f2248c);
            a10.append(", mStructureChanged=");
            a10.append(this.f2251f);
            a10.append(", mInPreLayout=");
            a10.append(this.f2252g);
            a10.append(", mRunSimpleAnimations=");
            a10.append(this.f2254j);
            a10.append(", mRunPredictiveAnimations=");
            a10.append(this.f2255k);
            a10.append('}');
            return a10.toString();
        }
    }

    public static abstract class u {
    }

    public class v implements Runnable {

        /* renamed from: a  reason: collision with root package name */
        public int f2259a;

        /* renamed from: b  reason: collision with root package name */
        public int f2260b;

        /* renamed from: c  reason: collision with root package name */
        public OverScroller f2261c;

        /* renamed from: d  reason: collision with root package name */
        public Interpolator f2262d;

        /* renamed from: e  reason: collision with root package name */
        public boolean f2263e = false;

        /* renamed from: f  reason: collision with root package name */
        public boolean f2264f = false;

        public v() {
            Interpolator interpolator = RecyclerView.f2137k1;
            this.f2262d = interpolator;
            this.f2261c = new OverScroller(RecyclerView.this.getContext(), interpolator);
        }

        public void a() {
            if (this.f2263e) {
                this.f2264f = true;
                return;
            }
            RecyclerView.this.removeCallbacks(this);
            RecyclerView recyclerView = RecyclerView.this;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            recyclerView.postOnAnimation(this);
        }

        public void b(int i10, int i11, int i12, @Nullable Interpolator interpolator) {
            int i13;
            if (i12 == Integer.MIN_VALUE) {
                int abs = Math.abs(i10);
                int abs2 = Math.abs(i11);
                boolean z10 = abs > abs2;
                int sqrt = (int) Math.sqrt((double) 0);
                int sqrt2 = (int) Math.sqrt((double) ((i11 * i11) + (i10 * i10)));
                RecyclerView recyclerView = RecyclerView.this;
                int width = z10 ? recyclerView.getWidth() : recyclerView.getHeight();
                int i14 = width / 2;
                float f10 = (float) width;
                float f11 = (float) i14;
                float sin = (((float) Math.sin((double) ((Math.min(1.0f, (((float) sqrt2) * 1.0f) / f10) - 0.5f) * 0.47123894f))) * f11) + f11;
                if (sqrt > 0) {
                    i13 = Math.round(Math.abs(sin / ((float) sqrt)) * 1000.0f) * 4;
                } else {
                    if (!z10) {
                        abs = abs2;
                    }
                    i13 = (int) (((((float) abs) / f10) + 1.0f) * 300.0f);
                }
                i12 = Math.min(i13, 2000);
            }
            if (interpolator == null) {
                interpolator = RecyclerView.f2137k1;
            }
            if (this.f2262d != interpolator) {
                this.f2262d = interpolator;
                this.f2261c = new OverScroller(RecyclerView.this.getContext(), interpolator);
            }
            this.f2260b = 0;
            this.f2259a = 0;
            RecyclerView.this.setScrollState(2);
            this.f2261c.startScroll(0, 0, i10, i11, i12);
            a();
        }

        public void c() {
            RecyclerView.this.removeCallbacks(this);
            this.f2261c.abortAnimation();
        }

        public void run() {
            int i10;
            int i11;
            RecyclerView recyclerView = RecyclerView.this;
            if (recyclerView.f2155l == null) {
                c();
                return;
            }
            this.f2264f = false;
            this.f2263e = true;
            recyclerView.n();
            OverScroller overScroller = this.f2261c;
            if (overScroller.computeScrollOffset()) {
                int currX = overScroller.getCurrX();
                int currY = overScroller.getCurrY();
                int i12 = currX - this.f2259a;
                int i13 = currY - this.f2260b;
                this.f2259a = currX;
                this.f2260b = currY;
                RecyclerView recyclerView2 = RecyclerView.this;
                int[] iArr = recyclerView2.f2146e1;
                iArr[0] = 0;
                iArr[1] = 0;
                if (recyclerView2.t(i12, i13, iArr, null, 1)) {
                    int[] iArr2 = RecyclerView.this.f2146e1;
                    i12 -= iArr2[0];
                    i13 -= iArr2[1];
                }
                if (RecyclerView.this.getOverScrollMode() != 2) {
                    RecyclerView.this.m(i12, i13);
                }
                RecyclerView recyclerView3 = RecyclerView.this;
                if (recyclerView3.f2154k != null) {
                    int[] iArr3 = recyclerView3.f2146e1;
                    iArr3[0] = 0;
                    iArr3[1] = 0;
                    recyclerView3.d0(i12, i13, iArr3);
                    RecyclerView recyclerView4 = RecyclerView.this;
                    int[] iArr4 = recyclerView4.f2146e1;
                    i10 = iArr4[0];
                    i11 = iArr4[1];
                    i12 -= i10;
                    i13 -= i11;
                    s sVar = recyclerView4.f2155l.f2202e;
                    if (sVar != null && !sVar.f2235d && sVar.f2236e) {
                        int b10 = recyclerView4.R0.b();
                        if (b10 == 0) {
                            sVar.d();
                        } else if (sVar.f2232a >= b10) {
                            sVar.f2232a = b10 - 1;
                            sVar.b(i10, i11);
                        } else {
                            sVar.b(i10, i11);
                        }
                    }
                } else {
                    i11 = 0;
                    i10 = 0;
                }
                if (!RecyclerView.this.f2158n.isEmpty()) {
                    RecyclerView.this.invalidate();
                }
                RecyclerView recyclerView5 = RecyclerView.this;
                int[] iArr5 = recyclerView5.f2146e1;
                iArr5[0] = 0;
                iArr5[1] = 0;
                recyclerView5.u(i10, i11, i12, i13, null, 1, iArr5);
                RecyclerView recyclerView6 = RecyclerView.this;
                int[] iArr6 = recyclerView6.f2146e1;
                int i14 = i12 - iArr6[0];
                int i15 = i13 - iArr6[1];
                if (!(i10 == 0 && i11 == 0)) {
                    recyclerView6.v(i10, i11);
                }
                if (!RecyclerView.this.awakenScrollBars()) {
                    RecyclerView.this.invalidate();
                }
                boolean z10 = overScroller.isFinished() || (((overScroller.getCurrX() == overScroller.getFinalX()) || i14 != 0) && ((overScroller.getCurrY() == overScroller.getFinalY()) || i15 != 0));
                RecyclerView recyclerView7 = RecyclerView.this;
                s sVar2 = recyclerView7.f2155l.f2202e;
                if ((sVar2 != null && sVar2.f2235d) || !z10) {
                    a();
                    RecyclerView recyclerView8 = RecyclerView.this;
                    n nVar = recyclerView8.P0;
                    if (nVar != null) {
                        nVar.a(recyclerView8, i10, i11);
                    }
                } else {
                    if (recyclerView7.getOverScrollMode() != 2) {
                        int currVelocity = (int) overScroller.getCurrVelocity();
                        int i16 = i14 < 0 ? -currVelocity : i14 > 0 ? currVelocity : 0;
                        if (i15 < 0) {
                            currVelocity = -currVelocity;
                        } else if (i15 <= 0) {
                            currVelocity = 0;
                        }
                        RecyclerView recyclerView9 = RecyclerView.this;
                        Objects.requireNonNull(recyclerView9);
                        if (i16 < 0) {
                            recyclerView9.x();
                            if (recyclerView9.f2173v0.isFinished()) {
                                recyclerView9.f2173v0.onAbsorb(-i16);
                            }
                        } else if (i16 > 0) {
                            recyclerView9.y();
                            if (recyclerView9.f2176x0.isFinished()) {
                                recyclerView9.f2176x0.onAbsorb(i16);
                            }
                        }
                        if (currVelocity < 0) {
                            recyclerView9.z();
                            if (recyclerView9.f2174w0.isFinished()) {
                                recyclerView9.f2174w0.onAbsorb(-currVelocity);
                            }
                        } else if (currVelocity > 0) {
                            recyclerView9.w();
                            if (recyclerView9.f2178y0.isFinished()) {
                                recyclerView9.f2178y0.onAbsorb(currVelocity);
                            }
                        }
                        if (!(i16 == 0 && currVelocity == 0)) {
                            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
                            recyclerView9.postInvalidateOnAnimation();
                        }
                    }
                    int[] iArr7 = RecyclerView.f2135i1;
                    n.b bVar = RecyclerView.this.Q0;
                    int[] iArr8 = bVar.f2447c;
                    if (iArr8 != null) {
                        Arrays.fill(iArr8, -1);
                    }
                    bVar.f2448d = 0;
                }
            }
            s sVar3 = RecyclerView.this.f2155l.f2202e;
            if (sVar3 != null && sVar3.f2235d) {
                sVar3.b(0, 0);
            }
            this.f2263e = false;
            if (this.f2264f) {
                RecyclerView.this.removeCallbacks(this);
                RecyclerView recyclerView10 = RecyclerView.this;
                WeakHashMap<View, j0.m> weakHashMap2 = ViewCompat.f1593a;
                recyclerView10.postOnAnimation(this);
                return;
            }
            RecyclerView.this.setScrollState(0);
            RecyclerView.this.l0(1);
        }
    }

    public static abstract class w {

        /* renamed from: s  reason: collision with root package name */
        public static final List<Object> f2266s = Collections.emptyList();
        @NonNull

        /* renamed from: a  reason: collision with root package name */
        public final View f2267a;

        /* renamed from: b  reason: collision with root package name */
        public WeakReference<RecyclerView> f2268b;

        /* renamed from: c  reason: collision with root package name */
        public int f2269c = -1;

        /* renamed from: d  reason: collision with root package name */
        public int f2270d = -1;

        /* renamed from: e  reason: collision with root package name */
        public long f2271e = -1;

        /* renamed from: f  reason: collision with root package name */
        public int f2272f = -1;

        /* renamed from: g  reason: collision with root package name */
        public int f2273g = -1;
        public w h = null;

        /* renamed from: i  reason: collision with root package name */
        public w f2274i = null;

        /* renamed from: j  reason: collision with root package name */
        public int f2275j;

        /* renamed from: k  reason: collision with root package name */
        public List<Object> f2276k = null;

        /* renamed from: l  reason: collision with root package name */
        public List<Object> f2277l = null;

        /* renamed from: m  reason: collision with root package name */
        public int f2278m = 0;

        /* renamed from: n  reason: collision with root package name */
        public p f2279n = null;

        /* renamed from: o  reason: collision with root package name */
        public boolean f2280o = false;

        /* renamed from: p  reason: collision with root package name */
        public int f2281p = 0;
        @VisibleForTesting

        /* renamed from: q  reason: collision with root package name */
        public int f2282q = -1;

        /* renamed from: r  reason: collision with root package name */
        public RecyclerView f2283r;

        public w(@NonNull View view) {
            if (view != null) {
                this.f2267a = view;
                return;
            }
            throw new IllegalArgumentException("itemView may not be null");
        }

        public void a(Object obj) {
            if (obj == null) {
                b(1024);
            } else if ((1024 & this.f2275j) == 0) {
                if (this.f2276k == null) {
                    ArrayList arrayList = new ArrayList();
                    this.f2276k = arrayList;
                    this.f2277l = Collections.unmodifiableList(arrayList);
                }
                this.f2276k.add(obj);
            }
        }

        public void b(int i10) {
            this.f2275j = i10 | this.f2275j;
        }

        public void c() {
            this.f2270d = -1;
            this.f2273g = -1;
        }

        public void d() {
            this.f2275j &= -33;
        }

        public final int e() {
            int i10 = this.f2273g;
            return i10 == -1 ? this.f2269c : i10;
        }

        public List<Object> f() {
            if ((this.f2275j & 1024) != 0) {
                return f2266s;
            }
            List<Object> list = this.f2276k;
            if (list == null || list.size() == 0) {
                return f2266s;
            }
            return this.f2277l;
        }

        public boolean g(int i10) {
            return (i10 & this.f2275j) != 0;
        }

        public boolean h() {
            return (this.f2267a.getParent() == null || this.f2267a.getParent() == this.f2283r) ? false : true;
        }

        public boolean i() {
            return (this.f2275j & 1) != 0;
        }

        public boolean j() {
            return (this.f2275j & 4) != 0;
        }

        public final boolean k() {
            if ((this.f2275j & 16) == 0) {
                View view = this.f2267a;
                WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
                if (!view.hasTransientState()) {
                    return true;
                }
            }
            return false;
        }

        public boolean l() {
            return (this.f2275j & 8) != 0;
        }

        public boolean m() {
            return this.f2279n != null;
        }

        public boolean n() {
            return (this.f2275j & 256) != 0;
        }

        public boolean o() {
            return (this.f2275j & 2) != 0;
        }

        public void p(int i10, boolean z10) {
            if (this.f2270d == -1) {
                this.f2270d = this.f2269c;
            }
            if (this.f2273g == -1) {
                this.f2273g = this.f2269c;
            }
            if (z10) {
                this.f2273g += i10;
            }
            this.f2269c += i10;
            if (this.f2267a.getLayoutParams() != null) {
                ((LayoutParams) this.f2267a.getLayoutParams()).f2190c = true;
            }
        }

        public void q() {
            this.f2275j = 0;
            this.f2269c = -1;
            this.f2270d = -1;
            this.f2271e = -1;
            this.f2273g = -1;
            this.f2278m = 0;
            this.h = null;
            this.f2274i = null;
            List<Object> list = this.f2276k;
            if (list != null) {
                list.clear();
            }
            this.f2275j &= -1025;
            this.f2281p = 0;
            this.f2282q = -1;
            RecyclerView.k(this);
        }

        public void r(int i10, int i11) {
            this.f2275j = (i10 & i11) | (this.f2275j & (~i11));
        }

        public final void s(boolean z10) {
            int i10 = this.f2278m;
            int i11 = z10 ? i10 - 1 : i10 + 1;
            this.f2278m = i11;
            if (i11 < 0) {
                this.f2278m = 0;
                Log.e("View", "isRecyclable decremented below 0: unmatched pair of setIsRecyable() calls for " + this);
            } else if (!z10 && i11 == 1) {
                this.f2275j |= 16;
            } else if (z10 && i11 == 0) {
                this.f2275j &= -17;
            }
        }

        public boolean t() {
            return (this.f2275j & 128) != 0;
        }

        public String toString() {
            StringBuilder b10 = androidx.appcompat.widget.i.b(getClass().isAnonymousClass() ? "ViewHolder" : getClass().getSimpleName(), "{");
            b10.append(Integer.toHexString(hashCode()));
            b10.append(" position=");
            b10.append(this.f2269c);
            b10.append(" id=");
            b10.append(this.f2271e);
            b10.append(", oldPos=");
            b10.append(this.f2270d);
            b10.append(", pLpos:");
            b10.append(this.f2273g);
            StringBuilder sb = new StringBuilder(b10.toString());
            if (m()) {
                sb.append(" scrap ");
                sb.append(this.f2280o ? "[changeScrap]" : "[attachedScrap]");
            }
            if (j()) {
                sb.append(" invalid");
            }
            if (!i()) {
                sb.append(" unbound");
            }
            boolean z10 = true;
            if ((this.f2275j & 2) != 0) {
                sb.append(" update");
            }
            if (l()) {
                sb.append(" removed");
            }
            if (t()) {
                sb.append(" ignored");
            }
            if (n()) {
                sb.append(" tmpDetached");
            }
            if (!k()) {
                StringBuilder a10 = com.duokan.airkan.server.f.a(" not recyclable(");
                a10.append(this.f2278m);
                a10.append(")");
                sb.append(a10.toString());
            }
            if ((this.f2275j & 512) == 0 && !j()) {
                z10 = false;
            }
            if (z10) {
                sb.append(" undefined adapter position");
            }
            if (this.f2267a.getParent() == null) {
                sb.append(" no parent");
            }
            sb.append("}");
            return sb.toString();
        }

        public boolean u() {
            return (this.f2275j & 32) != 0;
        }
    }

    static {
        Class<?> cls = Integer.TYPE;
        f2136j1 = new Class[]{Context.class, AttributeSet.class, cls, cls};
    }

    public RecyclerView(@NonNull Context context, @Nullable AttributeSet attributeSet) {
        this(context, attributeSet, R$attr.recyclerViewStyle);
    }

    @Nullable
    public static RecyclerView F(@NonNull View view) {
        if (!(view instanceof ViewGroup)) {
            return null;
        }
        if (view instanceof RecyclerView) {
            return (RecyclerView) view;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i10 = 0; i10 < childCount; i10++) {
            RecyclerView F = F(viewGroup.getChildAt(i10));
            if (F != null) {
                return F;
            }
        }
        return null;
    }

    public static w K(View view) {
        if (view == null) {
            return null;
        }
        return ((LayoutParams) view.getLayoutParams()).f2188a;
    }

    private j0.f getScrollingChildHelper() {
        if (this.f2141b1 == null) {
            this.f2141b1 = new j0.f(this);
        }
        return this.f2141b1;
    }

    public static void k(@NonNull w wVar) {
        WeakReference<RecyclerView> weakReference = wVar.f2268b;
        if (weakReference != null) {
            RecyclerView recyclerView = weakReference.get();
            while (recyclerView != null) {
                if (recyclerView != wVar.f2267a) {
                    ViewParent parent = recyclerView.getParent();
                    recyclerView = parent instanceof View ? (View) parent : null;
                } else {
                    return;
                }
            }
            wVar.f2268b = null;
        }
    }

    public String A() {
        StringBuilder a10 = com.duokan.airkan.server.f.a(" ");
        a10.append(super.toString());
        a10.append(", adapter:");
        a10.append(this.f2154k);
        a10.append(", layout:");
        a10.append(this.f2155l);
        a10.append(", context:");
        a10.append(getContext());
        return a10.toString();
    }

    public final void B(t tVar) {
        if (getScrollState() == 2) {
            OverScroller overScroller = this.O0.f2261c;
            overScroller.getFinalX();
            overScroller.getCurrX();
            Objects.requireNonNull(tVar);
            overScroller.getFinalY();
            overScroller.getCurrY();
            return;
        }
        Objects.requireNonNull(tVar);
    }

    @Nullable
    public View C(@NonNull View view) {
        ViewParent parent = view.getParent();
        while (parent != null && parent != this && (parent instanceof View)) {
            view = (View) parent;
            parent = view.getParent();
        }
        if (parent == this) {
            return view;
        }
        return null;
    }

    public final boolean D(MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        int size = this.f2160o.size();
        for (int i10 = 0; i10 < size; i10++) {
            m mVar = this.f2160o.get(i10);
            if (mVar.a(this, motionEvent) && action != 3) {
                this.f2162p = mVar;
                return true;
            }
        }
        return false;
    }

    public final void E(int[] iArr) {
        int e10 = this.f2145e.e();
        if (e10 == 0) {
            iArr[0] = -1;
            iArr[1] = -1;
            return;
        }
        int i10 = Reader.READ_DONE;
        int i11 = EventResultPersister.GENERATE_NEW_ID;
        for (int i12 = 0; i12 < e10; i12++) {
            w K = K(this.f2145e.d(i12));
            if (!K.t()) {
                int e11 = K.e();
                if (e11 < i10) {
                    i10 = e11;
                }
                if (e11 > i11) {
                    i11 = e11;
                }
            }
        }
        iArr[0] = i10;
        iArr[1] = i11;
    }

    @Nullable
    public w G(int i10) {
        w wVar = null;
        if (this.f2165q0) {
            return null;
        }
        int h6 = this.f2145e.h();
        for (int i11 = 0; i11 < h6; i11++) {
            w K = K(this.f2145e.g(i11));
            if (K != null && !K.l() && H(K) == i10) {
                if (!this.f2145e.k(K.f2267a)) {
                    return K;
                }
                wVar = K;
            }
        }
        return wVar;
    }

    public int H(w wVar) {
        if (!wVar.g(524) && wVar.i()) {
            a aVar = this.f2144d;
            int i10 = wVar.f2269c;
            int size = aVar.f2333b.size();
            for (int i11 = 0; i11 < size; i11++) {
                a.b bVar = aVar.f2333b.get(i11);
                int i12 = bVar.f2337a;
                if (i12 != 1) {
                    if (i12 == 2) {
                        int i13 = bVar.f2338b;
                        if (i13 <= i10) {
                            int i14 = bVar.f2340d;
                            if (i13 + i14 <= i10) {
                                i10 -= i14;
                            }
                        } else {
                            continue;
                        }
                    } else if (i12 == 8) {
                        int i15 = bVar.f2338b;
                        if (i15 == i10) {
                            i10 = bVar.f2340d;
                        } else {
                            if (i15 < i10) {
                                i10--;
                            }
                            if (bVar.f2340d <= i10) {
                                i10++;
                            }
                        }
                    }
                } else if (bVar.f2338b <= i10) {
                    i10 += bVar.f2340d;
                }
            }
            return i10;
        }
        return -1;
    }

    public long I(w wVar) {
        if (this.f2154k.f2196b) {
            return wVar.f2271e;
        }
        return (long) wVar.f2269c;
    }

    public w J(@NonNull View view) {
        ViewParent parent = view.getParent();
        if (parent == null || parent == this) {
            return K(view);
        }
        throw new IllegalArgumentException("View " + view + " is not a direct child of " + this);
    }

    public Rect L(View view) {
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        if (!layoutParams.f2190c) {
            return layoutParams.f2189b;
        }
        if (this.R0.f2252g && (layoutParams.b() || layoutParams.f2188a.j())) {
            return layoutParams.f2189b;
        }
        Rect rect = layoutParams.f2189b;
        rect.set(0, 0, 0, 0);
        int size = this.f2158n.size();
        for (int i10 = 0; i10 < size; i10++) {
            this.h.set(0, 0, 0, 0);
            Rect rect2 = this.h;
            Objects.requireNonNull(this.f2158n.get(i10));
            ((LayoutParams) view.getLayoutParams()).a();
            rect2.set(0, 0, 0, 0);
            int i11 = rect.left;
            Rect rect3 = this.h;
            rect.left = i11 + rect3.left;
            rect.top += rect3.top;
            rect.right += rect3.right;
            rect.bottom += rect3.bottom;
        }
        layoutParams.f2190c = false;
        return rect;
    }

    public boolean M() {
        return !this.f2168s || this.f2165q0 || this.f2144d.g();
    }

    public void N() {
        this.f2178y0 = null;
        this.f2174w0 = null;
        this.f2176x0 = null;
        this.f2173v0 = null;
    }

    public boolean O() {
        return this.f2169s0 > 0;
    }

    public void P(int i10) {
        if (this.f2155l != null) {
            setScrollState(2);
            this.f2155l.y0(i10);
            awakenScrollBars();
        }
    }

    public void Q() {
        int h6 = this.f2145e.h();
        for (int i10 = 0; i10 < h6; i10++) {
            ((LayoutParams) this.f2145e.g(i10).getLayoutParams()).f2190c = true;
        }
        p pVar = this.f2140b;
        int size = pVar.f2226c.size();
        for (int i11 = 0; i11 < size; i11++) {
            LayoutParams layoutParams = (LayoutParams) pVar.f2226c.get(i11).f2267a.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.f2190c = true;
            }
        }
    }

    public void R(int i10, int i11, boolean z10) {
        int i12 = i10 + i11;
        int h6 = this.f2145e.h();
        for (int i13 = 0; i13 < h6; i13++) {
            w K = K(this.f2145e.g(i13));
            if (K != null && !K.t()) {
                int i14 = K.f2269c;
                if (i14 >= i12) {
                    K.p(-i11, z10);
                    this.R0.f2251f = true;
                } else if (i14 >= i10) {
                    K.b(8);
                    K.p(-i11, z10);
                    K.f2269c = i10 - 1;
                    this.R0.f2251f = true;
                }
            }
        }
        p pVar = this.f2140b;
        int size = pVar.f2226c.size();
        while (true) {
            size--;
            if (size >= 0) {
                w wVar = pVar.f2226c.get(size);
                if (wVar != null) {
                    int i15 = wVar.f2269c;
                    if (i15 >= i12) {
                        wVar.p(-i11, z10);
                    } else if (i15 >= i10) {
                        wVar.b(8);
                        pVar.f(size);
                    }
                }
            } else {
                requestLayout();
                return;
            }
        }
    }

    public void S() {
        this.f2169s0++;
    }

    public void T(boolean z10) {
        int i10;
        boolean z11 = true;
        int i11 = this.f2169s0 - 1;
        this.f2169s0 = i11;
        if (i11 < 1) {
            this.f2169s0 = 0;
            if (z10) {
                int i12 = this.f2159n0;
                this.f2159n0 = 0;
                if (i12 != 0) {
                    AccessibilityManager accessibilityManager = this.f2161o0;
                    if (accessibilityManager == null || !accessibilityManager.isEnabled()) {
                        z11 = false;
                    }
                    if (z11) {
                        AccessibilityEvent obtain = AccessibilityEvent.obtain();
                        obtain.setEventType(2048);
                        obtain.setContentChangeTypes(i12);
                        sendAccessibilityEventUnchecked(obtain);
                    }
                }
                for (int size = this.f2148f1.size() - 1; size >= 0; size--) {
                    w wVar = this.f2148f1.get(size);
                    if (wVar.f2267a.getParent() == this && !wVar.t() && (i10 = wVar.f2282q) != -1) {
                        View view = wVar.f2267a;
                        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
                        view.setImportantForAccessibility(i10);
                        wVar.f2282q = -1;
                    }
                }
                this.f2148f1.clear();
            }
        }
    }

    public final void U(MotionEvent motionEvent) {
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.B0) {
            int i10 = actionIndex == 0 ? 1 : 0;
            this.B0 = motionEvent.getPointerId(i10);
            int x8 = (int) (motionEvent.getX(i10) + 0.5f);
            this.F0 = x8;
            this.D0 = x8;
            int y10 = (int) (motionEvent.getY(i10) + 0.5f);
            this.G0 = y10;
            this.E0 = y10;
        }
    }

    public void V() {
        if (!this.X0 && this.f2164q) {
            Runnable runnable = this.f2150g1;
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            postOnAnimation(runnable);
            this.X0 = true;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:45:0x007d, code lost:
        if ((r6.f2179z0 != null && r6.f2155l.K0()) != false) goto L_0x0081;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void W() {
        /*
        // Method dump skipped, instructions count: 132
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.W():void");
    }

    public void X(boolean z10) {
        this.f2167r0 = z10 | this.f2167r0;
        this.f2165q0 = true;
        int h6 = this.f2145e.h();
        for (int i10 = 0; i10 < h6; i10++) {
            w K = K(this.f2145e.g(i10));
            if (K != null && !K.t()) {
                K.b(6);
            }
        }
        Q();
        p pVar = this.f2140b;
        int size = pVar.f2226c.size();
        for (int i11 = 0; i11 < size; i11++) {
            w wVar = pVar.f2226c.get(i11);
            if (wVar != null) {
                wVar.b(6);
                wVar.a(null);
            }
        }
        d dVar = RecyclerView.this.f2154k;
        if (dVar == null || !dVar.f2196b) {
            pVar.e();
        }
    }

    public void Y(w wVar, ItemAnimator.c cVar) {
        wVar.r(0, 8192);
        if (this.R0.h && wVar.o() && !wVar.l() && !wVar.t()) {
            this.f2147f.f2356b.f(I(wVar), wVar);
        }
        this.f2147f.c(wVar, cVar);
    }

    public void Z() {
        ItemAnimator itemAnimator = this.f2179z0;
        if (itemAnimator != null) {
            itemAnimator.f();
        }
        j jVar = this.f2155l;
        if (jVar != null) {
            jVar.q0(this.f2140b);
            this.f2155l.r0(this.f2140b);
        }
        this.f2140b.b();
    }

    public final void a0(@NonNull View view, @Nullable View view2) {
        View view3 = view2 != null ? view2 : view;
        this.h.set(0, 0, view3.getWidth(), view3.getHeight());
        ViewGroup.LayoutParams layoutParams = view3.getLayoutParams();
        if (layoutParams instanceof LayoutParams) {
            LayoutParams layoutParams2 = (LayoutParams) layoutParams;
            if (!layoutParams2.f2190c) {
                Rect rect = layoutParams2.f2189b;
                Rect rect2 = this.h;
                rect2.left -= rect.left;
                rect2.right += rect.right;
                rect2.top -= rect.top;
                rect2.bottom += rect.bottom;
            }
        }
        if (view2 != null) {
            offsetDescendantRectToMyCoords(view2, this.h);
            offsetRectIntoDescendantCoords(view, this.h);
        }
        this.f2155l.v0(this, view, this.h, !this.f2168s, view2 == null);
    }

    @Override // android.view.View, android.view.ViewGroup
    public void addFocusables(ArrayList<View> arrayList, int i10, int i11) {
        j jVar = this.f2155l;
        if (jVar != null) {
            Objects.requireNonNull(jVar);
        }
        super.addFocusables(arrayList, i10, i11);
    }

    public final void b0() {
        VelocityTracker velocityTracker = this.C0;
        if (velocityTracker != null) {
            velocityTracker.clear();
        }
        boolean z10 = false;
        l0(0);
        EdgeEffect edgeEffect = this.f2173v0;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            z10 = this.f2173v0.isFinished();
        }
        EdgeEffect edgeEffect2 = this.f2174w0;
        if (edgeEffect2 != null) {
            edgeEffect2.onRelease();
            z10 |= this.f2174w0.isFinished();
        }
        EdgeEffect edgeEffect3 = this.f2176x0;
        if (edgeEffect3 != null) {
            edgeEffect3.onRelease();
            z10 |= this.f2176x0.isFinished();
        }
        EdgeEffect edgeEffect4 = this.f2178y0;
        if (edgeEffect4 != null) {
            edgeEffect4.onRelease();
            z10 |= this.f2178y0.isFinished();
        }
        if (z10) {
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            postInvalidateOnAnimation();
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:31:0x00e0  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x00f6  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean c0(int r18, int r19, android.view.MotionEvent r20) {
        /*
        // Method dump skipped, instructions count: 318
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.c0(int, int, android.view.MotionEvent):boolean");
    }

    public boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        return (layoutParams instanceof LayoutParams) && this.f2155l.g((LayoutParams) layoutParams);
    }

    public int computeHorizontalScrollExtent() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.e()) {
            return this.f2155l.k(this.R0);
        }
        return 0;
    }

    public int computeHorizontalScrollOffset() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.e()) {
            return this.f2155l.l(this.R0);
        }
        return 0;
    }

    public int computeHorizontalScrollRange() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.e()) {
            return this.f2155l.m(this.R0);
        }
        return 0;
    }

    public int computeVerticalScrollExtent() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.f()) {
            return this.f2155l.n(this.R0);
        }
        return 0;
    }

    public int computeVerticalScrollOffset() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.f()) {
            return this.f2155l.o(this.R0);
        }
        return 0;
    }

    public int computeVerticalScrollRange() {
        j jVar = this.f2155l;
        if (jVar != null && jVar.f()) {
            return this.f2155l.p(this.R0);
        }
        return 0;
    }

    public void d0(int i10, int i11, @Nullable int[] iArr) {
        w wVar;
        i0();
        S();
        int i12 = f0.b.f6259a;
        Trace.beginSection("RV Scroll");
        B(this.R0);
        int x02 = i10 != 0 ? this.f2155l.x0(i10, this.f2140b, this.R0) : 0;
        int z02 = i11 != 0 ? this.f2155l.z0(i11, this.f2140b, this.R0) : 0;
        Trace.endSection();
        int e10 = this.f2145e.e();
        for (int i13 = 0; i13 < e10; i13++) {
            View d10 = this.f2145e.d(i13);
            w J = J(d10);
            if (!(J == null || (wVar = J.f2274i) == null)) {
                View view = wVar.f2267a;
                int left = d10.getLeft();
                int top = d10.getTop();
                if (left != view.getLeft() || top != view.getTop()) {
                    view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                }
            }
        }
        T(true);
        k0(false);
        if (iArr != null) {
            iArr[0] = x02;
            iArr[1] = z02;
        }
    }

    public boolean dispatchNestedFling(float f10, float f11, boolean z10) {
        return getScrollingChildHelper().a(f10, f11, z10);
    }

    public boolean dispatchNestedPreFling(float f10, float f11) {
        return getScrollingChildHelper().b(f10, f11);
    }

    public boolean dispatchNestedPreScroll(int i10, int i11, int[] iArr, int[] iArr2) {
        return getScrollingChildHelper().c(i10, i11, iArr, iArr2);
    }

    public boolean dispatchNestedScroll(int i10, int i11, int i12, int i13, int[] iArr) {
        return getScrollingChildHelper().f(i10, i11, i12, i13, iArr);
    }

    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        onPopulateAccessibilityEvent(accessibilityEvent);
        return true;
    }

    @Override // android.view.View, android.view.ViewGroup
    public void dispatchRestoreInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchThawSelfOnly(sparseArray);
    }

    @Override // android.view.View, android.view.ViewGroup
    public void dispatchSaveInstanceState(SparseArray<Parcelable> sparseArray) {
        dispatchFreezeSelfOnly(sparseArray);
    }

    public void draw(Canvas canvas) {
        boolean z10;
        super.draw(canvas);
        int size = this.f2158n.size();
        boolean z11 = false;
        for (int i10 = 0; i10 < size; i10++) {
            this.f2158n.get(i10).e(canvas, this, this.R0);
        }
        EdgeEffect edgeEffect = this.f2173v0;
        boolean z12 = true;
        if (edgeEffect == null || edgeEffect.isFinished()) {
            z10 = false;
        } else {
            int save = canvas.save();
            int paddingBottom = this.f2149g ? getPaddingBottom() : 0;
            canvas.rotate(270.0f);
            canvas.translate((float) ((-getHeight()) + paddingBottom), Constant.VOLUME_FLOAT_MIN);
            EdgeEffect edgeEffect2 = this.f2173v0;
            z10 = edgeEffect2 != null && edgeEffect2.draw(canvas);
            canvas.restoreToCount(save);
        }
        EdgeEffect edgeEffect3 = this.f2174w0;
        if (edgeEffect3 != null && !edgeEffect3.isFinished()) {
            int save2 = canvas.save();
            if (this.f2149g) {
                canvas.translate((float) getPaddingLeft(), (float) getPaddingTop());
            }
            EdgeEffect edgeEffect4 = this.f2174w0;
            z10 |= edgeEffect4 != null && edgeEffect4.draw(canvas);
            canvas.restoreToCount(save2);
        }
        EdgeEffect edgeEffect5 = this.f2176x0;
        if (edgeEffect5 != null && !edgeEffect5.isFinished()) {
            int save3 = canvas.save();
            int width = getWidth();
            int paddingTop = this.f2149g ? getPaddingTop() : 0;
            canvas.rotate(90.0f);
            canvas.translate((float) (-paddingTop), (float) (-width));
            EdgeEffect edgeEffect6 = this.f2176x0;
            z10 |= edgeEffect6 != null && edgeEffect6.draw(canvas);
            canvas.restoreToCount(save3);
        }
        EdgeEffect edgeEffect7 = this.f2178y0;
        if (edgeEffect7 != null && !edgeEffect7.isFinished()) {
            int save4 = canvas.save();
            canvas.rotate(180.0f);
            if (this.f2149g) {
                canvas.translate((float) (getPaddingRight() + (-getWidth())), (float) (getPaddingBottom() + (-getHeight())));
            } else {
                canvas.translate((float) (-getWidth()), (float) (-getHeight()));
            }
            EdgeEffect edgeEffect8 = this.f2178y0;
            if (edgeEffect8 != null && edgeEffect8.draw(canvas)) {
                z11 = true;
            }
            z10 |= z11;
            canvas.restoreToCount(save4);
        }
        if (z10 || this.f2179z0 == null || this.f2158n.size() <= 0 || !this.f2179z0.g()) {
            z12 = z10;
        }
        if (z12) {
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            postInvalidateOnAnimation();
        }
    }

    public boolean drawChild(Canvas canvas, View view, long j10) {
        return super.drawChild(canvas, view, j10);
    }

    public void e0(int i10) {
        if (!this.f2177y) {
            m0();
            j jVar = this.f2155l;
            if (jVar == null) {
                Log.e("RecyclerView", "Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.");
                return;
            }
            jVar.y0(i10);
            awakenScrollBars();
        }
    }

    public final void f(w wVar) {
        View view = wVar.f2267a;
        boolean z10 = view.getParent() == this;
        this.f2140b.k(J(view));
        if (wVar.n()) {
            this.f2145e.b(view, -1, view.getLayoutParams(), true);
        } else if (!z10) {
            this.f2145e.a(view, -1, true);
        } else {
            c cVar = this.f2145e;
            int indexOfChild = ((x) cVar.f2342a).f2479a.indexOfChild(view);
            if (indexOfChild >= 0) {
                cVar.f2343b.h(indexOfChild);
                cVar.i(view);
                return;
            }
            throw new IllegalArgumentException("view is not a child, cannot hide " + view);
        }
    }

    @VisibleForTesting
    public boolean f0(w wVar, int i10) {
        if (O()) {
            wVar.f2282q = i10;
            this.f2148f1.add(wVar);
            return false;
        }
        View view = wVar.f2267a;
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        view.setImportantForAccessibility(i10);
        return true;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:110:0x0164, code lost:
        if (r3 > 0) goto L_0x0198;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:113:0x017e, code lost:
        if (r6 > 0) goto L_0x0198;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:114:0x0181, code lost:
        if (r3 < 0) goto L_0x0198;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:115:0x0184, code lost:
        if (r6 < 0) goto L_0x0198;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:119:0x018c, code lost:
        if ((r6 * r2) < 0) goto L_0x0197;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:123:0x0194, code lost:
        if ((r6 * r2) > 0) goto L_0x0197;
     */
    /* JADX WARNING: Removed duplicated region for block: B:126:0x019b  */
    /* JADX WARNING: Removed duplicated region for block: B:128:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0055  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x0057  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x005a  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x005c  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x0060  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x0063  */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x006a  */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x006c  */
    /* JADX WARNING: Removed duplicated region for block: B:42:0x006f  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public android.view.View focusSearch(android.view.View r14, int r15) {
        /*
        // Method dump skipped, instructions count: 416
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.focusSearch(android.view.View, int):android.view.View");
    }

    public void g(@NonNull i iVar) {
        j jVar = this.f2155l;
        if (jVar != null) {
            jVar.d("Cannot add item decoration during a scroll  or layout");
        }
        if (this.f2158n.isEmpty()) {
            setWillNotDraw(false);
        }
        this.f2158n.add(iVar);
        Q();
        requestLayout();
    }

    public void g0(@Px int i10, @Px int i11, @Nullable Interpolator interpolator, int i12, boolean z10) {
        j jVar = this.f2155l;
        if (jVar == null) {
            Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else if (!this.f2177y) {
            int i13 = 0;
            if (!jVar.e()) {
                i10 = 0;
            }
            if (!this.f2155l.f()) {
                i11 = 0;
            }
            if (i10 != 0 || i11 != 0) {
                if (i12 == Integer.MIN_VALUE || i12 > 0) {
                    if (z10) {
                        if (i10 != 0) {
                            i13 = 1;
                        }
                        if (i11 != 0) {
                            i13 |= 2;
                        }
                        j0(i13, 1);
                    }
                    this.O0.b(i10, i11, i12, interpolator);
                    return;
                }
                scrollBy(i10, i11);
            }
        }
    }

    public ViewGroup.LayoutParams generateDefaultLayoutParams() {
        j jVar = this.f2155l;
        if (jVar != null) {
            return jVar.t();
        }
        throw new IllegalStateException(b.a(this, com.duokan.airkan.server.f.a("RecyclerView has no LayoutManager")));
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        j jVar = this.f2155l;
        if (jVar != null) {
            return jVar.u(getContext(), attributeSet);
        }
        throw new IllegalStateException(b.a(this, com.duokan.airkan.server.f.a("RecyclerView has no LayoutManager")));
    }

    public CharSequence getAccessibilityClassName() {
        return "androidx.recyclerview.widget.RecyclerView";
    }

    @Nullable
    public d getAdapter() {
        return this.f2154k;
    }

    public int getBaseline() {
        j jVar = this.f2155l;
        if (jVar == null) {
            return super.getBaseline();
        }
        Objects.requireNonNull(jVar);
        return -1;
    }

    public int getChildDrawingOrder(int i10, int i11) {
        g gVar = this.Z0;
        if (gVar == null) {
            return super.getChildDrawingOrder(i10, i11);
        }
        return gVar.a(i10, i11);
    }

    public boolean getClipToPadding() {
        return this.f2149g;
    }

    @Nullable
    public z getCompatAccessibilityDelegate() {
        return this.Y0;
    }

    @NonNull
    public EdgeEffectFactory getEdgeEffectFactory() {
        return this.f2172u0;
    }

    @Nullable
    public ItemAnimator getItemAnimator() {
        return this.f2179z0;
    }

    public int getItemDecorationCount() {
        return this.f2158n.size();
    }

    @Nullable
    public j getLayoutManager() {
        return this.f2155l;
    }

    public int getMaxFlingVelocity() {
        return this.K0;
    }

    public int getMinFlingVelocity() {
        return this.J0;
    }

    public long getNanoTime() {
        return System.nanoTime();
    }

    @Nullable
    public l getOnFlingListener() {
        return this.I0;
    }

    public boolean getPreserveFocusAfterLayout() {
        return this.N0;
    }

    @NonNull
    public o getRecycledViewPool() {
        return this.f2140b.d();
    }

    public int getScrollState() {
        return this.A0;
    }

    public void h(@NonNull n nVar) {
        if (this.T0 == null) {
            this.T0 = new ArrayList();
        }
        this.T0.add(nVar);
    }

    public void h0(int i10) {
        if (!this.f2177y) {
            j jVar = this.f2155l;
            if (jVar == null) {
                Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            } else {
                jVar.I0(this, this.R0, i10);
            }
        }
    }

    public boolean hasNestedScrollingParent() {
        return getScrollingChildHelper().i();
    }

    public void i(String str) {
        if (O()) {
            if (str == null) {
                throw new IllegalStateException(b.a(this, com.duokan.airkan.server.f.a("Cannot call this method while RecyclerView is computing a layout or scrolling")));
            }
            throw new IllegalStateException(str);
        } else if (this.f2171t0 > 0) {
            Log.w("RecyclerView", "Cannot call this method in a scroll callback. Scroll callbacks mightbe run during a measure & layout pass where you cannot change theRecyclerView data. Any method call that might change the structureof the RecyclerView or the adapter contents should be postponed tothe next frame.", new IllegalStateException(b.a(this, com.duokan.airkan.server.f.a(""))));
        }
    }

    public void i0() {
        int i10 = this.f2170t + 1;
        this.f2170t = i10;
        if (i10 == 1 && !this.f2177y) {
            this.f2175x = false;
        }
    }

    public boolean isAttachedToWindow() {
        return this.f2164q;
    }

    public final boolean isLayoutSuppressed() {
        return this.f2177y;
    }

    public boolean isNestedScrollingEnabled() {
        return getScrollingChildHelper().k();
    }

    public final void j() {
        b0();
        setScrollState(0);
    }

    public boolean j0(int i10, int i11) {
        return getScrollingChildHelper().n(i10, i11);
    }

    public void k0(boolean z10) {
        if (this.f2170t < 1) {
            this.f2170t = 1;
        }
        if (!z10 && !this.f2177y) {
            this.f2175x = false;
        }
        if (this.f2170t == 1) {
            if (z10 && this.f2175x && !this.f2177y && this.f2155l != null && this.f2154k != null) {
                q();
            }
            if (!this.f2177y) {
                this.f2175x = false;
            }
        }
        this.f2170t--;
    }

    public void l() {
        int h6 = this.f2145e.h();
        for (int i10 = 0; i10 < h6; i10++) {
            w K = K(this.f2145e.g(i10));
            if (!K.t()) {
                K.c();
            }
        }
        p pVar = this.f2140b;
        int size = pVar.f2226c.size();
        for (int i11 = 0; i11 < size; i11++) {
            pVar.f2226c.get(i11).c();
        }
        int size2 = pVar.f2224a.size();
        for (int i12 = 0; i12 < size2; i12++) {
            pVar.f2224a.get(i12).c();
        }
        ArrayList<w> arrayList = pVar.f2225b;
        if (arrayList != null) {
            int size3 = arrayList.size();
            for (int i13 = 0; i13 < size3; i13++) {
                pVar.f2225b.get(i13).c();
            }
        }
    }

    public void l0(int i10) {
        getScrollingChildHelper().p(i10);
    }

    public void m(int i10, int i11) {
        boolean z10;
        EdgeEffect edgeEffect = this.f2173v0;
        if (edgeEffect == null || edgeEffect.isFinished() || i10 <= 0) {
            z10 = false;
        } else {
            this.f2173v0.onRelease();
            z10 = this.f2173v0.isFinished();
        }
        EdgeEffect edgeEffect2 = this.f2176x0;
        if (edgeEffect2 != null && !edgeEffect2.isFinished() && i10 < 0) {
            this.f2176x0.onRelease();
            z10 |= this.f2176x0.isFinished();
        }
        EdgeEffect edgeEffect3 = this.f2174w0;
        if (edgeEffect3 != null && !edgeEffect3.isFinished() && i11 > 0) {
            this.f2174w0.onRelease();
            z10 |= this.f2174w0.isFinished();
        }
        EdgeEffect edgeEffect4 = this.f2178y0;
        if (edgeEffect4 != null && !edgeEffect4.isFinished() && i11 < 0) {
            this.f2178y0.onRelease();
            z10 |= this.f2178y0.isFinished();
        }
        if (z10) {
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            postInvalidateOnAnimation();
        }
    }

    public void m0() {
        s sVar;
        setScrollState(0);
        this.O0.c();
        j jVar = this.f2155l;
        if (jVar != null && (sVar = jVar.f2202e) != null) {
            sVar.d();
        }
    }

    public void n() {
        if (!this.f2168s || this.f2165q0) {
            int i10 = f0.b.f6259a;
            Trace.beginSection("RV FullInvalidate");
            q();
            Trace.endSection();
        } else if (this.f2144d.g()) {
            Objects.requireNonNull(this.f2144d);
            if (this.f2144d.g()) {
                int i11 = f0.b.f6259a;
                Trace.beginSection("RV FullInvalidate");
                q();
                Trace.endSection();
            }
        }
    }

    public void o(int i10, int i11) {
        int paddingRight = getPaddingRight() + getPaddingLeft();
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        setMeasuredDimension(j.h(i10, paddingRight, getMinimumWidth()), j.h(i11, getPaddingBottom() + getPaddingTop(), getMinimumHeight()));
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.f2169s0 = 0;
        this.f2164q = true;
        this.f2168s = this.f2168s && !isLayoutRequested();
        j jVar = this.f2155l;
        if (jVar != null) {
            jVar.f2204g = true;
        }
        this.X0 = false;
        ThreadLocal<n> threadLocal = n.f2439e;
        n nVar = threadLocal.get();
        this.P0 = nVar;
        if (nVar == null) {
            this.P0 = new n();
            WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
            Display display = getDisplay();
            float f10 = 60.0f;
            if (!isInEditMode() && display != null) {
                float refreshRate = display.getRefreshRate();
                if (refreshRate >= 30.0f) {
                    f10 = refreshRate;
                }
            }
            n nVar2 = this.P0;
            nVar2.f2443c = (long) (1.0E9f / f10);
            threadLocal.set(nVar2);
        }
        this.P0.f2441a.add(this);
    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ItemAnimator itemAnimator = this.f2179z0;
        if (itemAnimator != null) {
            itemAnimator.f();
        }
        m0();
        this.f2164q = false;
        j jVar = this.f2155l;
        if (jVar != null) {
            p pVar = this.f2140b;
            jVar.f2204g = false;
            jVar.Z(this, pVar);
        }
        this.f2148f1.clear();
        removeCallbacks(this.f2150g1);
        Objects.requireNonNull(this.f2147f);
        do {
        } while (d0.a.f2357d.a() != null);
        n nVar = this.P0;
        if (nVar != null) {
            nVar.f2441a.remove(this);
            this.P0 = null;
        }
    }

    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int size = this.f2158n.size();
        for (int i10 = 0; i10 < size; i10++) {
            this.f2158n.get(i10).d(canvas, this, this.R0);
        }
    }

    public boolean onGenericMotionEvent(MotionEvent motionEvent) {
        float f10;
        float f11;
        if (this.f2155l != null && !this.f2177y && motionEvent.getAction() == 8) {
            if ((motionEvent.getSource() & 2) != 0) {
                f11 = this.f2155l.f() ? -motionEvent.getAxisValue(9) : 0.0f;
                if (this.f2155l.e()) {
                    f10 = motionEvent.getAxisValue(10);
                    if (!(f11 == Constant.VOLUME_FLOAT_MIN && f10 == Constant.VOLUME_FLOAT_MIN)) {
                        c0((int) (f10 * this.L0), (int) (f11 * this.M0), motionEvent);
                    }
                }
            } else {
                if ((motionEvent.getSource() & 4194304) != 0) {
                    float axisValue = motionEvent.getAxisValue(26);
                    if (this.f2155l.f()) {
                        f11 = -axisValue;
                    } else if (this.f2155l.e()) {
                        f10 = axisValue;
                        f11 = 0.0f;
                        c0((int) (f10 * this.L0), (int) (f11 * this.M0), motionEvent);
                    }
                }
                f11 = 0.0f;
                f10 = 0.0f;
                c0((int) (f10 * this.L0), (int) (f11 * this.M0), motionEvent);
            }
            f10 = 0.0f;
            c0((int) (f10 * this.L0), (int) (f11 * this.M0), motionEvent);
        }
        return false;
    }

    /* JADX DEBUG: Failed to insert an additional move for type inference into block B:38:0x00bd */
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        Object[] objArr;
        if (this.f2177y) {
            return false;
        }
        this.f2162p = null;
        if (D(motionEvent)) {
            j();
            return true;
        }
        j jVar = this.f2155l;
        if (jVar == null) {
            return false;
        }
        boolean e10 = jVar.e();
        boolean f10 = this.f2155l.f();
        if (this.C0 == null) {
            this.C0 = VelocityTracker.obtain();
        }
        this.C0.addMovement(motionEvent);
        int actionMasked = motionEvent.getActionMasked();
        int actionIndex = motionEvent.getActionIndex();
        if (actionMasked == 0) {
            if (this.f2157m0) {
                this.f2157m0 = false;
            }
            this.B0 = motionEvent.getPointerId(0);
            int x8 = (int) (motionEvent.getX() + 0.5f);
            this.F0 = x8;
            this.D0 = x8;
            int y10 = (int) (motionEvent.getY() + 0.5f);
            this.G0 = y10;
            this.E0 = y10;
            if (this.A0 == 2) {
                getParent().requestDisallowInterceptTouchEvent(true);
                setScrollState(1);
                l0(1);
            }
            int[] iArr = this.d1;
            iArr[1] = 0;
            iArr[0] = 0;
            if (f10) {
                boolean z10 = e10 ? 1 : 0;
                char c10 = e10 ? 1 : 0;
                e10 = z10 | true;
            }
            int i10 = e10 ? 1 : 0;
            int i11 = e10 ? 1 : 0;
            int i12 = e10 ? 1 : 0;
            j0(i10, 0);
        } else if (actionMasked == 1) {
            this.C0.clear();
            l0(0);
        } else if (actionMasked == 2) {
            int findPointerIndex = motionEvent.findPointerIndex(this.B0);
            if (findPointerIndex < 0) {
                StringBuilder a10 = com.duokan.airkan.server.f.a("Error processing scroll; pointer index for id ");
                a10.append(this.B0);
                a10.append(" not found. Did any MotionEvents get skipped?");
                Log.e("RecyclerView", a10.toString());
                return false;
            }
            int x10 = (int) (motionEvent.getX(findPointerIndex) + 0.5f);
            int y11 = (int) (motionEvent.getY(findPointerIndex) + 0.5f);
            if (this.A0 != 1) {
                int i13 = x10 - this.D0;
                int i14 = y11 - this.E0;
                if (!e10 || Math.abs(i13) <= this.H0) {
                    objArr = null;
                } else {
                    this.F0 = x10;
                    objArr = 1;
                }
                if (f10 && Math.abs(i14) > this.H0) {
                    this.G0 = y11;
                    objArr = 1;
                }
                if (objArr != null) {
                    setScrollState(1);
                }
            }
        } else if (actionMasked == 3) {
            j();
        } else if (actionMasked == 5) {
            this.B0 = motionEvent.getPointerId(actionIndex);
            int x11 = (int) (motionEvent.getX(actionIndex) + 0.5f);
            this.F0 = x11;
            this.D0 = x11;
            int y12 = (int) (motionEvent.getY(actionIndex) + 0.5f);
            this.G0 = y12;
            this.E0 = y12;
        } else if (actionMasked == 6) {
            U(motionEvent);
        }
        if (this.A0 == 1) {
            return true;
        }
        return false;
    }

    public void onLayout(boolean z10, int i10, int i11, int i12, int i13) {
        int i14 = f0.b.f6259a;
        Trace.beginSection("RV OnLayout");
        q();
        Trace.endSection();
        this.f2168s = true;
    }

    public void onMeasure(int i10, int i11) {
        j jVar = this.f2155l;
        if (jVar == null) {
            o(i10, i11);
            return;
        }
        boolean z10 = false;
        if (jVar.U()) {
            int mode = View.MeasureSpec.getMode(i10);
            int mode2 = View.MeasureSpec.getMode(i11);
            this.f2155l.f2199b.o(i10, i11);
            if (mode == 1073741824 && mode2 == 1073741824) {
                z10 = true;
            }
            if (!z10 && this.f2154k != null) {
                if (this.R0.f2249d == 1) {
                    r();
                }
                this.f2155l.B0(i10, i11);
                this.R0.f2253i = true;
                s();
                this.f2155l.D0(i10, i11);
                if (this.f2155l.G0()) {
                    this.f2155l.B0(View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 1073741824));
                    this.R0.f2253i = true;
                    s();
                    this.f2155l.D0(i10, i11);
                }
            }
        } else if (this.f2166r) {
            this.f2155l.f2199b.o(i10, i11);
        } else {
            t tVar = this.R0;
            if (tVar.f2255k) {
                setMeasuredDimension(getMeasuredWidth(), getMeasuredHeight());
                return;
            }
            d dVar = this.f2154k;
            if (dVar != null) {
                tVar.f2250e = dVar.a();
            } else {
                tVar.f2250e = 0;
            }
            i0();
            this.f2155l.f2199b.o(i10, i11);
            k0(false);
            this.R0.f2252g = false;
        }
    }

    public boolean onRequestFocusInDescendants(int i10, Rect rect) {
        if (O()) {
            return false;
        }
        return super.onRequestFocusInDescendants(i10, rect);
    }

    public void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof SavedState)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        SavedState savedState = (SavedState) parcelable;
        this.f2142c = savedState;
        super.onRestoreInstanceState(savedState.f1662a);
        j jVar = this.f2155l;
        if (jVar != null && (parcelable2 = this.f2142c.f2192c) != null) {
            jVar.m0(parcelable2);
        }
    }

    public Parcelable onSaveInstanceState() {
        SavedState savedState = new SavedState(super.onSaveInstanceState());
        SavedState savedState2 = this.f2142c;
        if (savedState2 != null) {
            savedState.f2192c = savedState2.f2192c;
        } else {
            j jVar = this.f2155l;
            if (jVar != null) {
                savedState.f2192c = jVar.n0();
            } else {
                savedState.f2192c = null;
            }
        }
        return savedState;
    }

    public void onSizeChanged(int i10, int i11, int i12, int i13) {
        super.onSizeChanged(i10, i11, i12, i13);
        if (i10 != i12 || i11 != i13) {
            N();
        }
    }

    /* JADX DEBUG: Failed to insert an additional move for type inference into block B:53:0x00f8 */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v9 */
    /* JADX WARN: Type inference failed for: r1v10 */
    /* JADX WARN: Type inference failed for: r1v12 */
    /* JADX WARN: Type inference failed for: r7v7 */
    /* JADX WARN: Type inference failed for: r7v8 */
    /* JADX WARN: Type inference failed for: r7v9 */
    /* JADX WARN: Type inference failed for: r7v10 */
    /* JADX WARN: Type inference failed for: r7v11 */
    /* JADX WARN: Type inference failed for: r7v13 */
    /* JADX WARN: Type inference failed for: r11v18 */
    /* JADX WARN: Type inference failed for: r11v19 */
    /* JADX WARN: Type inference failed for: r11v20 */
    /* JADX WARN: Type inference failed for: r11v21 */
    /* JADX WARN: Type inference failed for: r11v22 */
    /* JADX WARN: Type inference failed for: r2v40 */
    /* JADX WARN: Type inference failed for: r4v32 */
    /* JADX WARNING: Code restructure failed: missing block: B:142:0x0250, code lost:
        if (r7 == false) goto L_0x0253;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:152:0x02a9, code lost:
        if (r1 != false) goto L_0x02ae;
     */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00fa  */
    /* JADX WARNING: Removed duplicated region for block: B:60:0x0110  */
    /* JADX WARNING: Unknown variable types count: 6 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouchEvent(android.view.MotionEvent r23) {
        /*
        // Method dump skipped, instructions count: 736
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    public void p(View view) {
        w K = K(view);
        d dVar = this.f2154k;
        if (!(dVar == null || K == null)) {
            Objects.requireNonNull(dVar);
        }
        List<k> list = this.f2163p0;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.f2163p0.get(size).b(view);
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:133:0x032d, code lost:
        if (r15.f2145e.k(getFocusedChild()) == false) goto L_0x03f0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:172:0x03b6, code lost:
        r5 = r0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public void q() {
        /*
        // Method dump skipped, instructions count: 1018
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.q():void");
    }

    /* JADX WARNING: Removed duplicated region for block: B:105:0x0085 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x00a1  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void r() {
        /*
        // Method dump skipped, instructions count: 491
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.recyclerview.widget.RecyclerView.r():void");
    }

    public void removeDetachedView(View view, boolean z10) {
        w K = K(view);
        if (K != null) {
            if (K.n()) {
                K.f2275j &= -257;
            } else if (!K.t()) {
                StringBuilder sb = new StringBuilder();
                sb.append("Called removeDetachedView with a view which is not flagged as tmp detached.");
                sb.append(K);
                throw new IllegalArgumentException(b.a(this, sb));
            }
        }
        view.clearAnimation();
        p(view);
        super.removeDetachedView(view, z10);
    }

    public void requestChildFocus(View view, View view2) {
        s sVar = this.f2155l.f2202e;
        boolean z10 = true;
        if (!(sVar != null && sVar.f2236e) && !O()) {
            z10 = false;
        }
        if (!z10 && view2 != null) {
            a0(view, view2);
        }
        super.requestChildFocus(view, view2);
    }

    public boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z10) {
        return this.f2155l.v0(this, view, rect, z10, false);
    }

    public void requestDisallowInterceptTouchEvent(boolean z10) {
        int size = this.f2160o.size();
        for (int i10 = 0; i10 < size; i10++) {
            this.f2160o.get(i10).c(z10);
        }
        super.requestDisallowInterceptTouchEvent(z10);
    }

    public void requestLayout() {
        if (this.f2170t != 0 || this.f2177y) {
            this.f2175x = true;
        } else {
            super.requestLayout();
        }
    }

    public final void s() {
        i0();
        S();
        this.R0.a(6);
        this.f2144d.c();
        this.R0.f2250e = this.f2154k.a();
        t tVar = this.R0;
        tVar.f2248c = 0;
        tVar.f2252g = false;
        this.f2155l.k0(this.f2140b, tVar);
        t tVar2 = this.R0;
        tVar2.f2251f = false;
        this.f2142c = null;
        tVar2.f2254j = tVar2.f2254j && this.f2179z0 != null;
        tVar2.f2249d = 4;
        T(true);
        k0(false);
    }

    public void scrollBy(int i10, int i11) {
        j jVar = this.f2155l;
        if (jVar == null) {
            Log.e("RecyclerView", "Cannot scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else if (!this.f2177y) {
            boolean e10 = jVar.e();
            boolean f10 = this.f2155l.f();
            if (e10 || f10) {
                if (!e10) {
                    i10 = 0;
                }
                if (!f10) {
                    i11 = 0;
                }
                c0(i10, i11, null);
            }
        }
    }

    public void scrollTo(int i10, int i11) {
        Log.w("RecyclerView", "RecyclerView does not support scrolling to an absolute position. Use scrollToPosition instead");
    }

    public void sendAccessibilityEventUnchecked(AccessibilityEvent accessibilityEvent) {
        int i10 = 0;
        if (O()) {
            int contentChangeTypes = accessibilityEvent != null ? accessibilityEvent.getContentChangeTypes() : 0;
            if (contentChangeTypes != 0) {
                i10 = contentChangeTypes;
            }
            this.f2159n0 |= i10;
            i10 = 1;
        }
        if (i10 == 0) {
            super.sendAccessibilityEventUnchecked(accessibilityEvent);
        }
    }

    public void setAccessibilityDelegateCompat(@Nullable z zVar) {
        this.Y0 = zVar;
        ViewCompat.k(this, zVar);
    }

    public void setAdapter(@Nullable d dVar) {
        setLayoutFrozen(false);
        d dVar2 = this.f2154k;
        if (dVar2 != null) {
            dVar2.f2195a.unregisterObserver(this.f2138a);
            Objects.requireNonNull(this.f2154k);
        }
        Z();
        a aVar = this.f2144d;
        aVar.l(aVar.f2333b);
        aVar.l(aVar.f2334c);
        d dVar3 = this.f2154k;
        this.f2154k = dVar;
        if (dVar != null) {
            dVar.f2195a.registerObserver(this.f2138a);
        }
        p pVar = this.f2140b;
        d dVar4 = this.f2154k;
        pVar.b();
        o d10 = pVar.d();
        Objects.requireNonNull(d10);
        if (dVar3 != null) {
            d10.f2219b--;
        }
        if (d10.f2219b == 0) {
            for (int i10 = 0; i10 < d10.f2218a.size(); i10++) {
                d10.f2218a.valueAt(i10).f2220a.clear();
            }
        }
        if (dVar4 != null) {
            d10.f2219b++;
        }
        this.R0.f2251f = true;
        X(false);
        requestLayout();
    }

    public void setChildDrawingOrderCallback(@Nullable g gVar) {
        if (gVar != this.Z0) {
            this.Z0 = gVar;
            setChildrenDrawingOrderEnabled(gVar != null);
        }
    }

    public void setClipToPadding(boolean z10) {
        if (z10 != this.f2149g) {
            N();
        }
        this.f2149g = z10;
        super.setClipToPadding(z10);
        if (this.f2168s) {
            requestLayout();
        }
    }

    public void setEdgeEffectFactory(@NonNull EdgeEffectFactory edgeEffectFactory) {
        Objects.requireNonNull(edgeEffectFactory);
        this.f2172u0 = edgeEffectFactory;
        N();
    }

    public void setHasFixedSize(boolean z10) {
        this.f2166r = z10;
    }

    public void setItemAnimator(@Nullable ItemAnimator itemAnimator) {
        ItemAnimator itemAnimator2 = this.f2179z0;
        if (itemAnimator2 != null) {
            itemAnimator2.f();
            this.f2179z0.f2180a = null;
        }
        this.f2179z0 = itemAnimator;
        if (itemAnimator != null) {
            itemAnimator.f2180a = this.W0;
        }
    }

    public void setItemViewCacheSize(int i10) {
        p pVar = this.f2140b;
        pVar.f2228e = i10;
        pVar.l();
    }

    @Deprecated
    public void setLayoutFrozen(boolean z10) {
        suppressLayout(z10);
    }

    public void setLayoutManager(@Nullable j jVar) {
        if (jVar != this.f2155l) {
            m0();
            if (this.f2155l != null) {
                ItemAnimator itemAnimator = this.f2179z0;
                if (itemAnimator != null) {
                    itemAnimator.f();
                }
                this.f2155l.q0(this.f2140b);
                this.f2155l.r0(this.f2140b);
                this.f2140b.b();
                if (this.f2164q) {
                    j jVar2 = this.f2155l;
                    p pVar = this.f2140b;
                    jVar2.f2204g = false;
                    jVar2.Z(this, pVar);
                }
                this.f2155l.E0(null);
                this.f2155l = null;
            } else {
                this.f2140b.b();
            }
            c cVar = this.f2145e;
            c.a aVar = cVar.f2343b;
            aVar.f2345a = 0;
            c.a aVar2 = aVar.f2346b;
            if (aVar2 != null) {
                aVar2.g();
            }
            int size = cVar.f2344c.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                }
                x xVar = (x) cVar.f2342a;
                Objects.requireNonNull(xVar);
                w K = K(cVar.f2344c.get(size));
                if (K != null) {
                    xVar.f2479a.f0(K, K.f2281p);
                    K.f2281p = 0;
                }
                cVar.f2344c.remove(size);
            }
            x xVar2 = (x) cVar.f2342a;
            int b10 = xVar2.b();
            for (int i10 = 0; i10 < b10; i10++) {
                View a10 = xVar2.a(i10);
                xVar2.f2479a.p(a10);
                a10.clearAnimation();
            }
            xVar2.f2479a.removeAllViews();
            this.f2155l = jVar;
            if (jVar != null) {
                if (jVar.f2199b == null) {
                    jVar.E0(this);
                    if (this.f2164q) {
                        this.f2155l.f2204g = true;
                    }
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append("LayoutManager ");
                    sb.append(jVar);
                    sb.append(" is already attached to a RecyclerView:");
                    throw new IllegalArgumentException(b.a(jVar.f2199b, sb));
                }
            }
            this.f2140b.l();
            requestLayout();
        }
    }

    @Deprecated
    public void setLayoutTransition(LayoutTransition layoutTransition) {
        if (layoutTransition == null) {
            super.setLayoutTransition(null);
            return;
        }
        throw new IllegalArgumentException("Providing a LayoutTransition into RecyclerView is not supported. Please use setItemAnimator() instead for animating changes to the items in this RecyclerView");
    }

    public void setNestedScrollingEnabled(boolean z10) {
        getScrollingChildHelper().l(z10);
    }

    public void setOnFlingListener(@Nullable l lVar) {
        this.I0 = lVar;
    }

    @Deprecated
    public void setOnScrollListener(@Nullable n nVar) {
        this.S0 = nVar;
    }

    public void setPreserveFocusAfterLayout(boolean z10) {
        this.N0 = z10;
    }

    public void setRecycledViewPool(@Nullable o oVar) {
        p pVar = this.f2140b;
        o oVar2 = pVar.f2230g;
        if (oVar2 != null) {
            oVar2.f2219b--;
        }
        pVar.f2230g = oVar;
        if (oVar != null && RecyclerView.this.getAdapter() != null) {
            pVar.f2230g.f2219b++;
        }
    }

    public void setRecyclerListener(@Nullable q qVar) {
        this.f2156m = qVar;
    }

    /* access modifiers changed from: package-private */
    public void setScrollState(int i10) {
        s sVar;
        if (i10 != this.A0) {
            this.A0 = i10;
            if (i10 != 2) {
                this.O0.c();
                j jVar = this.f2155l;
                if (!(jVar == null || (sVar = jVar.f2202e) == null)) {
                    sVar.d();
                }
            }
            j jVar2 = this.f2155l;
            if (jVar2 != null) {
                jVar2.o0(i10);
            }
            n nVar = this.S0;
            if (nVar != null) {
                nVar.a(this, i10);
            }
            List<n> list = this.T0;
            if (list != null) {
                int size = list.size();
                while (true) {
                    size--;
                    if (size >= 0) {
                        this.T0.get(size).a(this, i10);
                    } else {
                        return;
                    }
                }
            }
        }
    }

    public void setScrollingTouchSlop(int i10) {
        ViewConfiguration viewConfiguration = ViewConfiguration.get(getContext());
        if (i10 != 0) {
            if (i10 != 1) {
                Log.w("RecyclerView", "setScrollingTouchSlop(): bad argument constant " + i10 + "; using default value");
            } else {
                this.H0 = viewConfiguration.getScaledPagingTouchSlop();
                return;
            }
        }
        this.H0 = viewConfiguration.getScaledTouchSlop();
    }

    public void setViewCacheExtension(@Nullable u uVar) {
        Objects.requireNonNull(this.f2140b);
    }

    public boolean startNestedScroll(int i10) {
        return getScrollingChildHelper().m(i10);
    }

    public void stopNestedScroll() {
        getScrollingChildHelper().o();
    }

    public final void suppressLayout(boolean z10) {
        if (z10 != this.f2177y) {
            i("Do not suppressLayout in layout or scroll");
            if (!z10) {
                this.f2177y = false;
                if (!(!this.f2175x || this.f2155l == null || this.f2154k == null)) {
                    requestLayout();
                }
                this.f2175x = false;
                return;
            }
            long uptimeMillis = SystemClock.uptimeMillis();
            onTouchEvent(MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, Constant.VOLUME_FLOAT_MIN, Constant.VOLUME_FLOAT_MIN, 0));
            this.f2177y = true;
            this.f2157m0 = true;
            m0();
        }
    }

    public boolean t(int i10, int i11, int[] iArr, int[] iArr2, int i12) {
        return getScrollingChildHelper().d(i10, i11, iArr, null, i12);
    }

    public final void u(int i10, int i11, int i12, int i13, int[] iArr, int i14, @NonNull int[] iArr2) {
        getScrollingChildHelper().e(i10, i11, i12, i13, iArr, i14, iArr2);
    }

    public void v(int i10, int i11) {
        this.f2171t0++;
        int scrollX = getScrollX();
        int scrollY = getScrollY();
        onScrollChanged(scrollX, scrollY, scrollX - i10, scrollY - i11);
        n nVar = this.S0;
        if (nVar != null) {
            nVar.b(this, i10, i11);
        }
        List<n> list = this.T0;
        if (list != null) {
            for (int size = list.size() - 1; size >= 0; size--) {
                this.T0.get(size).b(this, i10, i11);
            }
        }
        this.f2171t0--;
    }

    public void w() {
        if (this.f2178y0 == null) {
            EdgeEffect a10 = this.f2172u0.a(this);
            this.f2178y0 = a10;
            if (this.f2149g) {
                a10.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
            } else {
                a10.setSize(getMeasuredWidth(), getMeasuredHeight());
            }
        }
    }

    public void x() {
        if (this.f2173v0 == null) {
            EdgeEffect a10 = this.f2172u0.a(this);
            this.f2173v0 = a10;
            if (this.f2149g) {
                a10.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
            } else {
                a10.setSize(getMeasuredHeight(), getMeasuredWidth());
            }
        }
    }

    public void y() {
        if (this.f2176x0 == null) {
            EdgeEffect a10 = this.f2172u0.a(this);
            this.f2176x0 = a10;
            if (this.f2149g) {
                a10.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
            } else {
                a10.setSize(getMeasuredHeight(), getMeasuredWidth());
            }
        }
    }

    public void z() {
        if (this.f2174w0 == null) {
            EdgeEffect a10 = this.f2172u0.a(this);
            this.f2174w0 = a10;
            if (this.f2149g) {
                a10.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
            } else {
                a10.setSize(getMeasuredWidth(), getMeasuredHeight());
            }
        }
    }

    public RecyclerView(@NonNull Context context, @Nullable AttributeSet attributeSet, int i10) {
        super(context, attributeSet, i10);
        TypedArray typedArray;
        char c10;
        ClassLoader classLoader;
        Constructor<? extends U> constructor;
        this.f2138a = new r();
        this.f2140b = new p();
        this.f2147f = new d0();
        this.h = new Rect();
        this.f2152i = new Rect();
        this.f2153j = new RectF();
        this.f2158n = new ArrayList<>();
        this.f2160o = new ArrayList<>();
        this.f2170t = 0;
        this.f2165q0 = false;
        this.f2167r0 = false;
        this.f2169s0 = 0;
        this.f2171t0 = 0;
        this.f2172u0 = new EdgeEffectFactory();
        this.f2179z0 = new l();
        this.A0 = 0;
        this.B0 = -1;
        this.L0 = Float.MIN_VALUE;
        this.M0 = Float.MIN_VALUE;
        this.N0 = true;
        this.O0 = new v();
        this.Q0 = new n.b();
        this.R0 = new t();
        this.U0 = false;
        this.V0 = false;
        this.W0 = new h();
        this.X0 = false;
        this.f2139a1 = new int[2];
        this.f2143c1 = new int[2];
        this.d1 = new int[2];
        this.f2146e1 = new int[2];
        this.f2148f1 = new ArrayList();
        this.f2150g1 = new a();
        this.f2151h1 = new c();
        setScrollContainer(true);
        setFocusableInTouchMode(true);
        ViewConfiguration viewConfiguration = ViewConfiguration.get(context);
        this.H0 = viewConfiguration.getScaledTouchSlop();
        this.L0 = viewConfiguration.getScaledHorizontalScrollFactor();
        this.M0 = viewConfiguration.getScaledVerticalScrollFactor();
        this.J0 = viewConfiguration.getScaledMinimumFlingVelocity();
        this.K0 = viewConfiguration.getScaledMaximumFlingVelocity();
        setWillNotDraw(getOverScrollMode() == 2);
        this.f2179z0.f2180a = this.W0;
        this.f2144d = new a(new y(this));
        this.f2145e = new c(new x(this));
        WeakHashMap<View, j0.m> weakHashMap = ViewCompat.f1593a;
        if (getImportantForAutofill() == 0) {
            setImportantForAutofill(8);
        }
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
        this.f2161o0 = (AccessibilityManager) getContext().getSystemService("accessibility");
        setAccessibilityDelegateCompat(new z(this));
        int[] iArr = R$styleable.RecyclerView;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, iArr, i10, 0);
        if (Build.VERSION.SDK_INT >= 29) {
            saveAttributeDataForStyleable(context, iArr, attributeSet, obtainStyledAttributes, i10, 0);
        }
        String string = obtainStyledAttributes.getString(R$styleable.RecyclerView_layoutManager);
        if (obtainStyledAttributes.getInt(R$styleable.RecyclerView_android_descendantFocusability, -1) == -1) {
            setDescendantFocusability(262144);
        }
        this.f2149g = obtainStyledAttributes.getBoolean(R$styleable.RecyclerView_android_clipToPadding, true);
        if (obtainStyledAttributes.getBoolean(R$styleable.RecyclerView_fastScrollEnabled, false)) {
            StateListDrawable stateListDrawable = (StateListDrawable) obtainStyledAttributes.getDrawable(R$styleable.RecyclerView_fastScrollVerticalThumbDrawable);
            Drawable drawable = obtainStyledAttributes.getDrawable(R$styleable.RecyclerView_fastScrollVerticalTrackDrawable);
            StateListDrawable stateListDrawable2 = (StateListDrawable) obtainStyledAttributes.getDrawable(R$styleable.RecyclerView_fastScrollHorizontalThumbDrawable);
            Drawable drawable2 = obtainStyledAttributes.getDrawable(R$styleable.RecyclerView_fastScrollHorizontalTrackDrawable);
            if (stateListDrawable == null || drawable == null || stateListDrawable2 == null || drawable2 == null) {
                throw new IllegalArgumentException(b.a(this, com.duokan.airkan.server.f.a("Trying to set fast scroller without both required drawables.")));
            }
            Resources resources = getContext().getResources();
            typedArray = obtainStyledAttributes;
            c10 = 2;
            new m(this, stateListDrawable, drawable, stateListDrawable2, drawable2, resources.getDimensionPixelSize(R$dimen.fastscroll_default_thickness), resources.getDimensionPixelSize(R$dimen.fastscroll_minimum_range), resources.getDimensionPixelOffset(R$dimen.fastscroll_margin));
        } else {
            typedArray = obtainStyledAttributes;
            c10 = 2;
        }
        typedArray.recycle();
        if (string != null) {
            String trim = string.trim();
            if (!trim.isEmpty()) {
                if (trim.charAt(0) == '.') {
                    trim = context.getPackageName() + trim;
                } else if (!trim.contains(".")) {
                    trim = RecyclerView.class.getPackage().getName() + '.' + trim;
                }
                try {
                    if (isInEditMode()) {
                        classLoader = getClass().getClassLoader();
                    } else {
                        classLoader = context.getClassLoader();
                    }
                    Class<? extends U> asSubclass = Class.forName(trim, false, classLoader).asSubclass(j.class);
                    Object[] objArr = null;
                    try {
                        constructor = asSubclass.getConstructor(f2136j1);
                        Object[] objArr2 = new Object[4];
                        objArr2[0] = context;
                        objArr2[1] = attributeSet;
                        objArr2[c10] = Integer.valueOf(i10);
                        objArr2[3] = 0;
                        objArr = objArr2;
                    } catch (NoSuchMethodException e10) {
                        try {
                            constructor = asSubclass.getConstructor(new Class[0]);
                        } catch (NoSuchMethodException e11) {
                            e11.initCause(e10);
                            throw new IllegalStateException(attributeSet.getPositionDescription() + ": Error creating LayoutManager " + trim, e11);
                        }
                    }
                    constructor.setAccessible(true);
                    setLayoutManager((j) constructor.newInstance(objArr));
                } catch (ClassNotFoundException e12) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Unable to find LayoutManager " + trim, e12);
                } catch (InvocationTargetException e13) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + trim, e13);
                } catch (InstantiationException e14) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + trim, e14);
                } catch (IllegalAccessException e15) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Cannot access non-public constructor " + trim, e15);
                } catch (ClassCastException e16) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Class is not a LayoutManager " + trim, e16);
                }
            }
        }
        int i11 = Build.VERSION.SDK_INT;
        int[] iArr2 = f2135i1;
        TypedArray obtainStyledAttributes2 = context.obtainStyledAttributes(attributeSet, iArr2, i10, 0);
        if (i11 >= 29) {
            saveAttributeDataForStyleable(context, iArr2, attributeSet, obtainStyledAttributes2, i10, 0);
        }
        boolean z10 = obtainStyledAttributes2.getBoolean(0, true);
        obtainStyledAttributes2.recycle();
        setNestedScrollingEnabled(z10);
    }

    public static class LayoutParams extends ViewGroup.MarginLayoutParams {

        /* renamed from: a  reason: collision with root package name */
        public w f2188a;

        /* renamed from: b  reason: collision with root package name */
        public final Rect f2189b = new Rect();

        /* renamed from: c  reason: collision with root package name */
        public boolean f2190c = true;

        /* renamed from: d  reason: collision with root package name */
        public boolean f2191d = false;

        public LayoutParams(Context context, AttributeSet attributeSet) {
            super(context, attributeSet);
        }

        public int a() {
            return this.f2188a.e();
        }

        public boolean b() {
            return this.f2188a.o();
        }

        public boolean c() {
            return this.f2188a.l();
        }

        public LayoutParams(int i10, int i11) {
            super(i10, i11);
        }

        public LayoutParams(ViewGroup.MarginLayoutParams marginLayoutParams) {
            super(marginLayoutParams);
        }

        public LayoutParams(ViewGroup.LayoutParams layoutParams) {
            super(layoutParams);
        }

        public LayoutParams(LayoutParams layoutParams) {
            super((ViewGroup.LayoutParams) layoutParams);
        }
    }

    @RestrictTo({RestrictTo.Scope.LIBRARY})
    public static class SavedState extends AbsSavedState {
        public static final Parcelable.Creator<SavedState> CREATOR = new a();

        /* renamed from: c  reason: collision with root package name */
        public Parcelable f2192c;

        public static class a implements Parcelable.ClassLoaderCreator<SavedState> {
            /* Return type fixed from 'java.lang.Object' to match base method */
            @Override // android.os.Parcelable.ClassLoaderCreator
            public SavedState createFromParcel(Parcel parcel, ClassLoader classLoader) {
                return new SavedState(parcel, classLoader);
            }

            @Override // android.os.Parcelable.Creator
            public Object[] newArray(int i10) {
                return new SavedState[i10];
            }

            @Override // android.os.Parcelable.Creator
            public Object createFromParcel(Parcel parcel) {
                return new SavedState(parcel, null);
            }
        }

        public SavedState(Parcel parcel, ClassLoader classLoader) {
            super(parcel, classLoader);
            this.f2192c = parcel.readParcelable(classLoader == null ? j.class.getClassLoader() : classLoader);
        }

        @Override // androidx.customview.view.AbsSavedState
        public void writeToParcel(Parcel parcel, int i10) {
            parcel.writeParcelable(this.f1662a, i10);
            parcel.writeParcelable(this.f2192c, 0);
        }

        public SavedState(Parcelable parcelable) {
            super(parcelable);
        }
    }

    @Override // android.view.ViewGroup
    public ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        j jVar = this.f2155l;
        if (jVar != null) {
            return jVar.v(layoutParams);
        }
        throw new IllegalStateException(b.a(this, com.duokan.airkan.server.f.a("RecyclerView has no LayoutManager")));
    }
}
