package androidx.constraintlayout.solver.widgets;

import androidx.constraintlayout.solver.widgets.ConstraintAnchor;
import java.util.ArrayList;

/* compiled from: Snapshot */
public class j {

    /* renamed from: a  reason: collision with root package name */
    public int f1386a;

    /* renamed from: b  reason: collision with root package name */
    public int f1387b;

    /* renamed from: c  reason: collision with root package name */
    public int f1388c;

    /* renamed from: d  reason: collision with root package name */
    public int f1389d;

    /* renamed from: e  reason: collision with root package name */
    public ArrayList<a> f1390e = new ArrayList<>();

    /* compiled from: Snapshot */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public ConstraintAnchor f1391a;

        /* renamed from: b  reason: collision with root package name */
        public ConstraintAnchor f1392b;

        /* renamed from: c  reason: collision with root package name */
        public int f1393c;

        /* renamed from: d  reason: collision with root package name */
        public ConstraintAnchor.Strength f1394d;

        /* renamed from: e  reason: collision with root package name */
        public int f1395e;

        public a(ConstraintAnchor constraintAnchor) {
            this.f1391a = constraintAnchor;
            this.f1392b = constraintAnchor.f1279d;
            this.f1393c = constraintAnchor.b();
            this.f1394d = constraintAnchor.f1282g;
            this.f1395e = constraintAnchor.h;
        }
    }

    public j(ConstraintWidget constraintWidget) {
        this.f1386a = constraintWidget.I;
        this.f1387b = constraintWidget.J;
        this.f1388c = constraintWidget.n();
        this.f1389d = constraintWidget.h();
        ArrayList<ConstraintAnchor> arrayList = constraintWidget.B;
        int size = arrayList.size();
        for (int i10 = 0; i10 < size; i10++) {
            this.f1390e.add(new a(arrayList.get(i10)));
        }
    }
}
