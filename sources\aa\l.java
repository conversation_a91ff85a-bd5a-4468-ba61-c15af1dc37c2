package aa;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/* compiled from: ASN1Object */
public abstract class l implements e {
    @Override // aa.e
    public abstract q c();

    public byte[] e() throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        new p(byteArrayOutputStream).h(this);
        return byteArrayOutputStream.toByteArray();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof e)) {
            return false;
        }
        return c().equals(((e) obj).c());
    }

    public byte[] f(String str) throws IOException {
        if (str.equals("DER")) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            new y0(byteArrayOutputStream).h(this);
            return byteArrayOutputStream.toByteArray();
        } else if (!str.equals("DL")) {
            return e();
        } else {
            ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
            new l1(byteArrayOutputStream2).h(this);
            return byteArrayOutputStream2.toByteArray();
        }
    }

    public int hashCode() {
        return c().hashCode();
    }
}
