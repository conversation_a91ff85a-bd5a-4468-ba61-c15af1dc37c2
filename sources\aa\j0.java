package aa;

import java.io.IOException;
import org.spongycastle.asn1.ASN1ParsingException;

/* compiled from: BERTaggedObjectParser */
public class j0 implements e, q1 {

    /* renamed from: a  reason: collision with root package name */
    public boolean f191a;

    /* renamed from: b  reason: collision with root package name */
    public int f192b;

    /* renamed from: c  reason: collision with root package name */
    public v f193c;

    public j0(boolean z10, int i10, v vVar) {
        this.f191a = z10;
        this.f192b = i10;
        this.f193c = vVar;
    }

    @Override // aa.e
    public q c() {
        try {
            return d();
        } catch (IOException e10) {
            throw new ASN1ParsingException(e10.getMessage());
        }
    }

    @Override // aa.q1
    public q d() throws IOException {
        return this.f193c.b(this.f191a, this.f192b);
    }
}
