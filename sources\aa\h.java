package aa;

import java.io.IOException;
import mb.a;

/* compiled from: ASN1GeneralizedTime */
public class h extends q {

    /* renamed from: a  reason: collision with root package name */
    public byte[] f182a;

    public h(byte[] bArr) {
        this.f182a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof h)) {
            return false;
        }
        return a.a(this.f182a, ((h) qVar).f182a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(24, this.f182a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f182a);
    }

    @Override // aa.q
    public int i() {
        int length = this.f182a.length;
        return v1.a(length) + 1 + length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }
}
