package androidx.appcompat.app;

import android.view.View;
import androidx.core.view.ViewCompat;
import j0.m;
import j0.o;
import java.util.WeakHashMap;

/* compiled from: AppCompatDelegateImpl */
public class j extends o {

    /* renamed from: d  reason: collision with root package name */
    public final /* synthetic */ AppCompatDelegateImpl f446d;

    public j(AppCompatDelegateImpl appCompatDelegateImpl) {
        this.f446d = appCompatDelegateImpl;
    }

    @Override // j0.n
    public void d(View view) {
        this.f446d.f382o.setAlpha(1.0f);
        this.f446d.f388r.d(null);
        this.f446d.f388r = null;
    }

    @Override // j0.n, j0.o
    public void e(View view) {
        this.f446d.f382o.setVisibility(0);
        this.f446d.f382o.sendAccessibilityEvent(32);
        if (this.f446d.f382o.getParent() instanceof View) {
            WeakHashMap<View, m> weakHashMap = ViewCompat.f1593a;
            ((View) this.f446d.f382o.getParent()).requestApplyInsets();
        }
    }
}
