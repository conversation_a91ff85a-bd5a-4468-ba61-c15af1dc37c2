package androidx.appcompat.widget;

import android.content.Context;
import android.graphics.RectF;
import android.os.Build;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import com.duokan.airkan.common.Constant;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

/* compiled from: AppCompatTextViewAutoSizeHelper */
public class r {

    /* renamed from: k  reason: collision with root package name */
    public static final RectF f1188k = new RectF();

    /* renamed from: l  reason: collision with root package name */
    public static ConcurrentHashMap<String, Method> f1189l = new ConcurrentHashMap<>();

    /* renamed from: m  reason: collision with root package name */
    public static ConcurrentHashMap<String, Field> f1190m = new ConcurrentHashMap<>();

    /* renamed from: a  reason: collision with root package name */
    public int f1191a = 0;

    /* renamed from: b  reason: collision with root package name */
    public boolean f1192b = false;

    /* renamed from: c  reason: collision with root package name */
    public float f1193c = -1.0f;

    /* renamed from: d  reason: collision with root package name */
    public float f1194d = -1.0f;

    /* renamed from: e  reason: collision with root package name */
    public float f1195e = -1.0f;

    /* renamed from: f  reason: collision with root package name */
    public int[] f1196f = new int[0];

    /* renamed from: g  reason: collision with root package name */
    public boolean f1197g = false;
    @NonNull
    public final TextView h;

    /* renamed from: i  reason: collision with root package name */
    public final Context f1198i;

    /* renamed from: j  reason: collision with root package name */
    public final c f1199j;

    @RequiresApi(23)
    /* compiled from: AppCompatTextViewAutoSizeHelper */
    public static class a extends c {
    }

    @RequiresApi(29)
    /* compiled from: AppCompatTextViewAutoSizeHelper */
    public static class b extends a {
    }

    /* compiled from: AppCompatTextViewAutoSizeHelper */
    public static class c {
    }

    public r(@NonNull TextView textView) {
        this.h = textView;
        this.f1198i = textView.getContext();
        if (Build.VERSION.SDK_INT >= 29) {
            this.f1199j = new b();
        } else {
            this.f1199j = new a();
        }
    }

    public final int[] a(int[] iArr) {
        int length = iArr.length;
        if (length == 0) {
            return iArr;
        }
        Arrays.sort(iArr);
        ArrayList arrayList = new ArrayList();
        for (int i10 : iArr) {
            if (i10 > 0 && Collections.binarySearch(arrayList, Integer.valueOf(i10)) < 0) {
                arrayList.add(Integer.valueOf(i10));
            }
        }
        if (length == arrayList.size()) {
            return iArr;
        }
        int size = arrayList.size();
        int[] iArr2 = new int[size];
        for (int i11 = 0; i11 < size; i11++) {
            iArr2[i11] = ((Integer) arrayList.get(i11)).intValue();
        }
        return iArr2;
    }

    public final boolean b() {
        if (!d() || this.f1191a != 1) {
            this.f1192b = false;
        } else {
            if (!this.f1197g || this.f1196f.length == 0) {
                int floor = ((int) Math.floor((double) ((this.f1195e - this.f1194d) / this.f1193c))) + 1;
                int[] iArr = new int[floor];
                for (int i10 = 0; i10 < floor; i10++) {
                    iArr[i10] = Math.round((((float) i10) * this.f1193c) + this.f1194d);
                }
                this.f1196f = a(iArr);
            }
            this.f1192b = true;
        }
        return this.f1192b;
    }

    public final boolean c() {
        int[] iArr = this.f1196f;
        int length = iArr.length;
        boolean z10 = length > 0;
        this.f1197g = z10;
        if (z10) {
            this.f1191a = 1;
            this.f1194d = (float) iArr[0];
            this.f1195e = (float) iArr[length - 1];
            this.f1193c = -1.0f;
        }
        return z10;
    }

    public final boolean d() {
        return !(this.h instanceof AppCompatEditText);
    }

    public final void e(float f10, float f11, float f12) throws IllegalArgumentException {
        if (f10 <= Constant.VOLUME_FLOAT_MIN) {
            throw new IllegalArgumentException("Minimum auto-size text size (" + f10 + "px) is less or equal to (0px)");
        } else if (f11 <= f10) {
            throw new IllegalArgumentException("Maximum auto-size text size (" + f11 + "px) is less or equal to minimum auto-size text size (" + f10 + "px)");
        } else if (f12 > Constant.VOLUME_FLOAT_MIN) {
            this.f1191a = 1;
            this.f1194d = f10;
            this.f1195e = f11;
            this.f1193c = f12;
            this.f1197g = false;
        } else {
            throw new IllegalArgumentException("The auto-size step granularity (" + f12 + "px) is less or equal to (0px)");
        }
    }
}
