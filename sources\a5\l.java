package a5;

import io.netty.util.ResourceLeakDetector;
import java.lang.reflect.Constructor;
import s0.q;

/* compiled from: ResourceLeakDetectorFactory */
public abstract class l {

    /* renamed from: a  reason: collision with root package name */
    public static final d5.a f132a = q.b0(l.class.getName());

    /* renamed from: b  reason: collision with root package name */
    public static volatile l f133b = new a();

    /* compiled from: ResourceLeakDetectorFactory */
    public static final class a extends l {

        /* renamed from: c  reason: collision with root package name */
        public final Constructor<?> f134c;

        /* renamed from: d  reason: collision with root package name */
        public final Constructor<?> f135d;

        /* JADX WARNING: Removed duplicated region for block: B:20:0x0067 A[Catch:{ all -> 0x007c }] */
        /* JADX WARNING: Removed duplicated region for block: B:21:0x0076 A[Catch:{ all -> 0x007c }] */
        /* Code decompiled incorrectly, please refer to instructions dump. */
        public a() {
            /*
            // Method dump skipped, instructions count: 133
            */
            throw new UnsupportedOperationException("Method not decompiled: a5.l.a.<init>():void");
        }

        @Override // a5.l
        public <T> ResourceLeakDetector<T> b(Class<T> cls, int i10) {
            Constructor<?> constructor = this.f135d;
            if (constructor != null) {
                try {
                    ResourceLeakDetector<T> resourceLeakDetector = (ResourceLeakDetector) constructor.newInstance(cls, Integer.valueOf(i10));
                    l.f132a.debug("Loaded custom ResourceLeakDetector: {}", this.f135d.getDeclaringClass().getName());
                    return resourceLeakDetector;
                } catch (Throwable th) {
                    l.f132a.error("Could not load custom resource leak detector provided: {} with the given resource: {}", this.f135d.getDeclaringClass().getName(), cls, th);
                }
            }
            ResourceLeakDetector<T> resourceLeakDetector2 = new ResourceLeakDetector<>(cls, i10);
            l.f132a.debug("Loaded default ResourceLeakDetector: {}", resourceLeakDetector2);
            return resourceLeakDetector2;
        }

        @Override // a5.l
        public <T> ResourceLeakDetector<T> c(Class<T> cls, int i10, long j10) {
            Constructor<?> constructor = this.f134c;
            if (constructor != null) {
                try {
                    ResourceLeakDetector<T> resourceLeakDetector = (ResourceLeakDetector) constructor.newInstance(cls, Integer.valueOf(i10), Long.valueOf(j10));
                    l.f132a.debug("Loaded custom ResourceLeakDetector: {}", this.f134c.getDeclaringClass().getName());
                    return resourceLeakDetector;
                } catch (Throwable th) {
                    l.f132a.error("Could not load custom resource leak detector provided: {} with the given resource: {}", this.f134c.getDeclaringClass().getName(), cls, th);
                }
            }
            ResourceLeakDetector<T> resourceLeakDetector2 = new ResourceLeakDetector<>(cls, i10);
            l.f132a.debug("Loaded default ResourceLeakDetector: {}", resourceLeakDetector2);
            return resourceLeakDetector2;
        }
    }

    public final <T> ResourceLeakDetector<T> a(Class<T> cls) {
        return b(cls, ResourceLeakDetector.h);
    }

    public <T> ResourceLeakDetector<T> b(Class<T> cls, int i10) {
        return c(cls, ResourceLeakDetector.h, Long.MAX_VALUE);
    }

    @Deprecated
    public abstract <T> ResourceLeakDetector<T> c(Class<T> cls, int i10, long j10);
}
