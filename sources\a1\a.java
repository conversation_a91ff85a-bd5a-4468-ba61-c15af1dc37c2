package a1;

import b1.h;
import java.lang.reflect.Method;

/* compiled from: SystemPropertiesIA */
public class a {

    /* renamed from: a  reason: collision with root package name */
    public static Class<?> f7a;

    /* renamed from: b  reason: collision with root package name */
    public static Method f8b;

    static {
        try {
            f7a = Class.forName("android.os.SystemProperties", false, Thread.currentThread().getContextClassLoader());
        } catch (Exception e10) {
            h.f(5, "SystemPropertiesIA", "Failed to reflect SystemProperties", e10, new Object[0]);
        }
    }
}
