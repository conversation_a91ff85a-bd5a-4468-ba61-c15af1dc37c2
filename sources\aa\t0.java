package aa;

import java.io.IOException;
import mb.a;
import mb.c;

/* compiled from: DERIA5String */
public class t0 extends q implements w {

    /* renamed from: a  reason: collision with root package name */
    public final byte[] f231a;

    public t0(byte[] bArr) {
        this.f231a = bArr;
    }

    @Override // aa.q
    public boolean g(q qVar) {
        if (!(qVar instanceof t0)) {
            return false;
        }
        return a.a(this.f231a, ((t0) qVar).f231a);
    }

    @Override // aa.w
    public String getString() {
        return c.a(this.f231a);
    }

    @Override // aa.q
    public void h(p pVar) throws IOException {
        pVar.e(22, this.f231a);
    }

    @Override // aa.l
    public int hashCode() {
        return a.d(this.f231a);
    }

    @Override // aa.q
    public int i() {
        return v1.a(this.f231a.length) + 1 + this.f231a.length;
    }

    @Override // aa.q
    public boolean k() {
        return false;
    }

    public String toString() {
        return getString();
    }
}
